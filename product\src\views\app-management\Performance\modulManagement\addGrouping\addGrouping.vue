<template>
  <div class="addGrouping">
    <el-form
      :model="form"
      :rules="rules"
      inline
      ref="form"
      label-position="top"
      class="newForm"
    >
      <el-form-item
        label="背景图"
        class="form-title"
      >
        <el-upload
          class="form-icon-uploader"
          action="/"
          :before-upload="handleImg"
          :http-request="imgUpload"
          :show-file-list="false"
        >
          <img
            v-if="file.fullUrl"
            :src="file.fullUrl"
            class="user-img"
          >
          <i
            v-else
            class="el-icon-plus user-uploader-icon"
          ></i>
        </el-upload>
      </el-form-item>

      <el-form-item
        label="文本左边距"
        class="form-input"
      >
        <el-input
          placeholder="请输入文本左边距"
          v-model="form.locationLeft"
          clearable
        >
        </el-input>
      </el-form-item>
      <!-- <el-form-item
        label="文本右边距"
        class="form-input"
      >
        <el-input
          placeholder="请输入文本右边距"
          v-model="form.localtionRight"
          clearable
        >
        </el-input>
      </el-form-item> -->
      <el-form-item
        label="文本顶边距"
        class="form-input"
      >
        <el-input
          placeholder="请输入文本顶边距"
          v-model="form.localtionTop"
          clearable
        >
        </el-input>
      </el-form-item>
      <!-- <el-form-item
        label="文本底边距"
        class="form-input"
      >
        <el-input
          placeholder="请输入文本底边距"
          v-model="form.localtionBottom"
          clearable
        >
        </el-input>
      </el-form-item> -->

      <el-form-item
        label="边距单位"
        class="form-input"
      >
        <el-input
          placeholder="请输入边距单位"
          v-model="form.localtionUnit"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item
        label="排序"
        class="form-input"
      >
        <el-input
          placeholder="请输入排序"
          v-model="form.sort"
          clearable
        >
        </el-input>
      </el-form-item>

      <el-form-item
        label="选择分组"
        class="form-input"
      >
        <zy-select
          width="296"
          node-key="id"
          v-model="form.groupId"
          :props="{ label: 'value' }"
          :data="groupData"
          placeholder="请选择分组"
        ></zy-select>
      </el-form-item>
      <el-form-item
        prop="content"
        class="form-ue"
      >
        <span
          slot="label"
          class="label-button"
        >正文
        </span>
        <UEditor
          v-model="form.content"
          ref="ue"
          :maximumWords="2000"
        ></UEditor>
      </el-form-item>

      <div
        class="tableData"
        id="tableData"
      >
        <zy-table>
          <el-table
            :data="counters"
            stripe
            border
            ref="table"
            slot="zytable"
          >
            <el-table-column
              label="分组名称"
              prop="id"
            >
            </el-table-column>
            <el-table-column
              label="分组名称"
              prop="value"
            >
            </el-table-column>
          </el-table>
        </zy-table>
      </div>

      <div class="form-button">
        <el-button
          type="primary"
          @click="submitForm('form')"
        >确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      form: {
        backgroundImg: '',
        content: '',
        locationLeft: '',
        localtionRight: '',
        localtionBottom: '',
        localtionTop: '',
        localtionUnit: '',
        sort: ''
      },
      rules: {
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ]
      },
      file: {},
      groupData: [],
      counters: []
    }
  },
  created () {
    if (this.id) {
      this.getinfo()
    }
    this.getyearsummaryselects()
  },
  methods: {
    async getyearsummaryselects () {
      const res = await this.$api.appManagement.yearsummaryselects(this.id)
      console.log(res)
      this.groupData = res.data.groups
      this.counters = res.data.counters
    },
    async getinfo () {
      const res = await this.$api.appManagement.yearsummaryinfo(this.id)
      // console.log(res)
      var { data } = res
      this.form = data
      this.file.fullUrl = data.backgroundImg
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const param = new FormData()
      param.append('file', files.file)
      this.$api.general.fileImg(param).then(res => {
        var { data } = res
        this.file = data
      })
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/yearsummary/add'
          if (this.id) {
            url = '/yearsummary/edit'
          }
          this.$api.appManagement.yearsummary(url, {
            id: this.id,
            backgroundImg: this.file.fullUrl,
            content: this.form.content,
            locationLeft: this.form.locationLeft,
            // localtionRight: this.form.localtionRight,
            // localtionBottom: this.form.localtionBottom,
            localtionTop: this.form.localtionTop,
            localtionUnit: this.form.localtionUnit,
            sort: this.form.sort,
            groupId: this.form.groupId
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss" >
.addGrouping {
    width: 692px;
    height: 100%;
    padding: 24px 40px;

    #tableData {
        width: 100%;
        height: 300px;
    }
    .form-icon-uploader {
        width: 128px;
        height: 128px;
        border: 1px dashed #ccc;

        &:hover {
            border-color: #199bc5;
        }

        .user-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 128px;
            height: 128px;
            line-height: 128px;
            text-align: center;
        }

        .user-img {
            width: 128px;
            height: 128px;
            display: block;
        }
    }
}
</style>
