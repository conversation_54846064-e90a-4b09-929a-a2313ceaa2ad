/**
 * EasyUI for jQuery 1.7.0
 */
(function ($) {
  function _1 (_2, _3) {
    var _4 = $.data(_2, "linkbutton").options;
    if (_3) {
      $.extend(_4, _3);
    }
    if (_4.width || _4.height || _4.fit) {
      var _5 = $(_2);
      var _6 = _5.parent();
      var _7 = _5.is(":visible");
      if (!_7) {
        var _8 = $("<div style=\"display:none\"></div>").insertBefore(_2);
        var _9 = { position: _5.css("position"), display: _5.css("display"), left: _5.css("left") };
        _5.appendTo("body");
        _5.css({ position: "absolute", display: "inline-block", left: -20000 });
      }
      _5._size(_4, _6);
      var _a = _5.find(".l-btn-left");
      _a.css("margin-top", 0);
      _a.css("margin-top", parseInt((_5.height() - _a.height()) / 2) + "px");
      if (!_7) {
        _5.insertAfter(_8);
        _5.css(_9);
        _8.remove();
      }
    }
  };
  function _b (_c) {
    var _d = $.data(_c, "linkbutton").options;
    var t = $(_c).empty();
    t.addClass("l-btn").removeClass("l-btn-plain l-btn-selected l-btn-plain-selected l-btn-outline");
    t.removeClass("l-btn-small l-btn-medium l-btn-large").addClass("l-btn-" + _d.size);
    if (_d.plain) {
      t.addClass("l-btn-plain");
    }
    if (_d.outline) {
      t.addClass("l-btn-outline");
    }
    if (_d.selected) {
      t.addClass(_d.plain ? "l-btn-selected l-btn-plain-selected" : "l-btn-selected");
    }
    t.attr("group", _d.group || "");
    t.attr("id", _d.id || "");
    var _e = $("<span class=\"l-btn-left\"></span>").appendTo(t);
    if (_d.text) {
      $("<span class=\"l-btn-text\"></span>").html(_d.text).appendTo(_e);
    } else {
      $("<span class=\"l-btn-text l-btn-empty\">&nbsp;</span>").appendTo(_e);
    }
    if (_d.iconCls) {
      $("<span class=\"l-btn-icon\">&nbsp;</span>").addClass(_d.iconCls).appendTo(_e);
      _e.addClass("l-btn-icon-" + _d.iconAlign);
    }
    t.unbind(".linkbutton").bind("focus.linkbutton", function () {
      if (!_d.disabled) {
        $(this).addClass("l-btn-focus");
      }
    }).bind("blur.linkbutton", function () {
      $(this).removeClass("l-btn-focus");
    }).bind("click.linkbutton", function () {
      if (!_d.disabled) {
        if (_d.toggle) {
          if (_d.selected) {
            $(this).linkbutton("unselect");
          } else {
            $(this).linkbutton("select");
          }
        }
        _d.onClick.call(this);
      }
    });
    _f(_c, _d.selected);
    _10(_c, _d.disabled);
  };
  function _f (_11, _12) {
    var _13 = $.data(_11, "linkbutton").options;
    if (_12) {
      if (_13.group) {
        $("a.l-btn[group=\"" + _13.group + "\"]").each(function () {
          var o = $(this).linkbutton("options");
          if (o.toggle) {
            $(this).removeClass("l-btn-selected l-btn-plain-selected");
            o.selected = false;
          }
        });
      }
      $(_11).addClass(_13.plain ? "l-btn-selected l-btn-plain-selected" : "l-btn-selected");
      _13.selected = true;
    } else {
      if (!_13.group) {
        $(_11).removeClass("l-btn-selected l-btn-plain-selected");
        _13.selected = false;
      }
    }
  };
  function _10 (_14, _15) {
    var _16 = $.data(_14, "linkbutton");
    var _17 = _16.options;
    $(_14).removeClass("l-btn-disabled l-btn-plain-disabled");
    if (_15) {
      _17.disabled = true;
      var _18 = $(_14).attr("href");
      if (_18) {
        _16.href = _18;
        $(_14).attr("href", "javascript:;");
      }
      if (_14.onclick) {
        _16.onclick = _14.onclick;
        _14.onclick = null;
      }
      _17.plain ? $(_14).addClass("l-btn-disabled l-btn-plain-disabled") : $(_14).addClass("l-btn-disabled");
    } else {
      _17.disabled = false;
      if (_16.href) {
        $(_14).attr("href", _16.href);
      }
      if (_16.onclick) {
        _14.onclick = _16.onclick;
      }
    }
  };
  $.fn.linkbutton = function (_19, _1a) {
    if (typeof _19 == "string") {
      return $.fn.linkbutton.methods[_19](this, _1a);
    }
    _19 = _19 || {};
    return this.each(function () {
      var _1b = $.data(this, "linkbutton");
      if (_1b) {
        $.extend(_1b.options, _19);
      } else {
        $.data(this, "linkbutton", { options: $.extend({}, $.fn.linkbutton.defaults, $.fn.linkbutton.parseOptions(this), _19) });
        $(this)._propAttr("disabled", false);
        $(this).bind("_resize", function (e, _1c) {
          if ($(this).hasClass("easyui-fluid") || _1c) {
            _1(this);
          }
          return false;
        });
      }
      _b(this);
      _1(this);
    });
  };
  $.fn.linkbutton.methods = {
    options: function (jq) {
      return $.data(jq[0], "linkbutton").options;
    }, resize: function (jq, _1d) {
      return jq.each(function () {
        _1(this, _1d);
      });
    }, enable: function (jq) {
      return jq.each(function () {
        _10(this, false);
      });
    }, disable: function (jq) {
      return jq.each(function () {
        _10(this, true);
      });
    }, select: function (jq) {
      return jq.each(function () {
        _f(this, true);
      });
    }, unselect: function (jq) {
      return jq.each(function () {
        _f(this, false);
      });
    }
  };
  $.fn.linkbutton.parseOptions = function (_1e) {
    var t = $(_1e);
    return $.extend({}, $.parser.parseOptions(_1e, ["id", "iconCls", "iconAlign", "group", "size", "text", { plain: "boolean", toggle: "boolean", selected: "boolean", outline: "boolean" }]), { disabled: (t.attr("disabled") ? true : undefined), text: ($.trim(t.html()) || undefined), iconCls: (t.attr("icon") || t.attr("iconCls")) });
  };
  $.fn.linkbutton.defaults = {
    id: null, disabled: false, toggle: false, selected: false, outline: false, group: null, plain: false, text: "", iconCls: null, iconAlign: "left", size: "small", onClick: function () {
    }
  };
})(jQuery);

