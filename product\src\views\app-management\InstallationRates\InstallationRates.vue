<template>
  <div class="InstallationRates">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input placeholder="请输入内容"
                v-model="keyword"
                clearable
                @keyup.enter.native="search">
      </el-input>
      <el-select v-model="countUserType"
                 filterable
                 clearable
                 placeholder="请选择用户类型">
        <el-option v-for="item in countUserTypeData"
                   :key="item.id"
                   :label="item.value"
                   :value="item.id">
        </el-option>
      </el-select>
      <zy-select width="222"
                 node-key="id"
                 v-model="officeId"
                 :data="officeIdData"
                 placeholder="请选择所属机构"></zy-select>
      <el-select v-model="representerTeam"
                 filterable
                 clearable
                 placeholder="请选择代表团">
        <el-option v-for="item in representerTeamData"
                   :key="item.id"
                   :label="item.value"
                   :value="item.id">
        </el-option>
      </el-select>
    </screening-box>
    <div class="InstallationRatesBox">
      <div class="InstallationRatesFigure">
        <InstallationRatesRing title="APP安装率统计"
                               :data="work"></InstallationRatesRing>
      </div>
      <div class="tableData">
        <zy-table>
          <el-table :data="tableData"
                    stripe
                    border
                    slot="zytable">
            <el-table-column type="selection"
                             fixed="left"
                             width="60"></el-table-column>
            <el-table-column label="用户姓名"
                             prop="userName">
            </el-table-column>
            <el-table-column label="用户名照片">
              <template slot-scope="scope">
                <div class="table-img">
                  <img :src="scope.row.headImg"
                       alt="">
                </div>
              </template>
            </el-table-column>
            <el-table-column label="是否安装"
                             prop="isJoinApp">
            </el-table-column>
          </el-table>
        </zy-table>
      </div>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import InstallationRatesRing from './InstallationRates-ring/InstallationRates-ring'
export default {
  name: 'InstallationRates',
  data () {
    return {
      keyword: '',
      officeId: '',
      officeIdData: [],
      countUserType: '',
      countUserTypeData: [],
      representerTeam: '',
      representerTeamData: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      work: []
    }
  },
  components: {
    InstallationRatesRing
  },
  mounted () {
    this.treeList()
    this.joinappusertype()
    this.dictionaryPubkvs()
    this.joinapp()
  },
  methods: {
    search () {
      this.joinapp()
    },
    reset () {
      this.keyword = ''
      this.officeId = ''
      this.countUserType = ''
      this.representerTeam = ''
      this.joinapp()
    },
    async joinappusertype () {
      const res = await this.$api.appManagement.joinappusertype()
      var { data } = res
      this.countUserTypeData = data
    },
    /**
     *机构树
    */
    async treeList () {
      const res = await this.$api.systemSettings.treeList({ treeType: 1 })
      var { data } = res
      this.officeIdData = data
    },
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'representer_team'
      })
      var { data } = res
      this.representerTeamData = data.representer_team
    },
    async joinapp () {
      const res = await this.$api.appManagement.joinapp({
        pageNo: this.page,
        pageSize: this.pageSize,
        countUserType: this.countUserType,
        officeId: this.officeId,
        representerTeam: this.representerTeam,
        keyword: this.keyword
      })
      var { data, total } = res
      this.tableData = data.users
      this.total = total
      this.work = [
        { name: '已安装', num: data.hasJoinAppUsers },
        { name: '未安装', num: data.userAmount - data.hasJoinAppUsers }
      ]
    },
    howManyArticle (val) {
      this.joinapp()
    },
    whatPage (val) {
      this.joinapp()
    }

  }
}
</script>
<style lang="scss">
@import "./InstallationRates.scss";
</style>
