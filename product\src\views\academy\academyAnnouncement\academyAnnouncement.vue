<template>
  <div class="academyAnnouncement">
    <search-button-box @search-click="search"
                       @reset-click="reset">
      <template slot="button">
        <el-button icon="el-icon-plus"
                   @click="newData"
                   type="primary">新增</el-button>
        <el-button icon="el-icon-delete"
                   @click="deleteClick"
                   type="primary">删除</el-button>
      </template>
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <zy-select width="222"
                   node-key="id"
                   v-model="officeId"
                   :data="institutions"
                   placeholder="请选择发布部门"></zy-select>
      </template>
    </search-button-box>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="序号"
                           width="80"
                           prop="sort"></el-table-column>
          <el-table-column label="标题"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="details(scope.row)">{{scope.row.title}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="发布部门"
                           width="180"
                           prop="officeName"></el-table-column>
          <el-table-column label="关联书籍"
                           width="120"
                           prop="relevance">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="associated(scope.row)">关联书籍</el-button>
            </template>
          </el-table-column>
          <el-table-column label="关联活动"
                           width="120"
                           prop="relevance">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="activity(scope.row)">关联活动</el-button>
            </template>
          </el-table-column>
          <el-table-column label="发布日期"
                           width="190">
            <template slot-scope="scope">{{scope.row.publishDate|datefmt('YYYY-MM-DD HH:mm:ss')}}</template>
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="220">
            <template slot-scope="scope">
              <el-button type="primary"
                         plain
                         size="mini"
                         @click="release(scope.row)">{{scope.row.isPublish==1?'撤下':'发布'}}</el-button>
              <el-button @click="addeditor(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="show"
               :title="id==''?'新增读书公告':'修改读书公告'">
      <academyAnnouncementNew :id="id"
                              @callback="newCallback"> </academyAnnouncementNew>
    </zy-pop-up>
    <zy-pop-up v-model="detailsShow"
               title="读书公告详情">
      <academyAnnouncementDetails :id="id"> </academyAnnouncementDetails>
    </zy-pop-up>
    <zy-pop-up v-model="associatedShow"
               title="公告关联书籍">
      <announcementAssociated :id="id"
                              @callback="newCallback"> </announcementAssociated>
    </zy-pop-up>
    <zy-pop-up v-model="activityShow"
               title="公告关联活动">
      <announcementActivity :id="id"> </announcementActivity>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import academyAnnouncementNew from './academyAnnouncementNew'
import academyAnnouncementDetails from './academyAnnouncementDetails'
import announcementAssociated from './announcementAssociated'
import announcementActivity from './announcementActivity'
export default {
  name: 'academyAnnouncement',
  data () {
    return {
      keyword: '',
      officeId: '',
      institutions: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      id: '',
      show: false,
      detailsShow: false,
      associatedShow: false,
      activityShow: false
    }
  },
  mixins: [tableData],
  components: {
    academyAnnouncementNew,
    academyAnnouncementDetails,
    announcementAssociated,
    announcementActivity
  },
  mounted () {
    this.treeList()
    this.syNoticeList()
  },
  methods: {
    search () {
      this.syNoticeList()
    },
    reset () {
      this.keyword = ''
      this.officeId = ''
      this.syNoticeList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    associated (row) {
      this.id = row.id
      this.associatedShow = true
    },
    addeditor (row) {
      this.id = row.id
      this.show = true
    },
    activity (row) {
      this.id = row.id
      this.activityShow = true
    },
    newCallback () {
      this.syNoticeList()
      this.show = false
      this.associatedShow = false
    },
    /**
     *机构树
    */
    async treeList () {
      const res = await this.$api.systemSettings.treeList({})
      var { data } = res
      this.institutions = data
    },
    async syNoticeList () {
      const res = await this.$api.academy.syNoticeList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        officeId: this.officeId
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle () {
      this.syNoticeList()
    },
    whatPage () {
      this.syNoticeList()
    },
    release (row) {
      this.publish(row.id, row.isPublish == null ? 0 : row.isPublish)
    },
    async publish (id, publish) {
      const res = await this.$api.academy.publish({
        id: id,
        publish: publish
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.syNoticeList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的读书公告, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.syNoticeDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async syNoticeDel (id) {
      const res = await this.$api.academy.syNoticeDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.syNoticeList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.academyAnnouncement {
  height: 100%;
  width: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
