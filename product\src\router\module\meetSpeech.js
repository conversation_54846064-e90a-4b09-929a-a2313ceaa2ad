const meetSpeech = [
  {
    path: '/meetSpeechSubmit',
    name: 'meetSpeechSubmit',
    component: () => import('@meetSpeech/meetSpeechSubmit/')
  },
  {
    path: '/meetSpeechMine',
    name: 'meetSpeechMine',
    component: () => import('@meetSpeech/meetSpeechMine/')
  },
  {
    path: '/meetSpeechDraft',
    name: 'meetSpeechDraft',
    component: () => import('@meetSpeech/meetSpeechDraft/')
  },
  {
    path: '/meetSpeechAdoption',
    name: 'meetSpeechAdoption',
    component: () => import('@meetSpeech/meetSpeechAdoption/')
  },
  {
    path: '/meetSpeechAll',
    name: 'meetSpeechAll',
    component: () => import('@meetSpeech/meetSpeechAll/')
  },
  {
    path: '/meetSpeechIncluded',
    name: 'meetSpeechIncluded',
    component: () => import('@meetSpeech/meetSpeechIncluded/')
  },
  {
    path: '/meetSpeechNotice',
    name: 'meetSpeechNotice',
    component: () => import('@meetSpeech/meetSpeechNotice/')
  },
  {
    path: '/meetSpeechAdoptionDetail',
    name: 'meetSpeechAdoptionDetail',
    component: () => import('@meetSpeech/meetSpeechAdoption/detail')
  },
  {
    path: '/meetSpeechNoticeAdd',
    name: 'meetSpeechNoticeAdd',
    component: () => import('@meetSpeech/meetSpeechNotice/add')
  },
  {
    path: '/meetSpeechNoticeDetail',
    name: 'meetSpeechNoticeDetail',
    component: () => import('@meetSpeech/meetSpeechNotice/detail')
  }
]
export default meetSpeech
