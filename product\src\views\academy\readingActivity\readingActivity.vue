<template>
  <div class="readingActivity">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input placeholder="请输入关键词"
                v-model="keyword"
                clearable
                @keyup.enter.native="search">
      </el-input>
      <el-select v-model="schemeType"
                 filterable
                 clearable
                 placeholder="请选择活动类型">
        <el-option v-for="item in schemeTypeData"
                   :key="item.id"
                   :label="item.value"
                   :value="item.id">
        </el-option>
      </el-select>
      <el-select v-model="schemeStatus"
                 filterable
                 clearable
                 placeholder="请选择活动状态">
        <el-option v-for="item in schemeStatusData"
                   :key="item.id"
                   :label="item.value"
                   :value="item.id">
        </el-option>
      </el-select>
    </screening-box>
    <div class="button-box">
      <el-button icon="el-icon-plus"
                 @click="newData"
                 type="primary">新增</el-button>
      <el-button icon="el-icon-delete"
                 @click="deleteClick"
                 type="primary">删除</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="标题"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="details(scope.row)">{{scope.row.title}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="活动类型"
                           width="120"
                           prop="schemeType"></el-table-column>
          <el-table-column label="组织部门"
                           width="160"
                           prop="officeName"></el-table-column>
          <el-table-column label="活动状态"
                           width="100"
                           prop="schemeStatus"></el-table-column>
          <el-table-column label="关联书籍"
                           min-width="220"
                           prop="relevance">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="associated(scope.row)">{{scope.row.books}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="活动时间"
                           width="360">
            <template slot-scope="scope">{{scope.row.startTime|datefmt('YYYY-MM-DD HH:mm:ss')}} — {{scope.row.endTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</template>
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="180">
            <template slot-scope="scope">
              <el-button @click="addeditor(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
              <el-button @click="publish(scope.row)"
                         type="primary"
                         plain
                         size="mini">{{scope.row.isPublish==1?'撤回':'发布'}}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="show"
               :title="id==''?'新增读书活动':'修改读书活动'">
      <readingActivityNew :id="id"
                          @callback="newCallback"> </readingActivityNew>
    </zy-pop-up>
    <zy-pop-up v-model="detailsShow"
               title="读书活动详情">
      <readingActivityDetails :id="id"> </readingActivityDetails>
    </zy-pop-up>
    <zy-pop-up v-model="associatedShow"
               title="活动关联">
      <readingActivityAssociated :id="id"
                                 @callback="callback"></readingActivityAssociated>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import readingActivityNew from './readingActivityNew'
import readingActivityDetails from './readingActivityDetails'
import readingActivityAssociated from './readingActivityAssociated'
export default {
  name: 'readingActivity',
  data () {
    return {
      keyword: '',
      schemeType: '',
      schemeTypeData: [],
      schemeStatus: '',
      schemeStatusData: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      id: '',
      show: false,
      detailsShow: false,
      associatedShow: false
    }
  },
  mixins: [tableData],
  components: {
    readingActivityNew,
    readingActivityDetails,
    readingActivityAssociated
  },
  mounted () {
    this.dictionaryPubkvs()
    this.readschemeList()
  },
  methods: {
    search () {
      this.readschemeList()
    },
    reset () {
      this.keyword = ''
      this.schemeType = ''
      this.schemeStatus = ''
      this.readschemeList()
    },
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'library_scheme_type,library_scheme_status'
      })
      var { data } = res
      this.schemeTypeData = data.library_scheme_type
      this.schemeStatusData = data.library_scheme_status
    },
    newData () {
      this.id = ''
      this.show = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    associated (row) {
      this.id = row.id
      this.associatedShow = true
    },
    addeditor (row) {
      this.id = row.id
      this.show = true
    },
    callback () {
      this.readschemeList()
      this.associatedShow = false
    },
    newCallback () {
      this.readschemeList()
      this.show = false
    },
    async readschemeList () {
      const res = await this.$api.academy.readschemeList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        schemeType: this.schemeType,
        schemeStatus: this.schemeStatus
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle () {
      this.readschemeList()
    },
    whatPage () {
      this.readschemeList()
    },
    publish (row) {
      this.readschemePublish(row.id, row.isPublish)
    },
    async readschemePublish (id, type) {
      const res = await this.$api.academy.readschemePublish({
        id: id,
        isPublish: type
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.readschemeList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的读书公告, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.readschemeDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async readschemeDel (id) {
      const res = await this.$api.academy.readschemeDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.readschemeList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.readingActivity {
  height: 100%;
  width: 100%;
  .tableData {
    height: calc(100% - 180px);
  }
}
</style>
