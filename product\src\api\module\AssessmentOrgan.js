// *****机关考核 相关接口 *****

// 导入封装的方法
import {
  get,
  post,
  // postform,
  exportFile
} from '../http'

const AssessmentOrgan = {
  // 获取月工作纪实 列表
  getMonthlyWorkRecord (params) {
    return post('/peacetimemonth/list?', params)
  },
  // 获取月工作纪实 年份时间 (时间查询功能)
  getMonthlyWorkTime () {
    return get('/yearsedate/selected')
  },
  // 审核 月工作纪实
  reqCheckMonthlyWork (params) {
    return post('/peacetimemonth/audit?', params)
  },
  // 获取月工作纪实 详情
  reqWorkDetails (id) {
    return post(`/peacetimemonth/info/${id}`)
  },
  // 删除工作纪实
  reqDel (ids) {
    return post('/peacetimemonth/dels?', ids)
  },
  // // 添加月工作纪实模块
  // reqAddWorkDetails (params) {
  //   return post('/peacetimemonth/add?', params)
  // }
  // 新增月工作纪实 模块
  reqAddWorkDetails (url, params) {
    return post(url, params)
  },
  // 查询季度考核列表
  reqQuarterList (params) {
    return post('/peacetimequarter/list?', params)
  },
  // 保存季度考核
  reqSaveQuarter (params) {
    return post('/peacetimequarter/save?', params)
  },

  // 查询工作目标列表
  reqBusinessObjectivesList (params) {
    return post('/functional/work/list?', params)
  },
  // 添加工作目标
  reqAddBusinessObjectives (url, params) {
    return post(url, params)
  },
  // 审核工作目标
  reqCheckWork (params) {
    return post('/functional/work/audit?', params)
  },
  // 删除工作目标
  reqDelBusinessObjective (ids) {
    return post('/functional/work/dels?', ids)
  },
  // 获取工作目标详情
  reqBusinessObjectiveDetails (id) {
    return post(`/functional/work/info/${id}`)
  },

  // 查询业务职能目标完成情况
  reqfinishDetailList (params) {
    return post('/functionalperformance/list?', params)
  },
  // 添加/编辑 完成情况
  reqAddfinishDetail (url, params) {
    return post(url, params)
  },
  // 获取完成情况 详情
  reqfinishDetail (id) {
    return post(`/functionalperformance/info/${id}`)
  },
  // 审核 完成情况
  reqCheckFinishDetail (params) {
    return post('/functionalperformance/audit?', params)
  },
  // 删除完成情况
  reqDelFinishDetail (ids) {
    return post('/functionalperformance/dels?', ids)
  },

  // 查询创新创优列表
  reqInnovationExcellenceList (params) {
    return post('/functional/innovate/list?', params)
  },
  // 获取创新创优详情
  reqInnovationDetails (id) {
    return post(`/functional/innovate/info/${id}`)
  },
  // 审核创新创优
  reqCheckInnovation (params) {
    return post('/functional/innovate/audit?', params)
  },
  // 添加创新创优
  reqAddInnovationExcellence (url, params) {
    return post(url, params)
  },
  // 删除创新创优
  reqDelInnovationExcellence (ids) {
    return post('/functional/innovate/dels?', ids)
  },

  // 查询双招双引
  reqDoubleQuoteList (params) {
    return post('/samedouble/list?', params)
  },
  // 审核双招双引
  reqCheckDouble (params) {
    return post('/samedouble/audit?', params)
  },
  // 删除双招双引
  reqDelDoubleQuote (ids) {
    return post('/samedouble/dels?', ids)
  },
  // 获取双招双引 详情
  reqDoubleQuoteDetails (id) {
    return post(`/samedouble/info/${id}`)
  },
  // 添加/编辑 双招双引
  reqAddDoubleQuote (url, params) {
    return post(url, params)
  },

  // 三双活动列表
  reqThreeActivitiesList (params) {
    return post('/evaluation/activity/list/three?', params)
  },
  // 获取三双活动 详情
  reqThreeDetails (id) {
    return post('/evaluation/activity/info?', id)
  },
  // 添加/编辑 三双
  reqAddOrEditThree (url, params) {
    return post(url, params)
  },
  // 删除 三双活动
  reqDelThree (ids) {
    return post('/evaluation/activity/dels?', ids)
  },
  // // 编辑三双活动类型 (TODO:要取消掉)
  // reqEditThreeClass (params) {
  //   return post('/evaluation/activity/editype?', params)
  // },
  // 审核 三双
  reqCheckThree (params) {
    return post('/evaluation/activity/audit?', params)
  },

  // 五进五送 列表
  reqIntoFiveList (params) {
    return post('/evaluation/activity/list/five?', params)
  },
  // 添加/编辑 五进五送
  reqAddOrEditFive (url, params) {
    return post(url, params)
  },
  // 获取五送 详情
  reqFiveDetails (id) {
    return post('/evaluation/activity/info?', id)
  },
  // 删除 五进五送
  reqDelFive (ids) {
    return post('/evaluation/activity/dels?', ids)
  },
  // 审核 五进
  reqCheckFive (params) {
    return post('/evaluation/activity/audit?', params)
  },

  // 机关民主评议列表 OrganDemocraticReview
  reqOrganDemocraticReviewList (params) {
    return post('/democraticoffice/list?', params)
  },
  // 编辑 机关民主评议
  reqEditOrganDemocraticReview (url, params) {
    return post(url, params)
  },
  // 机关民主评议 详情
  reqOrganDetails (id) {
    return post(`/democraticoffice/info/${id}`)
  },

  // 用户民主评议列表 OrganDemocraticReview
  reqUserDemocraticReviewList (params) {
    return post('/democraticuser/list?', params)
  },
  // 获取用户民主评议 机构 (机构查询功能)
  reqOffice () {
    return post('/tree/list?treeType=1')
  },
  // 编辑 用户民主评议
  reqEditUserDemocraticReview (url, params) {
    return post(url, params)
  },
  // 用户民主评议 详情
  reqUserDemocraticDetails (id) {
    return post(`/democraticuser/info/${id}`)
  },

  // 查询加分项列表
  reqAddMarksList (params) {
    return post('/pluselement/list?', params)
  },
  // 查询加分项类别
  reqAddClass (params) {
    return post('/plusitem/list?', params)
  },
  // 获取加分项 详情
  reqAddMarksDetails (id) {
    return post(`/pluselement/info/${id}`)
  },
  // (新增时)获取加分项 其他人列表信息
  reqOtherUser () {
    return post('/pluselement/defaultusers')
  },
  // 审核加分项
  reqCheckAddMarks (params) {
    return post('/pluselement/audit?', params)
  },
  // 同步督办件
  reqOversee () {
    return post('/pluselement/pulloversees')
  },
  // 添加/编辑 加分项
  reqAddMarksAddOrEdit (url, params) {
    return post(url, params)
  },
  // 删除 加分项
  reqDelAddMarks (ids) {
    return post('/pluselement/dels?', ids)
  },

  // 查询年份列表
  reqYearseDateList (params) {
    return post('/yearsedate/list?', params)
  },
  // 添加/编辑 年份
  reqYearseDateAddOrEdit (url, params) {
    return post(url, params)
  },
  // 获取年份 详情
  reqYearseDateDetails (id) {
    return post(`/yearsedate/info/${id}`)
  },
  // 删除年份
  reqDelYearseDate (ids) {
    return post('/yearsedate/dels?', ids)
  },

  // 查看得分配置列表
  reqScoreConfigurationList (params) {
    return post('/evaluationscore/list?', params)
  },
  // 删除分值配置
  reqDelScore (code) {
    return post('/evaluationscore/dels?', code)
  },
  // 编辑分值配置
  reqScoreEdit (url, params) {
    return post(url, params)
  },

  // 个人 考核结果列表
  reqPersonalAssessmentResultsList (params) {
    return post('/evaluationcount/USER/list?', params)
  },
  // 个人考核结果 生成
  reqUserPreduce (params) {
    return post('/evaluationcount/USER/gen', params)
  },
  // 个人考核结果 导出
  reqPersonalExport (params) { //  导出
    return exportFile('/evaluationcount/USER/export?', params)
  },
  // 个人 考核结果详情
  reqPersonalAssessmentResultsDetail (url, params) {
    return post(url, params)
  },

  // 个人考核结果\部门考核结果 详情导出
  reqResultExport (url, params) { //  导出
    return exportFile(url, params)
  },

  // 部门 考核结果列表
  reqDepartmentResultsList (params) {
    return post('/evaluationcount/OFFICE/list?', params)
  },
  // 部门考核结果 生成
  reqDepartmentPreduce (params) {
    return post('/evaluationcount/OFFICE/gen', params)
  },
  // 部门考核结果 导出
  reqDepartmentExport (params) { //  导出
    return exportFile('/evaluationcount/OFFICE/export?', params)
  },

  // 查询加分项列表 (考核项管理)
  reqAddMarksColumnList (params) {
    return post('/plusitem/list?', params)
  },
  // 删除加分项类型 (考核项管理)
  reqDelAddMarksColumn (ids) {
    return post('/plusitem/dels?', ids)
  },
  // 加分项类型详情 (考核项管理)
  reqAddMarksColumnDetails (id) {
    return post(`/plusitem/info/${id}`)
  },
  // 获取下拉加分项 (考核项管理)
  getPlusitemKey () {
    return post('/plusitem/key/selected')
  },
  // 添加/编辑 加分项类型
  reqAddMarksColumnAddOrEdit (url, params) {
    return post(url, params)
  },

  // 计分规则管理 列表
  reqScoringRulesList (params) {
    return post('/computerule/list?', params)
  },
  // 添加/编辑 计分规则管理
  reqAddOrEditScoringRules (url, params) {
    return post(url, params)
  },
  // 详情 计分规则管理
  reqScoringRulesDetails (params) {
    return post('/computerule/info?', params)
  },
  // 删除 计分规则管理
  reqDelScoringRules (ids) {
    return post('/computerule/del?', ids)
  },

  // 部门考核及人员管理(部门)详情
  reqDepartmentDetails (id) {
    return post(`/partyassess/info/${id}`)
  },

  // 额外负分 部门 列表 departmentscore
  departmentList (params) {
    return post('/evaluationofficeextrapoint/list?', params)
  },

  // 编辑 部门
  departmentedit (url, params) {
    return post(url, params)
  },

  // 详情 部门
  departmentDetails (id) {
    return post(`/evaluationofficeextrapoint/info/${id}`)
  },

  // 额外负分 用户 userscore
  userscoreList (params) {
    return post('/evaluationuserextrapoint/list?', params)
  },

  // 编辑 用户
  userscoreedit (url, params) {
    return post(url, params)
  },

  // 详情 部门
  userscoretDetails (id) {
    return post(`/evaluationuserextrapoint/info/${id}`)
  }

}
export default AssessmentOrgan
