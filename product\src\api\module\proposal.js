// 导入封装的方法
import {
  get,
  post,
  postform,
  fileRequest
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const proposal = {
  proposalfile (params) {
    return postform('/attachment/uploadFile', params, { timeout: 80000 })
  },
  downloadFile (params, text) {
    fileRequest('/attachment/downloadFile?', params, text)
  },
  // 届次编号
  circleBoutInfoList (params) {
    return post('/submission/proposal/circleBoutInfo/list', params)
  },
  circleBoutInfoInfo (params) {
    return post(`/submission/proposal/circleBoutInfo/info/${params}`)
  },
  // 届次年
  yearRelationList (params) {
    return post('/submission/proposal/circleboutyear/list', params)
  },
  yearRelationInfo (params) {
    return post(`/submission/proposal/circleboutyear/info/${params}`)
  },
  yearRelationDel (params) {
    return post('/submission/proposal/circleboutyear/dels?', params)
  },
  // 提案分类
  proposalTypeList (params) {
    return post('/submission/proposal/topic/list?', params)
  },
  proposalTypeInfo (params) {
    return post(`/submission/proposal/topic/info/${params}`)
  },
  // 提案业务用户关系
  businessUser (params) {
    return post('/submission/proposal/businessUser/list', params)
  },
  businessUserdels (params) {
    return post('/submission/proposal/businessUser/dels', params)
  },
  enumData (params) {
    return post('/submission/proposal/businessUser/enumData', params)
  },
  enumDatainfo (params) {
    return post(params)
  },
  businessUserInfo (params) {
    return post(`/submission/proposal/businessUser/info/${params}`)
  },
  // 获取提案分类和主题词
  chooseList (params) {
    return post('/submission/proposal/topic/chooseList', params)
  },
  // 获取当前届次
  currentCircleAndBout (params) {
    return post('/submission/proposal/circleBoutInfo/currentCircleAndBout', params)
  },
  // 所有提案列表
  proposalList (params) {
    return post('/proposal/list', params)
  },
  // 提案基础详情
  proposalInfo (params) {
    return post(`/proposal/info/${params}`)
  },
  // 提案委删除提案
  powerDelete (params) {
    return post('/proposal/powerDelete', params)
  },
  // 委员删除提案
  memberDelete (params) {
    return post('/proposal/memberDelete', params)
  },
  // 我领衔的提案
  myProposalList (params) {
    return post('/proposal/myProposalList', params)
  },
  // 我联名的提案
  myJoinProposalList (params) {
    return post('/proposal/myJoinProposalList', params)
  },
  // 我的草稿提案
  myDraftsProposalList (params) {
    return post('/proposal/myDraftsProposalList', params)
  },
  // 获取提案枚举值
  getOperationList (params) {
    return post('/proposal/getOperationList', params)
  },
  // 解锁审查提案
  unlockProposal (params) {
    return post('/proposal/unlockProposal', params)
  },
  // 获取提案审查详情
  auditDetail (params) {
    return post('/proposal/auditDetail', params)
  },
  // 待专委会审查提案
  committeeAuditList (params) {
    return post('/proposal/committeeAuditList', params)
  },
  // 待审查提案
  examineList (params) {
    return post('/proposal/examineList', params)
  },
  // 待审查提案
  reviewList (params) {
    return post('/proposal/reviewList', params)
  },
  // 待审定提案
  lastAuditList (params) {
    return post('/proposal/lastAuditList', params)
  },
  // 不予立案提案
  rejectList (params) {
    return post('/proposal/rejectList', params)
  },
  // 转社情民意提案
  socialList (params) {
    return post('/proposal/socialList', params)
  },
  // 撤案提案
  banList (params) {
    return post('/proposal/banList', params)
  },
  // 转来信提案
  referList (params) {
    return post('/proposal/referList', params)
  },
  // 政协交办中列表
  ZXAssignList (params) {
    return post('/proposal/ZXAssignList', params)
  },
  // 政府交办中列表
  ZFAssignList (params) {
    return post('/proposal/ZFAssignList', params)
  },
  // 党委交办中列表
  DWAssignList (params) {
    return post('/proposal/DWAssignList', params)
  },
  // 两院交办中列表
  LYAssignList (params) {
    return post('/proposal/LYAssignList', params)
  },
  // 法院交办中列表
  FYAssignList (params) {
    return post('/proposal/FYAssignList', params)
  },
  // 检察院交办中列表
  JCYAssignList (params) {
    return post('/proposal/JCYAssignList', params)
  },
  // 交办详情
  assignDetail (params) {
    return post('/proposal/assignDetail', params)
  },
  // 提案批量二次交办
  batchSecondAssign (params) {
    return post('/proposal/batchSecondAssign', params)
  },
  // 提案批量交办办理单位
  batchAssignProposal (params) {
    return post('/proposal/batchAssignProposal', params)
  },
  // 提案委待签收
  allPreAssignList (params) {
    return post('/proposal/allPreAssignList', params)
  },
  // 提案委申请调整
  preAssignReviseList (params) {
    return post('/proposal/preAssignReviseList', params)
  },
  // 单位待签收列表
  groupPreAssignList (params) {
    return post('/proposal/groupPreAssignList', params)
  },
  // 单位申请调整列表
  groupPreAssignReviseList (params) {
    return post('/proposal/groupPreAssignReviseList', params)
  },
  // 单位申请调整列表
  groupPreAssignHistoryList (params) {
    return post('/proposal/groupPreAssignHistoryList', params)
  },
  // 提案委审查预交办申请调整提案
  auditReviseTransact (params) {
    return post('/proposal/auditReviseTransact', params)
  },
  // 提案委办理中提案
  allTransactList (params) {
    return post('/proposal/allTransactList', params)
  },
  // 提案委办理中提案
  transactProposalDetail (params) {
    return post('/proposal/transactProposalDetail', params)
  },
  // 办理单位办理中提案
  groupTransactList (params) {
    return post('/proposal/groupTransactList', params)
  },
  // 办理单位办理中提案更新内部流程
  updateTransactInnerStatus (params) {
    return post('/proposal/updateTransactInnerStatus', params)
  },
  // 办理单位办理中提案申请调整办理单位
  exchangeTransact (params) {
    return post('/proposal/exchangeTransact', params)
  },
  // 办理单位办理中提案申请延期
  saveFlowDelay (params) {
    return post('/proposal/saveFlowDelay', params)
  },
  // 办理单位办理中提案提案沟通情况列表
  flowContactList (params) {
    return post('/proposal/flowContactList', params)
  },
  // 办理单位办理中提案增加沟通情况提案沟通联系人
  contactPersonList (params) {
    return post('/proposal/contactPersonList', params)
  },
  // 办理单位办理中提案增加沟通情况
  saveFlowContact (params) {
    return post('/proposal/saveFlowContact', params)
  },
  // 办理单位办理中提案保存答复件
  saveFlowAnswer (params) {
    return post('/proposal/saveFlowAnswer', params)
  },
  // 答复件详情(点击查看)
  proposalAnswerDetail (params) {
    return post('/proposal/proposalAnswerDetail', params)
  },
  // 答复件详情(点击添加答复件)
  flowAnswerDetail (params) {
    return post('/proposal/flowAnswerDetail', params)
  },
  // 提案委已答复列表
  allAnsweredProposalList (params) {
    return post('/proposal/allAnsweredProposalList', params)
  },
  // 办理单位已答复列表
  groupAnsweredProposalList (params) {
    return post('/proposal/groupAnsweredProposalList', params)
  },
  // 办理单位申请跟踪办理
  requestTractAnswer (params) {
    return post('/proposal/requestTractAnswer', params)
  },
  // 提案重新办理
  reTransact (params) {
    return post('/proposal/reTransact', params)
  },
  // 提案委单个办结提案
  finishProposal (params) {
    return post('/proposal/finishProposal', params)
  },
  // 提案委已办结提案列表
  allFinishProposalList (params) {
    return post('/proposal/allFinishProposalList', params)
  },
  // 办理单位已办结提案列表
  groupFinishProposalList (params) {
    return post('/proposal/groupFinishProposalList', params)
  },
  // 申请延期办理单位列表
  flowDelayList (params) {
    return post('/proposal/flowDelayList', params)
  },
  // 申请调整办理单位列表
  flowBackList (params) {
    return post('/proposal/flowBackList', params)
  },
  // 延期审查
  flowDelayAudit (params) {
    return post('/proposal/flowDelayAudit', params)
  },
  // 调整审查
  auditExchangeTransact (params) {
    return post('/proposal/auditExchangeTransact', params)
  },
  // 提案委跟踪办理列表
  allTrackProposalList (params) {
    return post('/proposal/allTrackProposalList', params)
  },
  // 办理单位跟踪办理列表
  groupTrackProposalList (params) {
    return post('/proposal/groupTrackProposalList', params)
  },
  // 办理单位跟踪办理列表
  auditRequestTractAnswer (params) {
    return post('/proposal/auditRequestTractAnswer', params)
  },
  // 办理中历史申请调整
  groupChangeList (params) {
    return post('/proposal/groupChangeList', params)
  },
  // 委员确认联名关系
  confirmJoinSubmit (params) {
    return post('/proposal/confirmJoinSubmit', params)
  },
  // 满意度测评详情
  flowEvaluateDetail (params) {
    return post('/proposal/flowEvaluateDetail', params)
  },
  // 保存满意度测评
  saveFlowEvaluate (params) {
    return post('/proposal/saveFlowEvaluate', params)
  },
  // 统计分析
  proposalCount (params) {
    return post('/proposal/proposalCount', params)
  }
}
export default proposal
