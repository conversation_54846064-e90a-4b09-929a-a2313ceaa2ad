<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">setting.check.</span>enable</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.excheck</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Set to use checkbox or radio in zTree</p>
			<p>Default: false</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p> true means: use the checkbox or radio</p>
	<p> false means: don't use the checkbox or radio</p>
	</div>
	<h3>Examples of setting</h3>
	<h4>1. use the checkbox</h4>
	<pre xmlns=""><code>var setting = {
	check: {
		enable: true
	}
};
......</code></pre>
</div>
</div>