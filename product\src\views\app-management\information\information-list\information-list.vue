<template>
  <div class="information-list">
    <search-box @search-click="search"
                :resetButton="false"
                @reset-click="reast"
                title="资讯筛选">
      <zy-widget label="关键字">
        <el-input placeholder="请输入关键字"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </zy-widget>

      <zy-widget label="资讯类型">
        <el-select v-model="infoClass"
                   filterable
                   clearable
                   placeholder="请选择资讯类型">
          <el-option v-for="item in infoClassData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </zy-widget>

      <zy-widget label="显示类型">
        <el-select v-model="infoType"
                   filterable
                   clearable
                   placeholder="请选择显示类型">
          <el-option v-for="item in infoTypeData"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id">
          </el-option>
        </el-select>
      </zy-widget>

      <zy-widget label="审核状态">
        <el-select v-model="stateId"
                   filterable
                   clearable
                   placeholder="请选择审核状态">
          <el-option v-for="item in state"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id">
          </el-option>
        </el-select>
      </zy-widget>
    </search-box>
    <div class="button-box">
      <el-button type="primary"
                 icon="el-icon-plus"
                 v-permissions="'auth:zyinfodetail:add'"
                 @click="newData">新增</el-button>
      <el-button type="primary"
                 icon="el-icon-delete"
                 v-permissions="'auth:zyinfodetail:dels'"
                 @click="deleteClick">删除</el-button>
      <el-button type="primary"
                 icon="el-icon-circle-check"
                 v-permissions="'auth:zyinfodetail:batchUpdate'"
                 @click="passClick(1)">审核通过</el-button>
      <el-button type="primary"
                 icon="el-icon-remove-outline"
                 v-permissions="'auth:zyinfodetail:batchUpdate'"
                 @click="passClick(2)">审核不通过</el-button>
      <!-- <el-button type="primary"
                 icon="el-icon-upload2"
                 @click="pushClick()">推送</el-button>
      <el-button type="primary"
                 icon="el-icon-circle-close"
                 @click="CancelPushClick()">取消推送</el-button> -->
    </div>
    <div class="information-box">
      <div class="information-tree-box">
        <div class="information-tree-text">选择栏目</div>
        <div class="information-tree">
          <zy-tree :tree="tree"
                   v-model="treeId"
                   :props="{ children: 'children', label: 'name'}"
                   @on-tree-click="choiceClick"></zy-tree>
        </div>
      </div>
      <div class="information-data-box">
        <div class="information-list">
          <zy-table>
            <el-table :data="tableData"
                      stripe
                      border
                      ref="table"
                      slot="zytable"
                      @select="selected"
                      @select-all="selectedAll">
              <el-table-column type="selection"
                               fixed="left"
                               width="60"></el-table-column>
              <el-table-column label="序号"
                               width="80"
                               prop="sort">
              </el-table-column>
              <el-table-column label="标题"
                               min-width="260"
                               show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-button @click="details(scope.row)"
                             type="text">{{scope.row.title}}</el-button>
                </template>
              </el-table-column>
              <el-table-column label="发布人"
                               width="160"
                               prop="createBy">
              </el-table-column>
              <el-table-column label="发布时间"
                               width="190"
                               prop="publishDate">
              </el-table-column>
              <el-table-column label="审核状态"
                               width="120"
                               prop="auditingFlag">
              </el-table-column>
              <el-table-column label="是否置顶"
                               width="90">
                <template slot-scope="scope">
                  <div>{{scope.row.isTop==1?'置顶':'不置顶'}}</div>
                </template>
              </el-table-column>
              <!-- <el-table-column label="所属栏目"
                               width="160"
                               prop="structureName">
              </el-table-column>
              <el-table-column label="资讯类型"
                               width="90"
                               prop="infoClass">
              </el-table-column>
              <el-table-column label="显示类型"
                               width="90"
                               prop="infoType">
              </el-table-column>
              <el-table-column label="来源"
                               width="160"
                               prop="source">
              </el-table-column>
              <el-table-column label="官网推送"
                               width="90"
                               prop="isOfficialWebsitePush">
              </el-table-column> -->
              <!-- <el-table-column label="资讯关联"
                               v-if="$hasPermission(['auth:inforelation:list'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button :disabled="scope.row.canRelateInfo!=1"
                             @click="associated(scope.row)"
                             type="text">资讯关联</el-button>
                </template>
              </el-table-column> -->
              <el-table-column label="组图"
                               v-if="$hasPermission(['auth:zyinforeportpic:pic:list'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button :disabled="scope.row.infoType == '大张图'"
                             @click="mosaic(scope.row)"
                             type="text">组图</el-button>
                </template>
              </el-table-column>
              <el-table-column label="报道图片"
                               v-if="$hasPermission(['auth:zyinforeportpic:scrolling:report:list','auth:zyinforeportpic:scrolling:pic:list'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button :disabled="scope.row.canScrollReportPicOption!=1"
                             @click="rolling(scope.row)"
                             type="text">{{scope.row.infoClass}}</el-button>
                </template>
              </el-table-column>
              <!-- <el-table-column
                label="评论回复"
                v-if="$hasPermission(['auth:comment:list'])"
                width="120"
              >
                <template slot-scope="scope">
                  <el-button
                    :disabled="scope.row.commentCount==0"
                    @click="commentCount(scope.row)"
                    type="text"
                  >{{scope.row.commentCount}}</el-button>
                </template>
              </el-table-column> -->
              <el-table-column label="操作"
                               fixed="right"
                               v-if="$hasPermission(['auth:zyinfodetail:edit'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button @click="modify(scope.row)"
                             v-permissions="'auth:zyinfodetail:edit'"
                             type="primary"
                             plain
                             size="mini">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </zy-table>
        </div>
        <div class="paging_box">
          <el-pagination @size-change="howManyArticle"
                         @current-change="whatPage"
                         :current-page.sync="page"
                         :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                         :page-size.sync="pageSize"
                         background
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </div>

    <zy-pop-up v-model="show"
               :title="id?'编辑':'新增'">
      <information-add :id="id"
                       :parentId="treeId"
                       @callback="addCallback"></information-add>
    </zy-pop-up>

    <zy-pop-up v-model="detailsShow"
               title="详情">
      <information-details :id="id"></information-details>
    </zy-pop-up>

    <zy-pop-up v-model="mosaicShow"
               title="组图">
      <information-mosaic :id="id"></information-mosaic>
    </zy-pop-up>
    <zy-pop-up v-model="pictureShow"
               title="滚动图片">
      <information-picture :id="id"></information-picture>
    </zy-pop-up>
    <zy-pop-up v-model="reportsShow"
               title="滚动报道">
      <information-reports :id="id"></information-reports>
    </zy-pop-up>
    <zy-pop-up v-model="associatedShow"
               title="关联资讯">
      <associated-information :id="id"></associated-information>
    </zy-pop-up>

    <!-- <zy-pop-up v-model="pushShow"
               title="推送">
      <PushWebsite :id="ids"
                   @callback="callback"></PushWebsite>
    </zy-pop-up>
    <zy-pop-up v-model="CancelPushShow"
               title="取消推送">
      <CancelPushWebsite @callback="callback"></CancelPushWebsite>
    </zy-pop-up>

    <zy-pop-up v-model="deleteShow"
               title="同步删除">
      <DeletePushWebsite :id="ids"
                         @callback="deleteCallback"></DeletePushWebsite>
    </zy-pop-up> -->
  </div>
</template>
<script>
import tableData from '../../../../mixins/tableData'
import informationAdd from '../information-add/information-add'
import informationMosaic from './information-mosaic/information-mosaic'
import informationPicture from './information-picture/information-picture'
import informationReports from './information-reports/information-reports'
import associatedInformation from './associated-information/associated-information'
import informationDetails from '../information-details/information-details'
// import PushWebsite from '../../components/PushWebsite'
// import CancelPushWebsite from '../../components/CancelPushWebsite'
// import DeletePushWebsite from '../../components/DeletePushWebsite'
export default {
  name: 'informationList',
  data () {
    return {
      keyword: '',
      treeId: '1',
      tree: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      infoClass: '',
      infoType: '',
      stateId: '',
      state: [
        { id: '1', name: '审核通过' },
        { id: '0', name: '待审核' },
        { id: '2', name: '审核不通过' }
      ],
      infoClassData: [
        { id: 1, name: '普通资讯' },
        { id: 3, name: '滚动报道' },
        { id: 4, name: '滚动图片' }
      ],
      infoTypeData: [
        { id: 1, name: '左侧图' },
        { id: 2, name: '置顶图' },
        { id: 3, name: '大张图' }
      ],
      id: '',
      show: false,
      mosaicShow: false,
      pictureShow: false,
      reportsShow: false,
      associatedShow: false,
      detailsShow: false,
      ids: '',
      pushShow: false,
      CancelPushShow: false,
      deleteShow: false
    }
  },
  inject: ['newTab'],
  mixins: [tableData],
  components: {
    informationAdd,
    informationMosaic,
    informationPicture,
    informationReports,
    associatedInformation,
    informationDetails
    // PushWebsite,
    // CancelPushWebsite,
    // DeletePushWebsite
  },
  mounted () {
    this.dictionaryPubkvs()
    this.informationList()
    this.informationColumnTree()
  },
  methods: {
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'zy_info_type'
      })
      var { data } = res
      this.infoClassData = data.zy_info_type
    },
    search () {
      this.informationList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    pushClick () {
      if (this.choose.length) {
        this.$confirm('此操作将会把选择的资讯推送到官网, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.ids = this.choose.join(',')
          this.pushShow = true
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    CancelPushClick () {
      this.CancelPushShow = true
    },
    callback () {
      this.pushShow = false
      this.CancelPushShow = false
    },
    /**
     * 打开评论 5
     */
    commentCount (row) {
      this.newTab({ name: '评论回复', menuId: '1', to: '/memberGuestReply', params: { id: row.id, type: '5' } })
    },
    associated (row) {
      this.id = row.id
      this.associatedShow = true
    },
    modify (row) {
      this.id = row.id
      this.show = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    addCallback () {
      this.informationList()
      this.show = false
    },
    rolling (row) {
      this.id = row.id
      if (row.infoClass === '滚动报道') {
        this.reportsShow = true
      } else if (row.infoClass === '滚动图片') {
        this.pictureShow = true
      }
    },
    passClick (auditingFlag) {
      if (this.choose.length) {
        this.$confirm(`此操作将选择的资讯的状态改为${auditingFlag === 1 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.informationBatchUpdate(this.choose.join(','), auditingFlag)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async informationBatchUpdate (id, auditingFlag) {
      const res = await this.$api.appManagement.informationBatchUpdate({ ids: id, auditingFlag: auditingFlag })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.informationList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async informationColumnTree () {
      const res = await this.$api.appManagement.informationColumnTree({ module: 1 })
      var { data } = res
      var arr = [{ children: [], id: '1', name: '所有' }]
      this.tree = arr.concat(data)
    },
    choiceClick (item) {
      this.informationList()
    },
    async informationList () {
      const res = await this.$api.appManagement.informationList({
        module: 1,
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        structureId: this.treeId,
        infoClass: this.infoClass,
        infoType: this.infoType,
        auditingFlag: this.stateId
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    reast () {
      this.keyword = ''
      this.infoClass = ''
      this.infoType = ''
      this.stateId = ''
      this.informationList()
    },
    mosaic (row) {
      this.id = row.id
      this.mosaicShow = true
    },
    mosaicCallback () {
      this.informationList()
      this.mosaicShow = false
    },
    howManyArticle (val) {
      this.informationList()
    },
    whatPage (val) {
      this.informationList()
    },
    deleteClick (row) {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的资讯, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // this.officeList(this.choose.join(','))
          this.informationListDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async informationListDel (id) {
      const res = await this.$api.appManagement.informationListDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.informationList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async officeList () {
      const res = await this.$api.appManagement.officeList({
        pageNo: 1,
        pageSize: 10,
        detailIds: this.id
      })
      var { data } = res
      if (data.length) {
        this.$confirm('当前检查到删除的数据有推送至官网的资讯, 是否同步删除?', '提示', {
          confirmButtonText: '同步删除',
          cancelButtonText: '不删除',
          type: 'warning'
        }).then(() => {
          this.ids = this.choose.join(',')
          this.deleteShow = true
        }).catch(() => {
          this.informationListDel(this.choose.join(','))
        })
      } else {
        this.informationListDel(this.choose.join(','))
      }
    },
    deleteCallback () {
      this.deleteShow = false
      this.informationListDel(this.choose.join(','))
    }
  }
}
</script>
<style lang="scss">
@import "./information-list.scss";
</style>
