<template>
  <div class="module-management">
    <search-button-box
      @search-click="search"
      :resetButton="false"
    >
      <template slot="button">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="newData"
        >新增</el-button>
        <!-- v-permissions="'auth:module:add'" -->

        <el-button
          type="primary"
          icon="el-icon-delete"
          @click="deleteClick"
        >删除</el-button>
      </template>
      <template slot="search">
        <el-input
          placeholder="请输入内容"
          v-model="keyword"
          clearable
          @keyup.enter.native="search"
        >
        </el-input>
        <zy-select
          width="222"
          node-key="id"
          :props="props"
          v-model="typeId"
          :data="type"
          placeholder="请选择所属模块"
        ></zy-select>
      </template>
    </search-button-box>
    <div class="tableData">
      <zy-table>
        <el-table
          :data="tableData"
          stripe
          border
          ref="table"
          slot="zytable"
          @select="selected"
          @select-all="selectedAll"
        >
          <el-table-column
            type="selection"
            fixed="left"
            width="60"
          ></el-table-column>
          <el-table-column
            label="序号"
            width="90"
            prop="sort"
          >
          </el-table-column>
          <el-table-column
            label="模块名称"
            prop="title"
          >
          </el-table-column>
          <el-table-column
            label="所属模块"
            prop="type"
          >
          </el-table-column>
          <el-table-column
            label="菜单位置"
            prop="position"
          >
          </el-table-column>
          <el-table-column
            label="图标"
            width="120"
          >
            <template slot-scope="scope">
              <div class="table-img">
                <img
                  :src="scope.row.iconUrl"
                  alt=""
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="选中图标"
            width="120"
          >
            <template slot-scope="scope">
              <div class="table-img">
                <img
                  :src="scope.row.selectIconUrl"
                  alt=""
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="是否显示"
            width="120"
            prop="isShow"
          >
          </el-table-column>
          <el-table-column
            label="更新时间"
            width="190"
            prop="createDate"
          >
          </el-table-column>
          <el-table-column
            label="操作"
            v-if="$hasPermission(['auth:module:edit'])"
          >
            <template slot-scope="scope">
              <el-button
                @click="modify(scope.row)"
                type="primary"
                v-permissions="'auth:module:edit'"
                plain
                size="mini"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination
        @size-change="howManyArticle"
        @current-change="whatPage"
        :current-page.sync="page"
        :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
        :page-size.sync="pageSize"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <zy-pop-up
      v-model="show"
      title="新增用户"
    >
      <module-new
        :id="id"
        @newCallback="newCallback"
      ></module-new>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '../../../mixins/tableData'
import moduleNew from './module-new'
export default {
  name: 'moduleManagement',
  data () {
    return {
      id: '',
      type: [],
      typeId: '',
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      props: {
        children: 'children',
        label: 'value'
      },
      data: [],
      show: false
    }
  },
  inject: ['newTab'],
  mixins: [tableData],
  components: {
    moduleNew
  },
  mounted () {
    this.dictionaryPubkvs()
    this.moduleList()
  },
  methods: {
    search () {
      this.moduleList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    newCallback () {
      this.moduleList()
      this.show = false
    },
    modify (row) {
      this.id = row.id
      this.show = true
    },
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'module_type'
      })
      var { data } = res
      this.type = data.module_type
    },
    async moduleList () {
      const res = await this.$api.appManagement.moduleList({ title: this.keyword, type: this.typeId, pageNo: this.page, pageSize: this.pageSize })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.moduleList()
    },
    whatPage (val) {
      this.moduleList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将永久删除该菜单, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.moduleDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async moduleDel (id) {
      const res = await this.$api.appManagement.moduleDel({ ids: id })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.moduleList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }

}
</script>
<style lang="scss">
.module-management {
    height: 100%;
    width: 100%;

    .tableData {
        height: calc(100% - 116px);

        .table-img {
            width: 36px;
            height: 36px;
            overflow: hidden;

            img {
                height: 100%;
            }
        }
    }
}
</style>
