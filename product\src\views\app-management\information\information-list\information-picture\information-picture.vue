<template>
  <div class="information-picture">
    <div class="button-box-list">
      <el-button type="primary"
                 v-permissions="'auth:zyinforeportpic:scrolling:pic:add'"
                 @click="newData">新增</el-button>
      <el-button type="primary"
                 v-permissions="'auth:zyinforeportpic:dels'"
                 @click="deleteClick">删除</el-button>
    </div>
    <div class="information-mosaic-list scrollBar">
      <el-table ref="multipleTable"
                :data="tableData"
                stripe
                border
                style="width: 100%"
                @selection-change="handleSelectionChange">
        <el-table-column type="selection"
                         width="48">
        </el-table-column>
        <el-table-column label="序号"
                         width="80"
                         prop="sort">
        </el-table-column>
        <el-table-column label="标题"
                         prop="title"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="创建人"
                         prop="createBy"
                         width="120"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="创建时间"
                         width="160"
                         prop="createDate"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="操作"
                         v-if="$hasPermission(['auth:zyinforeportpic:scrolling:pic:edit'])"
                         width="80">
          <template slot-scope="scope">
            <el-button @click="handleClick(scope.row)"
                       type="text"
                       size="small">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <zy-pop-up v-model="show"
               :title="mosaicId==''?'新增滚动图片':'编辑滚动图片'">
      <picture-new :id="id"
                   :mosaicId="mosaicId"
                   @newCallback="newCallback"></picture-new>
    </zy-pop-up>
  </div>
</template>
<script>
import pictureNew from './picture-new/picture-new'
export default {
  name: 'informationPicture',
  data () {
    return {
      tableData: [],
      multipleSelection: [],
      mosaicId: '',
      show: false
    }
  },
  props: ['id'],
  components: {
    pictureNew
  },
  mounted () {
    if (this.id) {
      this.pictureList()
    }
  },
  methods: {
    async pictureList () {
      const res = await this.$api.appManagement.pictureList({ detailId: this.id, pageNo: 1, pageSize: 1000 })
      var { data } = res
      this.tableData = data
    },
    handleClick (row) {
      this.mosaicId = row.id
      this.show = true
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    newData () {
      this.mosaicId = ''
      this.show = true
    },
    newCallback () {
      this.pictureList()
      this.show = false
    },
    deleteClick () {
      if (this.multipleSelection.length) {
        this.$confirm('此操作将删除当前选中的滚动图片, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var arr = []
          this.multipleSelection.forEach(item => {
            arr.push(item.id)
          })
          var idSets = arr.join(',')
          this.picDel(idSets)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async picDel (id) {
      const res = await this.$api.appManagement.picDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.pictureList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
@import "./information-picture.scss";
</style>
