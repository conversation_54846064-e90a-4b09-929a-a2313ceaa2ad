<template>
  <div class="add-file">
    <el-form :model="form" ref="addForm" :rules="rules" label-position="top">
      <el-form-item label="序号" prop="sort" class="form-item-wd100">
        <el-input-number v-model="form.sort" :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="材料属性" prop="isAppShow">
        <el-radio-group v-model="form.isAppShow">
          <el-radio :label="1">公开</el-radio>
          <el-radio :label="0">不公开</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="活动资料" prop="files">
        <upload v-model="form.files"></upload>
      </el-form-item>
      <div class="form-footer-btn">
        <el-button size="small" @click="$emit('callback')">取消</el-button>
        <el-button size="small" type="primary" @click="onSubmit('addForm')">保存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import upload from './upload'
export default {
  components: { upload },
  data() {
    return {
      form: {
        isAppShow: 1,
        files: [],
        sort: 1
      },
      rules: {
        isAppShow: [{ required: true, message: '请选择材料属性', trigger: 'change' }],
        files: [{ required: true, message: '请上传活动材料', trigger: 'file' }]
      }
    }
  },
  methods: {
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const arr = this.form.files.map(v => {
            return {
              sort: this.form.sort,
              fileId: v.id,
              isAppShow: this.form.isAppShow,
              name: v.name,
              format: v.type,
              dete: dayjs().format('YYYY/MM/DD HH:mm'),
              userName: JSON.parse(sessionStorage.getItem('user' + this.$logo())).userName
            }
          })
          this.$emit('callback', arr)
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.add-file {
  width: 450px;
  padding: 10px 25px;
  box-sizing: border-box;
}
</style>
