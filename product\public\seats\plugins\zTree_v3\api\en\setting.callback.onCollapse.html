<div class="apiDetail">
<div>
	<h2><span>Function(event, treeId, treeNode)</span><span class="path">setting.callback.</span>onCollapse</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Callback for collapse node.</p>
			<p class="highlight_red">If you set 'setting.callback.beforeCollapse',and return false, zTree will not collapse node, and will not trigger the 'onCollapse' callback.</p>
			<p>Default: null</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>event</b><span>js event Object</span></h4>
	<p>event Object</p>
	<h4 class="topLine"><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>.</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>JSON data object of the node to be collapsed</p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. When collapse node, alert info about 'tId' and 'name'.</h4>
	<pre xmlns=""><code>function myOnCollapse(event, treeId, treeNode) {
    alert(treeNode.tId + ", " + treeNode.name);
};
var setting = {
	callback: {
		onCollapse: myOnCollapse
	}
};
......</code></pre>
</div>
</div>