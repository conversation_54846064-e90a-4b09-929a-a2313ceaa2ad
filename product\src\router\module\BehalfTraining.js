const TrainingMaterials = () => import('@BehalfTraining/TrainingMaterials/TrainingMaterials')
const TrainingMaterialsColumn = () => import('@BehalfTraining/TrainingMaterialsColumn/TrainingMaterialsColumn')
const TrainingProject = () => import('@BehalfTraining/TrainingProject/TrainingProject')
const DataAccess = () => import('@BehalfTraining/TrainingMaterials/DataAccess/DataAccess')

const BehalfTraining = [
  { // 培训资料管理
    path: '/TrainingMaterials',
    name: 'TrainingMaterials',
    component: TrainingMaterials
  },
  { // 培训资料栏目管理
    path: '/TrainingMaterialsColumn',
    name: 'TrainingMaterialsColumn',
    component: TrainingMaterialsColumn
  },
  { // 培训专题
    path: '/TrainingProject',
    name: 'TrainingProject',
    component: TrainingProject
  },
  { // 资料查阅情况
    path: '/DataAccess',
    name: 'DataAccess',
    component: DataAccess
  },
  {
    path: '/materialRead',
    name: 'materialRead',
    component: () => import(/** 资料阅读情况 */ '@BehalfTraining/StatisticalAnalysis/materialRead')
  },
  {
    path: '/examination',
    name: 'examination',
    component: () => import(/** 考试情况 */ '@BehalfTraining/StatisticalAnalysis/examination')
  },
  {
    path: '/TrainingSourceManage',
    name: 'TrainingSourceManage',
    component: () => import(/** 培训记分管理 */ '@BehalfTraining/TrainingSourceManage/TrainingSourceManage')
  }
]

export default BehalfTraining
