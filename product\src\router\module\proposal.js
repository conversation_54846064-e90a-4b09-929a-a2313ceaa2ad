/**
 * 提案者
 */
// 提交提案
const proposalNew = () => import('@proposal/proposalMember/proposalNew')
// 我领衔的提案
const proposalLedBy = () => import('@proposal/proposalMember/proposalLedBy')
// 我联名的提案
const proposalJoint = () => import('@proposal/proposalMember/proposalJoint')
// 提案草稿箱
const proposalDraftBox = () => import('@proposal/proposalMember/proposalDraftBox')

// 提案详情
const proposalDetails = () => import('@proposal/components/proposalDetails')
/**
 * 提案管理
 */
// 提交提案
const proposalAll = () => import('@proposal/proposalManagement/proposalAll/proposalAll')
// 专委会审查
const stayBranchReview = () => import('@proposal/proposalManagement/review/stayBranchReview')
// 待审查提案
const WaitingReview = () => import('@proposal/proposalManagement/review/WaitingReview')
// 待复审提案
const WaitingRecheck = () => import('@proposal/proposalManagement/review/WaitingRecheck')
// 待审定提案
const WaitingApproval = () => import('@proposal/proposalManagement/review/WaitingApproval')
// 不予立案
const NotToPutOnRecord = () => import('@proposal/proposalManagement/NotToPutOnRecord/NotToPutOnRecord')
// 转社情民意
const turnPublicOpinion = () => import('@proposal/proposalManagement/NotToPutOnRecord/turnPublicOpinion')
// 转来信
const turnLetterFrom = () => import('@proposal/proposalManagement/NotToPutOnRecord/turnLetterFrom')
// 撤案
const cancel = () => import('@proposal/proposalManagement/NotToPutOnRecord/cancel')
// 政协交办中
const cppccAssignedBy = () => import('@proposal/proposalManagement/assignedBy/cppccAssignedBy')
// 政府交办中
const governmentAssignedBy = () => import('@proposal/proposalManagement/assignedBy/governmentAssignedBy')
// 党委交办中
const partyCommitteeAssignedBy = () => import('@proposal/proposalManagement/assignedBy/partyCommitteeAssignedBy')
// 两院交办中
const bothHousesAssignedBy = () => import('@proposal/proposalManagement/assignedBy/bothHousesAssignedBy')
// 法院交办中
const courtAssignedBy = () => import('@proposal/proposalManagement/assignedBy/courtAssignedBy')
// 检察院交办中
const procuratorateAssignedBy = () => import('@proposal/proposalManagement/assignedBy/procuratorateAssignedBy')
// 待签收提案
const waitingSignProposal = () => import('@proposal/proposalManagement/assignedBy/waitingSignProposal')
// 待签收申请调整
const applyAdjustProposal = () => import('@proposal/proposalManagement/assignedBy/applyAdjustProposal')
// 办理中提案
const DealtWithInProposal = () => import('@proposal/proposalManagement/DealtWithInProposal/DealtWithInProposal')
// 已答复提案
const alreadyReplyProposal = () => import('@proposal/proposalManagement/alreadyReplyProposal/alreadyReplyProposal')
// 已办结提案
const alreadyEndProposal = () => import('@proposal/proposalManagement/alreadyEndProposal/alreadyEndProposal')
// 申请延期提案
const applyForDelayProposal = () => import('@proposal/proposalManagement/applyForDelayProposal/applyForDelayProposal')
// 跟踪办理
const trackingHandleProposal = () => import('@proposal/proposalManagement/trackingHandleProposal/trackingHandleProposal')
// 申请调整
const applyForAdjustUnit = () => import('@proposal/proposalManagement/applyForAdjustUnit/applyForAdjustUnit')
// 统计分析
const proposalStatisticalAnalysis = () => import('@proposal/proposalManagement/proposalStatisticalAnalysis/proposalStatisticalAnalysis')
// 统计分析详情
const proposalStatisticalAnalysisDetails = () => import('@proposal/proposalManagement/proposalStatisticalAnalysis/proposalStatisticalAnalysisDetails')
/**
 * 承办单位
 */
// 待签收提案
const waitingSignProposalUnit = () => import('@proposal/undertakeUnit/assignedByUnit/waitingSignProposalUnit')
// 待签收申请调整
const applyAdjustProposalUnit = () => import('@proposal/undertakeUnit/assignedByUnit/applyAdjustProposalUnit')
// 历史调整记录
const AdjustRecordProposal = () => import('@proposal/undertakeUnit/assignedByUnit/AdjustRecordProposal')
// 办理中提案
const UnitDealtWithInProposal = () => import('@proposal/undertakeUnit/UnitDealtWithInProposal/UnitDealtWithInProposal')
// 已答复提案
const UnitAlreadyReplyProposal = () => import('@proposal/undertakeUnit/UnitAlreadyReplyProposal/UnitAlreadyReplyProposal')
// 已办结提案
const UnitAlreadyEndProposal = () => import('@proposal/undertakeUnit/UnitAlreadyEndProposal/UnitAlreadyEndProposal')
// 跟踪办理
const UnittrackingHandleProposal = () => import('@proposal/undertakeUnit/UnittrackingHandleProposal/UnittrackingHandleProposal')
/**
 * 配置管理
 */
// 界次编号
const proposalTimeSerialNumber = () => import('@proposal/proposalConfiguration/proposalTimeSerialNumber/proposalTimeSerialNumber')
// 界次年份
const proposalTimeYear = () => import('@proposal/proposalConfiguration/proposalTimeYear/proposalTimeYear')
// 提案分类
const proposalType = () => import('@proposal/proposalConfiguration/proposalType/proposalType')
// 办理单位
const HandleUnitproposal = () => import('@proposal/proposalConfiguration/proposalHandleUnit/HandleUnitproposal')
// 提案分类用户
const proposalBusinessAndUsers = () => import('@proposal/proposalConfiguration/proposalBusinessAndUsers/proposalBusinessAndUsers')

const proposal = [
  {
    path: '/proposalNew',
    name: 'proposalNew',
    component: proposalNew
  },
  {
    path: '/proposalLedBy',
    name: 'proposalLedBy',
    component: proposalLedBy
  },
  {
    path: '/proposalJoint',
    name: 'proposalJoint',
    component: proposalJoint
  },
  {
    path: '/proposalDraftBox',
    name: 'proposalDraftBox',
    component: proposalDraftBox
  },
  {
    path: '/proposalAll',
    name: 'proposalAll',
    component: proposalAll
  },
  {
    path: '/stayBranchReview',
    name: 'stayBranchReview',
    component: stayBranchReview
  },
  {
    path: '/WaitingReview',
    name: 'WaitingReview',
    component: WaitingReview
  },
  {
    path: '/WaitingRecheck',
    name: 'WaitingRecheck',
    component: WaitingRecheck
  },
  {
    path: '/WaitingApproval',
    name: 'WaitingApproval',
    component: WaitingApproval
  },
  {
    path: '/NotToPutOnRecord',
    name: 'NotToPutOnRecord',
    component: NotToPutOnRecord
  },
  {
    path: '/turnPublicOpinion',
    name: 'turnPublicOpinion',
    component: turnPublicOpinion
  },
  {
    path: '/turnLetterFrom',
    name: 'turnLetterFrom',
    component: turnLetterFrom
  },
  {
    path: '/cancel',
    name: 'cancel',
    component: cancel
  },
  {
    path: '/cppccAssignedBy',
    name: 'cppccAssignedBy',
    component: cppccAssignedBy
  },
  {
    path: '/governmentAssignedBy',
    name: 'governmentAssignedBy',
    component: governmentAssignedBy
  },
  {
    path: '/partyCommitteeAssignedBy',
    name: 'partyCommitteeAssignedBy',
    component: partyCommitteeAssignedBy
  },
  {
    path: '/bothHousesAssignedBy',
    name: 'bothHousesAssignedBy',
    component: bothHousesAssignedBy
  },
  {
    path: '/courtAssignedBy',
    name: 'courtAssignedBy',
    component: courtAssignedBy
  },
  {
    path: '/procuratorateAssignedBy',
    name: 'procuratorateAssignedBy',
    component: procuratorateAssignedBy
  },
  {
    path: '/waitingSignProposal',
    name: 'waitingSignProposal',
    component: waitingSignProposal
  },
  {
    path: '/applyAdjustProposal',
    name: 'applyAdjustProposal',
    component: applyAdjustProposal
  },
  {
    path: '/DealtWithInProposal',
    name: 'DealtWithInProposal',
    component: DealtWithInProposal
  },
  {
    path: '/alreadyReplyProposal',
    name: 'alreadyReplyProposal',
    component: alreadyReplyProposal
  },
  {
    path: '/alreadyEndProposal',
    name: 'alreadyEndProposal',
    component: alreadyEndProposal
  },
  {
    path: '/applyForDelayProposal',
    name: 'applyForDelayProposal',
    component: applyForDelayProposal
  },
  {
    path: '/trackingHandleProposal',
    name: 'trackingHandleProposal',
    component: trackingHandleProposal
  },
  {
    path: '/applyForAdjustUnit',
    name: 'applyForAdjustUnit',
    component: applyForAdjustUnit
  },
  {
    path: '/proposalDetails',
    name: 'proposalDetails',
    component: proposalDetails
  },
  {
    path: '/waitingSignProposalUnit',
    name: 'waitingSignProposalUnit',
    component: waitingSignProposalUnit
  },
  {
    path: '/applyAdjustProposalUnit',
    name: 'applyAdjustProposalUnit',
    component: applyAdjustProposalUnit
  },
  {
    path: '/AdjustRecordProposal',
    name: 'AdjustRecordProposal',
    component: AdjustRecordProposal
  },
  {
    path: '/proposalStatisticalAnalysis',
    name: 'proposalStatisticalAnalysis',
    component: proposalStatisticalAnalysis
  },
  {
    path: '/proposalStatisticalAnalysisDetails',
    name: 'proposalStatisticalAnalysisDetails',
    component: proposalStatisticalAnalysisDetails
  },
  {
    path: '/UnitDealtWithInProposal',
    name: 'UnitDealtWithInProposal',
    component: UnitDealtWithInProposal
  },
  {
    path: '/UnitAlreadyReplyProposal',
    name: 'UnitAlreadyReplyProposal',
    component: UnitAlreadyReplyProposal
  },
  {
    path: '/UnitAlreadyEndProposal',
    name: 'UnitAlreadyEndProposal',
    component: UnitAlreadyEndProposal
  },
  {
    path: '/UnittrackingHandleProposal',
    name: 'UnittrackingHandleProposal',
    component: UnittrackingHandleProposal
  },
  { // 界次编号
    path: '/proposalTimeSerialNumber',
    name: 'proposalTimeSerialNumber',
    component: proposalTimeSerialNumber
  },
  { // 界次年份
    path: '/proposalTimeYear',
    name: 'proposalTimeYear',
    component: proposalTimeYear
  },
  { // 提案分类
    path: '/proposalType',
    name: 'proposalType',
    component: proposalType
  },
  { // 用户关系管理
    path: '/proposalBusinessAndUsers',
    name: 'proposalBusinessAndUsers',
    component: proposalBusinessAndUsers
  },
  { // 办理单位
    path: '/HandleUnitproposal',
    name: 'HandleUnitproposal',
    component: HandleUnitproposal
  }
]
export default proposal
