<template>
  <div class="ring-view">
    <ve-ring :tooltip="tooltip"
             :title="extend.title"
             :settings="chartSettings"
             legend-position="right"
             :extend="chartExteend"
             :data="chartData"></ve-ring>
  </div>
</template>
<script>
import 'echarts/lib/component/title'
import ring from 'v-charts/lib/ring.common'
export default {
  name: 'InstallationRatesRing',
  components: {
    [ring.name]: ring
  },
  data () {
    return {
      // v-chats列表数据
      chartData: {
        columns: ['name', 'num'],
        rows: this.data
      },
      extend: {
        title: {
          show: true,
          text: this.title,
          padding: [40, 0, 0, 60]
        }
      },
      chartSettings: {
        radius: [50, 80],
        offsetY: 200,
        labelLine: {
          normal: {
            length: 28,
            length2: 20
          }
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: this.formatter
      },
      chartExteend: {
        // series: {
        //   center: [260, 230]
        // },
        legend: {
          right: '22'
        }
      }
    }
  },
  props: {
    title: {
      type: String
    },
    data: {
      type: Array,
      default: () => []
    },
    formatter: {
      type: String,
      default: '进度：{b} <br/>数量：{c} <br/>占比：({d}%)'
    }
  },
  mounted () {
  },
  watch: {
    title (val) {
      this.extend.title.text = val
    },
    data (val) {
      this.chartData.rows = this.data
    },
    formatter (val) {
      this.tooltip.formatter = val
    }
  }
}
</script>
<style lang="scss">
@import "./InstallationRates-ring.scss";
</style>
