<template>
  <div class="zy-menu-tree scrollBar">
    <div v-for="(menu, index) in menuItem"
         :key="index">
      <div :class="['menu-item', menu.active?'menu-item-active':'']"
           :style="{ paddingLeft: padding(hierarchy) + 'px' }"
           v-if="judge(menu,false)"
           @click="selected(menu)">{{menu[props.label]}}</div>
      <div v-if="judge(menu,true)">
        <div class="menu-item menu-item-title"
             :style="{ paddingLeft: padding(hierarchy) + 'px' }"
             @click="submenu(menu)">{{menu[props.label]}} <div :class="['menu-icon',menu.hidden? 'menu-icon-active':'']"></div>
        </div>
        <el-collapse-transition>
          <zy-menu-tree v-if="menu.hidden"
                        :show="show"
                        :menu="menu[props.children]"
                        :props="props"
                        v-model="menuId"
                        :nodeKey="nodeKey"
                        :hierarchy="hierarchy+1"></zy-menu-tree>
        </el-collapse-transition>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'zyMenuTree',
  data () {
    return {
      menuId: this.value,
      menuItem: []
    }
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    menu: {
      type: Array,
      default: function () {
        return []
      }
    },
    hierarchy: {
      type: Number,
      default: 0
    },
    show: {
      type: Boolean,
      default: false
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label',
          show: 'show'
        }
      }
    }
  },
  model: {
    prop: 'value',
    event: 'id'
  },
  created () {
    this.menudata(this.deepCopy(this.menu))
  },
  watch: {
    menu () {
      this.menudata(this.deepCopy(this.menu))
    },
    value (val) {
      this.menuId = val
      this.selectedId()
    },
    menuId (val) {
      this.$emit('id', val)
    }
  },
  methods: {
    judge (data, type) {
      var show = false
      if (this.show) {
        if (type) {
          if (data[this.props.children].length !== 0 && data[this.props.show]) {
            show = true
          } else {
            show = false
          }
        } else {
          if (data[this.props.children].length === 0 && data[this.props.show]) {
            show = true
          } else {
            show = false
          }
        }
      } else {
        if (type) {
          if (data[this.props.children].length) {
            show = true
          } else {
            show = false
          }
        } else {
          if (data[this.props.children].length) {
            show = false
          } else {
            show = true
          }
        }
      }
      return show
    },
    padding (index) {
      var hierarchy = 24 + (16 * index)
      return hierarchy
    },
    menudata (data) {
      data.forEach(item => {
        if (item[this.props.children].length === 0) {
          item.active = false
          if (this.menuId === item[this.nodeKey]) {
            item.active = true
          }
        } else {
          item.hidden = false
        }
      })
      this.menuItem = data
      this.selectedId()
    },
    submenu (data) {
      const arr = this.menuItem
      arr.forEach(item => {
        if (item[this.nodeKey] === data[this.nodeKey]) {
          item.hidden = !item.hidden
        }
      })
    },
    // rowClick (result) {
    //   this.$emit('on-row-click', result)
    // },
    selected (data) {
      this.menuId = data[this.nodeKey]
      // let result = this.makeData(data)
      // this.$emit('on-row-click', result)
    },
    selectedId (type) {
      if (this.hierarchy === 0) {
        this.menuhierarchy(this.menuItem)
      }
      const arr = this.menuItem
      arr.forEach(item => {
        if (item[this.props.children].length === 0) {
          item.active = false
          if (item[this.nodeKey] === this.menuId) {
            item.active = true
            if (this.$route.path !== item.to) {
              this.$router.push({ path: item.to })
            }
          }
        }
      })
    },
    menuhierarchy (data) {
      data.forEach(item => {
        if (item[this.nodeKey] === this.menuId) {
          sessionStorage.setItem('curMenuItem', JSON.stringify(item))
          if (this.$route.path !== item.to) {
            this.$router.push({ path: item.to })
          }
          const result = this.makeData(item)
          this.$emit('on-row-click', result)
        }
        if (item[this.props.children].length) {
          this.menuhierarchy(item[this.props.children])
        }
      })
    },
    deepCopy (data) {
      var t = this.type(data)
      var o
      var i
      var ni
      if (t === 'array') {
        o = []
      } else if (t === 'object') {
        o = {}
      } else {
        return data
      }
      if (t === 'array') {
        for (i = 0, ni = data.length; i < ni; i++) {
          o.push(this.deepCopy(data[i]))
        }
        return o
      } else if (t === 'object') {
        for (i in data) {
          o[i] = this.deepCopy(data[i])
        }
        return o
      }
    },
    type (obj) {
      var toString = Object.prototype.toString
      var map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'undefined',
        '[object Null]': 'null',
        '[object Object]': 'object'
      }
      return map[toString.call(obj)]
    },
    makeData (data) {
      const t = this.type(data)
      let o
      if (t === 'array') {
        o = []
      } else if (t === 'object') {
        o = {}
      } else {
        return data
      }
      if (t === 'array') {
        for (let i = 0; i < data.length; i++) {
          o.push(this.makeData(data[i]))
        }
      } else if (t === 'object') {
        for (const i in data) {
          if (i != 'active' && i != 'hidden') {// eslint-disable-line
            o[i] = this.makeData(data[i])
          }
        }
      }
      return o
    }
  }
}
</script>
<style lang="scss">
@import "./zy-menu-tree.scss";
</style>
