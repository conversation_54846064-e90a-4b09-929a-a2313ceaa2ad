.zy-sliding {
  width: 100%;
  padding: 0 24px;
  padding-top: 16px;
  height: 68px;
  border-bottom: 1px solid #e6e6e6;
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;

  .zy-sliding-box {
    width: 100%;
    overflow: hidden;

    .zy-sliding-item-box {
      height: 100%;
      white-space: nowrap;
      float: left;
      position: relative;

      .zy-sliding-item {
        height: 52px;
        line-height: 52px;
        display: inline-block;
        cursor: pointer;
        font-size: $textSize14;
      }

      .zy-sliding-item-a {
        color: $zy-color;
      }

      .zy-sliding-item-sliding {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 24px;
        height: 4px;
        background-color: $zy-color;
      }

      .zy-sliding-item + .zy-sliding-item {
        margin-left: 24px;
      }
    }
  }
}
