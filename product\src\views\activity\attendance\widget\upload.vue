<template>
  <el-upload class="upload-demo" action="/" :disabled="disabled" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.PNG,.JPG" :http-request="customUpload" :on-remove="handleRemove" :file-list="files" multiple :limit="9">
    <el-button size="small" type="primary">点击上传</el-button>
    <div slot="tip" class="el-upload__tip">只支持上传pdf/word文件</div>
  </el-upload>
</template>

<script>
export default {
  props: {
    value: [Array],
    disabled: {
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'value',
    event: 'file'
  },
  watch: {
    value (val) {
      if (val) {
        this.files = val
      } else {
        this.files = []
      }
    },
    files (val) {
      this.$emit('file', val)
    }
  },
  data () {
    return {
      files: this.value
    }
  },
  methods: {
    // 移除文件
    handleRemove (file) {
      for (const i in this.files) {
        if (this.files[i].uid === file.uid) {
          this.files.splice(i, 1)
        }
      }
    },
    // 上传逻辑
    customUpload (file) {
      const siteId = JSON.parse(sessionStorage.getItem('user' + this.$logo())).areaId
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', 'activity-sign-up')
      formData.append('siteId', siteId)
      this.$api.microAdvice.uploadFile(formData).then(res => {
        const { errcode, data } = res
        if (errcode === 200) {
          this.files.push({
            name: data[0].fileName,
            size: data[0].fileSize,
            type: data[0].fileType,
            url: data[0].filePath,
            id: data[0].id,
            uid: data[0].uid
          })
        }
      })
    }
  }
}
</script>
