import time from './time'
const datefmt = (data, pattern = 'YYYY-MM-DD HH:mm:ss') => {
  if (data) {
    return time.DateUtil.format(new Date(data), pattern)
  } else {
    return ''
  }
}
export const timePoor = (data, data1) => {
  var o = new Date(data).getTime()
  var n = new Date(data1).getTime()
  var f = n - o
  // var bs = (f >= 0 ? '前' : '后')
  var bs = ''
  f = Math.abs(f)
  if (f < 6e4) {
    return '刚刚'
  }
  if (f < 36e5) {
    return parseInt(f / 6e4) + '分钟' + bs
  }
  if (f < 864e5) {
    return parseInt(f / 36e5) + '小时' + bs
  }
  if (f < 2592e6) {
    return parseInt(f / 864e5) + '天' + bs
  }
  if (f < 31536e6) {
    return parseInt(f / 2592e6) + '个月' + bs
  }
  return parseInt(f / 31536e6) + '年' + bs
}

const proposalTitle = (title) => {
  if (title) {
    var TextTitle = ''
    var length = title.length
    var text = title.slice(0, 2)
    // var textMiddle = title.slice(2, length - 3)
    var textend = title.slice(length - 3)
    if (text !== '关于') {
      TextTitle = '关于' + title
    } else {
      TextTitle = title
    }
    if (textend !== '的提案') {
      TextTitle = TextTitle + '的提案'
    }
    return TextTitle
  }
}
/**
 * 隐藏用户手机号中间四位 示例：188****2620
 * @param {String|Number} phone
 */
const hidePhone = (phone) => {
  return String(phone).replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 获取文件后缀名 .xxx
 * @param {String} fileName
 */
const getExtension = (fileName) => {
  return fileName.substring(fileName.lastIndexOf('.') + 1)
}

export default {
  datefmt,
  timePoor,
  hidePhone,
  proposalTitle,
  getExtension
}
