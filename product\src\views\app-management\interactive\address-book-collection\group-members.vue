<template>
  <div class="group-members">
    <div class="role-usre-text-box">
      <div class="role-usre-text">
        群名称：{{name}}<span>群人数：（{{total}}人）</span>
      </div>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  stripe
                  border
                  slot="zytable">
          <el-table-column label="姓名"
                           width='180'
                           prop="userName"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="性别"
                           width='100'
                           prop="sex">
          </el-table-column>
          <el-table-column label="民族"
                           width='100'
                           prop="nation">
          </el-table-column>
          <el-table-column label="手机"
                           width='160'
                           prop="mobile">
          </el-table-column>
          <el-table-column label="所属机构"
                           width='160'
                           prop="officeName"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="职务"
                           width='160'
                           prop="position"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="所属单位"
                           prop="companyName"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="是否群主"
                           width='120'>
            <template slot-scope="scope">
              {{scope.row.isGroupOwner?'是':'否'}}
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
  </div>
</template>
<script>
export default {
  name: 'groupMembers',
  data () {
    return {
      id: this.$route.query.id,
      name: this.$route.query.name,
      tableData: [],
      total: 0
    }
  },
  mounted () {
    if (this.id) {
      this.groupUserList()
    }
  },
  methods: {
    async groupUserList () {
      const res = await this.$api.appManagement.groupUserList({ groupId: this.id })
      var { data, total } = res
      this.tableData = data
      this.total = total
    }
  }
}
</script>
<style lang="scss">
.group-members {
  height: 100%;
  width: 100%;
  padding-top: 16px;

  .role-usre-text-box {
    width: 100%;
    padding-left: 24px;
    padding-right: 26px;
    padding-bottom: 16px;

    .role-usre-text {
      height: 32px;
      width: 100%;
      line-height: 32px;
      font-size: $textSize14;
      text-align: center;

      span {
        margin-left: 36px;
      }
    }
  }

  .tableData {
    height: calc(100% - 48px);
  }
}
</style>
