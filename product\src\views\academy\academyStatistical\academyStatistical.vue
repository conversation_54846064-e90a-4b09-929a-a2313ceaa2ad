<template>
  <div class="academyStatistical">
    <zy-sliding v-model="slidingId"
                :sliding="sliding"
                :props="{id:'id',name:'name'}"
                @sliding-click="slidingClick"></zy-sliding>
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input placeholder="请输入关键词"
                v-model="keyword"
                clearable
                @keyup.enter.native="search">
      </el-input>
      <el-date-picker v-model="time"
                      type="datetimerange"
                      value-format="timestamp"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期">
      </el-date-picker>
    </screening-box>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  stripe
                  border
                  slot="zytable">
          <el-table-column label="排序"
                           prop="sort"> </el-table-column>
          <el-table-column :label="view('1')"
                           prop="name"> </el-table-column>
          <el-table-column :label="view('2')"
                           prop="nameOne"> </el-table-column>
          <el-table-column :label="view('3')"
                           prop="nameTwo"> </el-table-column>
        </el-table>
      </zy-table>
    </div>
  </div>
</template>
<script>
const delay = (function () {
  let timer = 0
  return function (callback, ms) {
    clearTimeout(timer)
    timer = setTimeout(callback, ms)
  }
})()
export default {
  name: 'academyStatistical',
  data () {
    return {
      slidingId: '1',
      sliding: [
        { id: '1', name: '热门书籍统计' },
        { id: '2', name: '人员阅读统计' },
        { id: '3', name: '交流群活跃度统计' }
      ],
      value: '',
      keyword: '',
      time: '',
      tableData: []
    }
  },
  methods: {
    slidingClick (item) {
      this.value = item.id
      delay(() => {
        if (item.id === '1') {
          this.librarycountBook()
        } else if (item.id === '2') {
          this.librarycountUser()
        } else if (item.id === '3') {
          this.librarycountGorup()
        }
      }, 500)
    },
    search () {
      delay(() => {
        if (this.value === '1') {
          this.librarycountBook()
        } else if (this.value === '2') {
          this.librarycountUser()
        } else if (this.value === '3') {
          this.librarycountGorup()
        }
      }, 500)
    },
    reset () {
      this.keyword = ''
      this.time = ''
      delay(() => {
        if (this.value === '1') {
          this.librarycountBook()
        } else if (this.value === '2') {
          this.librarycountUser()
        } else if (this.value === '3') {
          this.librarycountGorup()
        }
      }, 500)
    },
    view (type) {
      var text = ''
      if (this.value === '1') {
        if (type === '1') {
          text = '书名'
        } else if (type === '2') {
          text = '作者'
        } else if (type === '3') {
          text = '点击量'
        }
      } else if (this.value === '2') {
        if (type === '1') {
          text = '用户名'
        } else if (type === '2') {
          text = '阅读量'
        } else if (type === '3') {
          text = '笔记数'
        }
      } else if (this.value === '3') {
        if (type === '1') {
          text = '群名称'
        } else if (type === '2') {
          text = '消息数'
        } else if (type === '3') {
          text = '参与人数'
        }
      }
      return text
    },
    async librarycountBook () {
      const res = await this.$api.academy.librarycountBook({
        keyword: this.keyword,
        beginDate: this.time ? this.time[0] : '',
        endDate: this.time ? this.time[1] : ''
      })
      var { data } = res
      data.forEach(item => {
        item.name = item.bookName
        item.nameOne = item.authorName
        item.nameTwo = item.clickAmount
      })
      this.tableData = data
    },
    async librarycountUser () {
      const res = await this.$api.academy.librarycountUser({
        keyword: this.keyword,
        beginDate: this.time ? this.time[0] : '',
        endDate: this.time ? this.time[1] : ''
      })
      var { data } = res
      data.forEach(item => {
        item.name = item.userName
        item.nameOne = item.readAmount
        item.nameTwo = item.noteAmount
      })
      this.tableData = data
    },
    async librarycountGorup () {
      const res = await this.$api.academy.librarycountGorup({
        keyword: this.keyword,
        beginDate: this.time ? this.time[0] : '',
        endDate: this.time ? this.time[1] : ''
      })
      var { data } = res
      data.forEach(item => {
        item.name = item.groupName
        item.nameOne = item.messageAmount
        item.nameTwo = item.joinerAmount
      })
      this.tableData = data
    }
  }
}
</script>
<style lang="scss">
.academyStatistical {
  height: 100%;
  width: 100%;
  .tableData {
    height: calc(100% - 132px);
  }
}
</style>
