<template>
  <div class="goodWordsNew">
    <el-form
      :model="form"
      :rules="rules"
      inline
      ref="form"
      label-position="top"
      class="newForm"
    >
      <el-form-item label="内容" prop="content" class="form-ue">
        <el-input
          type="textarea"
          :autosize="{ minRows: 6, maxRows: 9 }"
          :maxlength="700"
          show-word-limit
          placeholder="请输入内容"
          v-model="form.content"
        >
        </el-input>
      </el-form-item>
      <el-form-item>
        <template slot="label"
          ><span style="color: red">* </span>关联书籍</template
        >
        <el-button type="primary" @click="BooksClick" class="goodWordsNewButton"
          >选择书籍</el-button
        >
        <div class="goodWordsNewItem" v-if="book">
          <div class="goodWordsNewItemImg">
            <img :src="book.coverImgUrl" alt="" />
          </div>
          <div class="goodWordsNewItemBox">
            <div class="goodWordsNewItemName">{{ book.bookName }}</div>
            <div class="goodWordsNewItemIntroduction">
              {{ book.bookDescription }}
            </div>
            <div class="goodWordsNewItemAuthor">
              <div class="goodWordsNewItemAuthorText">
                {{ book.authorName }}
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary" @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
    <zy-pop-up v-model="show" title="选择书籍">
      <RelatedBooks
        :data="book ? [book] : []"
        :max="1"
        @callback="callback"
      ></RelatedBooks>
    </zy-pop-up>
  </div>
</template>
<script>
import RelatedBooks from '../RelatedBooks/RelatedBooks'
export default {
  name: 'goodWordsNew',
  data () {
    return {
      form: {
        content: ''
      },
      rules: {
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ]
      },
      show: false,
      book: null
    }
  },
  props: ['id'],
  components: {
    RelatedBooks
  },
  mounted () {
    if (this.id) {
      this.goldworInfo()
    }
  },
  methods: {
    async goldworInfo () {
      const res = await this.$api.academy.goldworInfo({ id: this.id })
      var { data } = res
      this.form.content = data.content
      if (data.book) {
        this.book = data.book
      }
    },
    BooksClick () {
      this.show = true
    },
    callback (data, type) {
      if (data.length) {
        this.book = data[0]
      }
      this.show = false
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.book === null || !this.book.id) {
            this.$message({
              message: '请选择关联书籍！',
              type: 'warning'
            })
            return
          }
          let url = '/goldword/add'
          if (this.id) {
            url = '/goldword/edit'
          }
          this.$api.general.generalAdd(url, {
            id: this.id,
            content: this.form.content,
            bookId: this.book.id
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.goodWordsNew {
  width: 880px;
  padding: 24px;
  .goodWordsNewButton {
    height: 36px;
    padding: 0 16px;
  }
  .goodWordsNewItem {
    margin-top: 12px;
    display: flex;
    justify-content: space-between;
    margin-right: 24px;
    margin-bottom: 24px;
    width: 332px;
    height: 128px;
    cursor: pointer;
    .goodWordsNewItemImg {
      height: 128px;
      width: 95px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .goodWordsNewItemBox {
      width: 222px;
      height: 100%;
      position: relative;
      .goodWordsNewItemName {
        color: #333;
        line-height: 21px;
        font-size: $textSize16;
        margin-bottom: 7px;
      }
      .goodWordsNewItemIntroduction {
        line-height: 24px;
        color: #666;
        letter-spacing: 0.93px;
        height: 72px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        font-size: 13px;
      }
      .goodWordsNewItemAuthor {
        position: absolute;
        left: 0;
        bottom: -2px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .goodWordsNewItemAuthorText {
          font-size: 13px;
          color: #999;
          letter-spacing: 1px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
