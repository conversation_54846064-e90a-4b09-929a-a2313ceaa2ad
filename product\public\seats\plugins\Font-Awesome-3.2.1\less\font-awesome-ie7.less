/*!
 *  Font Awesome 3.2.1
 *  the iconic font designed for <PERSON><PERSON><PERSON>
 *  ------------------------------------------------------------------------------
 *  The full suite of pictographic icons, examples, and documentation can be
 *  found at http://fontawesome.io.  Stay up to date on Twitter at
 *  http://twitter.com/fontawesome.
 *
 *  License
 *  ------------------------------------------------------------------------------
 *  - The Font Awesome font is licensed under SIL OFL 1.1 -
 *    http://scripts.sil.org/OFL
 *  - Font Awesome CSS, LESS, and SASS files are licensed under MIT License -
 *    http://opensource.org/licenses/mit-license.html
 *  - Font Awesome documentation licensed under CC BY 3.0 -
 *    http://creativecommons.org/licenses/by/3.0/
 *  - Attribution is no longer required in Font Awesome 3.0, but much appreciated:
 *    "Font Awesome by <PERSON>y - http://fontawesome.io"
 *
 *  Author - Dave Gandy
 *  ------------------------------------------------------------------------------
 *  Email: <EMAIL>
 *  Twitter: http://twitter.com/byscuits
 *  Work: Lead Product Designer @ Kyruus - http://kyruus.com
 */

.icon-large {
  font-size: 4/3em;
  margin-top: -4px;
  padding-top: 3px;
  margin-bottom: -4px;
  padding-bottom: 3px;
  vertical-align: middle;
}

.nav {
  [class^="icon-"],
  [class*=" icon-"] {
    vertical-align: inherit;
    margin-top: -4px;
    padding-top: 3px;
    margin-bottom: -4px;
    padding-bottom: 3px;
    &.icon-large {
      vertical-align: -25%;
    }
  }
}

.nav-pills, .nav-tabs {
  [class^="icon-"],
  [class*=" icon-"] {
    &.icon-large {
      line-height: .75em;
      margin-top: -7px;
      padding-top: 5px;
      margin-bottom: -5px;
      padding-bottom: 4px;
    }
  }
}

.btn {
  [class^="icon-"],
  [class*=" icon-"] {
    &.pull-left, &.pull-right { vertical-align: inherit; }
    &.icon-large {
      margin-top: -.5em;
    }
  }
}

a [class^="icon-"],
a [class*=" icon-"] {
  cursor: pointer;
}

.ie7icon(@inner) { *zoom: ~"expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '@{inner}')"; }


.icon-glass {
  .ie7icon('&#xf000;');
}


.icon-music {
  .ie7icon('&#xf001;');
}


.icon-search {
  .ie7icon('&#xf002;');
}


.icon-envelope-alt {
  .ie7icon('&#xf003;');
}


.icon-heart {
  .ie7icon('&#xf004;');
}


.icon-star {
  .ie7icon('&#xf005;');
}


.icon-star-empty {
  .ie7icon('&#xf006;');
}


.icon-user {
  .ie7icon('&#xf007;');
}


.icon-film {
  .ie7icon('&#xf008;');
}


.icon-th-large {
  .ie7icon('&#xf009;');
}


.icon-th {
  .ie7icon('&#xf00a;');
}


.icon-th-list {
  .ie7icon('&#xf00b;');
}


.icon-ok {
  .ie7icon('&#xf00c;');
}


.icon-remove {
  .ie7icon('&#xf00d;');
}


.icon-zoom-in {
  .ie7icon('&#xf00e;');
}


.icon-zoom-out {
  .ie7icon('&#xf010;');
}


.icon-off {
  .ie7icon('&#xf011;');
}

.icon-power-off {
  .ie7icon('&#xf011;');
}


.icon-signal {
  .ie7icon('&#xf012;');
}


.icon-cog {
  .ie7icon('&#xf013;');
}

.icon-gear {
  .ie7icon('&#xf013;');
}


.icon-trash {
  .ie7icon('&#xf014;');
}


.icon-home {
  .ie7icon('&#xf015;');
}


.icon-file-alt {
  .ie7icon('&#xf016;');
}


.icon-time {
  .ie7icon('&#xf017;');
}


.icon-road {
  .ie7icon('&#xf018;');
}


.icon-download-alt {
  .ie7icon('&#xf019;');
}


.icon-download {
  .ie7icon('&#xf01a;');
}


.icon-upload {
  .ie7icon('&#xf01b;');
}


.icon-inbox {
  .ie7icon('&#xf01c;');
}


.icon-play-circle {
  .ie7icon('&#xf01d;');
}


.icon-repeat {
  .ie7icon('&#xf01e;');
}

.icon-rotate-right {
  .ie7icon('&#xf01e;');
}


.icon-refresh {
  .ie7icon('&#xf021;');
}


.icon-list-alt {
  .ie7icon('&#xf022;');
}


.icon-lock {
  .ie7icon('&#xf023;');
}


.icon-flag {
  .ie7icon('&#xf024;');
}


.icon-headphones {
  .ie7icon('&#xf025;');
}


.icon-volume-off {
  .ie7icon('&#xf026;');
}


.icon-volume-down {
  .ie7icon('&#xf027;');
}


.icon-volume-up {
  .ie7icon('&#xf028;');
}


.icon-qrcode {
  .ie7icon('&#xf029;');
}


.icon-barcode {
  .ie7icon('&#xf02a;');
}


.icon-tag {
  .ie7icon('&#xf02b;');
}


.icon-tags {
  .ie7icon('&#xf02c;');
}


.icon-book {
  .ie7icon('&#xf02d;');
}


.icon-bookmark {
  .ie7icon('&#xf02e;');
}


.icon-print {
  .ie7icon('&#xf02f;');
}


.icon-camera {
  .ie7icon('&#xf030;');
}


.icon-font {
  .ie7icon('&#xf031;');
}


.icon-bold {
  .ie7icon('&#xf032;');
}


.icon-italic {
  .ie7icon('&#xf033;');
}


.icon-text-height {
  .ie7icon('&#xf034;');
}


.icon-text-width {
  .ie7icon('&#xf035;');
}


.icon-align-left {
  .ie7icon('&#xf036;');
}


.icon-align-center {
  .ie7icon('&#xf037;');
}


.icon-align-right {
  .ie7icon('&#xf038;');
}


.icon-align-justify {
  .ie7icon('&#xf039;');
}


.icon-list {
  .ie7icon('&#xf03a;');
}


.icon-indent-left {
  .ie7icon('&#xf03b;');
}


.icon-indent-right {
  .ie7icon('&#xf03c;');
}


.icon-facetime-video {
  .ie7icon('&#xf03d;');
}


.icon-picture {
  .ie7icon('&#xf03e;');
}


.icon-pencil {
  .ie7icon('&#xf040;');
}


.icon-map-marker {
  .ie7icon('&#xf041;');
}


.icon-adjust {
  .ie7icon('&#xf042;');
}


.icon-tint {
  .ie7icon('&#xf043;');
}


.icon-edit {
  .ie7icon('&#xf044;');
}


.icon-share {
  .ie7icon('&#xf045;');
}


.icon-check {
  .ie7icon('&#xf046;');
}


.icon-move {
  .ie7icon('&#xf047;');
}


.icon-step-backward {
  .ie7icon('&#xf048;');
}


.icon-fast-backward {
  .ie7icon('&#xf049;');
}


.icon-backward {
  .ie7icon('&#xf04a;');
}


.icon-play {
  .ie7icon('&#xf04b;');
}


.icon-pause {
  .ie7icon('&#xf04c;');
}


.icon-stop {
  .ie7icon('&#xf04d;');
}


.icon-forward {
  .ie7icon('&#xf04e;');
}


.icon-fast-forward {
  .ie7icon('&#xf050;');
}


.icon-step-forward {
  .ie7icon('&#xf051;');
}


.icon-eject {
  .ie7icon('&#xf052;');
}


.icon-chevron-left {
  .ie7icon('&#xf053;');
}


.icon-chevron-right {
  .ie7icon('&#xf054;');
}


.icon-plus-sign {
  .ie7icon('&#xf055;');
}


.icon-minus-sign {
  .ie7icon('&#xf056;');
}


.icon-remove-sign {
  .ie7icon('&#xf057;');
}


.icon-ok-sign {
  .ie7icon('&#xf058;');
}


.icon-question-sign {
  .ie7icon('&#xf059;');
}


.icon-info-sign {
  .ie7icon('&#xf05a;');
}


.icon-screenshot {
  .ie7icon('&#xf05b;');
}


.icon-remove-circle {
  .ie7icon('&#xf05c;');
}


.icon-ok-circle {
  .ie7icon('&#xf05d;');
}


.icon-ban-circle {
  .ie7icon('&#xf05e;');
}


.icon-arrow-left {
  .ie7icon('&#xf060;');
}


.icon-arrow-right {
  .ie7icon('&#xf061;');
}


.icon-arrow-up {
  .ie7icon('&#xf062;');
}


.icon-arrow-down {
  .ie7icon('&#xf063;');
}


.icon-share-alt {
  .ie7icon('&#xf064;');
}

.icon-mail-forward {
  .ie7icon('&#xf064;');
}


.icon-resize-full {
  .ie7icon('&#xf065;');
}


.icon-resize-small {
  .ie7icon('&#xf066;');
}


.icon-plus {
  .ie7icon('&#xf067;');
}


.icon-minus {
  .ie7icon('&#xf068;');
}


.icon-asterisk {
  .ie7icon('&#xf069;');
}


.icon-exclamation-sign {
  .ie7icon('&#xf06a;');
}


.icon-gift {
  .ie7icon('&#xf06b;');
}


.icon-leaf {
  .ie7icon('&#xf06c;');
}


.icon-fire {
  .ie7icon('&#xf06d;');
}


.icon-eye-open {
  .ie7icon('&#xf06e;');
}


.icon-eye-close {
  .ie7icon('&#xf070;');
}


.icon-warning-sign {
  .ie7icon('&#xf071;');
}


.icon-plane {
  .ie7icon('&#xf072;');
}


.icon-calendar {
  .ie7icon('&#xf073;');
}


.icon-random {
  .ie7icon('&#xf074;');
}


.icon-comment {
  .ie7icon('&#xf075;');
}


.icon-magnet {
  .ie7icon('&#xf076;');
}


.icon-chevron-up {
  .ie7icon('&#xf077;');
}


.icon-chevron-down {
  .ie7icon('&#xf078;');
}


.icon-retweet {
  .ie7icon('&#xf079;');
}


.icon-shopping-cart {
  .ie7icon('&#xf07a;');
}


.icon-folder-close {
  .ie7icon('&#xf07b;');
}


.icon-folder-open {
  .ie7icon('&#xf07c;');
}


.icon-resize-vertical {
  .ie7icon('&#xf07d;');
}


.icon-resize-horizontal {
  .ie7icon('&#xf07e;');
}


.icon-bar-chart {
  .ie7icon('&#xf080;');
}


.icon-twitter-sign {
  .ie7icon('&#xf081;');
}


.icon-facebook-sign {
  .ie7icon('&#xf082;');
}


.icon-camera-retro {
  .ie7icon('&#xf083;');
}


.icon-key {
  .ie7icon('&#xf084;');
}


.icon-cogs {
  .ie7icon('&#xf085;');
}

.icon-gears {
  .ie7icon('&#xf085;');
}


.icon-comments {
  .ie7icon('&#xf086;');
}


.icon-thumbs-up-alt {
  .ie7icon('&#xf087;');
}


.icon-thumbs-down-alt {
  .ie7icon('&#xf088;');
}


.icon-star-half {
  .ie7icon('&#xf089;');
}


.icon-heart-empty {
  .ie7icon('&#xf08a;');
}


.icon-signout {
  .ie7icon('&#xf08b;');
}


.icon-linkedin-sign {
  .ie7icon('&#xf08c;');
}


.icon-pushpin {
  .ie7icon('&#xf08d;');
}


.icon-external-link {
  .ie7icon('&#xf08e;');
}


.icon-signin {
  .ie7icon('&#xf090;');
}


.icon-trophy {
  .ie7icon('&#xf091;');
}


.icon-github-sign {
  .ie7icon('&#xf092;');
}


.icon-upload-alt {
  .ie7icon('&#xf093;');
}


.icon-lemon {
  .ie7icon('&#xf094;');
}


.icon-phone {
  .ie7icon('&#xf095;');
}


.icon-check-empty {
  .ie7icon('&#xf096;');
}

.icon-unchecked {
  .ie7icon('&#xf096;');
}


.icon-bookmark-empty {
  .ie7icon('&#xf097;');
}


.icon-phone-sign {
  .ie7icon('&#xf098;');
}


.icon-twitter {
  .ie7icon('&#xf099;');
}


.icon-facebook {
  .ie7icon('&#xf09a;');
}


.icon-github {
  .ie7icon('&#xf09b;');
}


.icon-unlock {
  .ie7icon('&#xf09c;');
}


.icon-credit-card {
  .ie7icon('&#xf09d;');
}


.icon-rss {
  .ie7icon('&#xf09e;');
}


.icon-hdd {
  .ie7icon('&#xf0a0;');
}


.icon-bullhorn {
  .ie7icon('&#xf0a1;');
}


.icon-bell {
  .ie7icon('&#xf0a2;');
}


.icon-certificate {
  .ie7icon('&#xf0a3;');
}


.icon-hand-right {
  .ie7icon('&#xf0a4;');
}


.icon-hand-left {
  .ie7icon('&#xf0a5;');
}


.icon-hand-up {
  .ie7icon('&#xf0a6;');
}


.icon-hand-down {
  .ie7icon('&#xf0a7;');
}


.icon-circle-arrow-left {
  .ie7icon('&#xf0a8;');
}


.icon-circle-arrow-right {
  .ie7icon('&#xf0a9;');
}


.icon-circle-arrow-up {
  .ie7icon('&#xf0aa;');
}


.icon-circle-arrow-down {
  .ie7icon('&#xf0ab;');
}


.icon-globe {
  .ie7icon('&#xf0ac;');
}


.icon-wrench {
  .ie7icon('&#xf0ad;');
}


.icon-tasks {
  .ie7icon('&#xf0ae;');
}


.icon-filter {
  .ie7icon('&#xf0b0;');
}


.icon-briefcase {
  .ie7icon('&#xf0b1;');
}


.icon-fullscreen {
  .ie7icon('&#xf0b2;');
}


.icon-group {
  .ie7icon('&#xf0c0;');
}


.icon-link {
  .ie7icon('&#xf0c1;');
}


.icon-cloud {
  .ie7icon('&#xf0c2;');
}


.icon-beaker {
  .ie7icon('&#xf0c3;');
}


.icon-cut {
  .ie7icon('&#xf0c4;');
}


.icon-copy {
  .ie7icon('&#xf0c5;');
}


.icon-paper-clip {
  .ie7icon('&#xf0c6;');
}

.icon-paperclip {
  .ie7icon('&#xf0c6;');
}


.icon-save {
  .ie7icon('&#xf0c7;');
}


.icon-sign-blank {
  .ie7icon('&#xf0c8;');
}


.icon-reorder {
  .ie7icon('&#xf0c9;');
}


.icon-list-ul {
  .ie7icon('&#xf0ca;');
}


.icon-list-ol {
  .ie7icon('&#xf0cb;');
}


.icon-strikethrough {
  .ie7icon('&#xf0cc;');
}


.icon-underline {
  .ie7icon('&#xf0cd;');
}


.icon-table {
  .ie7icon('&#xf0ce;');
}


.icon-magic {
  .ie7icon('&#xf0d0;');
}


.icon-truck {
  .ie7icon('&#xf0d1;');
}


.icon-pinterest {
  .ie7icon('&#xf0d2;');
}


.icon-pinterest-sign {
  .ie7icon('&#xf0d3;');
}


.icon-google-plus-sign {
  .ie7icon('&#xf0d4;');
}


.icon-google-plus {
  .ie7icon('&#xf0d5;');
}


.icon-money {
  .ie7icon('&#xf0d6;');
}


.icon-caret-down {
  .ie7icon('&#xf0d7;');
}


.icon-caret-up {
  .ie7icon('&#xf0d8;');
}


.icon-caret-left {
  .ie7icon('&#xf0d9;');
}


.icon-caret-right {
  .ie7icon('&#xf0da;');
}


.icon-columns {
  .ie7icon('&#xf0db;');
}


.icon-sort {
  .ie7icon('&#xf0dc;');
}


.icon-sort-down {
  .ie7icon('&#xf0dd;');
}


.icon-sort-up {
  .ie7icon('&#xf0de;');
}


.icon-envelope {
  .ie7icon('&#xf0e0;');
}


.icon-linkedin {
  .ie7icon('&#xf0e1;');
}


.icon-undo {
  .ie7icon('&#xf0e2;');
}

.icon-rotate-left {
  .ie7icon('&#xf0e2;');
}


.icon-legal {
  .ie7icon('&#xf0e3;');
}


.icon-dashboard {
  .ie7icon('&#xf0e4;');
}


.icon-comment-alt {
  .ie7icon('&#xf0e5;');
}


.icon-comments-alt {
  .ie7icon('&#xf0e6;');
}


.icon-bolt {
  .ie7icon('&#xf0e7;');
}


.icon-sitemap {
  .ie7icon('&#xf0e8;');
}


.icon-umbrella {
  .ie7icon('&#xf0e9;');
}


.icon-paste {
  .ie7icon('&#xf0ea;');
}


.icon-lightbulb {
  .ie7icon('&#xf0eb;');
}


.icon-exchange {
  .ie7icon('&#xf0ec;');
}


.icon-cloud-download {
  .ie7icon('&#xf0ed;');
}


.icon-cloud-upload {
  .ie7icon('&#xf0ee;');
}


.icon-user-md {
  .ie7icon('&#xf0f0;');
}


.icon-stethoscope {
  .ie7icon('&#xf0f1;');
}


.icon-suitcase {
  .ie7icon('&#xf0f2;');
}


.icon-bell-alt {
  .ie7icon('&#xf0f3;');
}


.icon-coffee {
  .ie7icon('&#xf0f4;');
}


.icon-food {
  .ie7icon('&#xf0f5;');
}


.icon-file-text-alt {
  .ie7icon('&#xf0f6;');
}


.icon-building {
  .ie7icon('&#xf0f7;');
}


.icon-hospital {
  .ie7icon('&#xf0f8;');
}


.icon-ambulance {
  .ie7icon('&#xf0f9;');
}


.icon-medkit {
  .ie7icon('&#xf0fa;');
}


.icon-fighter-jet {
  .ie7icon('&#xf0fb;');
}


.icon-beer {
  .ie7icon('&#xf0fc;');
}


.icon-h-sign {
  .ie7icon('&#xf0fd;');
}


.icon-plus-sign-alt {
  .ie7icon('&#xf0fe;');
}


.icon-double-angle-left {
  .ie7icon('&#xf100;');
}


.icon-double-angle-right {
  .ie7icon('&#xf101;');
}


.icon-double-angle-up {
  .ie7icon('&#xf102;');
}


.icon-double-angle-down {
  .ie7icon('&#xf103;');
}


.icon-angle-left {
  .ie7icon('&#xf104;');
}


.icon-angle-right {
  .ie7icon('&#xf105;');
}


.icon-angle-up {
  .ie7icon('&#xf106;');
}


.icon-angle-down {
  .ie7icon('&#xf107;');
}


.icon-desktop {
  .ie7icon('&#xf108;');
}


.icon-laptop {
  .ie7icon('&#xf109;');
}


.icon-tablet {
  .ie7icon('&#xf10a;');
}


.icon-mobile-phone {
  .ie7icon('&#xf10b;');
}


.icon-circle-blank {
  .ie7icon('&#xf10c;');
}


.icon-quote-left {
  .ie7icon('&#xf10d;');
}


.icon-quote-right {
  .ie7icon('&#xf10e;');
}


.icon-spinner {
  .ie7icon('&#xf110;');
}


.icon-circle {
  .ie7icon('&#xf111;');
}


.icon-reply {
  .ie7icon('&#xf112;');
}

.icon-mail-reply {
  .ie7icon('&#xf112;');
}


.icon-github-alt {
  .ie7icon('&#xf113;');
}


.icon-folder-close-alt {
  .ie7icon('&#xf114;');
}


.icon-folder-open-alt {
  .ie7icon('&#xf115;');
}


.icon-expand-alt {
  .ie7icon('&#xf116;');
}


.icon-collapse-alt {
  .ie7icon('&#xf117;');
}


.icon-smile {
  .ie7icon('&#xf118;');
}


.icon-frown {
  .ie7icon('&#xf119;');
}


.icon-meh {
  .ie7icon('&#xf11a;');
}


.icon-gamepad {
  .ie7icon('&#xf11b;');
}


.icon-keyboard {
  .ie7icon('&#xf11c;');
}


.icon-flag-alt {
  .ie7icon('&#xf11d;');
}


.icon-flag-checkered {
  .ie7icon('&#xf11e;');
}


.icon-terminal {
  .ie7icon('&#xf120;');
}


.icon-code {
  .ie7icon('&#xf121;');
}


.icon-reply-all {
  .ie7icon('&#xf122;');
}


.icon-mail-reply-all {
  .ie7icon('&#xf122;');
}


.icon-star-half-empty {
  .ie7icon('&#xf123;');
}

.icon-star-half-full {
  .ie7icon('&#xf123;');
}


.icon-location-arrow {
  .ie7icon('&#xf124;');
}


.icon-crop {
  .ie7icon('&#xf125;');
}


.icon-code-fork {
  .ie7icon('&#xf126;');
}


.icon-unlink {
  .ie7icon('&#xf127;');
}


.icon-question {
  .ie7icon('&#xf128;');
}


.icon-info {
  .ie7icon('&#xf129;');
}


.icon-exclamation {
  .ie7icon('&#xf12a;');
}


.icon-superscript {
  .ie7icon('&#xf12b;');
}


.icon-subscript {
  .ie7icon('&#xf12c;');
}


.icon-eraser {
  .ie7icon('&#xf12d;');
}


.icon-puzzle-piece {
  .ie7icon('&#xf12e;');
}


.icon-microphone {
  .ie7icon('&#xf130;');
}


.icon-microphone-off {
  .ie7icon('&#xf131;');
}


.icon-shield {
  .ie7icon('&#xf132;');
}


.icon-calendar-empty {
  .ie7icon('&#xf133;');
}


.icon-fire-extinguisher {
  .ie7icon('&#xf134;');
}


.icon-rocket {
  .ie7icon('&#xf135;');
}


.icon-maxcdn {
  .ie7icon('&#xf136;');
}


.icon-chevron-sign-left {
  .ie7icon('&#xf137;');
}


.icon-chevron-sign-right {
  .ie7icon('&#xf138;');
}


.icon-chevron-sign-up {
  .ie7icon('&#xf139;');
}


.icon-chevron-sign-down {
  .ie7icon('&#xf13a;');
}


.icon-html5 {
  .ie7icon('&#xf13b;');
}


.icon-css3 {
  .ie7icon('&#xf13c;');
}


.icon-anchor {
  .ie7icon('&#xf13d;');
}


.icon-unlock-alt {
  .ie7icon('&#xf13e;');
}


.icon-bullseye {
  .ie7icon('&#xf140;');
}


.icon-ellipsis-horizontal {
  .ie7icon('&#xf141;');
}


.icon-ellipsis-vertical {
  .ie7icon('&#xf142;');
}


.icon-rss-sign {
  .ie7icon('&#xf143;');
}


.icon-play-sign {
  .ie7icon('&#xf144;');
}


.icon-ticket {
  .ie7icon('&#xf145;');
}


.icon-minus-sign-alt {
  .ie7icon('&#xf146;');
}


.icon-check-minus {
  .ie7icon('&#xf147;');
}


.icon-level-up {
  .ie7icon('&#xf148;');
}


.icon-level-down {
  .ie7icon('&#xf149;');
}


.icon-check-sign {
  .ie7icon('&#xf14a;');
}


.icon-edit-sign {
  .ie7icon('&#xf14b;');
}


.icon-external-link-sign {
  .ie7icon('&#xf14c;');
}


.icon-share-sign {
  .ie7icon('&#xf14d;');
}


.icon-compass {
  .ie7icon('&#xf14e;');
}


.icon-collapse {
  .ie7icon('&#xf150;');
}


.icon-collapse-top {
  .ie7icon('&#xf151;');
}


.icon-expand {
  .ie7icon('&#xf152;');
}


.icon-eur {
  .ie7icon('&#xf153;');
}

.icon-euro {
  .ie7icon('&#xf153;');
}


.icon-gbp {
  .ie7icon('&#xf154;');
}


.icon-usd {
  .ie7icon('&#xf155;');
}

.icon-dollar {
  .ie7icon('&#xf155;');
}


.icon-inr {
  .ie7icon('&#xf156;');
}

.icon-rupee {
  .ie7icon('&#xf156;');
}


.icon-jpy {
  .ie7icon('&#xf157;');
}

.icon-yen {
  .ie7icon('&#xf157;');
}


.icon-cny {
  .ie7icon('&#xf158;');
}

.icon-renminbi {
  .ie7icon('&#xf158;');
}


.icon-krw {
  .ie7icon('&#xf159;');
}

.icon-won {
  .ie7icon('&#xf159;');
}


.icon-btc {
  .ie7icon('&#xf15a;');
}

.icon-bitcoin {
  .ie7icon('&#xf15a;');
}


.icon-file {
  .ie7icon('&#xf15b;');
}


.icon-file-text {
  .ie7icon('&#xf15c;');
}


.icon-sort-by-alphabet {
  .ie7icon('&#xf15d;');
}


.icon-sort-by-alphabet-alt {
  .ie7icon('&#xf15e;');
}


.icon-sort-by-attributes {
  .ie7icon('&#xf160;');
}


.icon-sort-by-attributes-alt {
  .ie7icon('&#xf161;');
}


.icon-sort-by-order {
  .ie7icon('&#xf162;');
}


.icon-sort-by-order-alt {
  .ie7icon('&#xf163;');
}


.icon-thumbs-up {
  .ie7icon('&#xf164;');
}


.icon-thumbs-down {
  .ie7icon('&#xf165;');
}


.icon-youtube-sign {
  .ie7icon('&#xf166;');
}


.icon-youtube {
  .ie7icon('&#xf167;');
}


.icon-xing {
  .ie7icon('&#xf168;');
}


.icon-xing-sign {
  .ie7icon('&#xf169;');
}


.icon-youtube-play {
  .ie7icon('&#xf16a;');
}


.icon-dropbox {
  .ie7icon('&#xf16b;');
}


.icon-stackexchange {
  .ie7icon('&#xf16c;');
}


.icon-instagram {
  .ie7icon('&#xf16d;');
}


.icon-flickr {
  .ie7icon('&#xf16e;');
}


.icon-adn {
  .ie7icon('&#xf170;');
}


.icon-bitbucket {
  .ie7icon('&#xf171;');
}


.icon-bitbucket-sign {
  .ie7icon('&#xf172;');
}


.icon-tumblr {
  .ie7icon('&#xf173;');
}


.icon-tumblr-sign {
  .ie7icon('&#xf174;');
}


.icon-long-arrow-down {
  .ie7icon('&#xf175;');
}


.icon-long-arrow-up {
  .ie7icon('&#xf176;');
}


.icon-long-arrow-left {
  .ie7icon('&#xf177;');
}


.icon-long-arrow-right {
  .ie7icon('&#xf178;');
}


.icon-apple {
  .ie7icon('&#xf179;');
}


.icon-windows {
  .ie7icon('&#xf17a;');
}


.icon-android {
  .ie7icon('&#xf17b;');
}


.icon-linux {
  .ie7icon('&#xf17c;');
}


.icon-dribbble {
  .ie7icon('&#xf17d;');
}


.icon-skype {
  .ie7icon('&#xf17e;');
}


.icon-foursquare {
  .ie7icon('&#xf180;');
}


.icon-trello {
  .ie7icon('&#xf181;');
}


.icon-female {
  .ie7icon('&#xf182;');
}


.icon-male {
  .ie7icon('&#xf183;');
}


.icon-gittip {
  .ie7icon('&#xf184;');
}


.icon-sun {
  .ie7icon('&#xf185;');
}


.icon-moon {
  .ie7icon('&#xf186;');
}


.icon-archive {
  .ie7icon('&#xf187;');
}


.icon-bug {
  .ie7icon('&#xf188;');
}


.icon-vk {
  .ie7icon('&#xf189;');
}


.icon-weibo {
  .ie7icon('&#xf18a;');
}


.icon-renren {
  .ie7icon('&#xf18b;');
}


