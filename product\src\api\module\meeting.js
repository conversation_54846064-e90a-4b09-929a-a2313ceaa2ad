// 导入封装的方法
import {
  get,
  post,
  postform,
  _post,
  fileRequest,
  postformProgress,
  exportFile
} from '../http'

const meeting = {
  // 公用
  // 查询会议列表 top卡片位置
  meetMeeting: {
    meetOptions (params) {
      // 列表
      return post('/meetMeeting/options', params)
    },
    meetTree (params) {
      // 查询会议列表树
      return post('/meetMeeting/meetTree', params)
    }
  },
  // 所有会议
  allMeeting: {
    getConferee (id) {
      return post(`/meetMeeting/getConferee?meetId=${id}`)
    },
    meetingList (params) {
      // 所有会议列表
      return post('/meetMeeting/list', params)
    },
    dels (params) {
      // 批量删除会议
      return post('/meetMeeting/dels', params)
    },
    putMeet (params) {
      // 上架/下架会议
      return post('/meetMeeting/putMeet', params)
    },
    meetType (params) {
      // 获取会议类型
      return post('/meetMeeting/meetType', params)
    },
    organize (params) {
      // 获取组织部门
      return get('/tree/list', params)
    },
    saveOtherPeople (params, header) {
      // 保存其他人员
      return post('/meetUser/saveMeetOtherUser', params, header)
    },
    loadOtherPeople (params) {
      // 获取其他人员
      return post('/meetUser/searchMeetUser', params)
    },
    loadApplyInform (params) {
      // 报名、签到通知请求
      return post('/meetMeeting/applyInform', params)
    },
    loadAttendanceCheck (params) {
      // 考勤查看
      return post('/meetMeeting/attendanceCheck', params)
    },
    loadExportCols (params) {
      // 获取可导出的字段
      return post('/meetMeeting/getExportFields', params)
    },
    exportMeetTable (params, headers) {
      // 导出会议列表
      return exportFile(`/meetMeeting/exportMeet?${params}`, headers)
    }
  },
  // 人员分组
  meetGroup: {
    groupList (params) {
      // 列表
      return post('/meetGroup/groupList', params)
    },
    importTemplate (params) {
      // 导出人员分组模版
      return exportFile('/meetGroup/importTemplate', params)
    },
    import (params, data) {
      // 导入人员分组excel
      return postform(`/meetGroup/import?relationId=${params}`, data)
    },
    editGroup (params, headers) {
      // 编辑新增
      return post('/meetGroup/editGroup', params, headers)
    },
    delete (params, headers) {
      // 删除
      return post('/meetGroup/delete', params, headers)
    },
    export (params, headers) {
      // 导出固定人员分组模版
      return exportFile('/meetGroup/export', params)
    }
  },
  // 报名管理
  meetSignUp: {
    applyLeaveCount (params) {
      // 报名请假卡片数据统计
      return post('/meetApplyLeave/countApplyLeaveData', params)
    },
    applyLeaveList (params) {
      // 报名请假列表
      return post('/meetApplyLeave/getApplyLeaveList', params)
    },
    apply (params) {
      // 报名
      return post('/meetApplyLeave/applyByAdministrators', params)
    },
    leave (params) {
      // 请假
      return post('/meetApplyLeave/leaveByAdministrators', params)
    },
    exportList (params) {
      // 导出报名请假列表
      return exportFile(`/meetApplyLeave/exportMeetApplyLeave?${params}`)
    },
    leaveMeetList (params) {
      // 请假弹框会议树形数据
      return post('/meetMeeting/leaveMeetList', params)
    },
    getLeaveInfo (params) {
      // 获取请假详情
      return post('/meetApplyLeave/getLeaveInfoByAdministrators', params)
    },
    leaveApproval (params) {
      // 请假审批
      return post('/meetApplyLeave/leaveApprovalByAdministrators', params)
    },
    getApplyLeaveInfo (params) {
      // 获取处理详情
      return post('/meetApplyLeave/getApplyLeaveInfoList', params)
    }
  },
  // 签到管理
  meetSign: {
    signList (params) {
      // 列表
      return post('/meetSign/list', params)
    },
    signTypeList (params) {
      // 签到主题列表
      return post('/meetSignType/list', params)
    },
    exportCode (params) {
      // 签到二维码
      return post('/meetSign/exportCode', params)
    },
    adminSign (params) {
      // 签到
      return post('/meetSign/adminSign', params)
    },
    adminNotSign (params) {
      // 取消签到
      return post('/meetSign/adminNotSign', params)
    },
    updateSignType (params) {
      // 增删改签到类型
      return post('/meetSignType/updateSignType', params)
    },
    updateSignRange (params, headers) {
      // 新增编辑会议签到范围
      return post('/meetSignUser/updateSignRange', params, headers)
    },
    findMeetSignUserRange (params) {
      // 查询会议签到范围
      return get('/meetSignUser/findMeetSignUserRange', params)
    },
    export (params) {
      // 导出
      return exportFile('/meetSign/export', params)
    },
    // 二维码下载
    generateWord (params) {
      // 导出
      return exportFile('/meetSign/generateWord', params)
    },
    // 判斷是否是父會還是子會
    getParentChi (id) {
      return get(`/meetMeeting/getParent?meetId=${id}`)
    }
  },
  // 通知模板
  meetTemplate: {
    list (params) {
      // 列表
      return post('/meetMessageTemplate/list', params)
    },
    add (params) {
      // 增通知模板
      return post('/meetMessageTemplate/add', params)
    },
    edit (params) {
      // 改通知模板
      return post('/meetMessageTemplate/edit', params)
    },
    id (params) {
      // get info
      return post(`/meetMessageTemplate/info/${params}`)
    },
    dels (params) {
      return post('/meetMessageTemplate/dels', params)
    }
  },
  // pc模板
  meetModule: {
    moduleList (params) {
      // 列表
      return post('/meetPcModule/list', params)
    },
    moduleEdit (params) {
      // 增删改pc模板
      return post('/meetPcModule/editModule', params)
    },
    meetPcModuleList (params) {
      // 针对会议入口页面
      return post('/meetPcModule/meetPcModuleList', params)
    },
    templateRoles (params) {
      // 获取角色
      return post('/meetAppTemplate/templateRoles', params)
    },
    meetTypeList (params) {
      // 获取会议类型
      return post('/meetAppTemplate/meetTypeList', params)
    }
  },
  // 会议小助手
  meetAssistant: {
    list (params) {
      // 列表
      return post('/meetAssistant/list', params)
    },
    edit (params, headers) {
      // 小助手关键词
      return post('/meetAssistant/edit', params, headers)
    }
  },
  // 系统外人员管理
  personnelMng: {
    personnelList (params) {
      // 列表
      return post('/meetExtraPeople/list', params)
    },
    personnelAdd (params) {
      // 新增
      return post('/meetExtraPeople/add', params)
    },
    personnelDels (params) {
      // 删除
      return post('/meetExtraPeople/dels', params)
    },
    personnelInfo (id) {
      // 详情
      return get(`/meetExtraPeople/info/${id}`)
    },
    personnelEdit (params) {
      // 编辑
      return post('/meetExtraPeople/edit', params)
    },
    personnelTemplate () {
      // 下载导入模板
      return exportFile('/meetExtraPeople/importTemplate')
    },
    personnelImport (data) {
      // 导入
      return postform('/meetExtraPeople/import', data)
    }
  },
  // 事务安排字段配置
  transactionMng: {
    transactionList (params) {
      // 列表
      return get('/meetPlanConfig/list', params)
    },
    transactionlEdit (params) {
      // 编辑
      return post('/meetPlanConfig/edit', params)
    },
    transactionUseList () {
      // 已启用列表
      return get('/meetPlanConfig/getTree')
    }
  },
  // 事务安排
  transactionSchedule: {
    transactionList (params) {
      // 列表
      return get('/meetPlan/list', params)
    },
    transactionlDels (params) {
      // 删除
      return get('/meetPlan/dels', params)
    },
    transactionlSend (params) {
      // 发送通知
      return _post('/meetPlan/sendMessage', params)
    },
    transactionlTemplate () {
      // 下载导入模板
      return exportFile('/meetPlan/importTemplate')
    },
    transactionlImport (params, data) {
      // 导入
      return postform(`/meetPlan/import?meetId=${params}`, data)
    }
  },
  // 会议回执
  meeetingReceipt: {
    receiptList (params) {
      // 会议回执列表
      return post('/meetReceipt/list', params)
    },
    receiptStatistical (params) {
      // 数据统计
      return get('/meetReceipt/getReceiptCount', params)
    },
    receiptReply (params) {
      // 回复回执
      return _post('/meetReceipt/replyReceipt', params)
    },
    receiptExport (params) {
      // 导出会议回执
      return exportFile('/meetReceipt/export', params)
    },
    receiptOptionList (params) {
      // 回执选项列表
      return get('/meetReceiptOption/list', params)
    },
    receiptOptionAdd (params) {
      // 新增回执选项
      return post('/meetReceiptOption/add', params)
    },
    receiptOptionEdit (params) {
      // 编辑回执选项
      return post('/meetReceiptOption/edit', params)
    },
    receiptOptionDels (params) {
      // 删除回执选项
      return post('/meetReceiptOption/dels', params)
    },
    receiptOptionDetail (id) {
      // 回执选项详情
      return get(`/meetReceiptOption/info/${id}`)
    },
    receiptScope (params) {
      // 已选回执范围
      return post('/meetReceiptUser/list', params)
    },
    receiptScopeAdd (params) {
      // 设置回执范围
      return post('/meetReceiptUser/add', params)
    }
  },
  // 会议材料
  meetMaterial: {
    meetfileList (params) {
      // 列表
      return post('/meetFile/list', params)
    },
    materialAdd (params) {
      // 新增材料
      return post('/fileUploadController/uploadFile', params)
    },
    checkTitle (params) {
      return post('/meetFile/checkTitle', params)
    },
    materialAdds (params, callback, id) { // 新增材料获取上传进度
      return postformProgress('fileUploadController/uploadMeetFile', params, 880000, callback, id)
    },
    confirmMeetFile (params) { // 新增材料确定保存
      return post('/meetFile/confirmMeetFile', params)
    },
    materialTypeList (params) { // 材料类型列表
      return post('/meetFile/fileType', params)
    },
    materialDels (params) {
      // 删除
      return post('/meetFile/dels', params)
    },
    meetfileDownload (params, text) {
      // 下载
      return fileRequest('/fileUploadController/download', params, text)
    },
    findMeetFileControl (params) {
      // 文件已授权人员查询
      return post('/meetFileControl/findMeetFileControl', params)
    },
    saveMeetFileControl (params) {
      // 授权
      return post('/meetFileControl/saveMeetFileControl', params)
    },
    editMeetTitle (params) {
      return post(`/meetFile/editMeetTitle?${params}`)
    }
  },
  // 新增会议
  meetAdd: {
    removeApplyByMeetId (id) {
      // 清空指定会议的报名请假记录
      return post(`/meetApplyLeave/removeApplyByMeetId?relationId=${id}`)
    },
    meetmeetingtempList (params) {
      // 模板列表
      return post('/meetMeetingTemp/list', params)
    },
    meetmeetingtempInfo (id) {
      // 模板详情
      return post(`/meetMeetingTemp/info/${id}`)
    },
    meetMeetingInit (params) {
      // 初始化id
      return post('/meetMeeting/init', params)
    },
    meetmeetingAdd (params) {
      // 新增会议
      return post('/meetMeeting/add', params)
    },
    meetmeetingEdit (id) {
      // 编辑会议
      return post(`/meetMeeting/edit/${id}`)
    },
    meetmeetingInfo (id) {
      // 会议详情
      return post(`/meetMeeting/info/${id}`)
    },
    meetUserAdd (params) {
      // 新增人员
      return _post('/meetUser/saveMeetUser', params)
    },
    meetFileAdd (params) {
      // 新增材料
      return post('meetFile/add', params)
    },
    meetFileeEdit (params) {
      // 编辑材料
      return post('/meetFile/edit', params)
    },
    meetMeetingTempAdd (params) {
      // 新增会议模板
      return post('/meetMeetingTemp/add', params)
    },
    meetMeetingTempEdit (params) {
      // 编辑会议模板
      return post('/meetMeetingTemp/edit', params)
    },
    meetTypeList (params) {
      // 会议类型
      return post('/meetMeeting/meetType', params)
    },
    unitList (params) {
      // 组织部门
      return post('/tree/list', params)
    },
    meetPlaceList (params) {
      // 会议室
      return post('/meetPlace/listData', params)
    },
    checkMeetPlace (params) {
      // 会议室是否冲突
      return post('/meetMeeting/checkMeetPlace', params)
    },
    checkMeetName (params) {
      // 会议名称是否冲突
      return post('/meetMeeting/checkMeetName', params)
    },
    putMeet (params) {
      // 发布接口
      return post('/meetMeeting/putMeet', params)
    }
  },
  // 发送通知
  sendInforms: {
    searchMeetUser (params) {
      // 查询参会人员
      return post('/meetUser/searchMeetUser', params)
    },
    sendMessage (params) {
      // 发送
      return _post('/meetMessageLog/sendMessage', params)
    },
    sendSMS (params) {
      // 修改导入住宿安排信息发送短信状态
      return post('/meetImportStation/sendSMS', params)
    }
  },
  // 发送阅读记录
  sendRecords: {
    getMessageLog (params) {
      // 列表
      return post('/meetMessageLog/getMessageLog', params)
    },
    getMessageCount (params) {
      // 统计
      return post('meetMessageLog/getMessageCount', params)
    }
  },
  // 阅读记录
  readRecords: {
    readingRecordsList (params) {
      // 列表
      return post('/meetBrowseLog/readingList', params)
    },
    getReadingCount (params) {
      // 统计
      return post('/meetBrowseLog/getReadingCount', params)
    }
  },
  // app模板
  appTemplate: {
    moduleList (params) {
      // 列表
      return post('/meetAppModule/list', params)
    },
    moduleAdd (params) {
      return post('/meetAppModule/add', params)
    },
    moduleEdit (params) {
      return post('/meetAppModule/edit', params)
    },
    moduleDel (params) {
      // del
      return post(`/meetAppModule/del/${params}`)
    },
    moduleData () {
      return post('/meetAppModule/moduleData')
    },
    dictionaryList (params) {
      return post('/dictionary/list', params)
    },
    meetPlanConfigList (params) {
      return post('/meetPlanConfig/list', params)
    },
    // app模板
    templateList (params) {
      return post('/meetAppTemplate/list', params)
    },
    templateAdd (params) {
      return post('/meetAppTemplate/add', params)
    },
    templateEdit (params) {
      return post('/meetAppTemplate/edit', params)
    },
    templateInfo (params) {
      return post(`/meetAppTemplate/info/${params}`)
    },
    templateDel (params) {
      return post(`/meetAppTemplate/del/${params}`)
    }
  },
  // 会议模板
  meetingTemplate: {
    meetingTemplateDels (params) {
      // 删除
      return post('/meetMeetingTemp/dels', params)
    },
    meetingTemplateEdit (params) {
      // 编辑
      return post('/meetMeetingTemp/edit', params)
    }
  },
  // 选人
  meetPointTree: {
    importTemplate (data) {
      // 选人组件导入数据
      return postform('/meetPointTree/importTemplate', data)
    },
    exportTemplate (params) {
      // 选人组件导出模版文件
      return exportFile('/meetPointTree/exportTemplate', params)
    },
    meetAttendUser (params) {
      // 选人组件导出模版文件
      return post(`/meetPointTree/meetAttendUser?meetId=${params}`)
    },
    extraPeopleParent (data) {
      // 选人组件单位
      return postform('/meetPointTree/extraPeopleParent', data)
    },
    extraPeopleChild (data, headers) {
      // 选人组件人
      return postform('/meetPointTree/extraPeopleChild', data, headers)
    }
  },
  // 会议室管理
  meetingRoomMng: {
    meetRoomList (params) {
      // 会议室列表
      return post('/meetPlace/list', params)
    },
    meetRoomEdit (params) {
      // 会议室编辑
      return post('/meetPlace/save', params)
    },
    dels (params) {
      // 会议室删除
      return post('/meetPlace/deleteBatch', params)
    },
    saveSeatInfo (params) {
      // 保存会议室座位信息
      return post('/meetPlace/saveLayout', params)
    },
    loadSeatInfo (params) {
      // 加载会议室座位信息
      return post('/meetPlace/getLayout', params)
    }
  },
  // 住地管理
  meetingPlaceMng: {
    meetPlaceList (params) {
      // 住地列表
      return post('/meetHotel/list', params)
    },
    meetPlaceEdit (params) {
      // 住地编辑
      return post('/meetHotel/save', params)
    },
    dels (params) {
      // 住地删除
      return post('/meetHotel/deleteBatch', params)
    },
    saveRoomInfo (params) {
      // 保存住地房间信息
      return post('/meetHotel/saveLayout', params)
    },
    loadRoomInfo (params) {
      // 加载住地房间信息
      return post('/meetHotel/getLayout', params)
    }
  },
  // 会议排座
  meetingSeat: {
    getType (params) {
      // 获取会议排座方式信息
      return post('/meetSeatMode/get', params)
    },
    saveType (params) {
      // 保存会议排座方式信息
      return post('/meetSeatMode/save', params)
    },
    getSeatFile (params) {
      // 获取会议排座文件
      return post('/meetSeatMode/getFile', params)
    },
    delSeatFile (params) {
      // 删除会议排座文件
      return post('/meetSeatMode/deleteFile', params)
    }
  },
  // 住宿安排
  meetingRoom: {
    getType (params) {
      // 获取会议住地安排方式信息
      return post('/meetStationMode/get', params)
    },
    saveType (params) {
      // 保存会议住地安排方式信息
      return post('/meetStationMode/save', params)
    },
    getList (params) {
      // 获取住宿安排信息分页列表
      return post('/meetImportStation/list', params)
    },
    deleList (params) {
      // 获取界别下拉框信息
      return post('/meetImportStation/deleList', params)
    },
    stationList (params) {
      // 获取住地下拉框信息
      return post('/meetImportStation/stationList', params)
    },
    dels (params) {
      // 删除住地信息
      return post('/meetImportStation/dels', params)
    },
    pushApp (params) {
      // 推送导入住宿安排信息至APP
      return post('/meetImportStation/pushAPP', params)
    },
    getTemplate (params) {
      // 获取住宿安排的导入模板
      return exportFile('/meetImportStation/getTemplate', params)
    },
    stationImport (params, data) {
      // 导入
      return postform(
        `/meetImportStation/importStation?relationId=${params}`,
        data
      )
    }
  },
  newMeeting: {
    saveConference (params) {
      return post('/conference/saveConference', params, {
        'Content-Type': 'application/json;charset=UTF-8'
      })
    },
    getUsers (params) {
      return post('/conference/getUsers', params)
    },
    list (params) {
      return post('/conference/list', params)
    },
    info (params) {
      return post(`/conference/info/${params}`)
    },
    getAttendanceHeader (params) {
      return post('/conference/getAttendanceHeader', params)
    },
    getAttendanceListVos (params) {
      return post('/conference/getAttendanceListVos', params)
    },
    addSignUpJoinUser (params) {
      return post('/conference/addSignUpJoinUser', params)
    },
    delJoinUser (params) {
      return post('/conference/delJoinUser', params)
    },
    addSignInJoinUser (params) {
      return post('/conference/addSignInJoinUser', params)
    },
    addLeaves (params) {
      return post('/conference/addLeaves', params)
    },
    getQrCode (params) {
      return post('/conference/getQrCode', params)
    },
    smsSend (params) {
      return post('/conference/smsSend', params)
    },
    getConferenceWordVo (params) {
      return post('/conference/getConferenceWordVo', params)
    },
    getHaveSignInOrNoSignInUsers (params) {
      return post('/conference/getHaveSignInOrNoSignInUsers', params)
    },
    getIsSignInOrNoSignInUser (params) {
      return post('/conference/getIsSignInOrNoSignInUser', params)
    },
    updateIsAppShow (params) {
      return post('/conference/updateIsAppShow', params)
    },
    getConferenceUserTemplate (params) {
      return exportFile('/conference/getConferenceUserTemplate', params)
    },
    getConferenceUserExcel (params) {
      return post('/conference/getConferenceUserExcel', params)
    },
    templateToConference (params) {
      return post('/conference/templateToConference', params, {
        'Content-Type': 'application/json;charset=UTF-8'
      })
    },
    dels (params) {
      return post('/conference/dels', params)
    },
    deleteUsers (params) {
      return post('/conference/deleteUsers', params)
    },
    attachmentDels (params) {
      return post('/attachment/dels', params)
    },
    updateIsRelease (params) {
      return post('/conference/updateIsRelease', params)
    },
    updateLayout (params) {
      return post('/conference/updateLayout', params)
    }
  },
  conferenceparent: { // 全会
    saveConferenceParent (params) {
      return post('/conferenceparent/saveConferenceParent', params, {
        'Content-Type': 'application/json;charset=UTF-8'
      })
    },
    list (params) {
      return post('/conferenceparent/list', params)
    },
    info (params) {
      return post(`/conferenceparent/info/${params}`)
    },
    dels (params) {
      return post('/conferenceparent/dels', params)
    },
    updateIsRelease (params) {
      return post('/conferenceparent/updateIsRelease', params)
    }
  },
  conferencematerial: {
    list (params) {
      return post('/conferencematerial/list', params)
    },
    info (params) {
      return post(`/conferencematerial/info/${params}`)
    },
    add (params) {
      return post('/conferencematerial/add', params)
    },
    edit (params) {
      return post('/conferencematerial/edit', params)
    },
    dels (params) {
      return post('/conferencematerial/dels', params)
    },
    updateFileSort (params) {
      return post('/conferencematerial/updateFileSort', params)
    },
    getFileVos (params) {
      return post('/conferencematerial/getFileVos', params)
    }
  },
  conferenceroom: {
    list (params) {
      return post('/conferenceroom/list', params)
    },
    info (params) {
      return post(`/conferenceroom/info/${params}`)
    },
    add (params) {
      return post('/conferenceroom/add', params)
    },
    edit (params) {
      return post('/conferenceroom/edit', params)
    },
    dels (params) {
      return post('/conferenceroom/dels', params)
    },
    getConferenceRoomMap (params) {
      return post('/conferenceroom/getConferenceRoomMap', params)
    }
  },
  conferencetransaction: {
    list (params) {
      return post('/conferencetransaction/list', params)
    },
    dels (params) {
      return post('/conferencetransaction/dels', params)
    },
    export (params) {
      return exportFile('/conferencetransaction/export', params)
    },
    import (params) {
      return post('/conferencetransaction/import', params, {
        timeout: 80000
      })
    }
  },
  conferenceleave: {
    list (params) {
      return post('/conferenceleave/list', params)
    },
    info (params) {
      return post(`/conferenceleave/info/${params}`)
    },
    add (params) {
      return post('/conferenceleave/add', params)
    },
    edit (params) {
      return post('/conferenceleave/edit', params)
    },
    dels (params) {
      return post('/conferenceleave/dels', params)
    },
    auditLeave (params) {
      return post('/conferenceleave/auditLeave', params)
    }
  },
  conferenceroomlayout: {
    list (params) {
      return post('/conferenceroomlayout/list', params)
    },
    export (params) {
      return exportFile('/conferenceroomlayout/export', params)
    },
    saveConferenceRoomLayouts (params) {
      return post('/conferenceroomlayout/saveConferenceRoomLayouts', params, {
        'Content-Type': 'application/json;charset=UTF-8'
      })
    }
  },
  conferenceconnectors: {
    importemplate (params) {
      return exportFile('/conferenceconnectors/importemplate', params)
    },
    import (params) {
      return post('/conferenceconnectors/import', params)
    },
    add (params) {
      return post('/conferenceconnectors/add', params, {
        'Content-Type': 'application/json;charset=UTF-8'
      })
    }
  },
  faceSignIn: {
    facemeetingrelevance: {
      list (params) {
        return post('/facemeetingrelevance/list', params)
      },
      dels (params) {
        return post('/facemeetingrelevance/dels', params)
      },
      edit (params) {
        return post('/facemeetingrelevance/edit', params)
      },
      add (params) {
        return post('/facemeetingrelevance/add', params)
      },
      info (params) {
        return post(`/facemeetingrelevance/info/${params}`)
      }
    },
    spectaculars: {
      list (params) {
        return post('/spectaculars/list', params)
      },
      dels (params) {
        return post('/spectaculars/dels', params)
      },
      edit (params) {
        return post('/spectaculars/edit', params)
      },
      add (params) {
        return post('/spectaculars/add', params)
      }
    },
    equipment: {
      list (params) {
        return post('/equipment/list', params)
      },
      dels (params) {
        return post('/equipment/dels', params)
      },
      edit (params) {
        return post('/equipment/edit', params)
      },
      add (params) {
        return post('/equipment/add', params)
      },
      info (params) {
        return post(`/equipment/info/${params}`)
      }
    },
    camera: {
      list (params) {
        return post('/camera/list', params)
      },
      dels (params) {
        return post('/camera/dels', params)
      },
      del (params) {
        return post(`/camera/del/${params}`)
      },
      edit (params) {
        return post('/camera/edit', params)
      },
      add (params) {
        return post('/camera/add', params)
      },
      info (params) {
        return post(`/camera/info/${params}`)
      }
    },
    facedatabase: {
      list (params) {
        return post('/facedatabase/list', params)
      },
      warningPictureList (params) {
        return post('/facedatabase/warningPictureList', params)
      },
      del (params) {
        return post('/facedatabase/del', params)
      },
      edit (params) {
        return post('/facedatabase/edit', params)
      },
      add (params) {
        return post('/facedatabase/add', params)
      },
      info (params) {
        return post(`/facedatabase/info/${params}`)
      },
      uploadFaceImgBatch (params) {
        return post('/facedatabase/uploadFaceImgBatch', params)
      },
      faceImgList (params) {
        return post('/facedatabase/faceImgList', params)
      },
      delFaceImg (params) {
        return post('/facedatabase/delFaceImg', params)
      },
      updFaceImg (params) {
        return post('/facedatabase/updFaceImg', params)
      },
      photoBatchDel (params) {
        return post('/facedatabase/photoBatchDel', params)
      }
    },
    cityinfo () {
      return get('http://www.weather.com.cn/data/cityinfo/101120201.html')
    }
  }
}
export default meeting
