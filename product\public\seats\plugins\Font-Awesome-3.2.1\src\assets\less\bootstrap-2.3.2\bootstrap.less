/*!
 * Bootstrap v2.3.2
 *
 * Copyright 2012 Twitter, Inc
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Designed and built with all the love in the world @twitter by @mdo and @fat.
 */

// Core variables and mixins
@import "variables.less"; // Modify this for custom colors, font-sizes, etc
@import "mixins.less";

// CSS Reset
@import "reset.less";

// Grid system and page structure
@import "scaffolding.less";
@import "grid.less";
@import "layouts.less";

// Base CSS
@import "type.less";
@import "code.less";
@import "forms.less";
@import "tables.less";

// Components: common
@import "sprites.less";
@import "dropdowns.less";
@import "wells.less";
@import "component-animations.less";
@import "close.less";

// Components: Buttons & Alerts
@import "buttons.less";
@import "button-groups.less";
@import "alerts.less"; // Note: alerts share common CSS with buttons and thus have styles in buttons.less

// Components: Nav
@import "navs.less";
@import "navbar.less";
@import "breadcrumbs.less";
@import "pagination.less";
@import "pager.less";

// Components: Popovers
@import "modals.less";
@import "tooltip.less";
@import "popovers.less";

// Components: Misc
@import "thumbnails.less";
@import "media.less";
@import "labels-badges.less";
@import "progress-bars.less";
@import "accordion.less";
@import "carousel.less";
@import "hero-unit.less";

// Utility classes
@import "utilities.less"; // Has to be last to override when necessary
