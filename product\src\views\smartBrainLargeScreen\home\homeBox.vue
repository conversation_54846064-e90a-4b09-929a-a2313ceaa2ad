<template>
  <div class="big-screen" ref="bigScreen">
    <div class="screen-header">
      <div class="header-left">
        <span class="date-time">{{ currentTime }}</span>
        <span class="weather">晴 24℃ 东南风</span>
      </div>
      <div class="header-center">
        <img src="../../../assets/largeScreen/top_header_txt.png" alt="" style="height: 50px;">
      </div>
      <div class="header-right"></div>
    </div>
    <div class="screen-content">
      <div class="left-panel">
        <!-- 委员统计 -->
        <div class="committee_statistics">
          <div class="header_box">
            <span class="header_text_left">委员统计</span>
            <span class="header_text_right">十二届二次</span>
          </div>
          <div class="committee_statistics_content"></div>
        </div>
        <!-- 提案统计 -->
        <div class="proposal_statistics">
          <div class="header_box">
            <span class="header_text_left">提案统计</span>
            <span class="header_text_right">十二届二次会议</span>
            <span class="header_text_center">提交提案总数：<span>873</span>件</span>
          </div>
          <div class="proposal_statistics_content"></div>
        </div>
        <!-- 工作动态 -->
        <div class="work_dynamics">
          <div class="header_box">
            <span class="header_text_left">工作动态</span>
            <span class="header_text_right">本年</span>
          </div>
          <div class="work_dynamics_content"></div>
        </div>
      </div>
      <div class="center-panel">
        <!-- 地图 -->
        <div class="map_box">

        </div>
        <!-- 履职统计 -->
        <div class="performance_statistics">
          <div class="header_box">
            <span class="header_text_left">履职统计</span>
            <span class="header_text_right">十二届二次</span>
          </div>
          <div class="performance_statistics_content"></div>
        </div>
      </div>
      <div class="right-panel">
        <!-- 社情民意 -->
        <div class="social">
          <div class="header_box">
            <span class="header_text_left">社情民意</span>
            <span class="header_text_right">本年</span>
          </div>
          <div class="social_content"></div>
        </div>
        <!-- 会议活动 -->
        <div class="conference_activities">
          <div class="header_box">
            <span class="header_text_left">会议活动</span>
            <span class="header_text_right">本年</span>
          </div>
          <div class="conference_activities_content"></div>
        </div>
        <!-- 网络议政 -->
        <div class="discussions">
          <div class="header_box">
            <span class="header_text_left">网络议政</span>
            <span class="header_text_right"></span>
          </div>
          <div class="discussions_content"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { useIndex } from '../screen.js'

export default {
  name: 'BigScreen',
  data () {
    return {
      currentTime: '',
      personnelData: [
        { name: '消防', value: 32, percentage: 80 },
        { name: '医疗', value: 15, percentage: 60 },
        { name: '民政', value: 14, percentage: 55 },
        { name: '应急', value: 13, percentage: 50 },
        { name: '其他', value: 10, percentage: 40 }
      ],
      equipmentLegend: [
        { name: '应急分队', value: '22.52%', color: '#00d4ff' },
        { name: '救援车辆', value: '4.21%', color: '#ff6b6b' },
        { name: '医疗设备', value: '18.33%', color: '#4ecb73' },
        { name: '通信设备', value: '3.71%', color: '#ffd93d' }
      ],
      workNews: [
        { id: 1, date: '2025-06-03', title: '市应急局举办全市工作人员公务员"技能竞赛"' },
        { id: 2, date: '2025-05-30', title: '马晓红同志到我局调研工作进展情况' },
        { id: 3, date: '2025-05-30', title: '局党委"党建引领"文明实践志愿服务人员' },
        { id: 4, date: '2025-05-30', title: '市科技局局长带队深入我局开展调研' }
      ],
      tableData: [
        { category: '分析', total: 515, pending: 15, processing: 0, completed: 0, rate: 0, satisfaction: 0 },
        { category: '建议', total: 408, pending: 0, processing: 0, completed: 13, rate: 15, satisfaction: 0 },
        { category: '提案', total: 540, pending: 0, processing: 0, completed: 0, rate: 35, satisfaction: 0 },
        { category: '合计', total: 500, pending: 0, processing: 0, completed: 1, rate: 50, satisfaction: 0 }
      ]
    }
  },
  computed: {
    equipmentChartOptions () {
      return {
        tooltip: { trigger: 'item' },
        series: [{
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          data: [
            { value: 22.52, name: '应急分队' },
            { value: 18.33, name: '医疗设备' },
            { value: 4.21, name: '救援车辆' },
            { value: 3.71, name: '通信设备' }
          ],
          itemStyle: {
            borderRadius: 5,
            borderColor: '#fff',
            borderWidth: 2
          }
        }]
      }
    },
    opinionChartOptions () {
      return {
        tooltip: { trigger: 'item' },
        series: [{
          type: 'gauge',
          radius: '80%',
          center: ['50%', '60%'],
          startAngle: 200,
          endAngle: -20,
          min: 0,
          max: 1000,
          splitNumber: 5,
          data: [{ value: 1057, name: '总数' }],
          detail: {
            fontSize: 20,
            color: '#00d4ff'
          }
        }]
      }
    },
    tableHeaderStyle () {
      return {
        backgroundColor: '#1a3a5c',
        color: '#00d4ff',
        fontSize: '14px'
      }
    }
  },
  mounted () {
    this.initScreen()
    this.updateTime()
    this.timeInterval = setInterval(this.updateTime, 1000)
  },
  beforeDestroy () {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
  methods: {
    initScreen () {
      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)
      calcRate()
      windowDraw()
    },
    updateTime () {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.big-screen {
  width: 1920px;
  height: 1080px;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: left top;
  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;
  background-size: cover;
  background-position: center;

  .screen-header {
    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center;
    height: 65px;
    display: flex;
    align-items: center;
    padding: 0 40px;

    .header-left {
      display: flex;
      gap: 20px;
      font-size: 14px;
      color: #8cc8ff;
      flex: 1;
    }

    .header-center {
      width: 60%;
      text-align: center;
    }

    .header-right {
      flex: 1;
    }
  }

  .screen-content {
    height: calc(100% - 65px);
    display: flex;
    padding: 35px 20px 0 20px;
    gap: 30px;

    .header_box {
      position: absolute;
      top: 15px;
      left: 24px;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .header_text_left {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
      }

      .header_text_right {
        font-size: 15px;
        color: #FFD600;
      }

      .header_text_center {
        font-size: 15px;
        color: #FFFFFF;
        display: flex;
        align-items: center;

        span {
          font-weight: 500;
          font-size: 24px;
          color: #02FBFB;
          margin: 0 10px 0 6px;
        }
      }
    }

    .left-panel,
    .right-panel {
      width: 470px;
      display: flex;
      flex-direction: column;
      gap: 20px 30px;
    }

    .left-panel {
      .committee_statistics {
        background: url('../../../assets/largeScreen/committee_statistics_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 320px;
        width: 100%;

        .committee_statistics_content {}
      }

      .proposal_statistics {
        background: url('../../../assets/largeScreen/proposal_statistics_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 310px;
        width: 100%;

        .proposal_statistics_content {}
      }

      .work_dynamics {
        background: url('../../../assets/largeScreen/work_dynamics_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 270px;
        width: 100%;

        .work_dynamics_content {}
      }
    }

    .right-panel {
      .social {
        background: url('../../../assets/largeScreen/social_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 290px;
        width: 100%;

        .social_content {}
      }

      .conference_activities {
        background: url('../../../assets/largeScreen/conference_activities_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 290px;
        width: 100%;

        .conference_activities_content {}
      }

      .discussions {
        background: url('../../../assets/largeScreen/discussions_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 320px;
        width: 100%;

        .discussions_content {}
      }
    }

    .center-panel {
      flex: 1;
      gap: 20px;
      display: flex;
      flex-direction: column;

      .map_box {
        background: url('../../../assets/largeScreen/map_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        height: 648px;
        width: 100%;
      }

      .performance_statistics {
        background: url('../../../assets/largeScreen/performance_statistics_bg.png') no-repeat;
        background-size: 100% 100%;
        background-position: center;
        position: relative;
        height: 270px;
        width: 100%;

        .performance_statistics_content {}
      }
    }
  }
}
</style>
