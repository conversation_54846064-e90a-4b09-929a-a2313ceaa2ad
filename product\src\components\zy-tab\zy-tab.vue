<template>
  <div class="zy-tab"
       ref="zy-tab">
    <div class="zy-tab-left"
         v-if="show&&offset>0||offset!=0"
         @click.stop="tabsLeft"><i class="el-icon-d-arrow-left"></i></div>
    <div class="zy-tab-right"
         v-if="show&&offset<biggest"
         @click.stop="tabsRight"><i class="el-icon-d-arrow-right"></i></div>
    <div class="zy-tab-box"
         ref="zy-tab-box">
      <div class="zy-tab-item-list"
           ref="zy-tab-item-list">
        <div v-for="(item, index) in tabData"
             :key="index"
             @click.prevent="selectedMethods(item)"
             :ref="item.class?'zy-tab-item-active':'zy-tab-item'"
             :class="['zy-tab-item',item.class?'zy-tab-item-active':'']">
          <div class="zy-tab-item-label">{{item[props.label]}}</div>
          <span class="zy-tab-item-del-box"
                v-if="item.class">
            <span class="zy-tab-item-refresh"
                  @click.stop="refreshclick(item,index)"><i class="el-icon-refresh"></i></span>
          </span>
          <span class="zy-tab-item-del-box"
                v-if="tabData.length!=1">
            <span class="zy-tab-item-del"
                  @click.stop="deleteclick(item,index)"><i class="el-icon-close"></i></span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
export default {
  name: 'zyTab',
  data () {
    return {
      tabId: this.value,
      tabData: [],
      show: false,
      offset: 0,
      biggest: 0
    }
  },
  props: {
    value: {
      type: String
    },
    tabList: {
      type: Array,
      default: () => []
    },
    shift: {
      type: Number,
      default: 168
    },
    props: {
      type: Object,
      default: () => {
        return {
          id: 'id',
          label: 'label'
        }
      }
    }
  },
  created () {
    this.tabCopyData(this.deepCopy(this.tabList))
  },
  watch: {
    value (val) {
      this.tabId = val
      this.selected()
    },
    tabId (val) {
      this.$emit('input', val)
    },
    tabList (val) {
      this.tabCopyData(this.deepCopy(this.tabList))
      this.$nextTick(() => {
        this.biggestClick()
      })
    }
  },
  mounted () {
    this.$nextTick(() => {
      if (this.tabId) {
        this.tabBox()
      }
      const that = this
      erd.listenTo(this.$refs['zy-tab'], (element) => {
        that.$nextTick(() => {
          this.biggestClick()
        })
      })
    })
  },
  methods: {
    selectedMethods (item) {
      this.tabId = item[this.props.id]
      this.selected()
      if (item.type) {
        setTimeout(() => {
          this.$router.push({
            path: item.to,
            query: item.params
          })
        }, 200)
      }
    },
    selected (id) {
      var arr = this.tabData
      arr.forEach(item => {
        item.class = false
        if (item[this.props.id] === this.tabId) {
          item.class = true
        }
      })
      this.tabData = arr
      this.$nextTick(() => {
        this.tabBox()
      })
    },
    refreshclick (data) {
      this.$emit('tab-refresh', data)
    },
    deleteclick (data, index) {
      if (this.tabId === data[this.props.id]) {
        this.Before(index)
      }
      this.$emit('tab-click', data)
    },
    Before (i) {
      this.tabList.forEach((item, index) => {
        if (i === 0) {
          if (index === i + 1) {
            this.tabId = item[this.props.id]
            if (item.type) {
              setTimeout(() => {
                this.$router.push({
                  path: item.to,
                  query: item.params
                })
              }, 200)
            }
          }
        } else {
          if (index === i - 1) {
            this.tabId = item[this.props.id]
            if (item.type) {
              setTimeout(() => {
                this.$router.push({
                  path: item.to,
                  query: item.params
                })
              }, 200)
            }
          }
        }
      })
    },
    biggestClick () {
      var tabBox = this.$refs['zy-tab-box']
      var itemBox = this.$refs['zy-tab-item-list']
      if (itemBox.offsetWidth > tabBox.offsetWidth) {
        this.show = true
        this.biggest = itemBox.offsetWidth - tabBox.offsetWidth
      } else {
        this.show = false
      }
    },
    tabsLeft () {
      var itemBox = this.$refs['zy-tab-item-list']
      var offset = this.offset - this.shift
      if (this.offset - this.shift <= 0) {
        offset = 0
      }
      itemBox.style.transform = `translateX(-${offset}px)`
      itemBox.style.transitionDuration = '.4s'
      this.offset = offset
    },
    tabsRight () {
      var itemBox = this.$refs['zy-tab-item-list']
      var offset = this.offset + this.shift
      if (this.biggest < this.offset + this.shift) {
        offset = this.biggest
      }
      itemBox.style.transform = `translateX(-${offset}px)`
      itemBox.style.transitionDuration = '.4s'
      this.offset = offset
    },
    tabBox () {
      var tabBox = this.$refs['zy-tab-box']
      var itemBox = this.$refs['zy-tab-item-list']
      var itemActive = itemBox.querySelector('.zy-tab-item-active')
      if (tabBox.offsetWidth < itemBox.offsetWidth) {
        itemBox.style.transform = 'translateX(0px)'
        itemBox.style.transitionDuration = '.4s'
        if (itemBox.offsetWidth === itemActive.offsetLeft + itemActive.offsetWidth) {
          this.offset = itemBox.offsetWidth - tabBox.offsetWidth
          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`
        } else if (tabBox.offsetWidth / 2 > itemBox.offsetWidth - itemActive.offsetLeft) {
          this.offset = itemBox.offsetWidth - tabBox.offsetWidth
          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`
        } else {
          if (itemActive.offsetLeft - tabBox.offsetWidth / 2 < 0) {
            this.offset = 0
          } else {
            this.offset = itemActive.offsetLeft - tabBox.offsetWidth / 2
          }
          itemBox.style.transform = `translateX(-${itemActive.offsetLeft - tabBox.offsetWidth / 2}px)`
        }
      }
    },
    tabCopyData (data) {
      this.tabData = this.initData(data)
    },
    initData (items) {
      items.forEach((item, index) => {
        if ((typeof item.class) === 'undefined') { // eslint-disable-line
          item.class = false
        }
        if (this.tabId === item[this.props.id]) {
          item.class = true
          if (item.type) {
            setTimeout(() => {
              this.$router.push({
                path: item.to,
                query: item.params
              })
            }, 200)
          }
        }
      })
      return items
    },
    deepCopy (data) {
      var t = this.type(data)
      var o
      var i
      var ni
      if (t === 'array') {
        o = []
      } else if (t === 'object') {
        o = {}
      } else {
        return data
      }
      if (t === 'array') {
        for (i = 0, ni = data.length; i < ni; i++) {
          o.push(this.deepCopy(data[i]))
        }
        return o
      } else if (t === 'object') {
        for (i in data) {
          o[i] = this.deepCopy(data[i])
        }
        return o
      }
    },
    type (obj) {
      var toString = Object.prototype.toString
      var map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'undefined',
        '[object Null]': 'null',
        '[object Object]': 'object'
      }
      return map[toString.call(obj)]
    }
  },
  beforeDestroy () {
    erd.uninstall(this.$refs['zy-tab'])
  }
}
</script>
<style lang="scss">
@import "./zy-tab.scss";
</style>
