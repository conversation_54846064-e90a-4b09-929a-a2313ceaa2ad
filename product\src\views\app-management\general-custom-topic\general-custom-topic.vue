<template>
  <div class="general-custom-topic">
    <search-button-box @search-click="search"
                       :resetButton="false">
      <template slot="button">
        <el-button type="primary"
                   icon="el-icon-plus"
                   v-permissions="'auth:specialsubjectinfo:add'"
                   @click="newData">新增</el-button>
        <el-button type="primary"
                   @click="deleteClick"
                   v-permissions="'auth:specialsubjectinfo:dels'"
                   icon="el-icon-delete">删除</el-button>
      </template>
      <template slot="search">
        <el-input placeholder="请输入内容"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
    </search-button-box>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  stripe
                  border
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="序号"
                           width="80"
                           prop="sort">
          </el-table-column>
          <el-table-column label="标题"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button @click="details(scope.row)"
                         type="text">{{scope.row.title}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="主题图"
                           width="120">
            <template slot-scope="scope">
              <div class="table-img">
                <img :src="scope.row.themeImg"
                     alt="">
              </div>
            </template>
          </el-table-column>
          <el-table-column label="封面图"
                           width="120">
            <template slot-scope="scope">
              <div class="table-img">
                <img :src="scope.row.coverImg"
                     alt="">
              </div>
            </template>
          </el-table-column>
          <el-table-column label="是否置顶"
                           width="90">
            <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.isTop=='1'"></i>
                <i class="el-icon-close"
                   v-else></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="是否发布"
                           width="90">
            <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.isPublish=='1'"></i>
                <i class="el-icon-close"
                   v-else></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="发布人"
                           width="160"
                           prop="createBy">
          </el-table-column>
          <el-table-column label="发布时间"
                           width="190"
                           prop="publishTime">
          </el-table-column>
          <el-table-column label="栏目管理"
                           v-if="$hasPermission(['auth:zySpecialsubjectColumn:list'])"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="column(scope.row)"
                         type="text">栏目管理</el-button>
            </template>
          </el-table-column>
          <el-table-column label="专题关联"
                           v-if="$hasPermission(['auth:zySpecialsubjectRelateinfo:list'])"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="associated(scope.row)"
                         type="text">专题关联</el-button>
            </template>
          </el-table-column>
          <el-table-column label="专题资讯"
                           v-if="$hasPermission(['auth:specialsubjectnews:list'])"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="information(scope.row)"
                         type="text">专题资讯</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           v-if="$hasPermission(['auth:specialsubjectinfo:edit'])"
                           fixed="right"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="modify(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="show"
               :title="id?'编辑':'新增'">
      <custom-topic-add :id="id"
                        :module="module"
                        :columnModule="columnModule"
                        @callback="addCallback"></custom-topic-add>
    </zy-pop-up>

    <zy-pop-up v-model="detailsShow"
               title="详情">
      <custom-topic-details :id="id"
                            :name="name"></custom-topic-details>
    </zy-pop-up>
    <zy-pop-up v-model="columnShow"
               title="栏目管理">
      <project-column :id="id"></project-column>
    </zy-pop-up>
    <zy-pop-up v-model="informationShow"
               title="专题资讯">
      <project-information :id="id"></project-information>
    </zy-pop-up>
    <zy-pop-up v-model="associatedShow"
               title="专题关联">
      <project-associated :id="id"></project-associated>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@mixins/tableData'
import customTopicAdd from './custom-topic-add/custom-topic-add'
import projectColumn from './project-column/project-column'
import projectInformation from './project-information/project-information'
import projectAssociated from './project-associated/project-associated'
import customTopicDetails from './custom-topic-details/custom-topic-details'
export default {
  name: 'customTopicList',
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      id: '',
      name: '',
      show: false,
      columnShow: false,
      informationShow: false,
      associatedShow: false,
      detailsShow: false
    }
  },
  mixins: [tableData],
  props: ['module', 'columnModule'],
  components: {
    customTopicAdd,
    projectColumn,
    projectInformation,
    projectAssociated,
    customTopicDetails
  },
  mounted () {
    this.customTopicList()
  },
  methods: {
    search () {
      this.customTopicList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    details (row) {
      this.id = row.id
      this.name = row.createBy
      this.detailsShow = true
    },
    column (row) {
      this.id = row.id
      this.columnShow = true
    },
    information (row) {
      this.id = row.id
      this.informationShow = true
    },
    associated (row) {
      this.id = row.id
      this.associatedShow = true
    },
    modify (row) {
      this.id = row.id
      this.show = true
    },
    addCallback () {
      this.customTopicList()
      this.show = false
    },
    async customTopicList () {
      const res = await this.$api.appManagement.customTopicList({
        module: this.module,
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        structureId: this.treeId
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.customTopicList()
    },
    whatPage (val) {
      this.customTopicList()
    },
    deleteClick (row) {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的专题, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.customTopicListDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async customTopicListDel (id) {
      const res = await this.$api.appManagement.customTopicListDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.customTopicList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.general-custom-topic {
  width: 100%;
  height: 100%;

  .tableData {
    height: calc(100% - 116px);
    width: 100%;

    .table-img {
      width: 48px;
      height: 36px;
      background-color: #ccc;
      overflow: hidden;

      img {
        height: 100%;
      }
    }

    .table-icon {
      .el-icon-check {
        font-size: $textSize16;
        color: #35be38;
      }

      .el-icon-close {
        font-size: $textSize16;
        color: #e24c4b;
      }
    }
  }
}
</style>
