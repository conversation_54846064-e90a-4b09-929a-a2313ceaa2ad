var token = localStorage.getItem('token');
var areaId = localStorage.getItem("areaId");
var header = '{"Authorization": ' + token + ',"u-login-areaId": ' + areaId + '}'
var grantHeader = '{"Authorization": ' + token + ',"u-login-areaId": ' + areaId + ',"Content-Type": "application/json;charset=UTF-8"}'
var themeColor = window.localStorage.getItem("theme_color") != null ? window.localStorage.getItem("theme_color") : "#199BC5";
var tempData = [];
var mId = ""; //会议id
var rId; //会场id
var personArr; //存放了座位id的人员数组
var seatesArr; //存放已经保存的座位消息
var selDeptId = ""; //当前选中的组织部门id
var selDeptName = ""; //当前选中的组织部门Name
var selTreeNode = ""; //选中的树节点
var selSets; //选中的座位数组
var selSeatId; //存放选中的座位的座位号数组
var areaArr; // 区域信息
var impPerArr; //存放已导入的人员数组
var seatInfo; //人员对应座位信息
var maxX = 0; //座位容器内最大的x坐标
var maxY = 0; //座位容器内最大的y坐标
var maxSeatNo = 1; //座位容器内最大的座位号
var boxWidth = 0;

//初始化
$(function () {

  mId = window.localStorage.getItem("meetId");
  $('#setsDiv').css("height", $(this).height() - 84);
  $('.box').css("height", $('.box').height() - 65).css("overflow", "auto");
  boxWidth = $(this).width();

  changeThemeColor(themeColor);

  //layui导入人员部分代码
  layui.use('upload', function () {
    var $ = layui.jquery,
      upload = layui.upload;
    //指定允许上传的文件类型
    upload.render({
      elem: '#importPer_a',
      method: "post",
      headers: JSON.parse(header),
      contentType: 'application/json',
      url: server.local_path + "meetSeatPlan/import",
      accept: 'file', //普通文件
      exts: 'xls|xlsx',
      before: function (obj) {
        var lId = $(".bjNameRadioCheck").attr("id"); //选中的布局id
        this.data = {
          "meetId": mId,
          "layoutId": lId
        }; //关键代码
      },
      done: function (res) {
        if (res.errcode == 200) {
          $("#infoModal").modal("show");
          var sucInfo = res.data.nameList; //导入成功的用户
          var noMeetUserInfo = res.data.noMeetUserList; //导入时不在本次会议的用户集合
          var namesakeInfo = res.data.namesake; //重名且无法区分的用户集合
          var noSeatNoInfo = res.data.noSeatNumberUser; //导入时座位（顺序）号不存在的用户
          var noNameInfo = res.data.noNameList; //系统内没有的用户

          $("#noticeInfo").text("导入成功" + sucInfo.length + "条");

          if(noMeetUserInfo.length > 0) {
            var infoStr = "";
            for(var i=0; i<noMeetUserInfo.length; i++) {
              infoStr = infoStr + noMeetUserInfo[i] + ",";
              infoStr = infoStr.substring(0, infoStr.length - 1);
            }
            $("#noMeetList").text(infoStr);
          } else {
            $("#noMeetList").text("无");
          }

          if(namesakeInfo.length > 0) {
            var infoStr = "";
            for(var i=0; i<namesakeInfo.length; i++) {
              infoStr = infoStr + namesakeInfo[i] + ",";
              infoStr = infoStr.substring(0, infoStr.length - 1);
            }
            $("#nameRepeat").text(infoStr);
          } else {
            $("#nameRepeat").text("无");
          }

          if(noSeatNoInfo.length > 0) {
            var infoStr = "";
            for(var i=0; i<noSeatNoInfo.length; i++) {
              infoStr = infoStr + noSeatNoInfo[i] + ",";
              infoStr = infoStr.substring(0, infoStr.length - 1);
            }
            $("#noSeatNo").text(infoStr);
          } else {
            $("#noSeatNo").text("无");
          }

          if(noNameInfo.length > 0) {
            var infoStr = "";
            for(var i=0; i<noNameInfo.length; i++) {
              infoStr = infoStr + noNameInfo[i] + ",";
              infoStr = infoStr.substring(0, infoStr.length - 1);
            }
            $("#noNameList").text(infoStr);
          } else {
            $("#noNameList").text("无");
          }
        }
      }
    });
  });

  //已报名人员、出席列席人员单选按钮切换
  $('input:radio[name="perRadio"]').click(function () {
    //根据会场ID加载区域数据
    loadAreaInfo();
  });

  //按部门排序/姓氏笔画排序下拉框值改变事件
  $("#sortType").combobox({
    onChange: function (n, o) {
      //根据会场ID加载区域数据
      loadAreaInfo();
    }
  });
  //模版下拉框值改变事件
  $("#tempSelect").combobox({
    onChange: function (n, o) {
      if (tempData.length > 0) {
        for (let i = 0; i < tempData.length; i++) {
          if (n === tempData[i].id) {
            $("#msgContent").val(tempData[i].content)
          }
        }
      }
    }
  });
  //根据会议ID查会议详情
  loadMeetDetailById();
});

//根据会议ID查会议详情
function loadMeetDetailById () {
  //调用接口
  axios({
    method: "post",
    url: server.local_path + 'meetMeeting/info/' + mId,
    headers: JSON.parse(header)
  })
    .then(function (response) {
      var resultInfo = response.data.data.meetMeetingDetailVo;
      //会议名称
      $("#meetName").text(resultInfo.name);
      if(resultInfo.meetPlaceId == null) {
        layer.msg("此会议没有绑定会场信息");
      } else {
        //会场id
        rId = resultInfo.meetPlaceId;
        //根据会场ID加载区域数据
        loadAreaInfo();
        //加载布局信息
        loadlayoutInfo(rId);
      }
    }).catch(function (error) {

    });
}

//根据会场ID加载区域数据
function loadAreaInfo () {
  axios({
    method: "post",
    url: server.local_path + "meetSeatArea/listData",
    headers: JSON.parse(header)
  })
  .then(function (response) {
    var resultInfo = response.data;
    if (resultInfo.errcode == 200) {
      if(resultInfo.data.length > 0) {
        $(".areaTip").empty();
        var areaInfo = resultInfo.data;
        areaArr = new Array();
        areaArr = areaInfo;
        //座位类型信息
        if(areaInfo != null && areaInfo.length > 0) {
          for(var i = 0; i < areaInfo.length; i++) {
            var typeName = areaInfo[i].name; //区域名称
            var typeColor = areaInfo[i].colour; //区域颜色
            $(".areaTip").append("<label>" + typeName + "</label><label style='width: 20px;height: 20px;margin: 0 15px 0 -5px;color: " + typeColor + ";'>█</label>");
          }
         }
      }
    }
  }).catch(function (error) {
    layer.msg(error.response.data.message);
  });
}

//加载布局信息
function loadlayoutInfo(rId) {
	axios.post(server.local_path + "meetSeatLayout/listData", Qs.stringify({
		placeId: rId
	}), {
		headers: JSON.parse(header)
	})
	.then(function(response) {
		if(response.data.errcode == 200){
			var detailInfo = response.data.data;
			if(detailInfo.length > 0) {
        $(".bjBox").empty();
        for(var i=0; i,detailInfo.length; i++) {
          bjInxex = detailInfo.length;
          if(i == 0) {
            var bjStr = '<div class="bjRadio"><div id="'+ detailInfo[i].id +'" placeid="'+ detailInfo[i].placeId +'" class="bjNameRadio bjNameRadioCheck" onclick="radioClick($(this));"><div class="bjNameRadioChild"></div></div><input class="bjRadioInput" value="'+ detailInfo[i].name +'"/></div>';
            //加载排座信息
            loadSeatsInfo(rId, detailInfo[0].id);
          } else {
            var bjStr = '<div class="bjRadio"><div id="'+ detailInfo[i].id +'" placeid="'+ detailInfo[i].placeId +'" class="bjNameRadio bjNameRadioUnCheck" onclick="radioClick($(this));"><div class="bjNameRadioChild" style="background: #FFFFFF;"></div></div><input class="bjRadioInput" value="'+ detailInfo[i].name +'"/></div>';
          }
          $(".bjBox").append(bjStr);
        }
			}
		}
	}).catch(function(error) {

	});
}

//加载排座信息
function loadSeatsInfo(rId, lId) {
  //删除容器内的座位数据
  $(".box").find(".drag").remove();
  axios.post(server.local_path + "meetSeat/listData", Qs.stringify({
  	placeId: rId,
    layoutId: lId
  }), {
  	headers: JSON.parse(header)
  })
  .then(function(response) {
  	if(response.data.errcode == 200){
  		var detailInfo = response.data.data;
  		if(detailInfo != null && detailInfo.length > 0) {
  			for(var i = 0; i < detailInfo.length; i++) {
  				var sId = detailInfo[i].id; //座位id
  				var sNo = detailInfo[i].seatNumber; //座位号
  				var sTp = detailInfo[i].seatType; //座位类型
  				var sSt = ""; //座位状态
  				var sRm = detailInfo[i].remarks; //座位备注
  				var sH = detailInfo[i].seatH; //座位高度
  				var sW = detailInfo[i].seatW; //座位宽度
  				var sX = detailInfo[i].seatX; //座位x坐标
  				var sY = Number(detailInfo[i].seatY) + 20; //座位y坐标
  				var sL = detailInfo[i].label; //座位/标记  seat:座位-sign:标记
  				var sBC = detailInfo[i].bgColor; //座位背景色
  				var sP = detailInfo[i].position; //座位几排几座

  				if(Number(sX) > Number(maxX)) {
  					maxX = sX; //给容器内的最大x坐标赋值
  				}
  				if(Number(sY) > Number(maxY)) {
  					maxY = sY; //给容器内的最大y坐标赋值
  				}

          var sRow = "",
            sNos = ""; //座位几排几座
          if(sP != "") {
            var rowAndNos = sP.split(",");
            sRow = rowAndNos[0];
            sNos = rowAndNos[1];
          }

          var addStr = '<div onclick="seatesClick($(this));" class="drag" style="z-index: 1;width:' + sW + 'px;height:' + sH + 'px;top:' + sY + ';left:' + sX + ';">' +
            '<label style="color:white;">' + sNo + '</label>' + //第一个label放座位号
            '<label style="display:none; color:black;">' + sTp + '</label>' + //第二个label放座位类型
            '<label style="display:none; color:black;">' + sSt + '</label>' + //第三个label放座位状态
            '<label style="display:none; color:black;">' + sId + '</label>' + //第四个label放座位Id
            '<label style="display:none; color:black;">' + sRow + '</label>' + //第五个label放座位第几排
            '<label style="display:none; color:white;">' + sNos + '</label>' + //第六个label放座位第几座
            '<label style="display:none; color:black;">' + sBC + '</label>' + //第七个label放座位背景色
            '<label style="display:none; color:black;">' + sRm + '</label>' + //第八个label放座位备注
            '</div>';
  				$(".box").append(addStr);
  			}
        setColorByAreaType(1);
        loadPerList(lId);
  		}
  	}
  }).catch(function(error) {

  });
}

//根据会议，会场，布局加载排座人员信息
function loadPerList(layoutId) {
  axios.post(server.local_path + "meetSeatPlan/listData", Qs.stringify({
    meetId: meetId,
  	placeId: rId,
    layoutId: layoutId
  }), {
  	headers: JSON.parse(header)
  })
  .then(function(response) {
  	if(response.data.errcode == 200){
  		var detailInfo = response.data.data;
  		if(detailInfo != null && detailInfo.length > 0) {

  		}
  	}
  }).catch(function(error) {

  });
}

//布局名称单选按钮点击
function radioClick(item) {
  $(".bjNameRadio").removeClass('bjNameRadioCheck').addClass('bjNameRadioUnCheck');
  $(".bjNameRadioChild").css("background", "#FFFFFF");
  $(item).removeClass('bjNameRadioUnCheck').addClass('bjNameRadioCheck');
  $(item).children().css("background", themeColor)
  var lId = $(".bjNameRadioCheck").attr("id"); //选中的布局id
  //加载排座信息
  loadSeatsInfo(rId, lId);
}

//保存人员座位信息 把座位号，人员id封装到数组
$("#saveSeats").on("click", function () {
  var setsArr = new Array(); //座位信息数组
  //遍历所有座位
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      var pId = $(this).children().get(5).textContent; //人员id 用手机号代替
      var pName = $(this).children().get(6).textContent; //人员姓名
      var sId = $(this).children().get(4).textContent; //座位id
      var sNo = $(this).children().get(0).textContent; //座位号
      //过滤掉人员为空的座位
      //			if(pId != "") {
      var setsObj = new Object();
      setsObj.meetId = mId; //会议id
      setsObj.placeId = rId; //会场id
      setsObj.seatId = sId; //座位id
      setsObj.seatNumber = sNo; //座位号/顺序号
      setsObj.name = pName != null ? pName : ""; //人员姓名
      setsObj.phone = pId != null ? pId : ""; //人员电话
      setsObj.userId = pId != null ? pId : ""; //人员id 用手机号代替
      setsArr.push(setsObj);
      //			}
    }
  });

  axios.post(server.local_path + "meetroomplan/insertArrange", Qs.stringify({
    seatedArrange: JSON.stringify(setsArr)
  }), {
    headers: JSON.parse(header)
  })
    .then(function (response) {
      var resultInfo = response.data;
      if (resultInfo.errcode == 200) {
        setTimeout(function () {
          layer.msg("操作成功");
          //根据条件加载人员与座位信息
          loadPersonAndSeatsInfo();
        }, 1000);
      }
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
});

//删除座位
$("#deleteSeats").on("click", function () {
  var delSelStr = "";
  //遍历所有座位 获取选中的座位信息
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      if ($(this).is('.selected')) {
        var sId = $(this).children().get(5).textContent; //座位上的人员id
        if (sId != "") {
          delSelStr += sId + ",";
        }
      }
    }
  });

  delSelStr = delSelStr.substring(0, delSelStr.length - 1);

  if (delSelStr.length > 0) {
    layer.confirm('确定要删除吗？', {}, function (index) {
      axios.post(server.local_path + "meetroomplan/delSeatPlan", Qs.stringify({
        meetId: mId,
        placeId: rId,
        peopleId: delSelStr
      }), {
        headers: JSON.parse(header)
      })
        .then(function (response) {
          var resultInfo = response.data;
          if (resultInfo.errcode == 200) {
            setTimeout(function () {
              layer.msg("操作成功");
              //根据条件加载人员与座位信息
              loadPersonAndSeatsInfo();
            }, 1000);
          }
        }).catch(function (error) {
          layer.msg(error.response.data.message);
        });
      //关闭
      layer.close(index);
    });
  }
  else {
    layer.msg("请选择座位有人员信息的座位进行删除");
  }
});

//存放座位对调选中的座位
var swapSeatsArr;
//座位对调
$("#swapSeats").on("click", function () {
  swapSeatsArr = new Array();
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      if ($(this).is('.selected')) {
        swapSeatsArr.push($(this));
        $(this).removeClass("selected");
      }
    }
  });
  if (swapSeatsArr.length > 1 && swapSeatsArr.length < 3) {
    var firstPid = $(swapSeatsArr[0]).children().get(5).textContent; //第一个座位的人员id
    var secondPid = $(swapSeatsArr[1]).children().get(5).textContent; //第二个座位的人员id
    var firstPname = $(swapSeatsArr[0]).children().get(6).textContent; //第一个座位的人员姓名
    var secondPname = $(swapSeatsArr[1]).children().get(6).textContent; //第二个座位的人员姓名

    $(swapSeatsArr[0]).children().get(5).textContent = secondPid; //将第二个座位的人员id赋给第一个座位
    $(swapSeatsArr[0]).children().get(6).textContent = secondPname; //将第二个座位的人员姓名赋给第一个座位
    $(swapSeatsArr[1]).children().get(5).textContent = firstPid; //将第一个座位的人员id赋给第二个座位
    $(swapSeatsArr[1]).children().get(6).textContent = firstPname; //将第一个座位的人员姓名赋给第二个座位

    updateTreeNodeInfo();//将座位信息同步更新到树节点上
  }
  else {
    layer.msg("请选择两个座位进行对调");
  }
});

/**
 * @desc   对象数组排序
 * @param   {array} 数组
 * @param   {key} 对象中的key
 */
function tool_sortByKey (array, key) {
  return array.sort(function (a, b) {
    var x = Number(a[key]);
    var y = Number(b[key]);
    return x < y ? -1 : x > y ? 1 : 0;
  });
}

//自动补位
$("#autoPatch").on("click", function () {
  var seatsArr_Patch = new Array(); //存放自动补位选中的座位
  var allSeatsArr_Patch = new Array(); //所有座位信息
  var havePerSeatsArr_Patch = new Array(); //已经排有人员信息的座位
  $('.box div').each(function () {
    if ($(this).is('.drag')) {

      //将座位放入座位数组进行排序用
      var obj = new Object();
      obj.item = $(this);
      obj.value = $(this).children().get(0).textContent; //座位号
      allSeatsArr_Patch.push(obj);//所有座位信息

      var pId = $(this).children().get(5).textContent; //人员id
      var pName = $(this).children().get(6).textContent; //人员姓名
      var sNo = $(this).children().get(0).textContent; //座位号
      //过滤掉人员为空的座位
      if (pId != "") {
        var pobj = new Object();
        pobj.sNo = sNo;
        pobj.pId = pId;
        pobj.pName = pName;
        havePerSeatsArr_Patch.push(pobj); //有人员信息的座位
      }

      if ($(this).is('.selected')) {
        seatsArr_Patch.push($(this)); //选中的座位
        $(this).removeClass("selected");
      }
    }
  });

  //对“所有座位信息”对象数组进行排序
  tool_sortByKey(allSeatsArr_Patch, "value");
  //对“已经排有人员信息的座位”对象数组进行排序
  tool_sortByKey(havePerSeatsArr_Patch, "sNo");

  if (seatsArr_Patch.length > 0 && seatsArr_Patch.length < 2) {

    var selSeatNo = $(seatsArr_Patch[0]).children().get(0).textContent;//获取选中座位的座位号
    var selSeatUid = $(seatsArr_Patch[0]).children().get(5).textContent;//获取选中座位的人员Id

    //将座位号小于选中座位号的已经排有人员的座位过滤
    havePerSeatsArr_Patch = $.grep(havePerSeatsArr_Patch, function (hps) {
      if (Number(hps.sNo) >= Number(selSeatNo)) {
        return hps;
      }
    });

    //选中没有人员信息的座位才执行补位操作
    if (selSeatUid == "") {
      var passPatchNum = Number(maxSeatNo) - Number(selSeatNo) + 1; //可供自动补位的座位数
      //如果当前已经排有人员的座位数小于等于可供自动补位的座位数 可以自动补位
      if (havePerSeatsArr_Patch.length <= passPatchNum) {

        for (var i = 0; i < allSeatsArr_Patch.length; i++) {
          var sNo = $(allSeatsArr_Patch[i].item).children().get(0).textContent;

          if (Number(sNo) >= Number(selSeatNo)) {
            $(allSeatsArr_Patch[i].item).children().get(5).textContent = "";
            $(allSeatsArr_Patch[i].item).children().get(6).textContent = "";
          }

          if (Number(selSeatNo) == Number(sNo)) {
            if (havePerSeatsArr_Patch.length > 0) {
              var ps = havePerSeatsArr_Patch.shift();
              $(allSeatsArr_Patch[i].item).children().get(5).textContent = ps.pId;
              $(allSeatsArr_Patch[i].item).children().get(6).textContent = ps.pName;
              selSeatNo = Number(selSeatNo) + 1;
            }
          }
        }
        updateTreeNodeInfo();//将座位信息同步更新到树节点上
      }
      else {
        layer.msg("没有足够的座位可以补位");
      }
    }
    else {
      layer.msg("请选择空座位进行补位");
    }
  }
  else {
    layer.msg("请选择一个座位");
  }
});

//自动排座
$("#autoSet").on("click", function () {
  var autoSortArr = new Array(); //自动排座的座位信息
  var autoPersArr = new Array(); //自动排座的人员信息
  var autoSortType = 1;//$('input:radio[name="autoSortType"]:checked').val(); //自动排座类型

  //获取人员树所有选中的节点
  var treeObj = $.fn.zTree.getZTreeObj("perTree");
  var nodes = treeObj.getCheckedNodes(true);
  for (var i = 0; i < nodes.length; i++) {
    //将人员节点放入数组
    if (nodes[i].isPer == true) {
      var perObj = new Object();
      perObj.userId = nodes[i].uid; //树节点选中的人员id
      perObj.name = nodes[i].name; //树节点选中的人员姓名
      perObj.phone = nodes[i].umobile; //树节点选中的人员电话
      autoPersArr.push(perObj);
    }
  }

  //遍历所有座位，获取已设置人员的座位和已选中的座位信息
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      var pId = $(this).children().get(5).textContent; //人员id 用手机号代替
      var pName = $(this).children().get(6).textContent; //人员姓名
      var sId = $(this).children().get(4).textContent; //座位id
      var sNo = $(this).children().get(0).textContent; //座位号
      //过滤掉未选中的座位
      if ($(this).is('.selected')) {
        var stobj = new Object();
        stobj.meetId = mId; //会议id
        stobj.placeId = rId; //会场id
        stobj.seatId = sId; //座位id
        stobj.seatNumber = sNo; //座位号/顺序号
        stobj.name = pName != null ? pName : ""; //人员姓名
        stobj.phone = pId != null ? pId : ""; //人员电话
        stobj.userId = pId != null ? pId : ""; //人员id 用手机号代替
        autoSortArr.push(stobj);
      }
    }
  });
  //对“已选中的座位信息”对象数组进行排序
  tool_sortByKey(autoSortArr, "seatNumber");
  if (autoPersArr.length == 0) {
    layer.msg("请选择人员");
    return;
  }
  if (autoSortArr.length == 0) {
    layer.msg("请选择座位");
    return;
  }
  axios.post(server.local_path + "meetroomplan/autoArraySeat", Qs.stringify({ arrayType: autoSortType, seatJson: JSON.stringify(autoSortArr), peopleJson: JSON.stringify(autoPersArr) }),
    { headers: JSON.parse(header) })
    .then(function (response) {
      var resultInfo = response.data;
      if (resultInfo.errcode == 200) {
        setTimeout(function () {
          layer.msg("操作成功");
          //根据条件加载人员与座位信息
          loadPersonAndSeatsInfo();
        }, 1000);
      }
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
});

/**
 * 插入座位
 * insetType
 * 1:自动补齐 优先插入没有人员的座位，有人的座位依次类推
 * 2:座位类推 无视没有人员的座位，所有座位依次类推
 */
function insertSeatsByType (insertType) {
  var insetMaxSno = 1; //插入座位的已排人员座位的最大座位号
  var seatsArr_Inset = new Array(); //存放插入座位选中的座位
  var allSeatsArr_Inset = new Array(); //所有座位信息
  var havePerSeatsArr_Inset = new Array(); //已经排有人员信息的座位
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      //将座位放入座位数组进行排序用
      var obj = new Object();
      obj.item = $(this);
      obj.value = $(this).children().get(0).textContent; //座位号
      allSeatsArr_Inset.push(obj);//所有座位信息

      var pId = $(this).children().get(5).textContent; //人员id
      var pName = $(this).children().get(6).textContent; //人员姓名
      var sNo = $(this).children().get(0).textContent; //座位号
      //过滤掉人员为空的座位
      if (pId != "") {
        var pobj = new Object();
        pobj.sNo = sNo;
        pobj.pId = pId;
        pobj.pName = pName;
        havePerSeatsArr_Inset.push(pobj); //有人员信息的座位

        if (Number(sNo) > Number(insetMaxSno)) {
          insetMaxSno = sNo; //给插入座位的已排人员座位的最大座位号赋值
        }
      }

      if ($(this).is('.selected')) {
        seatsArr_Inset.push($(this)); //选中的座位
      }
    }
  });

  //对“所有座位信息”对象数组进行排序
  tool_sortByKey(allSeatsArr_Inset, "value");
  //对“已经排有人员信息的座位”对象数组进行排序
  tool_sortByKey(havePerSeatsArr_Inset, "sNo");

  if (seatsArr_Inset.length > 0 && seatsArr_Inset.length < 2) {

    var selSeatNo = $(seatsArr_Inset[0]).children().get(0).textContent; //获取选中座位的座位号
    var selSeatUid = $(seatsArr_Inset[0]).children().get(5).textContent;//获取选中座位的人员Id

    //将座位号小于选中座位号的已经排有人员的座位过滤
    havePerSeatsArr_Inset = $.grep(havePerSeatsArr_Inset, function (hps) {
      if (Number(hps.sNo) >= Number(selSeatNo)) {
        return hps;
      }
    });
    //对有人员的座位进行操作
    if (selSeatUid != "") {
      //自动补齐
      if (insertType == 1) {
        if (havePerSeatsArr_Inset.length > 0) {
          //循环起始值
          var startIndex = Number(selSeatNo);

          if (startIndex != allSeatsArr_Inset.length) {
            var num = 0; //可供插入的座位数
            for (var s = 0; s < allSeatsArr_Inset.length; s++) {
              var sno = $(allSeatsArr_Inset[s].item).children().get(0).textContent; //座位号
              if (Number(selSeatNo) < Number(sno)) {
                num++
              }
            }
            //如果要插入的座位数>可供插入的座位数
            if (havePerSeatsArr_Inset.length > num) {
              layer.msg("没有足够的座位");
              $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
            }
            else {
              $(seatsArr_Inset[0]).children().get(5).textContent = ""; //清空选中座位的人员id
              $(seatsArr_Inset[0]).children().get(6).textContent = ""; //清空选中座位的人员姓名

              for (var p = startIndex; p < allSeatsArr_Inset.length; p++) {
                var apname = $(allSeatsArr_Inset[p].item).children().get(6).textContent;
                $(allSeatsArr_Inset[p].item).children().get(5).textContent = ""; //清空座位的人员id
                $(allSeatsArr_Inset[p].item).children().get(6).textContent = ""; //清空座位的人员姓名
                //如果下一个座位的人员信息为空 赋值后跳出循环
                if (apname == "") {
                  var sno = $(allSeatsArr_Inset[p].item).children().get(0).textContent; //座位号
                  var ps = havePerSeatsArr_Inset.shift();
                  if ((Number(ps.sNo) + 1) == Number(sno)) {
                    $(allSeatsArr_Inset[p].item).children().get(5).textContent = ps.pId;
                    $(allSeatsArr_Inset[p].item).children().get(6).textContent = ps.pName;
                    break;
                  }
                }
                else {
                  var sno = $(allSeatsArr_Inset[p].item).children().get(0).textContent; //座位号
                  var ps = havePerSeatsArr_Inset.shift();
                  if ((Number(ps.sNo) + 1) == Number(sno)) {
                    $(allSeatsArr_Inset[p].item).children().get(5).textContent = ps.pId;
                    $(allSeatsArr_Inset[p].item).children().get(6).textContent = ps.pName;
                  }
                }
              }
              $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
              updateTreeNodeInfo();//将座位信息同步更新到树节点上
            }
          }
          else {
            layer.msg("没有足够的座位");
            $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
          }
        }
      }
      //座位类推
      else {
        if (Number(insetMaxSno) >= Number(maxSeatNo)) {
          layer.msg("没有足够的座位");
          $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
        }
        else {
          if (havePerSeatsArr_Inset.length > 0) {

            $(seatsArr_Inset[0]).children().get(5).textContent = ""; //清空选中座位的人员id
            $(seatsArr_Inset[0]).children().get(6).textContent = ""; //清空选中座位的人员姓名

            for (var i = 0; i < havePerSeatsArr_Inset.length; i++) {
              //循环起始值
              var startIndex = Number(selSeatNo);
              for (var p = startIndex; p < allSeatsArr_Inset.length; p++) {
                var pname = havePerSeatsArr_Inset[i].pName;
                var apname = $(allSeatsArr_Inset[p].item).children().get(6).textContent;
                if (pname == apname && apname != "") {
                  $(allSeatsArr_Inset[p].item).children().get(5).textContent = ""; //清空座位的人员id
                  $(allSeatsArr_Inset[p].item).children().get(6).textContent = ""; //清空座位的人员姓名
                }
                var sno = $(allSeatsArr_Inset[p].item).children().get(0).textContent; //座位号
                if ((Number(havePerSeatsArr_Inset[i].sNo) + 1) == Number(sno)) {
                  $(allSeatsArr_Inset[p].item).children().get(5).textContent = havePerSeatsArr_Inset[i].pId;
                  $(allSeatsArr_Inset[p].item).children().get(6).textContent = havePerSeatsArr_Inset[i].pName;
                  break;
                }
              }
            }
          }
        }
        $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
        updateTreeNodeInfo();//将座位信息同步更新到树节点上
      }
    }
    else {
      layer.msg("请选择有人员的座位进行操作");
      $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
    }
  }
  else {
    layer.msg("请选择一个座位");
  }
}

//将座位信息同步更新到树节点上
function updateTreeNodeInfo () {
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      var pId = $(this).children().get(5).textContent; //人员id
      var sId = $(this).children().get(4).textContent; //座位id
      var sNo = $(this).children().get(0).textContent; //座位号

      //过滤掉人员为空的座位
      if (pId != "") {
        var treeObj = $.fn.zTree.getZTreeObj("perTree");
        var treeNode = treeObj.getNodes();
        var treeNodes = treeObj.transformToArray(treeNode); //获取树所有节点
        for (var i = 0; i < treeNodes.length; i++) {
          var pId = treeNodes[i].umobile; //树节点人员id 用手机号代替
          var sId = $(this).children().get(5).textContent; //座位上的人员id
          //对比树节点人员id与座位上绑定的人员id
          if (pId == sId) {
            treeNodes[i].seatesId = sId; //设置树节点上座位id的值
            treeNodes[i].seatesNo = sNo; //设置树节点上座位号的值
            $("#" + treeNodes[i].tId + "_input").val(sNo); //设置树节点上文本框的值
          }
        }
      }
    }
  });
}

//查询点击事件
$("#query").click(function () {
  $("#winModal").modal("show")
});

//查询条件弹框确定
$("#confirmBtn").click(function () {
  //根据会场ID加载区域数据
  loadAreaInfo();
  $("#winModal, #msgModal").modal("hide");
});

//发送通知弹框关闭按钮
$("#closeModal").click(function () {
  $("#msgModal").modal("hide");
});

//下载模版弹框关闭按钮
$("#closeTempModal").click(function () {
  $("#tempModal").modal("hide");
});

//查询条件重置
$("#resetParam").click(function () {
  $("#personName").val("");
  $("#phone").val("");
  $("#deptId").val("");
  $("#deptName").val("");
  selDeptId = "";
  $("#pTypeSelect").combobox("setValue", $('#pTypeSelect').combobox('getData')[0].value);
  $("#circlesSelect").combobox("setValue", $('#circlesSelect').combobox('getData')[0].value);
  $("#groupSelect").combobox("setValue", $('#groupSelect').combobox('getData')[0].value);
});

//新增驻地标签 获得焦点 弹出驻地选择框
$("#addAccBtn").on('click', function (e) {
  var foucsInput = $(this);
  var selfHei = $("#rsdWin").innerHeight();
  var topHei = foucsInput.offset().top - selfHei; //当前点击的元素距离父容器panel顶部的距离
  //如果弹出框距父容器顶部的距离小于弹出框的高度,就把弹出框显示在标签下方，反之显示在上方
  if (topHei < selfHei) {
    $("#rsdWin").css("top", $(this).offset().top + 38);
  } else {
    $("#rsdWin").css("top", $(this).offset().top - selfHei - 5);
  }
  $("#rsdWin").css("left", "10px");
  $('#rsdWin').css("display", "block");
  //加载驻地信息
  axios({
    method: "post",
    url: server.local_path + "meetstation/listData",
    headers: JSON.parse(header),
  })
    .then(function (response) {
      var resultInfo = response.data.data;
      if (resultInfo != null && resultInfo != "") {
        $("#rsdList").empty();
        var regStr = '';
        for (var i = 0; i < resultInfo.length; i++) {
          var rId = resultInfo[i].id; //id
          var rName = resultInfo[i].name; //名称
          //已绑定驻地的数组包含驻地id，将此驻地选中
          if ($.inArray(rId, bingArr) == -1) {
            var regStr = '<div class="checkbox-custom">' +
              '<input type="checkbox" name="rsdCK" id="' + rId + '" value="' + rId + '">' +
              '<label style="position: relative; top: 2px; left: 15px;" for="' + rId + '">' + rName + '</label>' +
              '</div>';
          }
          else {
            var regStr = '<div class="checkbox-custom">' +
              '<input type="checkbox" name="rsdCK" id="' + rId + '" value="' + rId + '" checked="checked">' +
              '<label style="position: relative; top: 2px; left: 15px;" for="' + rId + '">' + rName + '</label>' +
              '</div>';
          }
          $("#rsdList").append(regStr);
        }
      }
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
  //阻止事件穿透
  e.stopPropagation();
});

//驻地选择框点击事件
$("#rsdWin").on('click', function (e) {
  //阻止事件穿透
  e.stopPropagation();
});

//失去焦点
$("body, #cancelRsd").on("click", function () {
  $('#rsdWin').css("display", "none");
});

//新增驻地标签 确定
$("#confirmRsd").on("click", function () {
  var ckdRsdId = "";
  $.each($('#rsdList input:checkbox:checked'), function () {
    ckdRsdId += $(this).val() + ",";
  });
  ckdRsdId = ckdRsdId.substring(0, ckdRsdId.length - 1);
  //保存驻地和会议的关联
  axios({
    method: "post",
    url: server.local_path + "meetMeeting/saveStation?meetId=" + mId + "&station=" + ckdRsdId,
    headers: JSON.parse(header),
  })
    .then(function (response) {
      var resultInfo = response.data;
      if (resultInfo.errcode == 200) {
        $('#rsdWin').css("display", "none");
        //加载已绑定的驻地信息
        loaRsdListInfo();
      }
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
});

var bingArr = Array(); //已绑定的驻地信息
//加载已绑定的驻地信息
function loaRsdListInfo () {
  axios({
    method: "post",
    url: server.local_path + "meetMeeting/listStation?meetId=" + mId,
    headers: JSON.parse(header),
  })
    .then(function (response) {
      $("#rsdList, #rsdBox").empty();
      bingArr = new Array();
      var resultInfo = response.data.data;
      if (resultInfo != null && resultInfo != "") {
        var regStr = '';
        for (var i = 0; i < resultInfo.length; i++) {
          var rsdId = resultInfo[i].id; //id
          var rsdName = resultInfo[i].name; //名称

          bingArr.push(rsdId); //将已绑定的驻地id放入已绑定驻地数组

          if (i == 0) {
            var rsdStr = '<div class="radio radio-info">' +
              '<input type="radio" name="rsdRadio" id="' + rsdId + 'b" value="' + rsdId + '" checked="checked">' +
              '<label for="' + rsdId + 'b">' + rsdName + '</label>' +
              '</div>';
            rId = rsdId; //赋值第一个驻地号
          }
          else {
            var rsdStr = '<div class="radio radio-info">' +
              '<input type="radio" name="rsdRadio" id="' + rsdId + 'c" value="' + rsdId + '">' +
              '<label for="' + rsdId + 'c">' + rsdName + '</label>' +
              '</div>';
          }
          $("#rsdBox").append(rsdStr);
        }
      }
      else {
        rId = ""; //驻地号置空
      }
      loadAreaInfo(); //加载房型信息
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
}

//驻地标签点击事件
$('#rsdBox').on("change", 'input:radio[name="rsdRadio"]', function () {
  rId = $('#rsdBox input:radio[name="rsdRadio"]:checked').val(); //驻地切换赋值驻地号
  //根据会场ID加载区域数据
  loadAreaInfo();
});

//授权查看人员
$("#grantPer").on("click", function () {
  $("#grantModal").modal("show");
  $(".itemBody").children(".bodyItem").nextAll().remove();
  axios({
    method: "post",
    url: server.local_path + "meetroomauthorization/getControl?meetId=" + mId,
    headers: JSON.parse(header),
  })
    .then(function (response) {
      var resultInfo = response.data.data;
      if (resultInfo.length > 0) {
        for (var i = 0; i < resultInfo.length; i++) {
          if (i == 0) {
            $("#firstGrant").val(resultInfo[i].userName);
            $("#firstGrant").attr("name", resultInfo[i].userId);
            if (resultInfo[i].wholeUserList.length > 0) {
              $("#firstRange").attr("data-grantList", JSON.stringify(resultInfo[i].wholeUserList));
              var aIds = ''
              for (var j = 0; j < resultInfo[i].wholeUserList.length; j++) {
                aIds += resultInfo[i].wholeUserList[j].id + ','
                if (resultInfo[i].wholeUserList.length > 1) {
                  $("#firstRange").val(resultInfo[i].wholeUserList.length + "人");
                } else {
                  $("#firstRange").val(resultInfo[i].wholeUserList[j].userName);
                  $("#firstRange").attr("name", resultInfo[i].wholeUserList[j].id);
                }
              }
              aIds = aIds.substring(0, aIds.length - 1)
              $("#firstRange").attr("name", aIds);
            } else {
              $("#firstRange").attr("data-grantList", JSON.stringify([]));
            }
          }
          else {
            if (resultInfo[i].wholeUserList.length > 0) {
              var perNums = resultInfo[i].wholeUserList.length;
              var aIds = ''
              for (var j = 0; j < resultInfo[i].wholeUserList.length; j++) {
                aIds += resultInfo[i].wholeUserList[j].id + ','
              }
              aIds = aIds.substring(0, aIds.length - 1)
            }
            $(".itemBody").append('<div class="bodyItem" style="display: inline-flex;">' +
              '<div style="width: 45%; margin-right: 15px;">' +
              '<p style=" text-align: left; margin-bottom: 5px;">被授权人</p>' +
              '<input type="text" class="gPer" placeholder="被授权人" value="' + resultInfo[i].userName + '" name="' + resultInfo[i].userId + '" style="width: 100%;" readonly="readonly">' +
              '</div>' +
              '<div style="width: 45%; margin-right: 15px;">' +
              '<p style=" text-align: left; margin-bottom: 5px;">可查看范围</p>' +
              '<input type="text" class="gRPer" value="' + (perNums > 1 ? perNums + '人' : resultInfo[i].wholeUserList[0].userName) + '" name="' + aIds + '" data-grantList=' + JSON.stringify(resultInfo[i].wholeUserList) + ' style="width: 100%;" readonly="readonly">' +
              '</div>' +
              '<label class="addItem" style="padding-top: 28px;font-size: 24px !important;padding-right: 10px;color:' + themeColor + ';">+</label>' +
              '<label class="delItem" style="padding-top: 27px;font-size: 35px !important;color:' + themeColor + ';">-</label>' +
              '</div>');
          }
        }
      } else {
        $("#firstRange").attr("data-grantList", JSON.stringify([]));
      }
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
});

//增加选项
$(".itemBody").on("click", ".addItem", function () {
  $(".itemBody").append('<div class="bodyItem" style="display: inline-flex;">' +
    '<div style="width: 45%; margin-right: 15px;">' +
    '<p style=" text-align: left; margin-bottom: 5px;">被授权人</p>' +
    '<input type="text" class="gPer" placeholder="被授权人" style="width: 100%;" readonly="readonly">' +
    '</div>' +
    '<div style="width: 45%; margin-right: 15px;">' +
    '<p style=" text-align: left; margin-bottom: 5px;">可查看范围</p>' +
    '<input type="text" class="gRPer" style="width: 100%;" readonly="readonly">' +
    '</div>' +
    '<label class="addItem" style="padding-top: 28px;font-size: 24px !important;padding-right: 10px;color:' + themeColor + ';">+</label>' +
    '<label class="delItem" style="padding-top: 27px;font-size: 35px !important;color:' + themeColor + ';">-</label>' +
    '</div>');
});

//删除选项
$(".itemBody").on("click", ".delItem", function () {
  var itemNums = $(".itemBody").children(".bodyItem").length;
  if (itemNums > 1) {
    $(this).parent().remove();
  }
});

//发送通知
$("#sendBtn").on("click", function () {
  loadTemp();
  $("#msgContent").val('')
  $("#msgModal").modal("show");
});

$("#confirmSendBtn").on("click", function () {
  axios.post(server.local_path + "meetroomplan/sendByRoomPlan", Qs.stringify({
    meetId: mId,
    content: $("#msgContent").val()
  }), {
    headers: JSON.parse(header)
  })
    .then(function (response) {
      var resultInfo = response.data;
      if (resultInfo.errcode == 200) {
        setTimeout(function () {
          layer.msg("操作成功");
          $("#msgModal").modal("hide");
        }, 1000);
      }
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
});

window.addEventListener("storage", function (e) {
  if (e.key == "theme_color") {
    themeColor = e.newValue;
    changeThemeColor(e.newValue);
  }
  if (e.key == "size") {
    if (e.newValue == 'false') {
      $('.box').css("width", boxWidth + 222);
    }
    else {
      $('.box').css("width", boxWidth);
    }
  }
});

var root = document.querySelector(':root');
//即时换色
// 设置需要换色的元素及其样式
function changeThemeColor (colo) {
  $("#meetName").css("color", colo);
  $(".btn-info").css("background", colo);
  $(".addItem, .delItem").css("color", colo);
  $(".modal-body span").css("color", colo);
  root.setAttribute('style', '--color: ' + colo);
}

$(".btn-info").hover(function () {
  $(this).css('opacity', ".6");
},
  function () {
    $(this).css('opacity', "1");
  });

// $(".cancel").hover(function () {
//   $(this).css("background", "transparent").css("border", "1px solid" + themeColor).css("color", themeColor);
// },
//   function () {
//     $(this).css("background", "transparent").css('border', "1px solid rgba(217,217,217,1)").css("color", "#000");
//   });

$(".dropdown-item").hover(function () {
  $(this).css("background", "transparent");
},
  function () {
    $(this).css("background", "transparent");
  });
