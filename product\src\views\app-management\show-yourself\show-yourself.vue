<template>
  <div class="members-said">
    <screening-box @search-click="search"
                   :resetButton="false">
      <el-input placeholder="请输入内容"
                v-model="keyword"
                clearable
                @keyup.enter.native="search">
      </el-input>
      <zy-select width="222"
                 node-key="id"
                 :props="props"
                 v-model="typeId"
                 :data="type"
                 placeholder="请选择审核情况"></zy-select>
    </screening-box>
    <div class="button-box">
      <!-- <el-button type="primary"
                 icon="el-icon-plus"
                 @click="newData">新增</el-button> -->
      <el-button type="primary"
                 icon="el-icon-delete"
                 v-permissions="'auth:showyourself:dels'"
                 @click="deleteClick">删除</el-button>
      <el-button type="primary"
                 icon="el-icon-circle-check"
                 v-permissions="'auth:showyourself:batchUpdate'"
                 @click="passClick(1)">审核通过</el-button>
      <el-button type="primary"
                 icon="el-icon-remove-outline"
                 v-permissions="'auth:showyourself:batchUpdate'"
                 @click="passClick(-1)">审核不通过</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  stripe
                  border
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <!-- <el-table-column label="序号"
                           width="90"
                           prop="sort">
          </el-table-column> -->
          <el-table-column label="内容"
                           min-width="260"
                           prop="content"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         class="btn-reply"
                         @click="openDetail(scope.row)">{{scope.row.content}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="建立人"
                           prop="publishBy">
          </el-table-column>
          <el-table-column label="建立时间"
                           width="190"
                           prop="publishDate">
          </el-table-column>
          <el-table-column label="审核状态"
                           width="190"
                           prop="auditingFlag">
            <template slot-scope="scope">
              <div>{{scope.row.auditingFlag==1?'审核通过':(scope.row.auditingFlag==0?'待审核':'审核不通过')}}</div>
            </template>
          </el-table-column>
          <el-table-column label="
                   评论回复"
                           width="120">
            <template slot-scope="scope">
              <el-button :disabled="scope.row.commentCount==0"
                         type="text"
                         class="btn-reply"
                         @click="reply(scope.row)">评论回复:{{scope.row.commentCount}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           v-if="$hasPermission(['auth:showyourself:edit'])">
            <template slot-scope="scope">
              <el-button @click="modify(scope.row)"
                         type="primary"
                         v-permissions="'auth:showyourself:edit'"
                         plain
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>

    <zy-pop-up v-model="show"
               title="新增">
      <show-new :id="id"
                @newCallback="newCallback"></show-new>
    </zy-pop-up>
    <zy-pop-up v-model="showDetail"
               title="详情">
      <show-detail :id="id"
                   @newCallback="newCallback"></show-detail>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '../../../mixins/tableData'
import showNew from './show-new/show-new'
import showDetail from './show-new/show-detail'
export default {
  name: 'showYourself',
  data () {
    return {
      id: '',
      type: [{ id: '0', value: '待审核' }, { id: '1', value: '审核通过' }, { id: '-1', value: '审核不通过' }],
      typeId: '',
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      props: {
        children: 'children',
        label: 'value'
      },
      data: [],
      show: false,
      showDetail: false
    }
  },
  inject: ['newTab'],
  mixins: [tableData],
  components: {
    showNew, showDetail
  },
  mounted () {
    this.showyourselfList()
  },
  methods: {
    search () {
      this.showyourselfList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    newCallback () {
      this.showyourselfList()
      this.show = false
    },
    modify (row) {
      this.id = row.id
      this.show = true
    },
    openDetail (row) {
      this.id = row.id
      this.showDetail = true
    },
    async showyourselfList () {
      const res = await this.$api.appManagement.showyourselfList({ keyword: this.keyword, auditingFlag: this.typeId, pageNo: this.page, pageSize: this.pageSize })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.showyourselfList()
    },
    whatPage (val) {
      this.showyourselfList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.showyourselfDels(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async showyourselfDels (id) {
      const res = await this.$api.appManagement.showyourselfDels({ ids: id })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.showyourselfList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    passClick (auditingFlag) {
      if (this.choose.length) {
        this.$confirm('此操作将审核' + (auditingFlag === 1 ? '' : '不') + '通过, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.showyourselfBatchUpdate(this.choose.join(','), auditingFlag)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async showyourselfBatchUpdate (id, auditingFlag) {
      const res = await this.$api.appManagement.showyourselfBatchUpdate({ ids: id, auditingFlag: auditingFlag })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.showyourselfList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    /**
     * 打开评论  {{$position()}}说24
     */
    reply (val) {
      this.newTab({ name: '评论回复', menuId: '1', to: '/memberGuestReply', params: { id: val.id, type: '24' } })
    }
  }
}
</script>
<style lang="scss">
@import "../members-said/members-said.scss";
</style>
