<template>
  <div class="screening-box" ref="screening">
    <slot></slot>
    <div :class="['screening-button',button?'screening-button-a':'']">
      <el-button type="primary" @click="search" v-if="searchButton">查询</el-button>
      <el-button @click="reset" v-if="resetButton">重置</el-button>
      <el-button type="text" v-if="more" @click="moreClick"><i :class="['el-icon-arrow-down',moreShow?'':'el-icon-arrow-down-a']"></i>{{moreShow?'更多':'收起'}}</el-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'screeningBox',
  data () {
    return {
      button: false,
      more: false,
      moreShow: true
    }
  },
  props: {
    searchButton: {
      type: Boolean,
      default: true
    },
    resetButton: {
      type: Boolean,
      default: true
    }
  },
  created () {
  },
  mounted () {
    this.$nextTick(() => {
      this.resolution()
    })
  },
  methods: {
    search () {
      this.$emit('search-click')
    },
    reset () {
      this.$emit('reset-click')
    },
    moreClick () {
      var screening = this.$refs.screening
      if (this.moreShow) {
        screening.style.height = 'auto'
      } else {
        screening.style.height = ''
      }
      this.$emit('more-click', screening.offsetHeight, this.moreShow)
      this.moreShow = !this.moreShow
    },
    resolution () {
      if (this.$refs.screening) {
        var screening = this.$refs.screening
        var Width = 246
        var length = 0
        for (let index = 0; index < screening.childNodes.length - 1; index++) {
          if (screening.childNodes[index].offsetWidth === undefined) {
          } else {
            Width += screening.childNodes[index].offsetWidth + 24
          }
          if (screening.offsetWidth < Width && length === 0) {
            length = index
          }
        }
        if (screening.offsetWidth < Width) {
          this.more = true
          var arr = []
          for (let index = length; index < screening.childNodes.length - 1; index++) {
            arr.push(screening.childNodes[index])
          }
          for (let index = 0; index < arr.length; index++) {
            screening.appendChild(arr[index])
          }
        }
      }
    }
  }
}
</script>
<style lang="scss">
@import "./screening-box.scss";
</style>
