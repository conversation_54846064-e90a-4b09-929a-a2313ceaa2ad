//
// Responsive: Tablet to desktop
// --------------------------------------------------


@media (min-width: 768px) and (max-width: 979px) {

  #iconCarousel {
    @size: 200px;
    font-size: @size;
    line-height: @size + 5;
    .carousel-control {
      top: @size + 30px;
      .square(30px);
      font-size: 40px;
      line-height: 40px;
      left: 228/2 - 40px;
      &.right {
        right: 228/2 - 40px;
      }
    }
  }

  .jumbotron-index {
    padding: 50px 0;
    h1 { font-size: 65px; }
    p { font-size: 25px; }
    .shameless-self-promotion { font-size: 12px; }
  }

  .jumbotron-ad {
    p { font-size: 28px; }
  }

  .jumbotron-icon {
    h1 small {
      display: block;
      margin-top: 15px;
      margin-left: 0;
      line-height: 20px;
    }
  }

  .stripe-ad {
    .lead {
      margin: 0;
      font-size: 19px;
    }
    @ad-width: 302px;
    .span8 { width: 476px + 228px - @ad-width; }
    .span4 { width: @ad-width; }
  }

  #why, #whats-new, #new-styles {
    .span4 { width: 352px; }
  }

  .the-icons {
    .span3 { width: 228px; }
  }

  .sticky-footer(110px, 40px, 40px, 60px); // sets default values for sticky footer
  .footer {
    padding-left: 20px;
    padding-right: 20px;
  }
}
