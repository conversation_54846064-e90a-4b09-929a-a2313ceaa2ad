/*初始化样式*/

html {
	/*background-color: #fff/*F5F5F5*/;*/
	background: transparent;
}

.container,
.container-fluid {
	/* background-color: #F5F5F5; */
}

.custom-select {
	background-color: #fff;
}

.control-label {
	line-height: 38px;
	padding: 0;
}

.dataTables_empty {
	text-align: center;
}

label {
	margin-bottom: 0;
	line-height: 38px;
	cursor: pointer;
}

.checkbox label {
	line-height: normal;
}

.btn-info:hover{
	background: rgba(4, 115, 222, .8);
	border-color: rgba(4, 115, 222, .8);
	color: #FFF;
}

.cancel:hover{
	border-color: #2585E1;
	color: #2585E1;
}


/* 表格样式 */

.table th {
	/*background-color: transparent;*/
	/*color:#fff;*/
}

.table tr:nth-of-type(odd),
.table tbody tr:hover {
	background-color: #f2f7f8;
}


/*辅助类*/

.text-auto-hidden {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.mouse-pointer {
	cursor: pointer;
}

.mouse-pointer:hover {
	color: #009efb;
}


/*滚动条美化*/

::-webkit-scrollbar-button {
	display: none;
}

::-webkit-scrollbar-button:hover {
	background-color: #00ffff;
}

::-webkit-scrollbar-thumb {
	background-color: #CCC;
	border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
	background-color: #CCC;
}

::-webkit-scrollbar-track {
	background-color: #ffffff;
	border-radius: 0;
}

::-webkit-scrollbar-track:hover {
	background-color: #ffffff;
	border-radius: 0;
}

::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}


/*表单验证样式*/

.border-danger::-webkit-input-placeholder {
	color: #dc3545;
	font-size: 12px;
}

.border-danger::-moz-input-placeholder {
	color: #dc3545;
	font-size: 12px;
}

.border-danger::-ms-input-placeholder {
	color: #dc3545;
	font-size: 12px;
}


/*图片预览样式*/

img[data-zoom] {
	cursor: -webkit-zoom-in;
}

.is-loading {
	background-color: black;
	background-position: center center;
	background-repeat: no-repeat;
	background-image: url("../assets/images/loading.gif");
}

.img-shade {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, .5);
	z-index: 9999999;
}

.is-loading img {
	opacity: 0;
}

.preview-zoom {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border: 5px solid #fff;
	border-radius: .2rem;
	box-shadow: 0 0 10px #bbb;
}

.preview-zoom>img {
	width: 100%;
	height: 100%;
	cursor: -webkit-zoom-out;
}

.loader {
	width: 100px;
	margin: 50px auto 70px;
	position: relative;
	display: none;
}

.loader .loading-1 {
	position: relative;
	width: 100px;
	height: 10px;
	border: 1px solid #b6b7b7;
	border-radius: 10px;
	animation: turn 4s linear 1.75s infinite;
}

.loader .loading-1:before {
	content: "";
	display: block;
	position: absolute;
	width: 0%;
	height: 100%;
	background: #b6b7b7;
	box-shadow: 10px 0px 15px 0px #b6b7b7;
	animation: load 2s linear infinite;
}

.loader .loading-2 {
	width: 100px;
	position: absolute;
	top: 10px;
	color: #b6b7b7;
	font-size: 12px;
	text-align: center;
	animation: bounce 2s linear infinite;
}

@keyframes load {
	0% {
		width: 0%;
	}
	87.5%,
	100% {
		width: 100%;
	}
}

@keyframes turn {
	0% {
		transform: rotateY(0deg);
	}
	6.25%,
	50% {
		transform: rotateY(180deg);
	}
	56.25%,
	100% {
		transform: rotateY(360deg);
	}
}

@keyframes bounce {
	0%,
	100% {
		top: 10px;
	}
	12.5% {
		top: 30px;
	}
}