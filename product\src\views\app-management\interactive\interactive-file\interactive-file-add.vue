<template>
  <div class="interactive-file-add">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <div class="file-user">所属者：{{owner}}</div>
      <div class="file-user">创建时间：{{createTime|datefmt}}</div>
      <el-form-item label="文件名称"
                    prop="name"
                    class="form-title">
        <el-input placeholder="请输入文件名称"
                  v-model="form.name"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="文件归类"
                    prop="fileClass"
                    class="form-input">
        <el-select v-model="form.fileClass"
                   filterable
                   placeholder="请选择文件归类">
          <el-option v-for="item in fileClass"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否公开"
                    prop="isPublic"
                    class="form-input">
        <el-radio-group v-model="form.isPublic">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'interactiveFileAdd',
  data () {
    return {
      owner: '',
      createTime: '',
      fileClass: [],
      form: {
        name: '',
        fileClass: '',
        isPublic: 0
      },
      rules: {
        name: [
          { required: true, message: '请输入文件名称', trigger: 'blur' }
        ],
        fileClass: [
          { required: true, message: '请选择文件归类', trigger: 'blur' }
        ],
        isPublic: [
          { required: true, message: '请选择是否公开', trigger: 'blur' }
        ]
      }
    }
  },
  props: ['id'],
  mounted () {
    if (this.id) {
      this.fileinfoInfo()
    }
    this.dictionaryPubkvs()
  },
  methods: {
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'file_info_class'
      })
      var { data } = res
      this.fileClass = data.file_info_class
    },
    /**
     *字典
    */
    async fileinfoInfo () {
      const res = await this.$api.appManagement.fileinfoInfo(this.id)
      var { data } = res
      this.form.name = data.name
      this.form.isPublic = data.isPublic
      this.form.fileClass = data.fileClass
      this.owner = data.owner
      this.createTime = data.createTime
    },
    /**
   * 提交提案
  */
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$api.general.generalAdd('/fileinfo/edit?', {
            id: this.id,
            name: this.form.name,
            fileClass: this.form.fileClass,
            isPublic: this.form.isPublic
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    /**
   * 取消按钮
  */
    cancel () {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.interactive-file-add {
  width: 680px;
  height: 100%;
  padding: 24px;

  .file-user {
    color: #199bc5;
    font-size: $textSize14;
    margin-bottom: 22px;
  }
}
</style>
