<!DOCTYPE html>
<HTML>
<HEAD>
	<TITLE> ZTREE DEMO - Other Mouse Event</TITLE>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="../../../css/demo.css" type="text/css">
	<link rel="stylesheet" href="../../../css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="../../../js/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.core.js"></script>
	<!--  <script type="text/javascript" src="../../../js/jquery.ztree.excheck.js"></script>
	  <script type="text/javascript" src="../../../js/jquery.ztree.exedit.js"></script>-->
	<SCRIPT type="text/javascript">
		<!--
		var setting = {
			data: {
				key: {
					title:"t"
				},
				simpleData: {
					enable: true
				}
			},
			callback: {
				beforeMouseDown: beforeMouseDown,
				beforeMouseUp: beforeMouseUp,
				beforeRightClick: beforeRightClick,
				onMouseDown: onMouseDown,
				onMouseUp: onMouseUp,
				onRightClick: onRightClick
			}
		};

		var zNodes =[
			{ id:1, pId:0, name:"不能 右键 0", t:"试试右键，没有 onRightClick 吧？", open:true, right:false},
			{ id:11, pId:1, name:"不能 右键 1", t:"试试右键，没有 onRightClick 吧？", right:false},
			{ id:12, pId:1, name:"不能 右键 2", t:"试试右键，没有 onRightClick 吧？", right:false},
			{ id:13, pId:1, name:"不能 右键 3", t:"试试右键，没有 onRightClick 吧？", right:false},
			{ id:2, pId:0, name:"不能 Down 0", t:"试试 MouseDown ，没有 onMouseDown 吧？", open:true, down:false},
			{ id:21, pId:2, name:"不能 Down 1", t:"试试 MouseDown ，没有 onMouseDown 吧？", down:false},
			{ id:22, pId:2, name:"不能 Down 2", t:"试试 MouseDown ，没有 onMouseDown 吧？", down:false},
			{ id:23, pId:2, name:"不能 Down 3", t:"试试 MouseDown ，没有 onMouseDown 吧？", down:false},
			{ id:3, pId:0, name:"不能 Up 0", t:"试试 MouseUp ，没有 onMouseUp 吧？", open:true, up:false},
			{ id:31, pId:3, name:"不能 Up 1", t:"试试 MouseUp ，没有 onMouseUp 吧？", up:false},
			{ id:32, pId:3, name:"不能 Up 2", t:"试试 MouseUp ，没有 onMouseUp 吧？", up:false},
			{ id:33, pId:3, name:"不能 Up 3", t:"试试 MouseUp ，没有 onMouseUp 吧？", up:false}
		];

		var log, className = {down:"dark", up:"dark", right:"dark"};
		function beforeMouseDown(treeId, treeNode) {
			className.down = (className.down === "dark" ? "":"dark");
			showLog("[ "+getTime()+" beforeMouseDown ]&nbsp;&nbsp;" + (treeNode?treeNode.name:"root"), "down" );
			return (!treeNode || treeNode.down != false);
		}
		function onMouseDown(event, treeId, treeNode) {
			showLog("[ "+getTime()+" onMouseDown ]&nbsp;&nbsp;" + (treeNode?treeNode.name:"root"), "down" );
		}
		function beforeMouseUp(treeId, treeNode) {
			className.up = (className.up === "dark" ? "":"dark");
			showLog("[ "+getTime()+" beforeMouseUp ]&nbsp;&nbsp;" + (treeNode?treeNode.name:"root"), "up" );
			return (!treeNode || treeNode.up != false);
		}
		function onMouseUp(event, treeId, treeNode) {
			showLog("[ "+getTime()+" onMouseUp ]&nbsp;&nbsp;" + (treeNode?treeNode.name:"root"), "up" );
		}
		function beforeRightClick(treeId, treeNode) {
			className.right = (className.right === "dark" ? "":"dark");
			showLog("[ "+getTime()+" beforeRightClick ]&nbsp;&nbsp;" + (treeNode?treeNode.name:"root"), "right" );
			return (!treeNode || treeNode.right != false);
		}
		function onRightClick(event, treeId, treeNode) {
			showLog("[ "+getTime()+" onRightClick ]&nbsp;&nbsp;" + (treeNode?treeNode.name:"root"), "right" );
		}
		function showLog(str, logType) {
			log = $("#log" + "_" + logType);
			log.append("<li class='"+className[logType]+"'>"+str+"</li>");
			if(log.children("li").length > 2) {
				log.get(0).removeChild(log.children("li")[0]);
			}
		}
		function getTime() {
			var now= new Date(),
			h=now.getHours(),
			m=now.getMinutes(),
			s=now.getSeconds(),
			ms=now.getMilliseconds();
			return (h+":"+m+":"+s+ " " +ms);
		}

		$(document).ready(function(){
			$.fn.zTree.init($("#treeDemo"), setting, zNodes);
		});
		//-->
	</SCRIPT>
</HEAD>

<BODY>
<h1>其他鼠标事件监听</h1>
<h6>[ 文件路径: core/otherMouse.html ]</h6>
<div class="content_wrap">
	<div class="zTreeDemoBackground left">
		<ul id="treeDemo" class="ztree"></ul>
	</div>
	<div class="right">
		<ul class="info">
			<li class="title"><h2>1、mousedown / mouseup / rightClick 事件回调函数控制</h2>
				<ul class="list">
				<li>zTree 提供 这几种鼠标事件响应，主要是为了便于用户针对一些特殊需求进行扩展开发，不会对 zTree 造成任何影响，这里简单演示如何监控此事件</li>
				<li><p><span class="highlight_red">请尝试鼠标在 zTree 上胡乱点击（左键、右键）吧，顺便看看 log</span><br/>
					mousedown event log:<br/>
					<ul id="log_down" class="log small"></ul>
					mouseup event log:<br/>
					<ul id="log_up" class="log small"></ul>
					rightClick event log:<br/>
					<ul id="log_right" class="log small"></ul></p>
				</li>
				</ul>
			</li>
			<li class="title"><h2>2、setting 配置信息说明</h2>
				<ul class="list">
				<li class="highlight_red">事件回调函数的使用，详细请参见 API 文档中 setting.callback 的相关内容</li>
				</ul>
			</li>
			<li class="title"><h2>3、treeNode 节点数据说明</h2>
				<ul class="list">
				<li>对 节点数据 没有特殊要求，用户可以根据自己的需求添加自定义属性</li>
				</ul>
			</li>
		</ul>
	</div>
</div>
</BODY>
</HTML>