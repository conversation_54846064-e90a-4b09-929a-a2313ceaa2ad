//生成图片
$("#makePic").on("click", function() {
	//判断排座容器有没有出现滚动条，根据情况调整生成图片的宽高
	var bx = $(".box");
	if(bx[0].scrollHeight > bx[0].clientHeight || bx[0].offsetHeight > bx[0].clientHeight) { //出现滚动条
		//获取节点高度，后面为克隆节点设置高度。
		var h = Number(maxY) + 50;
		var w = Number(maxX) + 50;
	} else { //未出现滚动条
		//获取节点高度，后面为克隆节点设置高度。
		var h = $(".box").height();
		var w = $(".box").width();
	}

	//克隆节点，默认为false，不复制方法属性，为true是全部复制。
	var cloneDom = $(".box").clone(true);
	$("body").css("overflow","hidden");
	//设置克隆节点的css属性，因为之前的层级为0，我们只需要比被克隆的节点层级低即可。
	cloneDom.css({
		"background-color": "white",
		"position": "absolute",
		"top": "5000px",
		"z-index": "-9",
		"height": h,
		"width": w
	});
	//将克隆节点动态追加到body后面。
	$("#cloneDiv").append(cloneDom);
	//插件生成base64img图片。
	html2canvas(cloneDom, {
		//Whether to allow cross-origin images to taint the canvas
		allowTaint: true,
		//Whether to test each image if it taints the canvas before drawing them
		taintTest: false,
		onrendered: function(canvas) {
			//添加属性
			canvas.setAttribute('id', 'thecanvas');
			//读取属性值
			// var value= canvas.getAttribute('id');
			document.getElementById('images').innerHTML = '';
			document.getElementById('images').appendChild(canvas);

			/*自动保存为png*/
			// 获取图片资源
			var oCanvas = document.getElementById("thecanvas");
			var img_data1 = Canvas2Image.saveAsPNG(oCanvas, true).getAttribute('src');
			var timeStamp = moment(new Date()).format("YYYYMMDDHHmmss");
			var picName = selRname + "-" + timeStamp + '.jpg'
			saveFile(img_data1, picName);
		}
	});
});

// 保存文件函数
var saveFile = function(data, filename) {
	var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
	save_link.href = data;
	save_link.download = filename;

	var event = document.createEvent('MouseEvents');
	event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
	save_link.dispatchEvent(event);
	//生成并下载图片后，将克隆的元素清空
	document.getElementById('cloneDiv').innerHTML = '';
};

//加载座位类型下拉框数据
function loadSeatedType() {
	//调用查询接口
	axios({
		method: "post",
		url: server.local_path + "meetPlace/info/"+selRid,
		headers: JSON.parse(header)
	})
	.then(function(response) {
		$(".areaTip").empty();
		var areaInfo = response.data.data.seatAreaList;
		areaArr = new Array();
		areaArr = areaInfo;
		//座位类型信息
		if(areaInfo != null && areaInfo.length > 0) {
			var options = [ /*{text:"请选择选项",value:""}*/ ];
			for(var i = 0; i < areaInfo.length; i++) {
				var typeId = areaInfo[i].id; //区域id
				var typeName = areaInfo[i].name; //区域名称
				var typeColor = areaInfo[i].colour; //区域颜色
				options.push({
					text: typeName,
					value: typeId
				});
				$(".areaTip").append("<label>" + typeName + "</label><label style='width: 20px;height: 20px;margin: 0 15px 0 3px;color: " + typeColor + ";'>█</label>");
			}
			$("#setsT").combobox("loadData", options);
			$('#setsT').combobox('select', options[0].value);

			//加载排座信息
			loadSetsInfo(selRid, selRimg);
		} else {
			//加载排座信息
			loadSetsInfo(selRid, selRimg);
		}
	}).catch(function(error) {
		layer.msg(error.response.data.message);
	});
}

//点击背景图片  去除选中座位的样式
//$("#bgImg").on("click", function() {
//	$('.box div').each(function() {
//		$(this).removeClass("selected ctrlSel");
//	});
//	clearContent();
//	selSets = undefined;
//	selSign = undefined;
//});

//给座位设置宽度
$("#setsW").mouseup(function() {
	if(selSets != undefined) {
		selSets.css("width", $("#setsW").val());
	} else {
		setSeatsWidth();
	}
});
$("#setsW").keyup(function() {
	if(selSets != undefined) {
		var num = $("#setsW").val();
		if(num < 50) {
			$("#setsW").val("50");
			selSets.css("width", "50");
		} else
			selSets.css("width", num);
	} else {
		setSeatsWidth();
	}
});

//设置所有选中的座位宽度
function setSeatsWidth() {
	if($("#setsW").val() == "") return;
	$('.box div').each(function() {
		if($(this).is('.selected')) {
			$(this).css("width", $("#setsW").val());
		}
	});
}

//给座位设置高度
$("#setsH").mouseup(function() {
	if(selSets != undefined) {
		selSets.css("height", $("#setsH").val());
	} else {
		setSeatsHeigth();
	}
});
$("#setsH").keyup(function() {
	if(selSets != undefined) {
		var num = $("#setsH").val();
		if(num < 50) {
			$("#setsH").val("50");
			selSets.css("height", "50");
		} else
			selSets.css("height", num);
	} else {
		setSeatsHeigth();
	}
});

//设置所有选中的座位高度
function setSeatsHeigth() {
	if($("#setsH").val() == "") return;
	$('.box div').each(function() {
		if($(this).is('.selected')) {
			$(this).css("height", $("#setsH").val());
		}
	});
}

//给座位设置第几排
$("#setsRow").mouseup(function() {
	if(selSets != undefined) {
		$(selSets).children().get(4).textContent = $("#setsRow").val();
	} else {
		setSeatsRows();
	}
});
$("#setsRow").keyup(function() {
	if(selSets != undefined) {
		var num = $("#setsRow").val();
		if(num == "")
			$(selSets).children().get(4).textContent = "";
		else if(num < 1) {
			$("#setsRow").val("");
			$(selSets).children().get(4).textContent = "";
		} else
			$(selSets).children().get(4).textContent = num;
	} else {
		setSeatsRows();
	}
});

//设置所有选中的座位排数
function setSeatsRows() {
	if($("#setsRow").val() == "") return;
	$('.box div').each(function() {
		if($(this).is('.selected')) {
			$(this).children().get(4).textContent = $("#setsRow").val();
		}
	});
}

//给座位设置第几座
$("#setsNum").mouseup(function() {
	if(selSets != undefined) {
		$(selSets).children().get(5).textContent = $("#setsNum").val();
	} else {
		setSeatsNos();
	}
});
$("#setsNum").keyup(function() {
	if(selSets != undefined) {
		var num = $("#setsNum").val();
		if(num == "")
			$(selSets).children().get(5).textContent = "";
		else if(num < 1) {
			$("#setsNum").val("");
			$(selSets).children().get(5).textContent = "";
		} else
			$(selSets).children().get(5).textContent = num;
	} else {
		setSeatsNos();
	}
});

//设置所有选中的座位第几座
function setSeatsNos() {
	if($("#setsNum").val() == "") return;
	$('.box div').each(function() {
		if($(this).is('.selected')) {
			$(this).children().get(5).textContent = $("#setsNum").val();
		}
	});
}

//给座位设置备注
$("#setsRmk").change(function() {
	var rmk = $("#setsRmk").val();
	if(selSets != undefined) {
		$(selSets).children().get(7).textContent = rmk;
	}
});

$("#initNum").keyup(function() {
	var num = $("#initNum").val();
	if(Number(num) < 1)
		$("#initNum").val("1");
	if(num == "")
		$("#initNum").val("");
});

$("#radius").keyup(function() {
	var num = $("#radius").val();
	if(num == "")
		$("#radius").val("50");
	else if(num > 400)
		$("#radius").val("400");
});

$("#rowPers").keyup(function() {
	var num = $("#rowPers").val();
	if(Number(num) < 1)
		$("#rowPers").val("1");
	if(num == "")
		$("#rowPers").val("");
});

//给标签设置宽度
$("#signW").keyup(function() {
	var num = $("#signW").val();
	if(num < 15)
		$("#signW").val("15");
});

//给标签设置高度
$("#signH").keyup(function() {
	var num = $("#signH").val();
	if(num < 15)
		$("#signH").val("15");
});

//标签设置弹出框 确定按钮
$("#confirm").on("click", function() {
	if(selSign != undefined) {
		$(selSign).html($("#sgContent").val());
		$(selSign).css("width", $("#signW").val()).css("height", $("#signH").val());
	}
	$('#inputWin').css("display", "none");
});

//清空文本框内容
function clearContent() {
	$("#editSetsNum").val("");
	$("#setsW").val("50");
	$("#setsH").val("50");
	$("#setsRow").val("");
	$("#setsNum").val("");
	$("#setsRmk").val("");
}

//以下保持按Ctrl时候能多选
var keepCtrl = false;
$(document).bind("keydown", function(e) {
	if(e.keyCode = 17) {
		keepCtrl = true;
	}
});

$(document).bind("keyup", function(e) {
	if(e.keyCode = 17) {
		keepCtrl = false;
	}
});

function num(obj) {
	obj.value = obj.value.replace(/[^0-9]/g, ''); //清除"数字"以外的字符
	obj.value = obj.value.replace(/^\./g, ""); //验证第一个字符是数字
	obj.value = obj.value.replace(/\.{2,}/g, "."); //只保留第一个, 清除多余的
	obj.value = obj.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
	obj.value = obj.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3'); //只能输入两个小数
}

//初始化拖拽
function initDrag() {
	//超出范围内还可以拖动
	$('.drag,.signDrag')
		.drag("init", function() {
			if($(this).is('.selected'))
				return $('.selected');
		})
		.drag(function(ev, dd) {
			$(this).css({
				top: dd.offsetY + scroH - 82,
				left: dd.offsetX + scroW - $('#seatInfo').width() - 4
			});
		});
	//在指定范围内可以拖动  在火狐浏览器里面有问题
	//	$('.drag')
	//	.drag("init", function() {
	//		if($(this).is('.selected'))
	//			return $('.selected');
	//	})
	//	.drag("start",function( ev, dd ){
	//		dd.limit = $("body").offset();
	//		dd.limit.bottom = dd.limit.top + $("body").outerHeight() - $( this ).outerHeight();
	//		dd.limit.right = dd.limit.left + $("body").outerWidth() - $( this ).outerWidth();
	//	})
	//	.drag(function( ev, dd ){
	//		$( this ).css({
	//			top: Math.min( dd.limit.bottom - 40, Math.max( dd.limit.top, dd.offsetY - 40) ),
	//			left: Math.min( dd.limit.right - $("#roomList").width() - 5, Math.max( dd.limit.left, dd.offsetX - $("#roomList").width() - 5) )
	//		});
	//	});
}

//在排座容器内用鼠标划出一块区域，并选中区域内的座位
function boxOnMouse(evt) {
	$('#inputWin').css("display", "none");
	setColorByAreaType(2);
	var selList = [];
	var fileNodes = document.getElementsByTagName("div");
	for(var i = 0; i < fileNodes.length; i++) {
		if(fileNodes[i].className.indexOf("drag") != -1) {
			fileNodes[i].className = "drag";
			selList.push(fileNodes[i]);
		}

		if(fileNodes[i].className.indexOf("signDrag") != -1) {
			fileNodes[i].className = "signDrag";
			selList.push(fileNodes[i]);
		}
	}
	var isSelect = true;
//	var evt = window.event || arguments[0];
	var startX = (evt.x || evt.clientX);
	var startY = (evt.y || evt.clientY);
	var selDiv = document.createElement("div");
	selDiv.style.cssText = "position:absolute;width:0px;height:0px;font-size:0px;margin:0px;padding:0px;border:1px dashed #0099FF;background-color:#C3D5ED;z-index:1000;filter:alpha(opacity:60);opacity:0.6;display:none;";
	selDiv.id = "selectDiv";
	document.body.appendChild(selDiv);
	selDiv.style.left = startX + scroW + "px";
	selDiv.style.top = startY + scroH + "px";
	var _x = null;
	var _y = null;
	clearEventBubble(evt);
	document.onmousemove = function() {
		evt = window.event || arguments[0];
		if(isSelect) {
			if(selDiv.style.display == "none") {
				selDiv.style.display = "";
			}
			_x = (evt.x || evt.clientX);
			_y = (evt.y || evt.clientY);
			selDiv.style.left = Math.min(_x, startX) + "px";
			selDiv.style.top = Math.min(_y, startY) + "px";
			selDiv.style.width = Math.abs(_x - startX) + "px";
			selDiv.style.height = Math.abs(_y - startY) + "px";
			// ---------------- 关键算法 ---------------------
			var _l = selDiv.offsetLeft + scroW - $('#seatInfo').width() - 5,
				_t = selDiv.offsetTop + scroH - 80;
			var _w = selDiv.offsetWidth,
				_h = selDiv.offsetHeight;
			for(var i = 0; i < selList.length; i++) {
				var sl = selList[i].offsetWidth + selList[i].offsetLeft;
				var st = selList[i].offsetHeight + selList[i].offsetTop;
				if(sl > _l && st > _t && selList[i].offsetLeft < _l + _w && selList[i].offsetTop < _t + _h) {
					if(selList[i].className.indexOf("selected") == -1) {
						selList[i].className = selList[i].className + " selected";
						$(selList[i]).css("background", "");
					}
				} else {
					if(selList[i].className.indexOf("selected") != -1) {
						$(selList[i]).removeClass("selected");
						if($(selList[i]).is(".drag")){
							$(selList[i]).css("background", $(selList[i]).children().get(6).textContent);
						}
					}
				}
			}
		}
		clearEventBubble(evt);
	}
	document.onmouseup = function() {
		isSelect = false;
		if(selDiv) {
			document.body.removeChild(selDiv);
			showSelDiv(selList);
		}
		selList = null, _x = null, _y = null, selDiv = null, startX = null, startY = null, evt = null;
	}
}

function clearEventBubble(evt) {
	if(evt.stopPropagation)
		evt.stopPropagation();
	else
		evt.cancelBubble = true;
	if(evt.preventDefault)
		evt.preventDefault();
	else
		evt.returnValue = false;
}

function showSelDiv(arr) {
	selSeatId = new Array();
	for(var i = 0; i < arr.length; i++) {
		if(arr[i].className.indexOf("selected") != -1) {
			if($(arr[i]).is(".isSeat")) {
				var selItem = $(arr[i]).children().get(0).innerHTML;
				selSeatId.push(selItem);
			}
		}
	}
	selSets = undefined;
	selSign = undefined;
}

//单选按钮点击事件
$("input:radio[name='setPlace']").on('click', function(event) {
  placeType =$(this).val()
	// if($(this).val() == "1") {
	// 	$("#radiusSpan").hide();
	// 	$("#matrixSpan").show();
	// } else {
	// 	$("#radiusSpan").show();
	// 	$("#matrixSpan").hide();
	// }
});

//颜色单选按钮点击事件
$("input:radio[name='zoneColor']").on('click', function(event) {
  $(foucsInput).css("background", $(this).val());
	$('#colorWin').css("display", "none");
});

//重新排列
$("#reset").on("click", function() {
	clearContent();
	setTheSeatsByType();
});

//按类型排列座位
function setTheSeatsByType() {
	var resetType = $("input:radio[name='setUp']:checked").val(); //按xx类型重新排列  1:矩阵  2:圆形
	//圆形排列
	if(resetType == "2") {
		var radNum = $("#radius").val();
		setMatrix(radNum);
	} else {
		setCircular();
	}
}

/**
 * @desc   对象数组排序
 * @param   {array} 数组
 * @param   {key} 对象中的key
 */
function tool_sortByKey(array, key) {
	return array.sort(function(a, b) {
		var x = Number(a[key]);
		var y = Number(b[key]);
		return x < y ? -1 : x > y ? 1 : 0;
	});
}

//呈圆形排列
function setMatrix(rad) {
	//中心点横坐标
	var dotLeft = ($(".box").width() - $(".dot").width()) / 2;
	//中心点纵坐标
	var dotTop = ($(".box").height() - $(".dot").height()) / 2;
	//起始角度
	var stard = 0;
	//半径
	var radius = rad;
	//每一个BOX对应的角度;
	var avd = 360 / $(".drag").length;
	//每一个BOX对应的弧度;
	var ahd = avd * Math.PI / 180;

	//设置圆的中心点的位置
	$(".dot").css({
		"left": dotLeft,
		"top": dotTop
	});

	var matrixList = new Array(); //座位数组  排序用
	//设置圆形排列
	$(".drag").each(function(index, element) {
		//清空座位第几排，第几座
		$(this).children().get(4).textContent = ""; //座位第几排
		$(this).children().get(5).textContent = ""; //座位第几座

		//将座位放入座位数组进行排序用
		var obj = new Object();
		obj.item = $(this);
		obj.value = $(this).children().get(0).innerHTML;
		matrixList.push(obj);
	});

	//对对象数组进行排序
	tool_sortByKey(matrixList, "value");

	for(var i=0; i<matrixList.length; i++){
		$(matrixList[i].item).css({
			"left": Math.sin((ahd * i)) * radius + dotLeft,
			"top": Math.cos((ahd * i)) * radius + dotTop
		});
	}
}

//矩阵排列
function setCircular() {

	var seatList = new Array(); //座位数组  排序用
	var selList = new Array(); //选中的座位，排列用
	$(".drag").each(function(index, element) {
		//将座位放入座位数组进行排序用
		var obj = new Object();
		obj.item = $(this);
		obj.value = $(this).children().get(0).innerHTML;
		seatList.push(obj);

		//将选中的座位放入选中数组
		if($(this).is('.selected'))
			selList.push($(this));

	});
	//对对象数组进行排序
	tool_sortByKey(seatList, "value");

	//选中部分座位矩阵排列
	if(selList.length > 0) {
		selSeatsSetCircular(selList);
	} else { //所有座位矩阵排列
		allSeatsSetCircular(seatList);
	}
}

//所有座位矩阵排列
function allSeatsSetCircular(dragList) {
	var seatRows = Number($("#rowPers").val()); //每排人数
	var startX = Number($(dragList[0].item).width()); //起始x坐标
	var startY = Number($(dragList[0].item).height()); //起始y坐标
	var brRow = 1; //当前第几次换行
	var brIndex = 1; //换行后重拍后的起始坐标
	var brStartX = startX /*40*/ ; //换行后起始x坐标
	var newIndex; //换行下标

	for(var i = 0; i < dragList.length; i++) {

		newIndex = Number(i) + 1;

		if(Number(newIndex) > Number(seatRows)) {
			if(newIndex % seatRows == 1) {
				brRow++;
				if(Number(brIndex) == Number(seatRows)) {
					brIndex = 1;
				}
				$(dragList[i].item).css({
					"left": brStartX,
					"top": brRow * startY + (brRow * 5 - 5)
				});
			} else {
				if(Number(brIndex) == Number(seatRows)) {
					brIndex = 1;
				}
				brIndex++;
				$(dragList[i].item).css({
					"left": brIndex * brStartX + (Number(brIndex) * 5 - 5),
					"top": brRow * startY + (brRow * 5 - 5)
				});
			}
			$(dragList[i].item).children().get(4).textContent = brRow; //座位第几排
			$(dragList[i].item).children().get(5).textContent = brIndex; //座位第几座
		} else {
			$(dragList[i].item).css({
				"left": newIndex * startX + (Number(i) * 5),
				"top": startY
			});

			$(dragList[i].item).children().get(4).textContent = 1; //座位第几排
			$(dragList[i].item).children().get(5).textContent = newIndex; //座位第几座
		}
	}
}

//选中部分座位矩阵排列
function selSeatsSetCircular(dragList) {
	var sortList = new Array(); //排序数组
	for(var i = 0; i < dragList.length; i++) {
		//将座位放入座位数组进行排序用
		var obj = new Object();
		obj.item = $(dragList[i]);
		obj.value = Number($(dragList[i]).children().get(0).innerHTML);
		sortList.push(obj);
	}
	//对对象数组进行排序
	tool_sortByKey(sortList, "value");

	var seatRows = Number($("#rowPers").val()); //每排人数
	var startX = $(sortList[0].item).position().left; //起始x坐标 选中的座位中最小的x坐标
	var startY = $(sortList[0].item).position().top; //起始y坐标 选中的座位中最小的y坐标
	var sW = Number($(sortList[0].item).width()) + 5; //座位左右间距为座位宽度+5个像素
	var sH = Number($(sortList[0].item).height()) + 5; //座位上下间距为座位高度+5个像素
	var brRow = 1; //当前第几次换行
	var brIndex = 1; //换行后重拍后的起始坐标
	var brStartX = startX; //换行后起始x坐标
	var newIndex; //换行下标
	//每排人数大于1,计算每排人数
	if(Number(seatRows) > 1){
		for(var i = 0; i < sortList.length; i++) {

			newIndex = Number(i) + 1;

			if(Number(newIndex) > Number(seatRows)) {
				if(newIndex % seatRows == 1) {
					brRow++;
					if(Number(brIndex) == Number(seatRows)) {
						brIndex = 1;
					}
					$(sortList[i].item).css({
						"left": brStartX,
						"top": startY + Number(brRow * sH - sH)
					});
				} else {
					if(Number(brIndex) == Number(seatRows)) {
						brIndex = 1;
					}
					brIndex++;
					$(sortList[i].item).css({
						"left": brStartX + Number(brIndex * sW - sW),
						"top": startY + Number(brRow * sH - sH),
					});
				}
				$(sortList[i].item).children().get(4).textContent = brRow; //座位第几排
				$(sortList[i].item).children().get(5).textContent = brIndex; //座位第几座
			} else {
				$(sortList[i].item).css({
					"left": startX + Number(i * sW),
					"top": startY
				});

				$(sortList[i].item).children().get(4).textContent = 1; //座位第几排
				$(sortList[i].item).children().get(5).textContent = newIndex; //座位第几座
			}
		}
	}
	//每排人数大于0小于1 直接排成一列
	else if(Number(seatRows) > 0 && Number(seatRows) == 1){
		for(var i = 0; i < sortList.length; i++) {

			newIndex = Number(i) + 1;

			$(sortList[i].item).css({
				"left": startX + Number(sW),
				"top": startY + Number(newIndex * sH - sH),
			});

			$(sortList[i].item).children().get(4).textContent = newIndex; //座位第几排
			$(sortList[i].item).children().get(5).textContent = 1; //座位第几座
		}
	}
}

//重新设置座位号
$("#setNum").on("click", function() {
	var dragList = new Array();
	$(".drag").each(function(index, element) {
		var obj = new Object();
		obj.item = $(this);
		obj.value = $(this).children().get(0).innerHTML;
		dragList.push(obj);
	});
	//对对象数组进行排序
	tool_sortByKey(dragList, "value");

	for(var i = 0; i < dragList.length; i++) {
		var index = Number(i) + 1;
		$(dragList[i].item).children().get(0).textContent = index;
	}
});

//新增一行颜色
$(".table-body").on("click", ".addRow", function() {
  var row = '<div class="table-row" id=""><div style="width: 100px;"><input type="text" style="width: 80px; height: 36px; font-size: 15px; outline: none; border: none; padding-left: 12px;"/></div><div style="width: 80px;"><p class="showColor"></p></div><div style="width: 120px;"><span><label class="tableBtn saveRow">保存</label><label class="tableBtn addRow">添加</label><label class="tableBtn delRow">删除</label></span></div>	</div>';
  $(".table-body").append(row);
});

//删除一行颜色
$(".table-body").on("click", ".delRow", function() {
  if($(".table-body").children().length > 1) {
    var sId = $(this).parent().parent().parent().attr("id"); //类型Id
    if(sId == "") {
      $(this).parent().parent().parent().remove();
    } else {
      axios({
        method: "post",
        url: server.local_path + "meetSeatArea/dels?ids=" + sId,
        headers: JSON.parse(header)
      })
      .then(function(response) {
      	var resultInfo = response.data;
      	if(resultInfo.errcode == 200) {
      		layer.msg("删除成功");
      		//查询区域列表
      		loadZoneList();
      	}
      	else{
      		layer.msg(resultInfo.errmsg);
      	}
      }).catch(function(error) {
      	layer.msg(error.response.data.message);
      });
    }
  }
});

$(".table-body").on('click', ".showColor", function(e) {
	foucsInput = $(this);
	var selfHei = $("#colorWin").innerHeight();
	var topHei = foucsInput.offset().top - selfHei; //当前点击的元素距离父容器panel顶部的距离
	//如果弹出框距父容器顶部的距离小于弹出框的高度,就把弹出框显示在标签下方，反之显示在上方
	if(topHei < selfHei) {
		$("#colorWin").css("top", $(this).offset().top + 38);
	} else {
		$("#colorWin").css("top", $(this).offset().top - selfHei - 5);
	}
	$("#colorWin").css("left", $(this).offset().left - 100);
	$('#colorWin').css("display", "block");
  $(":radio[name='zoneColor'][value='"+ rgb2hex($(this).css("background")) +"']").prop("checked", "checked");
	//阻止事件穿透
	e.stopPropagation();
});

//rgb转16进制
function zero_fill_hex(num, digits) {
  var s = num.toString(16);
  while (s.length < digits)
    s = "0" + s;
  return s;
}

function rgb2hex(rgb) {

  if (rgb.charAt(0) == '#')
    return rgb;
  var ds = rgb.split(/\D+/);
  var decimal = Number(ds[1]) * 65536 + Number(ds[2]) * 256 + Number(ds[3]);
  return "#" + zero_fill_hex(decimal, 6);
}
//rgb转16进制

//保存区域列表
$(".table-body").on("click", ".saveRow", function() {
  var zoneArr = new Array(); //区域信息数组
  $('.table-body div').each(function() {
  	if($(this).is('.table-row')) {
      var sId = $(this).attr("id"); //类型Id
      var sName = $(this).find('input').val(); //类型名称
      var sColor = rgb2hex($(this).find('p').css("background")); //类型颜色
      var typeObj = new Object();
      typeObj.id = sId;
      typeObj.name = sName;
      typeObj.colour = sColor;
      if(sName != "" && sColor != "#000000") {
        zoneArr.push(typeObj);
      }
  	}
  });
  axios({
    method: "post",
    url: server.local_path + "meetSeatArea/editSeatArea",
    headers: JSON.parse(header),
    data: zoneArr
  })
    .then(function (response) {
      var resultInfo = response.data;
      if (resultInfo.errcode == 200) {
        layer.msg("操作成功");
        //查询区域列表
        loadZoneList();
      }
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
});

//查询区域列表
function loadZoneList() {
  axios({
    method: "post",
    url: server.local_path + "meetSeatArea/listData",
    headers: JSON.parse(header)
  })
    .then(function (response) {
      var resultInfo = response.data;
      if (resultInfo.errcode == 200) {
        $(".table-body").empty();
        if(resultInfo.data.length > 0) {
          for(var i=0; i<resultInfo.data.length; i++) {
            var row = '<div class="table-row" id="'+ resultInfo.data[i].id +'"><div style="width: 100px;"><input type="text" value="'+ resultInfo.data[i].name +'" style="width: 80px; height: 36px; font-size: 15px; outline: none; border: none; padding-left: 12px;"/></div><div style="width: 80px;"><p class="showColor" style="background:'+ resultInfo.data[i].colour +'"></p></div><div style="width: 120px;"><span><label class="tableBtn saveRow">保存</label><label class="tableBtn addRow">添加</label><label class="tableBtn delRow">删除</label></span></div>	</div>';
            $(".table-body").append(row);
          }

          $(".areaTip").empty();
          var areaInfo = resultInfo.data;
          areaArr = new Array();
          areaArr = areaInfo;
          //座位类型信息
          if(areaInfo != null && areaInfo.length > 0) {
          	var options = [ /*{text:"请选择选项",value:""}*/ ];
          	for(var i = 0; i < areaInfo.length; i++) {
          		var typeId = areaInfo[i].id; //区域id
          		var typeName = areaInfo[i].name; //区域名称
          		var typeColor = areaInfo[i].colour; //区域颜色
          		options.push({
          			text: typeName,
          			value: typeId
          		});
          		$(".areaTip").append("<label>" + typeName + "</label><label style='width: 20px;height: 20px;margin: 0 15px 0 3px;color: " + typeColor + ";'>█</label>");
          	}
          	$("#setsCT").combobox("loadData", options);
          	$('#setsCT').combobox('select', options[0].value);
           }
        } else {
          var row = '<div class="table-row" id=""><div style="width: 100px;"><input type="text" style="width: 80px; height: 36px; font-size: 15px; outline: none; border: none; padding-left: 12px;"/></div><div style="width: 80px;"><p class="showColor"></p></div><div style="width: 120px;"><span><label class="tableBtn saveRow">保存</label><label class="tableBtn addRow">添加</label><label class="tableBtn delRow">删除</label></span></div>	</div>';
          $(".table-body").append(row);
        }
      }
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
}

//获得焦点 弹出标签属性框
$(".box").on('click', ".signDrag", function(e) {
	foucsInput = $(this);
	var selfHei = $("#inputWin").innerHeight();
	var topHei = foucsInput.offset().top - selfHei; //当前点击的元素距离父容器panel顶部的距离
	//如果弹出框距父容器顶部的距离小于弹出框的高度,就把弹出框显示在标签下方，反之显示在上方
	if(topHei < selfHei) {
		$("#inputWin").css("top", $(this).offset().top + 38);
	} else {
		$("#inputWin").css("top", $(this).offset().top - selfHei - 5);
	}
	$("#inputWin").css("left", $(this).offset().left - 55);
	$('#inputWin').css("display", "block");
	$('#sgContent').val($(selSign).get(0).innerHTML);
	$('#signW').val($(selSign).get(0).offsetWidth);
	$('#signH').val($(selSign).get(0).offsetHeight);
	//阻止事件穿透
	e.stopPropagation();
});

//签属性框点击事件
$("#colorWin").on('click', function(e) {
	//阻止事件穿透
	e.stopPropagation();
});

//失去焦点
$(document).on("click", function() {
	$('#colorWin').css("display", "none");
	setColorByAreaType(2);
});

//座位信息面板点击
$("#initNum, #setsW, #setsH").on("click", function() {
	clearSeatSelected();//清空座位选中样式
});

//监听滚动事件
var scroH = 0; //滚动高度
var scroW = 0; //滚动宽度
$(".box").on("scroll", function() {
	$('#colorWin').css("display", "none");
	scroH = $(".box").scrollTop(); //滚动高度
	scroW = $(".box").scrollLeft(); //滚动宽度
});

//显示座位号/顺序号
function showSeatNoOrSort(value) {
	// var value = $("#showType").combobox("getValue");
	//显示顺序号
	if(value == "0") {
		$('.box div').each(function() {
			$($(this).children().get(0)).css("display", "block"); //座位号
			$($(this).children().get(5)).css("display", "none"); //座位顺序号
		});
	}
	//显示座位号
	else {
		$('.box div').each(function() {
			$($(this).children().get(0)).css("display", "none"); //座位号
			$($(this).children().get(5)).css("display", "block"); //座位顺序号
		});
	}
}

//调换顺序号
$("#resortNo").on("click", function(){
	var seatNumbers = 0; //座位个数
	var dragList = new Array();
	$(".drag").each(function(index, element) {
		seatNumbers++;
		var obj = new Object();
		obj.item = $(this);
		obj.value = $(this).children().get(0).innerHTML;
		dragList.push(obj);
	});
	//对对象数组进行排序
	tool_sortByKey(dragList, "value");
	for(var i = 0; i < dragList.length; i++) {
		$(dragList[i].item).children().get(0).textContent = seatNumbers - i;
	}
});

//调换座位号
$("#resortNumber").on("click", function(){
	var row = $("#setsRow").val(); //获取排数
	var seatNumbers = 0; //同一排的座位个数
	if(row != ""){
		var dragList = new Array();
		$(".drag").each(function(index, element) {
			var thisRow = $(this).children().get(4).innerHTML; //排数
			if(thisRow == row){
				seatNumbers++;
				var obj = new Object();
				obj.item = $(this);
				obj.value = $(this).children().get(5).innerHTML;
				dragList.push(obj);
			}
		});
		//对对象数组进行排序
		tool_sortByKey(dragList, "value");
		for(var i = 0; i < dragList.length; i++) {
			$(dragList[i].item).children().get(5).textContent = seatNumbers - i;
		}
	}
});

//批量添加备注
$("#batchRemark").on("click", function(){
	var rmk = $.trim($("#setsRmk").val());
	if(rmk != ""){
		var selList = new Array(); //选择的座位
		$('.box div').each(function() {
			if($(this).is('.selected')) {
				//座位
				if($(this).is('.drag')) {
					selList.push($(this));
				}
			}
		});
		if(selList.length > 0){
			for(var i = 0; i < selList.length; i++) {
				$(selList[i]).children().get(7).textContent = rmk;
			}
		}
		else{
			layer.msg("请选择座位");
		}
	}
	else{
		layer.msg("请输入备注内容");
	}
});

//布局名称单选按钮点击
function radioClick(item) {
  $(".bjNameRadio").removeClass('bjNameRadioCheck').addClass('bjNameRadioUnCheck');
  $(".bjNameRadioChild").css("background", "#FFFFFF");
  $(item).removeClass('bjNameRadioUnCheck').addClass('bjNameRadioCheck');
  $(item).children().css("background", themeColor)
  var lId = $(".bjNameRadioCheck").attr("id"); //选中的布局id
  //加载排座信息
  loadSeatsInfo(selRid, lId);
}
