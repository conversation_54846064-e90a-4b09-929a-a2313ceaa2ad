:root {
    --color: #3279F4;
}

html{
	background: #fff;
}
*{
	box-sizing: border-box;
}
body {
	overflow: auto;
	margin: 0;
	box-sizing: border-box;
}

li {
	list-style: none outside none;
}

.box {
	width: 100%;
	height: 100%;
	position: relative;
	text-align: center;
}

p {
	font-size: 20px;
	color: #333;
	text-align: center;
	margin: 0 0 20px;
}

i.hander {
	display: block;
	width: 100%;
	height: 25px;
	background: #ccc;
	text-align: center;
	font-size: 12px;
	color: #333;
	line-height: 25px;
	font-style: normal;
}

.meetUl {
	margin: 0;
	color: #2E2E2E;
	padding: 0px 5px;
	text-align: center;
	background-color: #F2F2F2;
}

.meetUl li {
	margin-top: 0px;
	height: 50px;
	line-height: 50px;
	background-color: #F2F2F2;
	list-style-type: none;
	margin: 8px 0px;
	font-weight: bold;
	font-size: 15px;
	text-align: left;
	white-space: nowrap;
}

#setNumOne {
	width: 208px;
	height: 44px;
	margin-top: 15px;
	line-height: 44px;
	border-radius: 4px;
	margin-left: 25px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: white;
}

#bc {
	border-radius: 4px;
	padding: 10px 20px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: white;
}

#sc {
	border-radius: 4px;
	padding: 10px 20px;
	background-color: #FF3434 !important;
	text-align: center;
	cursor: pointer;
	color: white;
}

#lcw {
	border-radius: 4px;
	padding: 10px 20px;
	background-color: #FFFFFF !important;
	border: 1px solid #199BC5;
	text-align: center;
	cursor: pointer;
	color: #199BC5;
}

#showSXH {
	margin-left: 10px;
	border-radius: 4px;
	padding: 5px 20px;
	background-color: rgba(50, 121, 244, 0.2) !important;
	border: 1px solid #3279F4;
	text-align: center;
	cursor: pointer;
	color: #3279F4;
}

#showZWH {
	margin-left: 10px;
	border-radius: 4px;
	padding: 5px 20px;
	background-color: #FFFFFF !important;
	border: 1px solid #3279F4;
	text-align: center;
	cursor: pointer;
	color: #3279F4;
}


#reset {
	position: relative;
	top: 10px;
	left: 0;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: white;
}

#addSigns {
	float: left;
	margin-left: 5px;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #FFFFFF;
	text-align: center;
	cursor: pointer;
	color: #262626;
	border: 1px solid #D9D9D9;
}

#delSets {
	float: left;
	margin-left: 5px;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 4px;
	border: 1px solid #FF5064;
	background-color: #FFFFFF !important;
	text-align: center;
	cursor: pointer;
	color: #FF5064;
}

#delSets:hover{
	border-color: #FF5064 !important;
}

#saveSets {
	float: left;
	margin-left: 5px;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: white;
}

#allSel {
	float: left;
	margin-left: 10px;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 4px;
	background-color: #FFFFFF;
	text-align: center;
	cursor: pointer;
	color: #FFFFFF;
	border: 1px solid #D9D9D9;
}

#setNum {
	float: left;
	margin-left: 5px;
	width: 90px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #FFFFFF;
	text-align: center;
	cursor: pointer;
	color: #262626;
	border: 1px solid #D9D9D9;
}

#resortNo {
	float: left;
	margin-bottom: 10px;
	width: 110px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: #fff;
	border: 0px solid #D9D9D9;
}

#resortNumber {
	float: right;
	margin-bottom: 10px;
	width: 110px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: #fff;
	border: 0px solid #D9D9D9;
}

#makePic {
	float: left;
	margin-left: 5px;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #FFFFFF;
	text-align: center;
	cursor: pointer;
	color: #262626;
	border: 1px solid #D9D9D9;
}

.table-info p {
	margin: 0;
}

.table-header {
	display: inline-flex;
	width: 100%;
	background: #F5F7FB;
}

.table-header p {
	width: 100%;
	font-size: 15px;
	text-align: left;
	padding-left: 20px;
}

.table-body {
	width: 100%;
	height: 150px;
	overflow: auto;
	font-size: 15px;
}

.table-row {
	display: inline-flex;
	width: 100%;
}

/* .table-row div {
	width: 100%;
	font-size: 15px;
} */

.btn-info:hover{
	background: var(--color) !important;
	border-color: var(--color) !important;
	/* color: #FFF !important; */
}

.cancel:hover{
	border-color: var(--color) !important;
	color: var(--color) !important;
}

.textbox-focused{
	border-color:var(--color) !important;
}

.drag {
	position: absolute;
	border: 0px solid #000;
	border-radius: 5px;
	background: #00A0EA;
	font-size: 12px;
	cursor: move;
	display: flex;
	justify-content: center;
	align-items: center;
	/*background-image: url(../img/border.svg);
	background-repeat: no-repeat;
	background-size: 100% 100%;*/
}

.signDrag {
	position: absolute;
	border: 0px solid #FFFFFF;
	border-radius: 0px;
	background: #FFFFFF;
	cursor: move;
	display: flex;
	justify-content: center;
	align-items: center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.selected {
	background-color: #e46424;
	border-color: #e46424;
}

.dot {
	position: absolute;
	width: 20px;
	height: 20px;
	background: #F00;
}

.set_content {
	display: none;
	position: absolute;
	width: 240px;
	height: 200px;
	border: 2px solid #bdcbd4;
	background-color: #d5d8e0;
	opacity: 0.9;
	z-index: 1002;
	overflow-x: hidden;
	overflow-y: auto;
}

.signs_content {
	display: none;
	position: absolute;
	width: 260px;
	height: 80px;
	border: 2px solid #e1e9ec;
	background-color: #e1e9ec;
	padding: 5px;
	opacity: 1;
	z-index: 1002;
	text-align: center;
	overflow-x: hidden;
	overflow-y: auto;
}

.color_content {
	display: none;
	position: absolute;
	width: 260px;
	height: 255px;
	border: 1px solid #e1e9ec;
	background-color: #FFFFFF;
	border-radius: 6px;
	padding: 5px;
	opacity: 1;
	z-index: 1002;
	text-align: center;
	overflow-x: hidden;
	overflow-y: auto;
}

.areaTip {
	position: absolute;
	top: 0;
	right: 30px;
	z-index: 100;
	text-align: right;
}
.inputText{
	background:linear-gradient(0deg,rgba(255,255,255,1) 0%,rgba(249,250,251,1) 100%);
	border:1px solid rgba(217, 217, 217, 1);
	box-shadow:0px 1px 2px 0px rgba(0, 0, 0, 0.15);
	border-radius:2px;
	outline: none;
}

.rmkBox{
	width:20px;
 	height:25px; 
 	cursor: pointer;
	background: var(--color);
	margin: 4px 8px;
	border-radius: 3px;
}

.opt {
    height: 30px;
    line-height: 20px;
	padding-top: 5px;
}

.magic-radio, .magic-checkbox {
    position: absolute;
    display: none;
}

.magic-radio + label, .magic-checkbox + label {
    position: relative;
    display: block;
    padding-left: 30px;
    cursor: pointer;
}

.magic-radio:checked + label:before {
    border: 1px solid var(--color);
}

.magic-radio:checked + label:before, .magic-checkbox:checked + label:before {
    animation-name: none;
}
.magic-radio + label:before {
    border-radius: 50%;
}
.magic-radio + label:before, .magic-checkbox + label:before {
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block;
    width: 20px;
    height: 20px;
    content: '';
    border: 1px solid #c0c0c0;
}

.magic-radio:checked + label:after, .magic-checkbox:checked + label:after {
    display: block;
}
.magic-radio + label:after {
    top: 7px;
    left: 7px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color);
}
.magic-radio + label:after, .magic-checkbox + label:after {
    position: absolute;
    display: none;
    content: '';
}

.radioBox {
	width: 100%;
	height: 36px;
	display: inline-flex;
	white-space: nowrap; 
	padding: 5px 5px 0 5px;
}

.bjRadio {
	display: inline-flex;
}

.bjNameRadio {
	width: 22px;
	height: 22px; 
	border-radius: 20px;
	margin-top: 2px;cursor: pointer;
}

.bjNameRadioCheck {
	border: 1px solid #3279F4; 
}

.bjNameRadioUnCheck {
	border: 1px solid #c0c0c0; 
}

.bjNameRadioChild {
	width: 8px; height: 8px; 
	background-color: #3279F4;
	position: relative; 
	top: 6px; left: 6px; 
	border-radius: 8px; 
}

.bjRadioInput {
	width: 100px; 
	height: 25px;
	margin-left: 2px; 
	font-size: 16px;
	border: none; outline: none;
}

.showColor {
	width: 25px;
	height: 25px;
	margin: 5px !important;
	margin-left: 38px !important;
}

.tableBtn {
	margin: 2px;
	cursor: pointer;
	color: var(--color);
}