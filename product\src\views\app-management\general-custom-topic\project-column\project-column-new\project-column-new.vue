<template>
  <div class="project-column-new">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="栏目名称"
                    class="form-input"
                    prop="name">
        <el-input placeholder="请输入栏目名称"
                  v-model="form.name"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="上级栏目"
                    class="form-input"
                    prop="publishDate">
        <zy-select width="296"
                   node-key="id"
                   :data="parentId"
                   v-model="form.parentId"
                   placeholder="请选择上级栏目"
                   :props="{label:'name',children:'children'}"></zy-select>
      </el-form-item>
      <el-form-item label="序号"
                    class="form-input"
                    prop="sort">
        <el-input placeholder="请输入序号"
                  v-model="form.sort"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="是否启用"
                    class="form-input">
        <el-radio-group v-model="form.isOpen">
          <el-radio label="1">启用</el-radio>
          <el-radio label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否图标展示"
                    class="form-input">
        <el-radio-group v-model="form.iconopen">
          <el-radio :label="item.id"
                    v-for="(item, index) in yesNo"
                    :key="index">{{item.value}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="图标"
                    class="form-icon">
        <el-upload class="form-icon-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <img v-if="file.fullUrl"
               :src="file.fullUrl"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'projectColumnNew',
  data () {
    return {
      form: {
        name: '',
        parentId: '',
        sort: '',
        isOpen: '1',
        iconopen: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入序号', trigger: 'blur' }
        ]
      },
      parentId: [],
      file: {},
      yesNo: []
    }
  },
  props: ['id', 'mosaicId'],
  mounted () {
    this.dictionaryPubkvs()
    this.customTopicColumnTree()
    if (this.mosaicId) {
      this.customTopicColumnInfo()
    }
  },
  methods: {
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'yes_no'
      })
      var { data } = res
      this.yesNo = data.yes_no
    },
    async customTopicColumnTree () {
      const res = await this.$api.appManagement.customTopicColumnTree({ subjectId: this.id })
      var { data } = res
      this.parentId = data
    },
    async customTopicColumnInfo () {
      const res = await this.$api.appManagement.customTopicColumnInfo({ id: this.mosaicId })
      var { data: { name, parentId, isOpen, iconopen, icon, sort } } = res
      this.form.name = name
      this.form.sort = sort
      this.form.parentId = parentId
      this.form.isOpen = isOpen
      this.form.iconopen = iconopen
      if (icon) {
        this.file = icon
      }
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        data[0].name = data[0].fileName
        this.file = data[0]
      })
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.form.parentId === '') {
            this.form.parentId = 1
          }
          var url = '/zySpecialsubjectColumn/save'
          this.$api.systemSettings.generalAdd(url, {
            id: this.mosaicId,
            subjectId: this.id,
            name: this.form.name,
            parentId: this.form.parentId,
            sort: this.form.sort,
            isOpen: this.form.isOpen,
            iconopen: this.form.iconopen,
            icon: this.file.id
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
@import "./project-column-new.scss";
</style>
