<template>
  <div class="module-new">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="模块名称"
                    class="form-input"
                    prop="title">
        <el-input placeholder="请输入模块名称"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="所属模块"
                    prop="type">
        <zy-select width="296"
                   node-key="id"
                   :props="props"
                   v-model="form.type"
                   :data="type"
                   placeholder="请选择所属模块"></zy-select>
      </el-form-item>
      <el-form-item label="菜单位置"
                    prop="position">
        <zy-select width="296"
                   node-key="id"
                   :props="props"
                   v-model="form.position"
                   :data="position"
                   placeholder="请选择菜单位置"></zy-select>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-input">
        <el-input placeholder="请输入排序"
                  v-model="form.sort"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="所属区域"
                    prop="areaId">
        <zy-select width="296"
                   node-key="id"
                   :props="{ children: 'children', label: 'name' }"
                   v-model="form.areaId"
                   :data="areaId"
                   placeholder="请选择所属区域"></zy-select>
      </el-form-item>
      <el-form-item label="上级菜单">
        <zy-select width="296"
                   node-key="id"
                   :props="{ children: 'children', label: 'name' }"
                   v-model="form.parentId"
                   :data="parentId"
                   placeholder="请选择上级菜单"></zy-select>
      </el-form-item>
      <el-form-item label="是否显示"
                    class="form-input">
        <el-radio-group v-model="form.isShow">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="模块路径"
                    class="form-title">
        <el-input placeholder="请输入模块路径"
                  v-model="form.infoUrl"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="H5模块路径"
                    class="form-title">
        <el-input placeholder="请输入H5模块路径"
                  v-model="form.infoUrl2"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="图标"
                    class="form-icon">
        <el-upload class="form-icon-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <img v-if="file.fullUrl"
               :src="file.fullUrl"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="选中图标"
                    class="form-icon">
        <el-upload class="form-icon-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="selectImgUpload"
                   :show-file-list="false">
          <img v-if="selectFile.fullUrl"
               :src="selectFile.fullUrl"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="备注信息"
                    class="form-title">
        <el-input placeholder="请输入备注信息"
                  v-model="form.remarks"
                  clearable>
        </el-input>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'moduleNew',
  data () {
    return {
      menu: [],
      form: {
        title: '',
        iconUrl: '',
        type: '',
        sort: '',
        infoUrl: '',
        infoUrl2: '',
        position: '',
        isShow: '1',
        areaId: '',
        selectIconUrl: '',
        parentId: '',
        remarks: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入模块名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择所属模块', trigger: 'blur' }
        ],
        position: [
          { required: true, message: '请选择菜单位置', trigger: 'blur' }
        ],
        areaId: [
          { required: true, message: '请选择所属区域', trigger: 'blur' }
        ]
      },
      props: {
        children: 'children',
        label: 'value'
      },
      file: {},
      selectFile: {},
      type: [],
      position: [],
      areaId: [],
      parentId: []
    }
  },
  props: ['id'],
  mounted () {
    this.dictionaryPubkvs()
    this.areaTree()
    if (this.id) {
      this.moduleInfo()
    }
  },
  watch: {
    'form.areaId' (val) {
      if (val) {
        this.moduleTree()
      }
    }
  },
  methods: {
    async areaTree () {
      const res = await this.$api.appManagement.areaTree()
      var { data } = res
      this.areaId = data
    },
    async moduleTree () {
      const res = await this.$api.appManagement.moduleTree({ areaId: this.form.areaId })
      var { data } = res
      this.parentId = data
    },
    async moduleInfo () {
      const res = await this.$api.appManagement.moduleInfo(this.id)
      var { data: { title, iconUrl, type, sort, infoUrl, infoUrl2, position, isShow, areaId, selectIconUrl, parentId, remarks } } = res
      this.form.title = title
      this.form.iconUrl = iconUrl
      this.form.type = type
      this.form.sort = sort
      this.form.infoUrl = infoUrl
      this.form.infoUrl2 = infoUrl2
      this.form.position = position
      this.form.isShow = isShow
      this.form.areaId = areaId
      this.form.parentId = parentId
      this.form.selectIconUrl = selectIconUrl
      this.form.remarks = remarks
      if (iconUrl) {
        const arr = {}
        arr.fullUrl = iconUrl
        const obj = iconUrl.lastIndexOf('/')
        arr.shortName = iconUrl.substr(obj + 1)
        this.file = arr
      }
      if (selectIconUrl) {
        const arr = {}
        arr.fullUrl = selectIconUrl
        const obj = selectIconUrl.lastIndexOf('/')
        arr.shortName = selectIconUrl.substr(obj + 1)
        this.selectFile = arr
      }
    },
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'module_type,module_position_type'
      })
      var { data } = res
      this.type = data.module_type
      this.position = data.module_position_type
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const param = new FormData()
      param.append('file', files.file)
      this.$api.general.fileImg(param).then(res => {
        var { data } = res
        this.file = data
      })
    },
    selectImgUpload (files) {
      const param = new FormData()
      param.append('file', files.file)
      this.$api.general.fileImg(param).then(res => {
        var { data } = res
        this.selectFile = data
      })
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var superior = this.form.parentId
          if (this.form.superior === '') {
            superior = 0
          }
          var url = '/module/add'
          if (this.id) {
            url = '/module/edit'
          }
          this.$api.systemSettings.generalAdd(url, {
            id: this.id,
            parentId: superior,
            title: this.form.title,
            iconUrl: this.file.shortName,
            infoUrl2: this.form.infoUrl2,
            type: this.form.type,
            sort: this.form.sort,
            infoUrl: this.form.infoUrl,
            position: this.form.position,
            isShow: this.form.isShow,
            areaId: this.form.areaId,
            selectIconUrl: this.selectFile.shortName,
            remarks: encodeURIComponent(this.form.remarks)
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.module-new {
  width: 692px;
  height: 100%;
  padding: 24px 40px;

  .form-icon {
    width: 296px;

    .form-icon-uploader {
      width: 128px;
      height: 128px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: #199bc5;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 128px;
        height: 128px;
        line-height: 128px;
        text-align: center;
      }

      .user-img {
        width: 128px;
        height: 128px;
        display: block;
      }
    }
  }
}
</style>
