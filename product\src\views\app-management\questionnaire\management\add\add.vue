<template>
  <div class="vote-add">
    <step :active="active"></step>
    <base-form v-show="active === 1" ref="baseForm"></base-form>
    <page-settings v-show="active === 2" ref="pageSettings"></page-settings>
    <vote-item v-show="active === 3" ref="voteItem"></vote-item>
    <footerBtn :active.sync="active" @submit="handleSubmit"></footerBtn>
  </div>
</template>

<script>
import _ from 'lodash'
import step from './widget/step'
import baseForm from './widget/base-form'
import pageSettings from './widget/page-settings'
import voteItem from './widget/vote-item'
import footerBtn from './widget/footer-btn'
export default {
  name: 'questionnaire-add',
  components: { step, footerBtn, pageSettings, voteItem, baseForm },
  data () {
    return {
      active: 1,
      isEdit: false
    }
  },
  created () {
    const id = this.$route.query.id
    if (id) {
      this.getInfo(id)
      this.isEdit = true
    } else {
      this.isEdit = false
    }
    console.log(this.isEdit)
  },
  inject: ['tabDelJump'],
  methods: {
    getInfo (id) {
      this.$api.appManagement.questionnaire.info(id).then(res => {
        const {
          title, startTime,
          isShare, isPublishNotices,
          endTime, isPublish,
          description,
          isAlluser, questionnaireUser,
          publishPlatform,
          attachmentList
        } = res.data
        let { questionListVo } = res.data
        // 基本信息回显
        this.$refs.baseForm.form = {
          title,
          time: [startTime, endTime],
          isPublish,
          isAlluser,
          publishPlatform: publishPlatform.split(',').map(v => parseInt(v)),
          isShare,
          isPublishNotices,
          description
        }
        if (isAlluser === 0 && questionnaireUser.length !== 0) {
          this.$refs.baseForm.userData = questionnaireUser
        }
        // 页面信息回显
        if (attachmentList) {
          this.$refs.pageSettings.form = {
            files: attachmentList.filter(v => v.moduleType === 'bigImageforQS'),
            files1: attachmentList.filter(v => v.moduleType === 'bigImageforQSBG')
          }
        }
        questionListVo = questionListVo.map(el => {
          return {
            choiceText: el.answersListVo.map(item => {
              return item.choiceText
            }),
            question: el.question,
            answerType: el.answerType,
            isMust: el.isMust
          }
        })
        this.$refs.voteItem.formList = questionListVo
        // 问卷调查选项回显
      })
    },
    handleSubmit () {
      // 第一步的问卷基本信息
      const isForm = this.$refs.baseForm.validForm()
      if (!isForm) {
        return this.$message.warning('基本信息有必填项未填写')
      }
      const data = _.cloneDeep(this.$refs.baseForm.form)
      data.startTime = data.time[0]
      data.endTime = data.time[1]
      data.publishPlatform = data.publishPlatform.join(',')
      delete data.time
      if (data.isAlluser === 0) {
        data.questionnaireUserIds = this.$refs.baseForm.userData.map(v => v.userId).join(',')
      }
      // 第二步的文件页面设置
      const isSettings = this.$refs.pageSettings.validForm()
      if (!isSettings) {
        return this.$message.warning('页面配置有必填项未填写')
      }
      const settingData = this.$refs.pageSettings.form
      data.attachmentIds = settingData.files.map(v => v.id).join(',')
      if (settingData.files1.length > 0) {
        data.attachmentIds = `${data.attachmentIds},${settingData.files1.map(v => v.id).join(',')}`
      }
      Object.assign(data, settingData)
      delete data.files
      delete data.files1
      // 处理 第三步
      const qsForm = _.cloneDeep(this.$refs.voteItem.formList)
      if (!qsForm.length) {
        return this.$message.warning('请至少录入一个问题')
      }
      const questionCheck = this.$refs.voteItem.validForm()
      if (!questionCheck) {
        return this.$message.warning('问题录入有必填项未填写')
      }
      qsForm.forEach((item, index) => {
        item.sort = index + 1
        if (item.choiceText) {
          item.choiceText = item.choiceText.join('|')
        }
      })
      data.questionnaireFormList = qsForm
      const id = this.$route.query.id
      if (id) {
        data.id = id
        this.$api.appManagement.questionnaire.edit(data).then(res => {
          this.handleClose()
        })
      } else {
        this.$api.appManagement.questionnaire.add(data).then(res => {
          this.handleClose()
        })
      }
    },
    // 关闭页面
    handleClose () {
      const { id, toId, mid } = this.$route.query
      if (id) {
        this.$message.success('编辑问卷调查成功')
        this.tabDelJump(mid, toId)
      } else {
        if (this.$route.query.mid) {
          this.$confirm('新增问卷调查成功, 是否离开此页面?', '提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'warning'
          }).then(() => {
            this.tabDelJump(mid, toId)
          }).catch(() => {
            this.$refs.baseForm.reset()
            this.$refs.userData = []
            this.$refs.pageSettings.reset()
            this.$refs.voteItem.formList = []
          })
        } else {
          this.$refs.baseForm.reset()
          this.$refs.userData = []
          this.$refs.pageSettings.reset()
          this.$refs.voteItem.formList = []
        }
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.vote-add {
  width: 100%;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);
  padding: 20px 30px;
}
</style>
