<html>
<!--单个的会场排座-->

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <title>座位布局</title>
  <!-- 引用css -->
  <link rel="stylesheet" href="js/icheck/skins/all.css">
  <link rel="stylesheet" href="css/setSeats.css">
  <link rel="stylesheet" href="plugins/easyui-1.7.0/themes/default/easyui.css">
  <link rel="stylesheet" href="plugins/easyui-1.7.0/themes/icon.css">
</head>

<body>

  <div id="seatInfo"
    style="position:relative; float: left;width: 300px;height: 100%;overflow-x: hidden;overflow-y: auto; background: #FFFFFF; padding-top: 10px; border: 2px solid #f4f4f4;">
    <h3 style="padding-left: 20px; margin: 0;">座位布局</h3>
	<div style="position: relative; top: 10px; padding-left: 20px;">
	  <div class="opt">
	    <input class="magic-radio" type="radio" name="setPlace" id="hzxRoom" value="1">
	    <label for="hzxRoom">回字形会议室</label>
	  </div>
	  <div style="height: 40px;line-height: 36px; margin-left: 30px;">
	    <span>
	      <label style="position: relative; top: 2px; left: 2px; width: 200px;">座位数量</label>
	      <input id="initNum" class="inputText" type="text" value="" min="1"
	        style="position: relative; top: 2px; left: 18px; width: 50px;height:30px;text-align: center;">
	    </span>
	  </div>
	  <div style="height: 40px;line-height: 36px; margin-left: 30px;">
	    <span>
	      <label style="position: relative; top: 2px; left: 2px; width: 200px;">前端座位数</label>
	      <input id="frontNum" class="inputText" type="text" value="" min="1"
	        style="position: relative; top: 2px; left: 2px; width: 50px;height:30px;text-align: center;">
	    </span>
	  </div>
	  <div style="height: 40px;line-height: 36px; margin-left: 30px;">
	    <span>
	      <label style="position: relative; top: 2px; left: 2px; width: 200px;">后端座位数</label>
	      <input id="afterNum" class="inputText" type="text" value="" min="1"
	        style="position: relative; top: 2px; left: 2px; width: 50px;height:30px;text-align: center;">
	    </span>
	  </div>
	  <div class="opt">
	    <input class="magic-radio" type="radio" name="setPlace" id="jxRoom" value="2">
	    <label for="jxRoom">矩形会议室</label>
	  </div>
	  <div style="height: 40px;line-height: 36px; margin-left: 30px;">
	    <span>
	      <label style="position: relative; top: 2px; left: 2px; width: 200px;">座位行数</label>
	      <input id="rowNum" class="inputText" type="text" value="" min="1"
	        style="position: relative; top: 2px; left: 18px; width: 50px;height:30px;text-align: center;">
	    </span>
	  </div>
	  <div style="height: 40px;line-height: 36px; margin-left: 30px;">
	    <span>
	      <label style="position: relative; top: 2px; left: 2px; width: 200px;">座位列数</label>
	      <input id="colNum" class="inputText" type="text" value="" min="1"
	        style="position: relative; top: 2px; left: 18px; width: 50px;height:30px;text-align: center;">
	    </span>
	  </div>
	</div>
	<hr style="border: 1px solid #f4f4f4; margin-top: 24px;" />
	<h3 style="padding-left: 20px;margin: 0;">设置座位号</h3>
	<div style="height: 40px;line-height: 36px;margin-top: 15px; padding-left: 25px;">
	  <select id="ruleSelect" class="easyui-combobox custom-select" data-options="editable:false" panelHeight="240px"
	    name="state" style="width:208px; height: 44px; margin-left: 20px;">
		<option value="1">规则1</option>
		<option value="2">规则2</option>
		<option value="3">规则3</option>
		<option value="4">规则4</option>
	  </select>
	</div>
	<div id="setNumOne" class="btn btn-info">设置为1号座位</div>
	<h3 style="padding-left: 20px;margin-bottom: 0;">当前座位</h3>
	<div style="height: 40px;line-height: 36px;margin-top: 15px;padding-left: 25px;">
		<select id="setsCT" class="easyui-combobox custom-select" data-options="editable:false" panelHeight="240px"
		  name="state" style="width:100px; height: 44px;">
		</select>
		<span>
		  <input id="setsRow" class="inputText" type="text" min="1"
			style="position: relative; top:0; left: -2px; width: 40px;height:44px;text-align: center;">
		  <label>排&nbsp;&nbsp;</label>
		  <input id="setsNum" class="inputText" type="text" min="1"
			style="position: relative; top:0; left: -2px; width: 40px;height:44px;text-align: center;">
		  <label>座</label>
		</span>
	</div>
	<div style="height: 40px;line-height: 36px; margin-top: 10px;padding-left: 25px;">
	  <span>
	    <label style="position: relative; top: 2px; left: 2px;">顺&nbsp;序&nbsp;号：</label>
	    <input id="editSetsNum" class="inputText" type="text" onchange="num(this)"
	      style="position: relative; left: -2px; top: 2px; width: 162px;height:44px;text-align: center;">
	  </span>
	</div>
	<hr style="border: 1px solid #f4f4f4; margin-top: 24px;" />
	<h3 style="padding-left: 20px;margin-bottom: 12px; margin-top: 5px;">座位类型</h3>
	<div class="table-info" style="height: 180px;line-height: 36px;text-align: center;">
		<div class="table-header">
			<p>类型名称</p><p>标记颜色</p><p>操作</p>
		</div>
		<div class="table-body">
		</div>
	</div>
	<div style="height: 40px;line-height: 36px;margin-top: 20px; text-align: center;">
		<span id="bc" class="btn btn-info">保存</span>
		<span id="sc" class="btn btn-info">删除</span>
		<span id="lcw" class="btn btn-info">另存为</span>
	</div>
  </div>
  <div id="setsDiv" style="position: absolute; left: 300px; height: 100%; padding-top: 10px;">
    <div style="width: 100%;height: 36px;display: inline-flex; white-space: nowrap;">
      <div id="delSets" class="btn btn-info">删除</div>
      <div id="allSel" class="btn btn-info">全选</div>
      <div id="showSXH" class="btn btn-info" onclick="showSeatNoOrSort(0)">显示顺序号</div>
      <div id="showZWH" class="btn btn-info" onclick="showSeatNoOrSort(1)">显示座位号</div>
      <!-- <div id="saveSets" class="btn btn-info">保存</div> -->
    </div>
	<div class="radioBox">
		
	</div>
    <div class="box" onmousedown="boxOnMouse(event);">
      <span class="areaTip"></span>
      <!--<img id="bgImg">-->
    </div>
  </div>

  <!--begain 标签内容弹出框-->
  <div id="inputWin" class="signs_content">
    <input type="text" id="sgContent" style="width: 100%;height:30px;text-align: left; margin-bottom: 5px;">
    <label style="position: relative; left: -25px;">宽</label>
    <input id="signW" type="number" min="15"
      style="width: 75px;height:25px;text-align: center;position: relative; left: -27px;">
    <label style="position: relative; left: -29px;">高</label>
    <input id="signH" type="number" min="15"
      style="width: 76px;height:25;text-align: center; position: relative; left: -31px;">
    <div id="confirm" style="position: absolute;top: 39px;right: 5px;"><button style="height: 28px;">确定</button></div>
  </div>
  <!--end 标签内容弹出框-->
  <!--begain 颜色选择弹出框-->
  <div id="colorWin" class="color_content">
	  <div class="opt">
	    <input class="magic-radio" type="radio" name="zoneColor" id="#f44336" value="#f44336">
	    <label for="#f44336"><p style="width: 100%; height: 22px; background-color: #f44336;"></p></label>
	  </div>
	  <div class="opt">
	    <input class="magic-radio" type="radio" name="zoneColor" id="#9c27b0" value="#9c27b0">
	    <label for="#9c27b0"><p style="width: 100%; height: 22px; background-color: #9c27b0;"></p></label>
	  </div>
	  <div class="opt">
	    <input class="magic-radio" type="radio" name="zoneColor" id="#00bcd4" value="#00bcd4">
	    <label for="#00bcd4"><p style="width: 100%; height: 22px; background-color: #00bcd4;"></p></label>
	  </div>
	  <div class="opt">
	    <input class="magic-radio" type="radio" name="zoneColor" id="#3f51b5" value="#3f51b5">
	    <label for="#3f51b5"><p style="width: 100%; height: 22px; background-color: #3f51b5;"></p></label>
	  </div>
	  <div class="opt">
	    <input class="magic-radio" type="radio" name="zoneColor" id="#ff9800" value="#ff9800">
	    <label for="#ff9800"><p style="width: 100%; height: 22px; background-color: #ff9800;"></p></label>
	  </div>
	  <div class="opt">
	    <input class="magic-radio" type="radio" name="zoneColor" id="#03a9f4" value="#03a9f4">
	    <label for="#03a9f4"><p style="width: 100%; height: 22px; background-color: #03a9f4;"></p></label>
	  </div>
	  <div class="opt">
	    <input class="magic-radio" type="radio" name="zoneColor" id="#009688" value="#009688">
	    <label for="#009688"><p style="width: 100%; height: 22px; background-color: #009688;"></p></label>
	  </div>
	  <div class="opt">
	    <input class="magic-radio" type="radio" name="zoneColor" id="#ff0081" value="#ff0081">
	    <label for="#ff0081"><p style="width: 100%; height: 22px; background-color: #ff0081;"></p></label>
	  </div>
  </div>
  <!--end 颜色选择弹出框-->
  <div id="cloneDiv"></div>
  <div id="images" style="display: none;"></div>

  <!-- 引用js -->
  <script src="plugins/axios/axios.min.js"></script>
  <script src="plugins/axios/qs.min.js"></script>
  <script type="text/javascript" src="plugins/jquery/jqueryMain.js"></script>
  <script type="text/javascript" src="js/drag/jquery.event.drag-2.2.js"></script>
  <script type="text/javascript" src="js/drag/jquery.event.drag.live-2.2.js"></script>
  <script type="text/javascript" src="js/drag/jquery.event.drop-2.2.js"></script>
  <script type="text/javascript" src="js/drag/jquery.event.drop.live-2.2.js"></script>
  <script type="text/javascript" src="js/drag/excanvas.min.js"></script>
  <script type="text/javascript" src="js/icheck/icheck.js"></script>
  <script type="text/javascript" src="plugins/easyui-1.7.0/jquery.easyui.min.js"></script>
  <script type="text/javascript" src="plugins/layui/layer/layer.js"></script>
  <script type="text/javascript" src="js/base.js"></script>
  <script type="text/javascript" src="js/setSeatsMin_Ind.js"></script>
  <script type="text/javascript" src="js/setSeats_Ind.js"></script>
  <!-- html2canvas将Dom节点在Canvas里边画出来 -->
  <script src="js/createPic/html2canvas.js"></script>
  <!-- 将canvas图片保存成图片 -->
  <!-- <script src="js/createPic/moment.js"></script> -->
  <script src="js/createPic/canvas2image.js"></script>
  <script src="js/createPic/base64.js"></script>
</body>

</html>