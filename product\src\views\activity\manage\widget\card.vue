<template>
  <div class="activity-card">
    <div class="card-btn">
      <el-button type="success" size="mini" @click="handlePDF">导出成PDF</el-button>
    </div>
    <div class="card-content" ref="cardRef">
      <h4>{{info.meetName}}</h4>
      <div class="card-detail">
        <div class="detail-item">
          <div class="detail-item-label">活动时间</div>
          <div class="detail-item-content">
            {{info.meetStartTime}}到{{info.meetEndTime}}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-item-label">活动地点</div>
          <div class="detail-item-content">{{info.address}}</div>
        </div>
        <div class="detail-item">
          <div class="detail-item-label">签到口令</div>
          <div class="detail-item-content">{{info.signInCommand}}</div>
        </div>
      </div>
      <vueQr :text="url" :size="200"></vueQr>
    </div>
  </div>
</template>

<script>
import vueQr from 'vue-qr'
import html2canvas from 'html2canvas'
import JsPDF from 'jspdf'
export default {
  components: { vueQr },
  props: {
    id: String
  },
  data() {
    return {
      info: {},
      url: ''
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    // 获取详情
    getInfo() {
      this.$api.activity.qrcode(this.id).then(res => {
        this.url = res.data
      })
      this.$api.activity.info(this.id).then(res => {
        this.info = res.data
      })
    },
    handlePDF() {
      html2canvas(this.$refs.cardRef, {
        useCORS: true,
        allowTaint: false,
        logging: false,
        letterRendering: true
      }).then(canvas => {
        var contentWidth = canvas.width
        var contentHeight = canvas.height
        // 方向默认竖直，尺寸ponits，格式a4[595.28,841.89]
        var imgWidth = 841.89 - 40
        var imgHeight = (841.89 - 40) / contentWidth * contentHeight
        // 生成canvas截图，1表示生成的截图质量（0-1）
        const pageData = canvas.toDataURL('image/jpeg', 1)
        // new JsPDF接收三个参数，landscape表示横向，（默认不填是纵向），打印单位和纸张尺寸
        const PDF = new JsPDF('landscape', 'pt', 'a4')
        const position = (595.28 - imgHeight) / 2
        // 当内容不超过a4纸一页的情况下
        PDF.addImage(pageData, 'JPEG', 20, position, imgWidth, imgHeight)
        // 调用save方法生成pdf文件
        PDF.save(`${this.info.meetName}${new Date().getTime()}` + '.pdf')
      })
    }
  }
}
</script>

<style lang="scss">
.activity-card {
  width: 765px;
  padding: 10px;
  .card-btn {
    height: 45px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .card-content {
    display: flex;
    flex-wrap: wrap;
    box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);
    h4 {
      width: 100%;
      text-align: center;
      font-size: 18px;
      color: #333;
      line-height: 45px;
    }
    .card-detail {
      width: calc(100% - 240px);
      margin-right: 20px;
      border-top: 1px solid #dcdee6;
      margin-top: 24px;
      .detail-item {
        width: 100%;
        display: flex;
        line-height: 45px;
        border-bottom: 1px solid #dcdee6;
        .detail-item-label {
          width: 100px;
          text-align: right;
          flex-shrink: 0;
          border-right: 1px solid #dcdee6;
          padding: 0 10px;
        }
        .detail-item-content {
          width: calc(100% - 100px);
          padding: 0 10px;
        }
      }
    }
  }
}
</style>
