// 导入封装的方法
import Vue from 'vue'
import {
  get,
  post,
  postform,
  postformProgress,
  fileRequest,
  exportFile,
  loginUc,
  baseURL,
  yunpan,
  dataCenter
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const general = {
  // 登陆
  export (params) {
    exportFile('/socialanalyse/exportArticleClass', params)
  },
  dsjuser () {
    return JSON.parse(sessionStorage.getItem('BigDataUser' + Vue.prototype.$logo()))
  },
  dataCenter () {
    return dataCenter
  },
  baseURL () {
    return baseURL
  },
  yunpan () {
    return yunpan
  },
  generalAdd (url, params) {
    return post(url, params)
  },
  loginUc (params) {
    console.log(params)
    return post(`${loginUc}/login?`, params)
  },
  login (params) {
    return post('/user/login', params)
  },
  loginimg (params) {
    return post('/loginimg/show', params)
  },
  logout (params) {
    return post('/user/logout', params)
  },
  sendmessage (params) { //  发送短信
    return post('/shortcode/send', params)
  },
  editpwd (params) { //  修改密码
    return post('/wholeuser/editpwd/byaccount', params)
  },
  enable (params) {
    return post('/shortcode/enable', params)
  },
  changearea (params) {
    return post('/changearea', params)
  },
  meetControlPeople (params) {
    return post(`lzt/meetcontrol/findControl?meetId=${params}`)
  },
  pointrees (params) {
    return post(`/pointrees/${params}`)
  },
  meetpointrees (params) {
    return post(`/meetMeeting/pointTrees/${params}`)
  },
  pointreeUsers (params) {
    return post('/pointree/users', params)
  },
  deleteApi (params) {
    return post('/talkroup/out', params)
  },
  pointreeGroupUsers (params) {
    return post('/meetMeeting/treeNodeUsers', params)
  },
  file (params) {
    return postform('/file/uploadimg4name', params)
  },
  fileImg (params) {
    return postform('/file/uploadimg', params)
  },
  apptoken (params) {
    // return postform('/gain/apptoken?', params)
    return postform(`${loginUc}/gain/apptoken?`, params)
  },
  getToken (params) {
    return post('http://1.192.128.76:18083/front/getToken', params)
  },
  getokearams (params) {
    return post('/valley/getokearams', params)
  },
  websiteLogin (params) {
    return post('/valley/login?', params)
  },
  searchData (params) {
    return post('/pushmsg/search', params)
  },
  searchPush (params) {
    return post('/socket/push', params)
  },
  exportFields (params) {
    return post('/export/fields?', params)
  },
  exportDatas (params) {
    return post('/export/datas', params)
  },
  testvoice (params) {
    return postform('/demo/testvoice', params)
  },
  // 履职
  // 履职档案列表
  findDutys (params) {
    return postform('/duty/findDutys?', params)
  },
  // 履职档案列表详情
  getDutyDetail (params) {
    return postform('/duty/getDutyDetail?', params)
  },
  // 我的履职档案详情
  getDutyDetailByUserId (params) {
    return postform('/duty/getDutyDetailByUserId', params)
  },
  // 生成履职档案
  generateDuty (params) {
    return postform('/duty/generateDuty?', params)
  },
  // 大数据登录
  noauthLogin (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/noauth/login`, params)
  },
  // 新增浏览
  browSeAdd (params) {
    return post('/browse/save', params)
  },
  // 获取公开的配置管理
  nologin (params) {
    return post('/readonfig/nologin?', params)
  },
  // 获取不公开的配置管理
  readonfig (params) {
    return post('/readonfig', params)
  },
  uploadFile (params, callback, id) {
    return postformProgress('/attachment/uploadFile', params, 880000, callback, id)
  },
  downloadFile (params, text) {
    fileRequest('/attachment/downloadFile?', params, text)
  },
  // 获取公开的配置管理
  getRedPointNumByModule (params) {
    return post('/redPoint/getRedPointNumByModule', params)
  },
  shortcodeVerify (params) {
    return post('/shortcode/verify?', params)
  },
  shortcodeSend (params) {
    return post('/shortcode/send?', params)
  },
  shortcodeEnable (params) {
    return post('/shortcode/enable?', params)
  }
}
export default general
