<!DOCTYPE html>
<HTML>
<HEAD>
	<TITLE> ZTREE DEMO - beforeClick / onClick</TITLE>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="../../../css/demo.css" type="text/css">
	<link rel="stylesheet" href="../../../css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="../../../js/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.core.js"></script>
	<!--  <script type="text/javascript" src="../../../js/jquery.ztree.excheck.js"></script>
	  <script type="text/javascript" src="../../../js/jquery.ztree.exedit.js"></script>-->
	<SCRIPT type="text/javascript">
		<!--
		var setting = {
			data: {
				key: {
					title:"t"
				},
				simpleData: {
					enable: true
				}
			},
			callback: {
				beforeClick: beforeClick,
				onClick: onClick
			}
		};

		var zNodes =[
			{ id:1, pId:0, name:"ordinary parent", t:"I am ordinary, just click on me", open:true},
			{ id:11, pId:1, name:"leaf node 1-1", t:"I am ordinary, just click on me"},
			{ id:12, pId:1, name:"leaf node 1-2", t:"I am ordinary, just click on me"},
			{ id:13, pId:1, name:"leaf node 1-3", t:"I am ordinary, just click on me"},
			{ id:2, pId:0, name:"strong parent", t:"You can click on me, but you can not click on the sub-node!", open:true},
			{ id:21, pId:2, name:"leaf node 2-1", t:"You can't click on me..", click:false},
			{ id:22, pId:2, name:"leaf node 2-2", t:"You can't click on me..", click:false},
			{ id:23, pId:2, name:"leaf node 2-3", t:"You can't click on me..", click:false},
			{ id:3, pId:0, name:"weak parent", t:"You can't click on me, but you can click on the sub-node!", open:true, click:false },
			{ id:31, pId:3, name:"leaf node 3-1", t:"please click on me.."},
			{ id:32, pId:3, name:"leaf node 3-2", t:"please click on me.."},
			{ id:33, pId:3, name:"leaf node 3-3", t:"please click on me.."}
		];

		var log, className = "dark";
		function beforeClick(treeId, treeNode, clickFlag) {
			className = (className === "dark" ? "":"dark");
			showLog("[ "+getTime()+" beforeClick ]&nbsp;&nbsp;" + treeNode.name );
			return (treeNode.click != false);
		}
		function onClick(event, treeId, treeNode, clickFlag) {
			showLog("[ "+getTime()+" onClick ]&nbsp;&nbsp;clickFlag = " + clickFlag + " (" + (clickFlag===1 ? "single selected": (clickFlag===0 ? "<b>cancel selected</b>" : "<b>multi selected</b>")) + ")");
		}		
		function showLog(str) {
			if (!log) log = $("#log");
			log.append("<li class='"+className+"'>"+str+"</li>");
			if(log.children("li").length > 8) {
				log.get(0).removeChild(log.children("li")[0]);
			}
		}
		function getTime() {
			var now= new Date(),
			h=now.getHours(),
			m=now.getMinutes(),
			s=now.getSeconds();
			return (h+":"+m+":"+s);
		}

		$(document).ready(function(){
			$.fn.zTree.init($("#treeDemo"), setting, zNodes);
		});
		//-->
	</SCRIPT>
</HEAD>

<BODY>
<h1>Control of Click Node</h1>
<h6>[ File Path: core/click.html ]</h6>
<div class="content_wrap">
	<div class="zTreeDemoBackground left">
		<ul id="treeDemo" class="ztree"></ul>
	</div>
	<div class="right">
		<ul class="info">
			<li class="title"><h2>1, 'beforeClick / onClick' callback function</h2>
				<ul class="list">
				<li>Use 'beforeClick / onClick' callback function can be achieved with the click on the various functions. This simple demo shows how to monitor the click event.</li>
				<li><p><span class="highlight_red">Try pressing the <b>Ctrl</b> or <b>Cmd</b> key for multi-node selection and deselection.</span><br/>
					click log:<br/>
					<ul id="log" class="log"></ul></p>
				</li>
				</ul>
			</li>
			<li class="title"><h2>2, Explanation of setting</h2>
				<ul class="list">
				<li class="highlight_red">You need to set attributes about setting.callback.beforeClick and setting.callback.onClick, see the API documentation for more related contents.</li>
				</ul>
			</li>
			<li class="title"><h2>3, Explanation of treeNode</h2>
				<ul class="list">
				<li>No special requirements on the node data, the user can add custom attributes.</li>
				</ul>
			</li>
		</ul>
	</div>
</div>
</BODY>
</HTML>