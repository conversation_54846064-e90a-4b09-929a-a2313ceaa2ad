import store from '@/store'
import router from '@/router'
import { Message } from 'element-ui'
import { Base64 } from 'js-base64'
import Docxtemplater from 'docxtemplater'
import <PERSON>z<PERSON>ip from 'pizzip'
import JSZipUtils from 'jszip-utils'
import { saveAs } from 'file-saver'

const utils = {
  exportclick (e) {
    console.log(e)
    const docxsrc = './deliberate.docx'
    const wordName = e.auditName
    JSZipUtils.getBinaryContent(docxsrc, function (error, content) {
      if (error) {
        throw error
      }
      const zip = new PizZip(content)
      const doc = new Docxtemplater().loadZip(zip)
      doc.setData({
        ...e,
        auditName: e.auditName ? e.auditName : '',
        department: e.department ? e.department : '',
        createTime: e.createTime ? e.createTime : '',
        meetingName: e.meetingName ? e.meetingName : '',
        meetingSite: e.meetingSite ? e.meetingSite : '',
        meetingTime: e.meetingTime ? e.meetingTime : '',
        participant: e.participant ? e.participant : '',
        opinionContent: e.opinionContent ? e.opinionContent : '',
        opinionContentTwo: e.opinionContentTwo ? e.opinionContentTwo : ''
      })
      try {
        doc.render()
      } catch (error) {
        const e = {
          message: error.message,
          name: error.name,
          stack: error.stack,
          properties: error.properties
        }
        console.log(JSON.stringify({
          error: e
        }))
        throw error
      }
      const file = doc.getZip().generate({
        type: 'blob',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      })
      saveAs(file, wordName)
    })
  },
  contrastExportClick (e) {
    const docxsrc = './contrast.docx'
    const wordTitle = e.title
    JSZipUtils.getBinaryContent(docxsrc, function (error, content) {
      if (error) {
        throw error
      }
      const zip = new PizZip(content)
      const doc = new Docxtemplater().loadZip(zip)
      doc.setData({
        ...e,
        title: e.title ? e.title : '',
        content: e.content ? e.content : ''
      })
      try {
        doc.render()
      } catch (error) {
        const e = {
          message: error.message,
          name: error.name,
          stack: error.stack,
          properties: error.properties
        }
        console.log(JSON.stringify({
          error: e
        }))
        throw error
      }
      const file = doc.getZip().generate({
        type: 'blob',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      })
      saveAs(file, wordTitle)
    })
  },
  emptyMenuParam (to) {
    return to.replace(/(\?|&)t=\d+/g, '')
  },
  openMenu (menu) {
    const menuLists = JSON.parse(localStorage.menuLists)
    menu = this.findMenuByTo(menuLists, menu.to) || menu
    store.commit('addTabsMenuItem', menu)
    router.replace(menu.to)
  },
  findMenuByTo (menuLists, to) {
    for (var i = 0; i < menuLists.length; i++) {
      const menu = menuLists[i]
      if (menu.to === to) {
        return menu
      } else {
        if (menu.children) {
          const r = this.findMenuByTo(menu.children, to)
          if (r) {
            return r
          }
        }
      }
    }
  },
  updateMenu (to) {
    to = this.emptyMenuParam(to)
    const tabsMenuList = store.state.tabsMenuList
    tabsMenuList.forEach((item, index) => {
      if (this.emptyMenuParam(item.to) === to) {
        store.commit('updateTabsMenuItem', { index, to })
      }
    })
  },
  updateMenuTable (to) {
    to = to.replace(/(\/operate)|(type=\w*&)/g, '')
    this.updateMenu(to)
  },
  updateMenuOperate (to, type) {
    to = to.replace(type, 'view')
    this.updateMenu(to)
  },
  closeMenu (to) {
    to = this.emptyMenuParam(to)
    const tabsMenuList = store.state.tabsMenuList
    if (tabsMenuList.length > 1) {
      tabsMenuList.forEach((item, index) => {
        if (this.emptyMenuParam(item.to) === to) {
          store.commit('deleteTabsMenuItem', index)
          to = tabsMenuList[Math.max(0, index - 1)].to
          router.replace(to)
        }
      })
    }
  },
  disabledType () {
    return ['view']
  },
  checkDisabledType (type) {
    return this.disabledType().includes(type.split('_')[0])
  },
  uploadFormat () {
    return ['doc', 'docx', 'xls', 'xlsx']
  },
  getUploadFormat () {
    return `仅支持${this.uploadFormat().join('、')}格式文件`
  },
  checkUploadFormat (file) {
    const suffix = file.name.split('.').reverse()[0]
    if (this.uploadFormat().includes(suffix)) {
      return true
    } else {
      Message.warning('不支持的文件类型')
      return false
    }
  },
  getUploadFile (file) {
    const docId = (file.uid || file.id) + ''
    const data = {
      doc: {
        docId: docId,
        title: file.name,
        fetchUrl: file.url
      },
      user: {
        uid: 'admin',
        nickName: '管理员',
        avatar: 'https://bisheng-upload.nodoc.cn/system/defaultAvatar.png'
      }
    }
    return `http://212.64.61.94/apps/editor/openPreview?data=${Base64.encode(JSON.stringify(data))}`
  },
  downloadFile (file) {
    const url = this.getUploadFile(file)
    window.open(url)
  },
  getObjByKeys (obj, arr) {
    const newObj = {}
    arr.forEach(item => {
      newObj[item] = obj[item]
    })
    return newObj
  },
  fillZero (i) {
    return i < 10 ? '0' + i : i
  },
  getCurrentDateTime () {
    const date = new Date()
    const Y = date.getFullYear() + '-'
    const M = this.fillZero(date.getMonth() + 1) + '-'
    const D = this.fillZero(date.getDate()) + ' '
    const h = this.fillZero(date.getHours()) + ':'
    const m = this.fillZero(date.getMinutes()) + ':'
    const s = this.fillZero(date.getSeconds())
    return Y + M + D + h + m + s
  },
  getCurrentDate () {
    return this.getCurrentDateTime.split(' ')[0]
  },
  getCurrentTime () {
    return this.getCurrentDateTime.split(' ')[1]
  },
  timestampToDateTime (timestamp) {
    const date = timestamp ? new Date(timestamp) : new Date()
    const Y = date.getFullYear() + '-'
    const M = this.fillZero(date.getMonth() + 1) + '-'
    const D = this.fillZero(date.getDate()) + ' '
    const h = this.fillZero(date.getHours()) + ':'
    const m = this.fillZero(date.getMinutes()) + ':'
    const s = this.fillZero(date.getSeconds())
    return Y + M + D + h + m + s
  },
  timestampToDate (timestamp) {
    return this.timestampToDateTime(timestamp).split(' ')[0]
  },
  timestampToTime (timestamp) {
    return this.timestampToDateTime(timestamp).split(' ')[1]
  },
  setDataGroup (oldData, count) {
    const group = Math.ceil(oldData.length / count)
    const newData = []
    for (let i = 0; i < group; i++) {
      newData.push(oldData.slice(i * count, (i + 1) * count))
    }
    return newData
  },
  getDateTimeDiff (start, end) {
    start = new Date(start).getTime()
    end = new Date(end).getTime()
    const diff = Math.abs(end - start) / 1000
    const day = Math.floor(diff / (24 * 60 * 60))
    const hour = Math.floor(diff % (24 * 60 * 60) / (60 * 60))
    const minute = Math.floor(diff % (60 * 60) / 60)
    const second = Math.floor(diff % 60)
    console.log(day, hour, minute, second)
  },
  sortArrayByName (array, name, sort = 1) {
    array.sort((m, n) => {
      return sort * (m[name] - n[name])
    })
    return array
  },
  objectToFormData (obj) {
    const formData = new FormData()
    Object.keys(obj).forEach(key => {
      if (obj[key] instanceof Array) {
        obj[key].forEach(item => {
          formData.append(key, item)
        })
        return
      }
      formData.append(key, obj[key])
    })
    return formData
  }
}
export default utils
