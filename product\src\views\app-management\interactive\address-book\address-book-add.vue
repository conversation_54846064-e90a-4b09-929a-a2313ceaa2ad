<template>
  <div class="address-book-add">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="姓名"
                    class="form-title">
        <el-input placeholder="请输入姓名"
                  v-model="form.name"
                  disabled
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-title"
                    prop="sort">
        <el-input placeholder="请输入排序"
                  v-model="form.sort"
                  clearable>
        </el-input>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'addressBookAdd',
  data () {
    return {
      form: {
        sort: '',
        name: ''
      },
      rules: {
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ]
      }
    }
  },
  props: ['businessId', 'businessType', 'userId', 'sort', 'name'],
  mounted () {
    this.form.sort = this.sort
    this.form.name = this.name
  },
  methods: {
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/wholeuser/chatersort?'
          this.$api.systemSettings.generalAdd(url, {
            businessId: this.businessId,
            userId: this.userId,
            businessType: this.businessType,
            sort: this.form.sort
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('addCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('addCallback')
    }
  }
}
</script>
<style lang="scss">
.address-book-add {
  width: 520px;
  height: 100%;
  padding: 24px 40px;
}
</style>
