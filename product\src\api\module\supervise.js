// 导入封装的方法
import {
  post,
  filedownload
} from '../http'
export default {
  // 待办
  upcomingList(params) {
    return post('/oversee/getBacklogList', params)
  },
  // 已办
  alreadyList(params) {
    return post('/oversee/getHaveDoneListVo', params)
  },
  // 报表
  postList(params) {
    return post('/oversee/getOverseeStatementVos', params)
  },
  // 编号
  getNumber() {
    return post('/oversee/getMaxNumber')
  },
  overseenodeAdd(params) {
    return post('/overseenode/add', params)
  },
  // 录入督办
  add(params) {
    return post('/oversee/add', params, {
      'Content-Type': 'application/json;charset=UTF-8'
    })
  },
  // 录入督办
  edit(params) {
    return post('/oversee/edit', params, {
      'Content-Type': 'application/json;charset=UTF-8'
    })
  },
  addManage(params) {
    return post('/overseetransact/add', params, {
      'Content-Type': 'application/json;charset=UTF-8'
    })
  },
  // 办理详情列表
  manageInfoList(id) {
    return post('/oversee/getOverseeProceeModel', {
      id
    })
  },
  // 流程图
  processImg(id) {
    return filedownload('/oversee/processimg', {
      id
    }, 'arraybuffer')
  },
  list(params) {
    return post('/oversee/list', params)
  },
  info(id) {
    return post(`/oversee/info/${id}`)
  },
  dels(ids) {
    return post('/oversee/dels', {
      ids
    })
  },
  // 送办/退回
  sendTask(params) {
    return post('/oversee/complateTask', params)
  },
  // 送办/退回状态查询
  nextStep(params) {
    return post('/oversee/getNextStep', params)
  },
  // 查询是否可以多选 是否可以退回
  canBack(params) {
    return post('/oversee/getIsSendBack', params)
  },
  getOfficeInfo(userId) {
    return post('/oversee/getOrgInfo', {
      userId
    })
  },
  addUrge(params) {
    return post('/overseerushdo/add', params)
  },
  countNumByYear(year) {
    return post('/oversee/getOverseeNumYearByMonth', {
      year
    })
  },
  orgAssignNum(yearMonth) {
    return post('/oversee/getOrgNumByMonth', {
      yearMonth
    })
  },
  orgManageNum(year) {
    return post('/oversee/getOrgNumByYear', {
      year
    })
  },
  read(params) {
    return post('/overseelookrecord/add', params)
  }
}
