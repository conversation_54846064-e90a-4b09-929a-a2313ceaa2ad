<template>
  <div class="zy-tabs">
    <div class="zy-tabs-left"
         v-if="show&&offset>0||offset!=0"
         @click.stop="tabsLeft"><i class="el-icon-d-arrow-left"></i></div>
    <div class="zy-tabs-right"
         v-if="show&&offset<biggest"
         @click.stop="tabsRight"><i class="el-icon-d-arrow-right"></i></div>
    <div class="zy-tabs-box">
      <div class="zy-tabs-item-list">
        <div :class="['zy-tabs-item',item.class?'zy-tabs-item-active':'']"
             v-for="(item, index) in tabsData"
             :key="index"
             @click="selected(item)">
          <div class="zy-tabs-item-number">{{item.number}}</div>
          <div class="zy-tabs-item-text">{{item.name}}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'zyTabs',
  data () {
    return {
      tabsData: [],
      show: false,
      offset: 0,
      biggest: 0,
      screenWidth: document.body.clientWidth,
      timer: false
    }
  },
  props: {
    value: {},
    tabsList: {
      type: Array,
      default: () => []
    },
    shift: {
      type: Number,
      default: 168
    },
    props: {
      type: Object,
      default: () => { }
    }
  },
  model: {
    prop: 'value',
    event: 'id'
  },
  created () {
    this.tabsCopyData(this.deepCopy(this.tabsList))
  },
  mounted () {
    this.biggestClick()
    // 绑定onresize事件 监听屏幕变化设置宽
    this.$nextTick(() => {
      this.screenWidth = document.body.clientWidth
    })
    window.onresize = () => {
      return (() => {
        window.screenWidth = document.body.clientWidth
        this.screenWidth = window.screenWidth
      })()
    }
  },
  watch: {
    value (val) {
      this.tabsData.forEach(item => {
        if (item.id === val) {
          this.selected(item)
        }
      })
    },
    tabsList (val) {
      this.tabsCopyData(this.deepCopy(this.tabsList))
    },
    tabsData (val) {
      if (val.length) {
        this.tabsData.forEach(item => {
          if (item.id === this.value) {
            this.selected(item)
          }
        })
      }
    },
    screenWidth (val) {
      if (!this.timer) {
        this.screenWidth = val
        this.timer = true
        this.biggestClick()
        setTimeout(() => {
          this.timer = false
        }, 400)
      }
    }
  },
  methods: {
    selected (data) {
      this.selectedClick(data)
      var arr = this.tabsData
      arr.forEach(item => {
        item.class = false
        if (item.id === data.id) {
          this.$emit('id', item.id)
          item.class = true
        }
      })
      setTimeout(() => {
        this.tabsBox()
      }, 300)
    },
    selectedClick (data) {
      var arr = []
      this.tabsList.forEach((item, index) => {
        if (!JSON.stringify(this.props) == '{}') {// eslint-disable-line
          if (data.id === item[this.props.id]) {
            arr = item
          }
        } else {
          if (data.id === item.id) {
            arr = item
          }
        }
      })
      this.$emit('tabs-click', arr)
    },
    biggestClick () {
      var tabBox = document.querySelector('.zy-tabs-box')
      var itemBox = document.querySelector('.zy-tabs-item-list')
      if (itemBox.offsetWidth > tabBox.offsetWidth) {
        this.show = true
        this.biggest = itemBox.offsetWidth - tabBox.offsetWidth
      } else {
        this.show = false
      }
    },
    tabsLeft () {
      var itemBox = document.querySelector('.zy-tabs-item-list')
      var offset = this.offset - this.shift
      if (this.offset - this.shift <= 0) {
        offset = 0
      }
      itemBox.style.transform = `translateX(-${offset}px)`
      this.offset = offset
    },
    tabsRight () {
      var itemBox = document.querySelector('.zy-tabs-item-list')
      var offset = this.offset + this.shift
      if (this.biggest < this.offset + this.shift) {
        offset = this.biggest
      }
      itemBox.style.transform = `translateX(-${offset}px)`
      this.offset = offset
    },
    tabsBox () {
      var tabBox = document.querySelector('.zy-tabs-box')
      var itemBox = document.querySelector('.zy-tabs-item-list')
      var item = document.querySelector('.zy-tabs-item-active')
      if (tabBox.offsetWidth < itemBox.offsetWidth) {
        itemBox.style.transform = 'translateX(0px)'
        itemBox.style.transitionDuration = '.4s'
        if (itemBox.offsetWidth === item.offsetLeft + item.offsetWidth) {
          this.offset = itemBox.offsetWidth - tabBox.offsetWidth
          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`
        } else if (tabBox.offsetWidth / 2 > itemBox.offsetWidth - item.offsetLeft) {
          this.offset = itemBox.offsetWidth - tabBox.offsetWidth
          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - tabBox.offsetWidth}px)`
        } else {
          if (item.offsetLeft - tabBox.offsetWidth / 2 < 0) {
            this.offset = 0
          } else {
            this.offset = item.offsetLeft - tabBox.offsetWidth / 2
          }
          itemBox.style.transform = `translateX(-${item.offsetLeft - tabBox.offsetWidth / 2}px)`
        }
      }
    },
    tabsCopyData (data) {
      this.initData(data)
      this.tabsData = data
    },
    initData (items) {
      items.forEach((item, index) => {
        if ((typeof item.id) === 'undefined') { // eslint-disable-line
          item.id = item[this.props.id]
        }
        if ((typeof item.name) === 'undefined') { // eslint-disable-line
          item.name = item[this.props.name]
        }
        if ((typeof item.number) === 'undefined') { // eslint-disable-line
          item.number = item[this.props.number]
        }
        if ((typeof item.class) === 'undefined') { // eslint-disable-line
          item.class = false
        }
        if (this.tabId === item.id) {
          item.class = true
        }
      })
    },
    deepCopy (data) {
      var t = this.type(data)
      var o
      var i
      var ni
      if (t === 'array') {
        o = []
      } else if (t === 'object') {
        o = {}
      } else {
        return data
      }
      if (t === 'array') {
        for (i = 0, ni = data.length; i < ni; i++) {
          o.push(this.deepCopy(data[i]))
        }
        return o
      } else if (t === 'object') {
        for (i in data) {
          o[i] = this.deepCopy(data[i])
        }
        return o
      }
    },
    type (obj) {
      var toString = Object.prototype.toString
      var map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'undefined',
        '[object Null]': 'null',
        '[object Object]': 'object'
      }
      return map[toString.call(obj)]
    }
  },
  beforeDestroy () {
    window.onresize = null
  }
}
</script>
<style lang="scss">
@import "./zy-tabs.scss";
</style>
