<template>
  <div class="information-column">
    <search-button-box @search-click="search"
                       :resetButton="false">
      <template slot="button">
        <el-button type="primary"
                   v-permissions="'auth:zyinfostructure:add'"
                   icon="el-icon-plus"
                   @click="newData">新增</el-button>
        <el-button type="primary"
                   icon="el-icon-delete"
                   v-permissions="'auth:zyinfostructure:dels'"
                   @click="deleteClick">删除</el-button>
      </template>
      <template slot="search">
        <el-input placeholder="请输入内容"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
    </search-button-box>
    <div class="information-box">
      <div class="information-tree-box">
        <div class="information-tree-text">选择栏目</div>
        <div class="information-tree">
          <zy-tree :tree="tree"
                   v-model="treeId"
                   :props="{ children: 'children', label: 'name'}"
                   @on-tree-click="choiceClick"></zy-tree>
        </div>
      </div>
      <div class="information-data-box">
        <div class="plenum-data">
          <zy-table>
            <el-table :data="tableData"
                      stripe
                      border
                      slot="zytable"
                      @select="selected"
                      @select-all="selectedAll">
              <el-table-column type="selection"
                               width="60"></el-table-column>
              <el-table-column label="序号"
                               width="90"
                               prop="sort">
              </el-table-column>
              <el-table-column label="名称"
                               prop="name">
              </el-table-column>
              <el-table-column label="上级"
                               prop="parentName">
              </el-table-column>
              <el-table-column label="是否置顶"
                               prop="isTop">
                <template slot-scope="scope">
                  <div>{{scope.row.isTop==1?'置顶':'不置顶'}}</div>
                </template>
              </el-table-column>
              <el-table-column label="是否APP显示"
                               prop="isPushApp">
              </el-table-column>
              <!-- <el-table-column label="是否自动推送置顶头条" width="190" prop="isPushTop">
              </el-table-column> -->
              <el-table-column label="操作"
                               v-if="$hasPermission(['auth:zyinfostructure:edit'])">
                <template slot-scope="scope">
                  <el-button @click="handleClick(scope.row)"
                             type="primary"
                             v-permissions="'auth:zyinfostructure:edit'"
                             plain
                             size="mini">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </zy-table>
        </div>
        <div class="paging_box">
          <el-pagination @size-change="howManyArticle"
                         @current-change="whatPage"
                         :current-page.sync="page"
                         :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                         :page-size.sync="pageSize"
                         background
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
    <zy-pop-up v-model="show"
               :title="id?'编辑栏目':'新增栏目'">
      <information-column-new :id="id"
                              @newCallback="newCallback"></information-column-new>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '../../../../mixins/tableData'
import informationColumnNew from './information-column-new'
export default {
  name: 'informationColumn',
  data () {
    return {
      keyword: '',
      treeId: '1',
      tree: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      id: '',
      show: false
    }
  },
  mixins: [tableData],
  components: {
    informationColumnNew
  },
  created () {
    this.informationColumn()
    this.informationColumnTree()
  },
  methods: {
    search () {
      this.informationColumn()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    choiceClick (item) {
      this.informationColumn()
    },
    async informationColumnTree () {
      const res = await this.$api.appManagement.informationColumnTree({ module: 1 })
      var { data } = res
      var arr = [{ children: [], id: '1', name: '所有' }]
      this.tree = arr.concat(data)
    },
    newCallback () {
      this.informationColumn()
      this.show = false
    },
    async informationColumn () {
      const res = await this.$api.appManagement.informationColumn({
        module: 1,
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        parentId: this.treeId
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
    },
    howManyArticle (val) {
      this.informationColumn()
    },
    whatPage (val) {
      this.informationColumn()
    },
    handleClick (row) {
      this.id = row.id
      this.show = true
    },
    deleteClick (row) {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的栏目, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.informationColumnDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async informationColumnDel (id) {
      const res = await this.$api.appManagement.informationColumnDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.informationColumn()
        this.informationColumnTree()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.information-column {
  height: 100%;
  width: 100%;

  .information-box {
    height: calc(100% - 64px);
    width: 100%;
    display: flex;

    .information-tree-box {
      width: 222px;
      height: 100%;

      .zy-tree {
        width: 222px;
        min-width: 222px;
      }

      .information-tree-text {
        height: 52px;
        line-height: 52px;
        padding-left: 24px;
        font-size: $textSize16;
        background-color: #e6e5e8;
      }

      .information-tree {
        height: calc(100% - 52px);
      }
    }

    .information-data-box {
      width: calc(100% - 222px);
      height: 100%;

      .plenum-data {
        height: calc(100% - 52px);
        width: 100%;
      }
    }
  }
}
</style>
