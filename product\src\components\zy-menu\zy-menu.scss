.zy-menu {
  width: 248px;
  height: 100%;

  .el-scrollbar__wrap {
    overflow-x: hidden !important;
  }

  .is-vertical {
    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.4);
    }
  }

  .el-menu {
    width: 248px;
    border: 0;

    .is-opened {
      .menu-color {
        font-weight: 600;
        color: #007bff;
      }
    }

    .el-menu-item {
      font-size: $textSize16;
      color: $zy-color;

      &.is-active {
        background-color: #fff;

        .menu-color {
          font-weight: 600;
          color: #007bff;
        }
      }
    }

    .el-menu--inline {
      background-color: #f2f8ff;

      .el-menu-item {
        font-size: $textSize16;
        color: $zy-color;

        &.is-active {
          background: linear-gradient(
            90deg,
            #3397f6 0%,
            #75bcff 100%
          ) !important;
          color: #fff;

          .menu-color {
            font-weight: 600;
            color: #fff;
          }
        }
      }
    }

    .el-submenu__title {
      font-size: $textSize16;
      color: $zy-color;
    }

    .menu-color {
      color: #333333;
    }

    .zy-menu-icon {
      display: inline-block;
      width: 24px;
      height: 24px;
      vertical-align: baseline;
      margin-right: 9px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
