
var mclickX;
var mclickY;

$.fn.extend({
    dragging:function(data){   
		var $this = $(this);
		var xPage;
		var yPage;
		var X;
		var Y;
		var xRand = 0;
		var yRand = 0;
		var father = $this.parent();
		var defaults = {
			move : 'both',
			randomPosition : true ,
			hander:1
		}
		var opt = $.extend({},defaults,data);
		var movePosition = opt.move;
		
		var hander = opt.hander;
		
		if(hander == 1){
			hander = $this; 
		}else{
			hander = $this.find(opt.hander);
		}
		
		father.css({"position":"relative","overflow":"hidden"});
		$this.css({"position":"absolute"});
		hander.css({"cursor":"move"});

		var faWidth = father.width();
		var faHeight = father.height();
		var thisWidth = $this.width()+parseInt($this.css('padding-left'))+parseInt($this.css('padding-right'));
		var thisHeight = $this.height()+parseInt($this.css('padding-top'))+parseInt($this.css('padding-bottom'));
		
		var mDown = false;
		var positionX;
		var positionY;
		var moveX ;
		var moveY ;
		
		hander.mousedown(function(e){
			father.children().css({"zIndex":"0"});
			$this.css({"zIndex":"1"});
			mDown = true;
			X = e.pageX;
			Y = e.pageY;
			mclickX = e.offsetX;
			mclickY = e.offsetY;
			positionX = $this.position().left;
			positionY = $this.position().top;
			selSets = $this;//赋值给选择的座位
			selSetsNum = $this.children().children().get(0).textContent;//赋值给选择的座位编号
			selSetsW = $this.get(0).offsetWidth;//选中的座位宽度
			selSetsH = $this.get(0).offsetHeight;//选中的座位高度
			addSelSetsCss();
			return false;
		});
			
		$(document).mouseup(function(e){
			mDown = false;
		});
		
		$(document).mousemove(function(e){
			
			xPage = e.pageX;
			moveX = positionX+xPage-X;
			
			yPage = e.pageY;
			moveY = positionY+yPage-Y;

			function thisAllMove(){
				if(mDown == true){
					$this.css({"left":moveX,"top":moveY});
				}else{
					return;
				}
				if(moveX < 0){
					$this.css({"left":"0"});
				}
				if(moveX > (faWidth-thisWidth)){
					$this.css({"left":faWidth-thisWidth});
				}

				if(moveY < 0){
					$this.css({"top":"0"});
				}
				if(moveY > (faHeight-thisHeight)){
					$this.css({"top":faHeight-thisHeight});
				}
			}
			thisAllMove();
		});
    }
}); 