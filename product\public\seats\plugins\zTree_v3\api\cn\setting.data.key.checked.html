<div class="apiDetail">
<div>
	<h2><span>String</span><span class="path">setting.data.key.</span>checked</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.excheck</span> 扩展 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>zTree 节点数据中保存 check 状态的属性名称。</p>
			<p>默认值："checked"</p>
			<p class="highlight_red">请勿与 zTree 节点数据的其他参数冲突，例如：checkedOld</p>
		</div>
	</div>
	<h3>setting 举例</h3>
	<h4>1. 设置 zTree 显示节点时，将 treeNode 的 isChecked 属性当做节点名称</h4>
	<pre xmlns=""><code>var setting = {
	data: {
		key: {
			checked: "isChecked"
		}
	}
};
......</code></pre>
</div>
</div>