<template>
  <el-popover :width="w"
              :height="h"
              placement="top"
              :disabled="disabled"
              :visible-arrow="false"
              popper-class="slide-verify-popover"
              trigger="hover">
    <div class="slide-verify-box">
      <canvas :width="w"
              :height="h"
              ref="canvas"></canvas>
      <div v-if="show"
           @click="refresh"
           class="slide-verify-refresh-icon"></div>
      <canvas :width="w"
              :height="h"
              ref="block"
              class="slide-verify-block"></canvas>
    </div>
    <div class="slide-verify-slider"
         slot="reference"
         :class="{'container-active': containerActive, 'container-success': containerSuccess, 'container-fail': containerFail}">
      <div class="slide-verify-slider-mask"
           :style="{width: sliderMaskWidth}">
        <div @mousedown="sliderDown"
             @touchstart="touchStartEvent"
             @touchmove="touchMoveEvent"
             @touchend="touchEndEvent"
             class="slide-verify-slider-mask-item"
             :style="{left: sliderLeft}">
          <div class="slide-verify-slider-mask-item-icon"></div>
        </div>
      </div>
      <span class="slide-verify-slider-text">{{sliderText}}</span>
    </div>
  </el-popover>
</template>
<script>
import img1 from './img/img1.jpg'
import img2 from './img/img2.jpg'
import img3 from './img/img3.jpg'
import img4 from './img/img4.jpg'
import img5 from './img/img5.jpg'
import img6 from './img/img6.jpg'

const PI = Math.PI

function sum (x, y) {
  return x + y
}

function square (x) {
  return x * x
}
export default {
  name: 'SlideVerify',
  props: {
    l: {
      type: Number,
      default: 42
    },
    r: {
      type: Number,
      default: 10
    },
    w: {
      type: Number,
      default: 304
    },
    h: {
      type: Number,
      default: 155
    },
    sliderText: {
      type: String,
      default: 'Slide filled right'
    },
    accuracy: {
      type: Number,
      default: 5 // 若为 -1 则不进行机器判断
    },
    show: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    imgs: {
      type: Array,
      default: () => [
        img1,
        img2,
        img3,
        img4,
        img5,
        img6
      ]
    },
    ws: {
      type: Number,
      default: 0 // 若为 -1 则不进行机器判断
    }
  },
  data () {
    return {
      containerActive: false,
      containerSuccess: false,
      containerFail: false,
      canvasCtx: null,
      blockCtx: null,
      block: null,
      block_x: undefined,
      block_y: undefined,
      L: this.l + this.r * 2 + 3,
      img: undefined,
      originX: undefined,
      originY: undefined,
      isMouseDown: false,
      trail: [],
      sliderLeft: 0,
      sliderMaskWidth: 0,
      success: false,
      timestamp: null
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      this.initDom()
      this.initImg()
      this.bindEvents()
    },
    initDom () {
      this.block = this.$refs.block
      this.canvasCtx = this.$refs.canvas.getContext('2d')
      this.blockCtx = this.block.getContext('2d')
    },
    initImg () {
      const img = this.createImg(() => {
        this.drawBlock()
        this.canvasCtx.drawImage(img, 0, 0, this.w, this.h)
        this.blockCtx.drawImage(img, 0, 0, this.w, this.h)
        const {
          block_x: x,
          block_y: y,
          r,
          L
        } = this
        const _y = y - r * 2 - 1
        const ImageData = this.blockCtx.getImageData(x, _y, L, L)
        this.block.width = L
        this.blockCtx.putImageData(ImageData, 0, _y)
      })
      this.img = img
    },
    drawBlock () {
      this.block_x = this.getRandomNumberByRange(this.L + 10, this.w - (this.L + 10))
      this.block_y = this.getRandomNumberByRange(10 + this.r * 2, this.h - (this.L + 10))
      this.draw(this.canvasCtx, this.block_x, this.block_y, 'fill')
      this.draw(this.blockCtx, this.block_x, this.block_y, 'clip')
    },
    draw (ctx, x, y, operation) {
      const {
        l,
        r
      } = this
      ctx.beginPath()
      ctx.moveTo(x, y)
      ctx.arc(x + l / 2, y - r + 2, r, 0.72 * PI, 2.26 * PI)
      ctx.lineTo(x + l, y)
      ctx.arc(x + l + r - 2, y + l / 2, r, 1.21 * PI, 2.78 * PI)
      ctx.lineTo(x + l, y + l)
      ctx.lineTo(x, y + l)
      ctx.arc(x + r - 2, y + l / 2, r + 0.4, 2.76 * PI, 1.24 * PI, true)
      ctx.lineTo(x, y)
      ctx.lineWidth = 2
      ctx.fillStyle = 'rgba(255, 255, 255, 0.7)'
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)'
      ctx.stroke()
      ctx[operation]()
      ctx.globalCompositeOperation = 'destination-over'
    },
    createImg (onload) {
      const img = document.createElement('img')
      img.crossOrigin = 'Anonymous'
      img.onload = onload
      img.onerror = () => {
        img.src = this.getRandomImg()
      }
      img.src = this.getRandomImg()
      return img
    },
    getRandomImg () {
      const len = this.imgs.length
      return this.imgs[this.getRandomNumberByRange(0, len)]
    },
    getRandomNumberByRange (start, end) {
      return Math.round(Math.random() * (end - start) + start)
    },
    refresh () {
      this.reset()
      this.$emit('refresh')
    },
    sliderDown (event) {
      if (this.success) return
      this.originX = event.clientX
      this.originY = event.clientY
      this.isMouseDown = true
      this.timestamp = +new Date()
    },
    touchStartEvent (e) {
      if (this.success) return
      this.originX = e.changedTouches[0].pageX
      this.originY = e.changedTouches[0].pageY
      this.isMouseDown = true
      this.timestamp = +new Date()
    },
    bindEvents () {
      document.addEventListener('mousemove', (e) => {
        if (!this.isMouseDown) return false
        const moveX = e.clientX - this.originX
        const moveY = e.clientY - this.originY
        if (moveX < 0 || moveX + 38 >= this.w) return false
        this.sliderLeft = moveX + 'px'
        const blockLeft = (this.w - 40 - 20) / (this.w - 40) * moveX
        this.block.style.left = blockLeft + 'px'

        this.containerActive = true
        this.sliderMaskWidth = moveX + this.ws + 'px'
        this.trail.push(moveY)
      })
      document.addEventListener('mouseup', (e) => {
        if (!this.isMouseDown) return false
        this.isMouseDown = false
        if (e.clientX === this.originX) return false
        this.containerActive = false
        this.timestamp = +new Date() - this.timestamp
        const {
          spliced,
          TuringTest
        } = this.verify()
        if (spliced) {
          if (this.accuracy === -1) {
            this.containerSuccess = true
            this.success = true
            this.$emit('success', this.timestamp)
            return
          }
          if (TuringTest) {
            this.containerSuccess = true
            this.success = true
            this.$emit('success', this.timestamp)
          } else {
            this.containerFail = true
            this.$emit('again')
          }
        } else {
          this.containerFail = true
          this.$emit('fail')
          setTimeout(() => {
            this.reset()
          }, 1000)
        }
      })
    },
    touchMoveEvent (e) {
      if (!this.isMouseDown) return false
      const moveX = e.changedTouches[0].pageX - this.originX
      const moveY = e.changedTouches[0].pageY - this.originY
      if (moveX < 0 || moveX + 38 >= this.w) return false
      this.sliderLeft = moveX + 'px'
      const blockLeft = (this.w - 40 - 20) / (this.w - 40) * moveX
      this.block.style.left = blockLeft + 'px'

      this.containerActive = true
      this.sliderMaskWidth = moveX + 'px'
      this.trail.push(moveY)
    },
    touchEndEvent (e) {
      if (!this.isMouseDown) return false
      this.isMouseDown = false
      if (e.changedTouches[0].pageX === this.originX) return false
      this.containerActive = false
      this.timestamp = +new Date() - this.timestamp
      const {
        spliced,
        TuringTest
      } = this.verify()
      if (spliced) {
        if (this.accuracy === -1) {
          this.containerSuccess = true
          this.success = true
          this.$emit('success', this.timestamp)
          return
        }
        if (TuringTest) {
          this.containerSuccess = true
          this.success = true
          this.$emit('success', this.timestamp)
        } else {
          this.containerFail = true
          this.$emit('again')
        }
      } else {
        this.containerFail = true
        this.$emit('fail')
        setTimeout(() => {
          this.reset()
        }, 1000)
      }
    },
    verify () {
      const arr = this.trail
      const average = arr.reduce(sum) / arr.length
      const deviations = arr.map(x => x - average)
      const stddev = Math.sqrt(deviations.map(square).reduce(sum) / arr.length)
      const left = parseInt(this.block.style.left)
      const accuracy = this.accuracy <= 1 ? 1 : this.accuracy > 10 ? 10 : this.accuracy
      return {
        spliced: Math.abs(left - this.block_x) <= accuracy,
        TuringTest: average !== stddev
      }
    },
    reset () {
      this.success = false
      this.containerActive = false
      this.containerSuccess = false
      this.containerFail = false
      this.sliderLeft = 0
      this.block.style.left = 0
      this.sliderMaskWidth = 0
      const {
        w,
        h
      } = this
      this.canvasCtx.clearRect(0, 0, w, h)
      this.blockCtx.clearRect(0, 0, w, h)
      this.block.width = w
      this.img.src = this.getRandomImg()
      this.$emit('fulfilled')
    }
  }
}
</script>
<style lang="scss">
@import "./slide-verify.scss";
</style>
