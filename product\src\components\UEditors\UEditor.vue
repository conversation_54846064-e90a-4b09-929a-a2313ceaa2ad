<template>
  <div class="hello">
    <vue-ueditor-wrap v-model="model"
                      @ready="ready"
                      :config="myConfig"
                      ref="editors"></vue-ueditor-wrap>
  </div>
</template>

<script>
import VueUeditorWrap from 'vue-ueditor-wrap'
export default {
  name: 'UEditors',
  components: {
    VueUeditorWrap// eslint-disable-line
  },
  data () {
    return {
      myConfig: {
        // 是否跟随内容撑开
        autoHeightEnabled: false,
        elementPathEnabled: false,
        wordCount: true,
        pasteplain: true, // 纯文本模式
        // 高度
        initialFrameHeight: 280,
        // 宽度
        initialFrameWidth: '100%',
        // 图片上传的路径
        serverUrl: `${this.$api.general.baseURL()}/ueditor/exec`,
        // serverUrl: `http://*************/ueditor/exec`,
        // 资源依赖的路径
        UEDITOR_HOME_URL: './UEditor/',
        toolbars: [
          // ['undo', 'redo', 'bold', 'italic', 'underline'], // 第一行工具栏按钮
          // ['justifyleft', 'justifycenter', 'justifyright', 'justifyjustify'], // 第二行工具栏按钮
          // ['insertunorderedlist', 'insertorderedlist', 'blockquote'] // 第三行工具栏按钮
          // ['link', 'unlink', 'insertimage'], // 第四行工具栏按钮
          // ['inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts'],
          // ['indent', 'autotypeset'] // /第五行工具栏按钮
          ['autotypeset'] // 第五行工具栏按钮
        ],
        // 鼠标右键的功能，但是一般不用编辑器带的功能，因为编辑器自带的不能右键复制，所以这个变量设置为空数组
        contextMenu: [],
        lineheight: [1, 1.25, 1.5, 1.75, 2, 2.25, 2.5, 2.75, 3, 3.25, 3.5, 3.75, 4],
        autotypeset: {
          mergeEmptyline: true, // 合并空行
          removeClass: true, // 去掉冗余的class
          removeEmptyline: true, // 去掉空行
          textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版
          imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版
          pasteFilter: true, // 根据规则过滤没事粘贴进来的内容
          clearFontSize: true, // 去掉所有的内嵌字号，使用编辑器默认的字号
          clearFontFamily: true, // 去掉所有的内嵌字体，使用编辑器默认的字体
          removeEmptyNode: true, // 去掉空节点
          // 可以去掉的标签
          removeTagNames: { 标签名字: 1 },
          indent: true, // 行首缩进
          indentValue: '2em', // 行首缩进的大小
          symbolConver: 'tobdc',
          bdc2sb: false,
          tobdc: true
          // ignoreChars: /[\uFF10-\uFF19]/g
        },
        maximumWords: 10000,
        zIndex: 999,
        fontfamily: [
          { label: '', name: '宋体, SimSun', val: '宋体, SimSun' },
          { label: '', name: '微软雅黑, Microsoft YaHei', val: '微软雅黑, Microsoft YaHei' },
          { label: '', name: '黑体, SimHei', val: '黑体, SimHei' }
          // 添加其他字体选项
        ]
      }
    }
  },
  watch: {
    model (newValue) {
      this.checkAndRemoveSpan(newValue)
      this.handleContent()
      // setTimeout(() => {
      //   this.formatContent(this.$refs.editor.editor)
      // }, 200)
    }
  },
  computed: {
    model: {
      get () {
        return this.value
      },
      set (value) {
        this.$emit('input', value)
      }
    }
  },
  props: {
    value: {
      type: String
    },
    maximumWords: {
      type: Number
    }
    // toolbars: {}
  },
  created () {
    if (this.maximumWords) {
      this.myConfig.maximumWords = this.maximumWords
    }
  },
  methods: {
    formatContent (editor) {
      console.log('触发')
      // editor.execCommand('removeFormat')
      editor.body.innerHTML = editor.body.innerHTML.replace(/\s*style="[^"]*"/g, ' ')
      // 调用UEditor的命令进行自动排版
      editor.execCommand('autotypeset', {
        mergeEmptyline: true, // 合并空行
        removeClass: true, // 去掉冗余的class
        removeEmptyline: true, // 去掉空行
        textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版
        imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版
        pasteFilter: true, // 根据规则过滤没事粘贴进来的内容
        clearFontSize: true, // 去掉所有的内嵌字号，使用编辑器默认的字号
        clearFontFamily: true, // 去掉所有的内嵌字体，使用编辑器默认的字体
        removeEmptyNode: true, // 去掉空节点
        // 可以去掉的标签
        removeTagNames: { 标签名字: 1 },
        indent: true, // 行首缩进
        indentValue: '2em', // 行首缩进的大小
        symbolConver: 'tobdc',
        bdc2sb: false,
        tobdc: true
      })
    },
    handleContent () {
      // 处理编辑器内容
      // 在自动排版后执行的操作
      // console.log('文本已自动排版')
      var _this = this
      var processedContent = this.model.replace(/[\uff21-\uff3a\uff41-\uff5a\uff10-\uff19／％．]/g, function (char) {
        // 忽略数字、冒号、逗号和句号的转换
        return String.fromCharCode(char.charCodeAt(0) - 65248)
      })
      // 将处理后的内容设置回编辑器
      // setTimeout(() => {
      _this.model = processedContent
      // }, 200)
      // console.log(processedContent)
      // console.log('变化')
    },
    checkAndRemoveSpan (content) {
      const hasSpan = /<span\b[^>]*>/i.test(content)
      if (hasSpan) {
        const newContent = content.replace(/<span\b[^>]*>/gi, '<span>')
        this.model = newContent
      }
    },
    blur () {
      this.$emit('blur')
      // this.handleContent()
    },
    ready (editor) {
      setTimeout(() => {
        this.formatContent(editor)
      }, 500)
      console.log(editor)
      // 监听粘贴事件
      editor.addListener('afterpaste', () => {
        this.formatContent(editor)
      })
      editor.addListener('blur', this.blur)
      editor.addListener('handleContent', this.handleContent)
      // console.log(editor.getContent())
      editor.ready(() => {
        editor.execCommand('fontfamily', '宋体') // 字体
        editor.execCommand('lineheight', 2) // 行间距
        editor.execCommand('fontsize', '26px') // 字号
        editor.execCommand('forecolor', '#262626') // 字体颜色
      })
    }
  }
}
</script>

<style lang="scss">
.hello {
  width: 100%;

  h1,
  h2 {
    font-weight: normal;
  }

  ul {
    list-style-type: none;
    padding: 0;
  }

  li {
    display: inline-block;
    margin: 0 10px;
  }

  a {
    color: #42b983;
  }

  .hint {
    color: red;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
  }

  .hint>img {
    width: 20px;
    height: 20px;
    border: 1px solid red;
    margin: 0 5px;
  }
}
</style>
