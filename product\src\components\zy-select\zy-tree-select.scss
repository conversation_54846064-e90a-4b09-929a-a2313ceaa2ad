.zy-tree-select {
  height: 40px;
  width: 240px;

  .el-input__suffix {
    width: 25px;
    line-height: 40px;
    text-align: center;
  }

  .zy-tree-select-icon {
    transition-duration: 0.4s;
    transform: rotate(0);
  }

  .el-icon-arrow-down-a {
    transition-duration: 0.4s;
    transform: rotate(-180deg);
  }
}

.zy-tree-select-popover {
  padding: 0 !important;
}

.zy-tree-select-body {
  width: 100%;
  height: 100%;

  .el-scrollbar__wrap {
    overflow-x: hidden;
  }

  .is-vertical {
    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.8);
    }
  }

  .el-tree-node__content {
    height: 42px;

    .el-tree-node__expand-icon {
      font-size: $textSize16;
    }
  }

  .el-tree--highlight-current {
    .el-tree-node.is-current > .el-tree-node__content {
      color: $zy-color;
      background-color: $zy-withColor;
    }
  }
}
