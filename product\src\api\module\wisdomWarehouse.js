// 导入封装的方法
import {
  post
} from '../http'

const wisdomWarehouse = {
  // 获取用户详情
  getUserDetail (params) {
    return post('/wholeuser/look/' + params)
  },
  // 获取履职列表
  getWisemanUserDuty (params) {
    return post('/dutyconfig/getWisemanUserDuty', params)
  },
  // 获取履职详情
  getWisemanUserDutyDetail (params) {
    return post('/dutyconfig/getWisemanUserDutyDetail', params)
  },
  // 获取汇编列表
  getAssemblyList (params) {
    return post('/assembly/list', params)
  },
  // 新增 / 修改 汇编数据
  addAssembly (url, params) {
    return post(url, params)
  },
  // 获取汇编详情
  assemblyInfo (parmas) {
    return post('/assembly/info/' + parmas)
  },
  // 删除汇编
  assemblyDel (parms) {
    return post('/assembly/dels', parms)
  },
  assemblycolumnList (params) {
    return post('/assemblycolumn/list', params)
  },
  // 新增 / 修改 汇编数据
  assemblycolumn (url, params) {
    return post(url, params)
  },
  // 获取汇编详情
  assemblycolumnInfo (parmas) {
    return post('/assemblycolumn/info/' + parmas)
  },
  // 删除汇编
  assemblycolumnDel (parms) {
    return post('/assemblycolumn/dels', parms)
  }
}
export default wisdomWarehouse
