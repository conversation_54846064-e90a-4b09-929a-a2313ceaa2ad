<div class="apiDetail">
<div>
	<h2><span>?</span><span class="path">treeNode.</span>* DIY *</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>用于保存节点的其他自定义数据信息，不要与 zTree 使用的属性相同即可，用户可随意设定。</p>
		</div>
	</div>
	<h3>treeNode 举例</h3>
	<h4>1. 设置节点的备用英文名称</h4>
	<pre xmlns=""><code>var node = { "id":1, "name":"test1", "ename":"test eName"};</code></pre>
</div>
</div>