:root {
    --color: #72B9D0;
}

body {
	overflow: auto;
}

html {
	background: #fff;
}

li {
	list-style: none outside none;
}

.box {
	width: 100%;
	/* height: 100%; */
}

p {
	font-size: 20px;
	color: #333;
	text-align: center;
	margin: 0 0 20px;
}

i.hander {
	display: block;
	width: 100%;
	height: 25px;
	background: #ccc;
	text-align: center;
	font-size: 12px;
	color: #333;
	line-height: 25px;
	font-style: normal;
}

.inputBorder {
	border-color: #94ACFF;
}

.row {
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	margin-right: 0;
	margin-left: 0;
	height: 40px;
	line-height: 35px;
}

#saveSeats,
#operationDesc {
	margin-right: 20px;
	background: #199BC5;
	border-radius: 2px;
	color: #fff;
	border: none;
	height: 36px;
	align-items: center;
}

#uploadPic {
	margin-left: 20px;
	background: #199BC5;
	border-radius: 2px;
	color: #fff;
	border: none;
	height: 36px;
	align-items: center;
}

#importPer,
#autoSort {
	height: 30px;
	line-height: 15px;
	margin: 2px auto;
}

.layui-upload-file {
	position: absolute;
	z-index: -1;
}

#meetName {
	position: absolute;
	top: -8px;
	right: 0;
	z-index: 100;
	font-size: 24px !important;
	margin-bottom: 0;
	width: 100%;
}

#roomName {
	position: absolute;
	top: 20px;
	right: 0;
	z-index: 100;
	font-size: 20px !important;
	margin-bottom: 0;
	color: #000000;
	width: 100%;
}

/* .btn-info:hover{
	background: #47afd1 !important;
	border-color: #47afd1 !important;
	color: #FFF !important;
} */

.cancel{
	background: transparent;
	border: 1px solid rgba(217,217,217,1);
}

/* .cancel:hover{
	border-color: #199BC5 !important;
	color: #199BC5 !important;
} */

.cr {
	margin-right: 30px;
	padding: 5px 15px;
	border-radius: 4px;
	text-align: center;
	cursor: pointer;
	color: white;
}

.qr {
	padding: 5px 15px;
	border-radius: 4px;
	text-align: center;
	cursor: pointer;
	color: white;
}

.toButton {
	margin-bottom: 16px;
}

.toRight {
	margin-right: 20px;
}

.importType {
	padding-top: 7px;
}

.importType .radio {
	padding-left: 25px;
}

.container {
	position: absolute;
	left: 32px;
	width: 210px;
	height: 45px;
	margin: 5px 5px 0px 5px;
}

.impContainer {
	position: absolute;
	left: 33px;
	width: 200px;
	height: 45px;
	margin: 5px 5px 0px 5px;
}

.sortContainer {
	position: relative;
	left: 0;
	top: 0;
	width: 95.5%;
	height: 45px;
	margin: 5px 5px 0px 5px;
}

.txt {
	width: 100%;
	border: 0px;
	font-size: 16px;
	outline: medium;
}

.group {
	width: 200px;
	height: 35px;
	margin: 2px 0;
	padding: 5px 5px 5px 12px;
	background: rgba(255, 255, 255, 1);
	border: 1px solid rgba(229, 229, 229, 1);
	border-radius: 4px;
	color: #666666;
}

.dept_content {
	display: none;
	position: absolute;
	width: 14%;
	height: 240px;
	border: 0 solid #9e9e9e;
	background-color: #fff;
	opacity: 1;
	z-index: 9999;
	overflow-x: hidden;
	overflow-y: auto;
	box-shadow: 0px 2px 8px 0px rgba(216, 216, 216, 0.5);
}

.drag {
	position: absolute;
	border-radius: 5px;
	background: #fff;
	cursor: move;
	display: inline-grid;
	font-size: 12px;
	/*background-image: url(../img/seat.svg);*/
	background-repeat: no-repeat;
	background-size: 100% 100%;
}

.signDrag {
	position: absolute;
	border: 0px solid #dce0e4;
	border-radius: 0px;
	cursor: move;
	display: flex;
	justify-content: center;
	align-items: center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size: 14px !important;
	color: #262626;
}

.selected {
	background-color: #e46424;
	border: 1px solid #000;
	color: white;
}

.combo-select {
	width: 200px;
	height: 35px;
	margin: 2px 0;
	font: 100% Helvetica, Arial, Sans-serif;
	background: url(../../../../../assets/images/table-select.png) right 0.75rem center no-repeat;
	border: 1px solid rgba(229, 229, 229, 1);
	border-radius: 4px;
	appearance: none;
	color: #aeaeae;
	padding: 5px 5px 5px 8px;
	-moz-appearance: none;
	-webkit-appearance: none;
	-ms-appearance: none;
}

#perList .radio {
	padding-left: 5px;
}

.layui-btn {
	display: inline-block;
	margin-left: 5px;
	height: 35px;
	line-height: 35px;
	padding: 0 18px;
	background-color: #009688;
	color: #fff;
	white-space: nowrap;
	text-align: center;
	font-size: 14px;
	border: none;
	border-radius: 5px;
	cursor: pointer;
}

.areaTip {
	display: flex;
	position: absolute;
	top: 0;
	right: 30px;
	z-index: 100;
	text-align: right;
}

.list {
	margin: 5px;
	padding: 5px;
}

.list li {
	display: inline;
}

.dropdown-menu a {
	cursor: pointer;
}

.radio-info input[type="radio"]:checked+label::before {
	border-color: var(--color);
}

.radio-info input[type="radio"]:checked+label::after {
	background-color: var(--color);
}

.textbox-focused{
	border-color:var(--color);
}

.layui-upload-file {
	opacity: 0;
}

.btn-info.active,
.btn-info:active,
.show>.btn-info.dropdown-toggle {
	background: #fff;
}

.btn.focus,
.btn:focus {
	box-shadow: none;
}

.btn {
	height: 36px;
	padding: 0 16px;
	align-items: center;
}

/* .btn-info:hover {
	border-color: rgba(217, 217, 217, 1);
} */

.radio label {
	padding-left: 0;
}

.searchResult {
	line-height: 48px;
	margin-left: 24px;
	color: #8c8c8c;
}

#winModal .modal-content {
	width: 500px;
	min-height: 531px;
	margin: 0 auto;
	border-radius: 0;
	border: none;
	margin-top: 120px;
}

#picModal .modal-content {
	width: 600px;
	height: 500px;
	margin: 0 auto;
	border-radius: 0;
	border: none;
	margin-top: 120px;
}

#infoModal .modal-content {
	width: 600px;
	height: 390px;
	margin: 0 auto;
	border-radius: 0;
	border: none;
	margin-top: 120px;
}

.modal-header {
	padding: 0 20px;
	height: 42px;
	border-bottom: 1px solid #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	border-bottom: 1px solid #e9ecef;
}

.modal-footer {
	padding: 0 20px;
	height: 56px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	border-top: 1px solid #e9ecef;
}

.modal-footer label {
	width: 68px;
	height: 34px;
	line-height: 34px;
	text-align: center;
	border: 1px solid rgba(217, 217, 217, 1);
	background: linear-gradient(0deg, rgba(249, 250, 251, 1) 0%, rgba(255, 255, 255, 1) 100%);
	border-radius: 2px;
	font-size: 14px;
	margin-bottom: 0;
	margin-right: 0;
	margin-left: 16px!important;
}

.modal-footer>label:nth-child(1) {
	background: #199BC5;
	color: #fff;
	border: none;
}

.modal-body {
	padding: 20px;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;
}

.modal-body>div {
	display: flex;
	margin-top: 20px !important;
}

.modal-body span {
	width: 100px;
	line-height: 44px;
	font-size: 14px;
	margin-right: 14px;
}

.modal-body input {
	height: 44px;
	background: rgba(255, 255, 255, 1);
	border: 1px solid rgba(217, 217, 217, 1);
	border-radius: 2px;
	padding-left: 16px;
	font-size: 14px;
}

.modal-body #_easyui_textbox_input2,
.modal-body .textbox-addon-right {
	height: 44px!important;
	line-height: 44px!important;
}

.modal-body .textbox-addon-right a,
.modal-body .textbox-addon-right {
	width: 26px;
	height: 44px!important;
	margin: 0;
}

.selectIco {
	width: 24px;
	height: 24px;
	position: absolute;
	bottom: 8px;
	top: 10px;
	right: 0;
	z-index: 5;
	color: #B3B3B3;
}

#organName::-webkit-input-placeholder {
	color: #8c8c8c;
}

.form-control:disabled,
.form-control[readonly] {
	opacity: 1;
}

.form-control:focus {
    color: #8C8C8C;
    border: 1px solid var(--color);;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.15);
    outline: 0;
}

.radioRow {
	background: rgba(255, 255, 255, 1);
	box-shadow: 0px -1px 0px 0px rgba(230, 230, 230, 1);
	border-bottom: 1px solid rgb(230, 230, 230);
	line-height: 36px;
	height: 40px;
}

.layui-table-cell .layui-form-checkbox[lay-skin="primary"] {
    top: 6px;
    padding: 0;
}
