/*-------------------------------------
zTree Style

version:	3.0
author:		Hunter.z
website:	http://code.google.com/p/jquerytree/

-------------------------------------*/

.ztree * {padding:0; margin:0; font-size:12px; font-family: Verdana, Arial, Helvetica, AppleGothic, sans-serif}
.ztree {margin:0; padding:5px; color:#333}
.ztree li{padding:0; margin:0; list-style:none; line-height:14px; text-align:left; white-space:nowrap}
.ztree li ul{ margin:0; padding:0 0 0 18px}
.ztree li ul.line{ background:url(./img/line_conn.gif) 0 0 repeat-y;}

.ztree li a {padding:1px 3px 0 0; margin:0; cursor:pointer; color:#333; height:17px; text-decoration:none; vertical-align:top; display: inline-block}
.ztree li a:hover {text-decoration:none}
.ztree li a.curSelectedNode {padding-top:0px; background-color:#FFE6B0; color:black; height:16px; border:1px #FFB951 solid;}
.ztree li a.curSelectedNode_Edit {padding-top:0px; background-color:#FFE6B0; color:black; height:16px; border:1px #FFB951 solid; opacity:0.8; filter:alpha(opacity=80)}
.ztree li a.tmpTargetNode_inner {padding-top:0px; background-color:#316AC5; color:white; height:16px; border:1px #316AC5 solid; opacity:0.8; filter:alpha(opacity=80)}
.ztree li a.tmpTargetNode_prev {}
.ztree li a.tmpTargetNode_next {}
.ztree li a input.rename {height:14px; width:80px; padding:0; margin:0;
	font-size:12px; border:1px #7EC4CC solid; *border:0px}
.ztree li span {line-height:16px; margin-right: 2px}
.ztree li span.button {line-height:0; margin:0;width:16px; height:16px; display: inline-block; vertical-align:middle;
	border:0 none; cursor: pointer;
	background-color:transparent; background-repeat:no-repeat; background-attachment: scroll;
	background-image:url("./img/zTreeStandard.png"); *background-image:url("./img/zTreeStandard.gif")}

.ztree li span.button.switch {width:1px; height:18px; visibility: hidden}

.zTreeDragUL {margin:0; padding:0; position:absolute; background-color:#cfcfcf; border:1px #00B83F dotted; opacity:0.8; filter:alpha(opacity=80)}
.zTreeMask {z-index:10000; background-color:#cfcfcf; opacity:0.0; filter:alpha(opacity=0); position:absolute}

/* level 等级样式*/
/*.ztree li button.level0 {
	display:none;
}
.ztree li ul.level0 {
	padding:0;
	background:none;
}*/

.ztree li span.button.core_ico_docu{margin-right:2px; background-position:-126px 0; vertical-align:top; *vertical-align:middle}
.ztree li span.button.check_ico_docu{margin-right:2px; background-position:-126px -16px; vertical-align:top; *vertical-align:middle}
.ztree li span.button.edit_ico_docu{margin-right:2px; background-position:-126px -32px; vertical-align:top; *vertical-align:middle}
.ztree li span.button.hide_ico_docu{margin-right:2px; background-position:-160px 0; vertical-align:top; *vertical-align:middle}
