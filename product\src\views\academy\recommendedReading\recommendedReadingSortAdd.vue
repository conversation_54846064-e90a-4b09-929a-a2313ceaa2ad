<template>
  <div class="recommendedReadingSortAdd">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item class="form-input"
                    prop="sort"
                    label="序号">
        <el-input placeholder="请输入序号"
                  type="number"
                  v-model="form.sort"
                  clearable>
        </el-input>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'recommendedReadingSortAdd',
  data () {
    return {
      form: {
        sort: ''
      },
      rules: {
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ]
      }
    }
  },
  props: ['id', 'sort'],
  mounted () {
    if (this.sort) {
      this.form.sort = this.sort
    }
  },
  methods: {
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$api.systemSettings.generalAdd('/recommendsort/edit', {
            id: this.id,
            sort: this.form.sort
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.recommendedReadingSortAdd {
  width: 692px;
  height: 100%;
  padding: 24px 40px;
}
</style>
