var token = localStorage.getItem('token');
var areaId = localStorage.getItem("areaId");
var header = '{"Authorization": ' + token + ',"u-login-areaId": ' + areaId + '}'
var themeColor = window.localStorage.getItem("theme_color") != null ? window.localStorage.getItem("theme_color") : "#3279F4";
var selRid = ''; //选中的会场Id
var selRname = ''; //选择的会场名称
var selRimg = ''; //会场图片
var selSets; //选中的座位
var selSign; //选中的标签
var seatNo = ""; //座位号
var selSeatId = new Array(); //存放选中的座位的座位号数组
var maxX = 0; //座位容器内最大的x坐标
var maxY = 0; //座位容器内最大的y坐标
var areaArr = new Array(); // 区域信息
var boxWidth = 0;
var placeType = 1; //1:回字形会议室 2:矩形会议室
var bjInxex = 1; //会议室布局下 标

$(function() {
	selRid = window.localStorage.getItem("meetPlaceId"); //从sessionStorage拿到到选中的会场Id
	selRname = window.localStorage.getItem("meetPlaceName"); //从sessionStorage拿到到选中的会场Name
	$('#setsDiv').css("width", $(this).width() - $('#seatInfo').width() + 8).css("height", $(this).height() - 60);
	$('.box').css("width", $(this).width() - $('#seatInfo').width() - 6).css("height", $('.box').height() - 72).css("overflow", "auto");
	$('.box img').css("width", $(this).width() - $('#seatInfo').width() - 6).css("height", $(this).height() - 72).css("overflow", "auto");
	boxWidth = $(this).width() - $('#seatInfo').width() - 6;

	changeThemeColor(themeColor);

	//初始化复选框样式
	$('.skin-square input').iCheck({
		checkboxClass: 'icheckbox_square-blue',
		radioClass: 'iradio_square-blue',
		increaseArea: '20%'
	});

	$('#circular, #hzxRoom').iCheck('check');

	//座位类型下拉框值改变事件
	$("#setsCT").combobox({
    	onChange:function(n,o){
	        //这里的参数n是select改变后的value,o是改变前的value
	        var areaId = n; //区域Id
			var bgColor = "";
			//遍历区域类型数组，获取区域颜色
			for(var i = 0; i < areaArr.length; i++) {
				if(areaId == areaArr[i].id) {
					bgColor = areaArr[i].colour;
					break;
				}
			}

			if(selSets != undefined) {
				var isPass = true;
				$('.box div').each(function() {
					if($(this).is('.drag')) {
						var sNo = $(this).children().get(0).innerHTML; //座位号
						var sTp = $(this).children().get(1).innerHTML; //座位类型
						//过滤掉座位号为空的座位
						if(sNo != "") {
							//相同座位号，相同类型的座位存在，不通过
							if(sNo == $("#editSetsNum").val() && sTp == areaId) {
								isPass = false;
								return isPass;
							} else {
								isPass = true;
								return isPass;
							}
						}
					}
				});

				if(isPass == false) {
					layer.msg($("#setsT").combobox("getText") + "已存在相同座位号，不能重复！");
					$('.box div').each(function() {
						$(this).removeClass("selected ctrlSel");
					});
					clearContent();
					selSets = undefined;
					return;
				} else {
					$(selSets).children().get(1).textContent = areaId;
					$(selSets).children().get(6).textContent = bgColor;
				}
			} else {
				$('.box div').each(function() {
					if($(this).is('.drag')) {
						if($(this).is('.selected')) {
							$(this).children().get(1).textContent = areaId;
							$(this).children().get(6).textContent = bgColor;
						}
					}
				});
			}
			setColorByAreaType(1);
    	}
    });

	//加载座位类型数据
	loadZoneList();
  //加载布局信息
  loadlayoutInfo(selRid);

});

//加载布局信息
function loadlayoutInfo(rId) {
	axios.post(server.local_path + "meetSeatLayout/listData", Qs.stringify({
		placeId: rId
	}), {
		headers: JSON.parse(header)
	})
	.then(function(response) {
		if(response.data.errcode == 200){
			var detailInfo = response.data.data;
			if(detailInfo.length > 0) {
        $(".radioBox").empty();
        for(var i=0; i,detailInfo.length; i++) {
          bjInxex = detailInfo.length;
          if(i == 0) {
            var bjStr = '<div class="bjRadio"><div id="'+ detailInfo[i].id +'" placeid="'+ detailInfo[i].placeId +'" class="bjNameRadio bjNameRadioCheck" onclick="radioClick($(this));"><div class="bjNameRadioChild"></div></div><input class="bjRadioInput" value="'+ detailInfo[i].name +'"/></div>';
            //加载排座信息
            loadSeatsInfo(selRid, detailInfo[0].id);
          } else {
            var bjStr = '<div class="bjRadio"><div id="'+ detailInfo[i].id +'" placeid="'+ detailInfo[i].placeId +'" class="bjNameRadio bjNameRadioUnCheck" onclick="radioClick($(this));"><div class="bjNameRadioChild" style="background: #FFFFFF;"></div></div><input class="bjRadioInput" value="'+ detailInfo[i].name +'"/></div>';
          }
          $(".radioBox").append(bjStr);
        }
			}
		}
	}).catch(function(error) {

	});
}

//加载排座信息
function loadSeatsInfo(rId, lId) {
  //删除容器内的座位数据
  $(".box").find("div").remove();
  axios.post(server.local_path + "meetSeat/listData", Qs.stringify({
  	placeId: rId,
    layoutId: lId
  }), {
  	headers: JSON.parse(header)
  })
  .then(function(response) {
  	if(response.data.errcode == 200){
  		var detailInfo = response.data.data;
  		if(detailInfo != null && detailInfo.length > 0) {
  			for(var i = 0; i < detailInfo.length; i++) {
  				var sId = detailInfo[i].id; //座位id
  				var sNo = detailInfo[i].seatNumber; //座位号
  				var sTp = detailInfo[i].seatType; //座位类型
  				var sSt = ""; //座位状态
  				var sRm = detailInfo[i].remarks; //座位备注
  				var sH = detailInfo[i].seatH; //座位高度
  				var sW = detailInfo[i].seatW; //座位宽度
  				var sX = detailInfo[i].seatX; //座位x坐标
  				var sY = detailInfo[i].seatY; //座位y坐标
  				var sL = detailInfo[i].label; //座位/标记  seat:座位-sign:标记
  				var sBC = detailInfo[i].bgColor; //座位背景色
  				var sP = detailInfo[i].position; //座位几排几座

  				if(Number(sX) > Number(maxX)) {
  					maxX = sX; //给容器内的最大x坐标赋值
  				}
  				if(Number(sY) > Number(maxY)) {
  					maxY = sY; //给容器内的最大y坐标赋值
  				}

          var sRow = "",
            sNos = ""; //座位几排几座
          if(sP != "") {
            var rowAndNos = sP.split(",");
            sRow = rowAndNos[0];
            sNos = rowAndNos[1];
          }

          var addStr = '<div onclick="seatesClick($(this));" class="drag" style="z-index: 1;width:' + sW + 'px;height:' + sH + 'px;top:' + sY + ';left:' + sX + ';">' +
            '<label style="color:white;">' + sNo + '</label>' + //第一个label放座位号
            '<label style="display:none; color:black;">' + sTp + '</label>' + //第二个label放座位类型
            '<label style="display:none; color:black;">' + sSt + '</label>' + //第三个label放座位状态
            '<label style="display:none; color:black;">' + sId + '</label>' + //第四个label放座位Id
            '<label style="display:none; color:black;">' + sRow + '</label>' + //第五个label放座位第几排
            '<label style="display:none; color:white;">' + sNos + '</label>' + //第六个label放座位第几座
            '<label style="display:none; color:black;">' + sBC + '</label>' + //第七个label放座位背景色
            '<label style="display:none; color:black;">' + sRm + '</label>' + //第八个label放座位备注
            '</div>';
  				$(".box").append(addStr);
  			}
  			initDrag();
        setColorByAreaType(1);
  		}
  	}
  }).catch(function(error) {

  });
}

//加载排座信息
function loadSetsInfo(rId, rImg) {
	//$("#bgImg").attr("src", rImg);
	//删除容器内的座位数据
	$(".box").find("div").remove();

	axios.post(server.local_path + "meetseat/selSeated", Qs.stringify({
		placeid: rId
	}), {
		headers: JSON.parse(header)
	})
	.then(function(response) {
		if(response.data.errcode == 200){
			var detailInfo = response.data.data;
			var allNums = 0; //总座位数
			if(detailInfo != null && detailInfo.length > 0) {
				seatNo = Number(detailInfo.length) + 1;
				for(var i = 0; i < detailInfo.length; i++) {
					var sId = detailInfo[i].id; //座位id
					var sNo = detailInfo[i].seatNumber; //座位号
					var sTp = detailInfo[i].seatType; //座位类型
					var sSt = detailInfo[i].seatState; //座位状态
					var sRm = detailInfo[i].remarks; //座位备注
					var sH = detailInfo[i].seatH; //座位高度
					var sW = detailInfo[i].seatW; //座位宽度
					var sX = detailInfo[i].seatX; //座位x坐标
					var sY = detailInfo[i].seatY; //座位y坐标
					var sL = detailInfo[i].label; //座位/标记  seat:座位-sign:标记
					var sBC = detailInfo[i].bgColor; //座位背景色
					var sP = detailInfo[i].position; //座位几排几座

					if(Number(sX) > Number(maxX)) {
						maxX = sX; //给容器内的最大x坐标赋值
					}
					if(Number(sY) > Number(maxY)) {
						maxY = sY; //给容器内的最大y坐标赋值
					}

					if(sL == "seat") {
						allNums++;
						var sRow = "",
							sNos = ""; //座位几排几座
						if(sP != "") {
							var rowAndNos = sP.split(",");
							sRow = rowAndNos[0];
							sNos = rowAndNos[1];
						}

						var addStr = '<div onclick="seatesClick($(this));" class="drag" style="z-index: 1;width:' + sW + 'px;height:' + sH + 'px;top:' + sY + ';left:' + sX + ';">' +
							'<label style="color:white;">' + sNo + '</label>' + //第一个label放座位号
							'<label style="display:none; color:black;">' + sTp + '</label>' + //第二个label放座位类型
							'<label style="display:none; color:black;">' + sSt + '</label>' + //第三个label放座位状态
							'<label style="display:none; color:black;">' + sId + '</label>' + //第四个label放座位Id
							'<label style="display:none; color:black;">' + sRow + '</label>' + //第五个label放座位第几排
							'<label style="display:none; color:white;">' + sNos + '</label>' + //第六个label放座位第几座
							'<label style="display:none; color:black;">' + sBC + '</label>' + //第七个label放座位背景色
							'<label style="display:none; color:black;">' + sRm + '</label>' + //第八个label放座位备注
							'</div>';
					} else {
						var addStr = '<div id="' + sId + '" onclick="signsClick($(this));" class="signDrag" style="z-index: 1;width:' + sW + 'px;height:' + sH + 'px;top:' + sY + ';left:' + sX + ';">' + sNo + '</div>';
					}

					$(".box").append(addStr);
				}
				$("#seatNum").text(allNums);
				initDrag();
				setColorByAreaType(1);
			}
		}
		else {
			seatNo = 0;
		}
		showSeatNoOrSort();
	}).catch(function(error) {
		layer.msg(error.response.data.message);
	});
}

//保存按钮
$("#bc").on("click", function() {
  var rule = $("#ruleSelect").combobox("getValue"); //排列规则
  //回字形会议室
  if(placeType === 1) {

  }
  //矩形会议室
  else {
    var rowNums = $("#rowNum").val();
    var colNums = $("#colNum").val();
     if(rowNums != "" && colNums != "") {
       var wNum = 120; //座位宽度
       var hNum = 50; //座位高度
       var areaId = $("#setsCT").combobox("getValue"); //区域Id
       var bgColor = "";
       //遍历区域类型数组，获取区域颜色
       for(var i = 0; i < areaArr.length; i++) {
       	if(areaId == areaArr[i].id) {
       		bgColor = areaArr[i].colour;
       		break;
       	}
       }
      var seatNo = 0; // 顺序号
      var seatRow = 0; // 排数
      var seatCol = 0; // 列数
     	for(var i = 0; i < rowNums; i++) {
     		var topNum = scroH + (Number(i) + 1) * (Number(hNum) + 5);
         seatRow++;
        for(var j = 0; j < colNums; j++) {
           var leftNum = scroW + (Number(j) + 1) * (Number(wNum) + 5);

           //规则1
           if(rule == 1) {
             seatNo++;
             if(j == 0) {
                seatCol = 0; // 换行置0，重新从0开始
             }
             seatCol++;
           }
           //规则2
           else if(rule == 2) {
              if(i%2 == 0) {
                if(i == 0) {
                  seatNo++;
                  seatCol++;
                } else {
                  seatNo = Number(seatRow * colNums + j) - Number(colNums) + 1;
                  if(j == 0) {
                     seatCol = 0; // 换行置0，重新从0开始
                  }
                  seatCol++;
                }
              } else {
                seatNo = Number(seatRow * colNums - j);
                seatCol = colNums - j
              }
           }

           var addStr = '<div onclick="seatesClick($(this));" class="drag isSeat" style="z-index: 1;width:' + wNum + 'px;height:' + hNum + 'px;top:' + topNum + ';left:' + leftNum + ';">' +
           	'<label style="color:white;">' + seatNo + '</label>' + //第一个label放座位号
           	'<label style="display:none; color:black;">' + areaId + '</label>' + //第二个label放座位类型
           	'<label style="display:none; color:black;"></label>' + //第三个label放座位状态
           	'<label style="display:none; color:black;"></label>' + //第四个label放座位Id
           	'<label style="display:none; color:black;">' + seatRow + '</label>' + //第五个label放座位第几排
           	'<label style="display:none; color:white;">' + seatCol + '</label>' + //第六个label放座位第几座
           	'<label style="display:none; color:black;">' + bgColor + '</label>' + //第七个label放座位背景色
           	'<label style="display:none; color:black;"></label>' + //第八个label放座位备注
           	'</div>';
           $(".box").append(addStr);
        }
     	}
      $("#rowNum, #colNum").val("");
      initDrag();
    }
  }
  //根据选中的布局保存座位
  saveSeatsByLayout();
});

//另存为
$("#lcw").on("click", function() {
  $(".bjNameRadio").removeClass('bjNameRadioCheck').addClass('bjNameRadioUnCheck');
  $(".bjNameRadioChild").css("background", "#FFFFFF");

  axios.post(server.local_path + "meetSeatLayout/add", Qs.stringify({
  	placeId: selRid
  }), {
  	headers: JSON.parse(header)
  })
  .then(function(response) {
  	if(response.data.errcode == 200){
      bjInxex++;
      var bjStr = '<div class="bjRadio"><div id="'+ response.data.data.id +'" placeid="'+ selRid +'" class="bjNameRadio bjNameRadioCheck" onclick="radioClick($(this));"><div class="bjNameRadioChild"></div></div><input class="bjRadioInput" value="'+ response.data.data.name + '"/></div>'
      $(".radioBox").append(bjStr);

       //保存座位信息
       //遍历排座容器内所有座位信息 封装座位信息
       var setsArr = new Array(); //座位信息数组
       $('.box div').each(function() {
       	if($(this).is('.drag')) {
       		var sNo = $(this).children().get(0).innerHTML; //座位号
           var sTp = $(this).children().get(1).innerHTML; //座位类型
           var sSt = $(this).children().get(2).innerHTML; //座位状态
           var sId = ""; //座位Id
           var sRow = $(this).children().get(4).innerHTML; //座位第几排
           var sNos = $(this).children().get(5).innerHTML; //座位第几座
           var sX = $(this).position().left.toFixed(2); //x坐标
           var sY = $(this).position().top.toFixed(2); //y坐标
           var sW = $(this).get(0).offsetWidth; //座位宽度
           var sH = $(this).get(0).offsetHeight; //座位高度
           var sBC = $(this).children().get(6).innerHTML; //座位背景色
           var sRm = $(this).children().get(7).innerHTML; //座位备注
           var sP = "";
           if(sRow == "") {
             sRow = "-"
           }
           if(sNos == "") {
             sNos = "-"
           }
           sP = sRow + "," + sNos;

           var setsObj = new Object();
           setsObj.placeId = selRid; // 会场id
           setsObj.layoutId = response.data.data.id; // 选中的布局id
           setsObj.id = sId;
           setsObj.seatNumber = sNo;
           setsObj.seatType = sTp;
           // setsObj.seatstate = sSt;
           setsObj.position = sP;
           setsObj.seatX = Number(sX) + Number(scroW);
           setsObj.seatY = Number(sY) + Number(scroH);
           setsObj.seatW = sW;
           setsObj.seatH = sH;
           setsObj.bgColor = sBC != "null" ? sBC : "";
           setsObj.remarks = sRm != "null" ? sRm : "";
           setsObj.placeType = "";
           // setsObj.label = "seat";
           setsArr.push(setsObj);
       	}
       });
       axios({
         method: "post",
         url: server.local_path + "meetSeat/addSeat",
         headers: JSON.parse(header),
         data: setsArr
       })
       .then(function (response) {
         var resultInfo = response.data;
         if (resultInfo.errcode == 200) {
           	layer.msg("保存成功");
         }
       }).catch(function (error) {
         layer.msg(error.response.data.message);
       });
  	}
  }).catch(function(error) {
  	layer.msg(error.response.data.message);
  });
});

//删除会议室布局
$("#sc").on("click", function() {
  if($(".radioBox").children().length > 1) {
    var layoutId = $(".bjNameRadioCheck").attr("id"); //选中的布局id
    axios({
      method: "post",
      url: server.local_path + "meetSeatLayout/dels?ids=" + layoutId,
      headers: JSON.parse(header)
    })
    .then(function(response) {
    	var resultInfo = response.data;
    	if(resultInfo.errcode == 200) {
        $('.radioBox div').find('.bjNameRadioCheck').parent().remove();
        $('.radioBox .bjRadio').last().find('.bjNameRadio').removeClass('bjNameRadioUnCheck').addClass('bjNameRadioCheck');
        $('.radioBox .bjRadio').last().find('.bjNameRadioChild').css("background", themeColor);
        var lId = $(".bjNameRadioCheck").attr("id"); //选中的布局id
        //加载排座信息
        loadSeatsInfo(selRid, lId);
    	}
    	else{
    		layer.msg(resultInfo.errmsg);
    	}
    }).catch(function(error) {
    	layer.msg(error.response.data.message);
    });
  }
});

//根据选中的布局保存座位
function saveSeatsByLayout() {
  var checkedLid = $(".bjNameRadioCheck").attr("id"); //选中的布局id
  var placeId = $(".bjNameRadioCheck").attr("placeid"); //会议室id

  //获取所有布局id 批量修改布局
  var layoutArr = new Array(); //布局信息数组
  $('.radioBox .bjRadio').find('.bjNameRadio').each(function() {
    var layoutId = $(this).attr("id"); //布局Id
    var layoutName = $(this).next().val(); //布局名称
    var layObj = new Object();
    layObj.id = layoutId;
    layObj.name = layoutName;
    layObj.placeId = selRid;
    layoutArr.push(layObj);
  });

  //批量编辑布局
  axios({
    method: "post",
    url: server.local_path + "meetSeatLayout/ediSeatLayout",
    headers: JSON.parse(header),
    data: layoutArr
  })
  .then(function (response) {
    var resultInfo = response.data;
    if (resultInfo.errcode == 200) {

    }
  }).catch(function (error) {
    layer.msg(error.response.data.message);
  });

  //保存座位信息
  //遍历排座容器内所有座位信息 封装座位信息
  var setsArr = new Array(); //座位信息数组
  $('.box div').each(function() {
  	if($(this).is('.drag')) {
  		var sNo = $(this).children().get(0).innerHTML; //座位号
      var sTp = $(this).children().get(1).innerHTML; //座位类型
      var sSt = $(this).children().get(2).innerHTML; //座位状态
      var sId = $(this).children().get(3).innerHTML; //座位Id
      var sRow = $(this).children().get(4).innerHTML; //座位第几排
      var sNos = $(this).children().get(5).innerHTML; //座位第几座
      var sX = $(this).position().left.toFixed(2); //x坐标
      var sY = $(this).position().top.toFixed(2); //y坐标
      var sW = $(this).get(0).offsetWidth; //座位宽度
      var sH = $(this).get(0).offsetHeight; //座位高度
      var sBC = $(this).children().get(6).innerHTML; //座位背景色
      var sRm = $(this).children().get(7).innerHTML; //座位备注
      var sP = "";
      if(sRow == "") {
        sRow = "-"
      }
      if(sNos == "") {
        sNos = "-"
      }
      sP = sRow + "," + sNos;

      var setsObj = new Object();
      setsObj.placeId = selRid; // 会场id
      setsObj.layoutId = checkedLid; // 选中的布局id
      setsObj.id = sId;
      setsObj.seatNumber = sNo;
      setsObj.seatType = sTp;
      // setsObj.seatstate = sSt;
      setsObj.position = sP;
      setsObj.seatX = Number(sX) + Number(scroW);
      setsObj.seatY = Number(sY) + Number(scroH);
      setsObj.seatW = sW;
      setsObj.seatH = sH;
      setsObj.bgColor = sBC != "null" ? sBC : "";
      setsObj.remarks = sRm != "null" ? sRm : "";
      setsObj.placeType = "";
      // setsObj.label = "seat";
      setsArr.push(setsObj);
  	}
  });
  axios({
    method: "post",
    url: server.local_path + "meetSeat/addSeat",
    headers: JSON.parse(header),
    data: setsArr
  })
  .then(function (response) {
    var resultInfo = response.data;
    if (resultInfo.errcode == 200) {
      	layer.msg("保存成功");
    }
  }).catch(function (error) {
    layer.msg(error.response.data.message);
  });
}

//保存排座信息
$("#saveSets").on("click", function() {
	saveSeats(true);
});

function saveSeats(showMsg) {
	//保存排座信息 传会场id,座位信息给后台,座位信息为数组
	//遍历排座容器内所有座位信息 封装座位信息
	var setsArr = new Array(); //座位信息数组
	var zxNum = 0; //执行次数
	var isSave = true; //是否通过保存
	var plcType = $('input:radio[name=setUp]:checked').val();//会场排座类型 1方形，2圆形

	$('.box div').each(function() {
		if($(this).is('.drag')) {
			var sNo = $(this).children().get(0).innerHTML; //座位号
			//过滤掉座位号为空的座位
			if(sNo != "") {
				var sTp = $(this).children().get(1).innerHTML; //座位类型
				var sSt = $(this).children().get(2).innerHTML; //座位状态
				var sId = $(this).children().get(3).innerHTML; //座位Id
				var sRow = $(this).children().get(4).innerHTML; //座位第几排
				var sNos = $(this).children().get(5).innerHTML; //座位第几座
				var sX = $(this).position().left.toFixed(2); //x坐标
				var sY = $(this).position().top.toFixed(2); //y坐标
				var sW = $(this).get(0).offsetWidth; //座位宽度
				var sH = $(this).get(0).offsetHeight; //座位高度
				var sBC = $(this).children().get(6).innerHTML; //座位背景色
				var sRm = $(this).children().get(7).innerHTML; //座位备注
				var sP = "";
				if(sRow == "") {
					sRow = "-"
				}
				if(sNos == "") {
					sNos = "-"
				}
				sP = sRow + "," + sNos;

				var setsObj = new Object();
				setsObj.id = sId;
				setsObj.seatnumber = sNo;
				setsObj.seattype = sTp;
				setsObj.seatstate = sSt;
				setsObj.position = sP;
				setsObj.seatx = Number(sX) + Number(scroW);
				setsObj.seaty = Number(sY) + Number(scroH);
				setsObj.seatw = sW;
				setsObj.seath = sH;
				setsObj.bgcolor = sBC != "null" ? sBC : "";
				setsObj.remarks = sRm != "null" ? sRm : "";
				setsObj.placeType = plcType;
				setsObj.label = "seat";
				setsArr.push(setsObj);
				isSave = true;
			} else {
				isSave = false;
				return false;
			}
		} else {
			var sC = $(this).text(); //标记内容
			if(sC != "") {
				var sId = $(this).attr("id"); //标签Id
				var sX = $(this).position().left; //x坐标
				var sY = $(this).position().top; //y坐标
				var sW = $(this).get(0).offsetWidth; //座位宽度
				var sH = $(this).get(0).offsetHeight; //座位高度

				var setsObj = new Object();
				setsObj.id = sId;
				setsObj.seatnumber = sC;
				setsObj.seattype = "";
				setsObj.seatstate = "";
				setsObj.remarks = "";
				setsObj.placeType = plcType;
				setsObj.seatx = Number(sX) + Number(scroW);
				setsObj.seaty = Number(sY) + Number(scroH);
				setsObj.seatw = sW;
				setsObj.seath = sH;
				setsObj.label = "sign";
				setsArr.push(setsObj);
			}
		}
	});

	if(isSave == true) {
		axios.post(server.local_path + "meetseat/insSeated", Qs.stringify({
			siteid: selRid,
			seateds: JSON.stringify(setsArr)
		}), {
			headers: JSON.parse(header)
		})
		.then(function(response) {
			var resultInfo = response.data;
			if(resultInfo.errcode == 200) {
				layer.msg("保存成功");
				setTimeout(function() {
					clearContent();
					//加载排座信息
					loadSetsInfo(selRid, selRimg);
				}, 1000);
			}
			else{
				layer.msg(resultInfo.errmsg);
			}
		}).catch(function(error) {
			layer.msg(error.response.data.message);
		});
	} else {
		layer.msg("座位编号设置不完整");
	}
}

//添加座位
$("#addSets").on("click", function() {
	clearSeatSelected();//清空座位选中样式
	var wNum = $("#setsW").val(); //座位宽度
	var hNum = $("#setsH").val(); //座位高度
	var areaId = $("#setsT").combobox("getValue"); //区域Id
	var bgColor = "";
	//遍历区域类型数组，获取区域颜色
	for(var i = 0; i < areaArr.length; i++) {
		if(areaId == areaArr[i].id) {
			bgColor = areaArr[i].colour;
			break;
		}
	}

	if(wNum != "" && hNum != "") {
		var seatsNum = $("#initNum").val();
		$("#initNum").val("1");
		for(var i = 0; i < seatsNum; i++) {
			var topNum = scroH + Number(hNum) /*40*/ ;
			var leftNum = scroW + (Number(i) + 1) * (Number(wNum) + 5) /*40*/ ;
			var addStr = '<div onclick="seatesClick($(this));" class="drag isSeat selected" style="z-index: 1;width:' + wNum + 'px;height:' + hNum + 'px;top:' + topNum + ';left:' + leftNum + ';">' +
				'<label style="color:white;"></label>' + //第一个label放座位号
				'<label style="display:none; color:black;">' + $("#setsT").combobox("getValue") + '</label>' + //第二个label放座位类型
				'<label style="display:none; color:black;"></label>' + //第三个label放座位状态
				'<label style="display:none; color:black;"></label>' + //第四个label放座位Id
				'<label style="display:none; color:black;"></label>' + //第五个label放座位第几排
				'<label style="display:none; color:white;"></label>' + //第六个label放座位第几座
				'<label style="display:none; color:black;">' + bgColor + '</label>' + //第七个label放座位背景色
				'<label style="display:none; color:black;"></label>' + //第八个label放座位备注
				'</div>';
			$(".box").append(addStr);
		}
		initDrag();
		setCircular();//矩阵排列
		setColorByAreaType(1);
		clearSeatSelected();//清空座位选中样式
	} else {
		layer.msg("请输入宽度与高度");
	}

});

//添加标记
$("#addSigns").on("click", function() {
	var wNum = $("#setsW").val(); //宽度
	var hNum = $("#setsH").val(); //高度
	if(wNum != "" && hNum != "") {
		var seatsNum = $("#initNum").val();
		$("#initNum").val("1");
		for(var i = 0; i < seatsNum; i++) {
			var topNum = scroH + scroH + Number(hNum) /*40*/ ;
			var leftNum = (Number(i) + 1) * (Number(wNum) + 5) /*40*/ ;
			var addStr = '<div id="" onclick="signsClick($(this));" class="signDrag isSign" style="z-index: 1;border: 1px solid #dce0e4;width:' + wNum + 'px;height:' + hNum + 'px;top:' + topNum + ';left:' + leftNum + ';"></div>';
			$(".box").append(addStr);
		}
		initDrag();
	}
});

//删除座位
$("#delSets").on("click", function() {
	var zxNum = 0; //执行次数
	var selList = new Array(); //选择的座位或标记

	seatNo = selSeatId[0];
	var sidStr = "";
	$('.box div').each(function() {
		if($(this).is('.selected')) {
			var sId = "";
			//座位
			if($(this).is('.drag')) {
				sId = $(this).children().get(3).innerHTML; //座位Id
				//过滤掉座位Id为空的座位
				if(sId != "") {
					sidStr += sId + ",";
				}
				selList.push($(this));
			}
			//标记
			else {
				sId = $(this).attr("id"); //标记Id
				//过滤掉标记Id为空的座位
				if(sId != "") {
					sidStr += sId + ",";
				}
				selList.push($(this));
			}
		}
	});

	clearContent();

	//组装ID字符串
	sidStr = sidStr.substring(0, sidStr.length - 1);
	if(sidStr != "") {
		layer.confirm('确定要删除吗？', {}, function(index) {
			//把选中的座位或标记在容器内删除
			for(var i = 0; i < selList.length; i++) {
				$(selList[i]).remove();
			}
			//把选中的座位或标记请求接口删除
			axios.post(server.local_path + "meetSeat/dels", Qs.stringify({
				ids: sidStr
			}), {
				headers: JSON.parse(header)
			})
			.then(function(response) {
				var resultInfo = response.data;
				if(resultInfo.errcode == 200) {
					layer.msg("删除成功");
				}
			}).catch(function(error) {
				layer.msg(error.response.data.message);
			});
			//关闭
			layer.close(index);
		});
	}
	else{
		layer.confirm('确定要删除吗？', {}, function(index) {
			//把选中的座位或标记在容器内删除
			for(var i = 0; i < selList.length; i++) {
				$(selList[i]).remove();
			}
			//关闭
			layer.close(index);
		});
	}
});

//全选座位
$("#allSel").on("click", function() {
	selSeatId = new Array();
	$('.box div').each(function() {
		if(!$(this).is('.selected')) {
			$(this).addClass("selected");
			$(this).css("background", "");
		}
		//座位
		if($(this).is('.drag')) {
			var selItem = $(this).children().get(0).innerHTML;
			selSeatId.push(selItem);
		}
	});
});

//给座位设置编号
$("#editSetsNum").change(function() {
	if(selSets != undefined) {
		var isPass = true;
		$('.box div').each(function() {
			if($(this).is('.drag')) {
				var sNo = $(this).children().get(0).textContent; //座位号
				var sTp = $(this).children().get(1).textContent; //座位类型
				//过滤掉座位号为空的座位
				if(sNo != "") {
					//相同座位号，相同类型的座位存在，不通过
					if(sNo == $("#editSetsNum").val() && sTp == $("#setsT").combobox("getValue")) {
						isPass = false;
						return isPass;
					} else {
						isPass = true;
						return isPass;
					}
				}
			}
		});

		if(isPass == false) {
			layer.msg($("#setsT").combobox("getText") + "已存在相同座位号，不能重复！");
			$(selSets).children().get(0).textContent = "";
			return;
		} else {
			$(selSets).children().get(0).textContent = $("#editSetsNum").val();
			$("#editSetsNum").blur();
		}
	}
});

//选中座位，展示座位信息到指定标签
function selSeatesInfo() {
	var selSetsNum = $(selSets).children().get(0).textContent; //选中的座位号
	var selSetsType = $(selSets).children().get(1).textContent; //选中的座位类型
	var selSetsRow = $(selSets).children().get(4).textContent; //选中的座位第几排
	var selSetsNos = $(selSets).children().get(5).textContent; //选中的座位第几座
	var selSetsRmk = $(selSets).children().get(7).textContent; //选中的座位备注
	var selSetsW = selSets.get(0).offsetWidth;
	var selSetsH = selSets.get(0).offsetHeight;
	$("#editSetsNum").val(selSetsNum);
	$("#editSetsNum").select();
	$("#setsW").val(selSetsW);
	$("#setsH").val(selSetsH);
	$("#setsT").combobox("setValue", selSetsType != "null" ? selSetsType : "");
	$("#setsRow").val(selSetsRow);
	$("#setsNum").val(selSetsNos);
	$("#setsRmk").val(selSetsRmk != "null" ? selSetsRmk : "");
}

//座位点击事件
function seatesClick(seate) {
	//如果没有按下ctrl键
	if(!keepCtrl) {
		selSets = seate;
		$('.box div').each(function() {
			$(this).removeClass("selected ctrlSel");
		});
		$(seate).addClass("selected");
		$(seate).css("background", "");
		selSeatId = new Array();
		var selItem = $(seate).children().get(0).innerHTML;
		selSeatId.push(selItem);
		selSeatesInfo();
	} else {
		selSets = undefined;
		$(seate).addClass("selected ctrlSel");
		selSeatId = new Array();
		$('.box div').each(function() {
			if($(this).is('.selected')) {
				$(this).addClass("ctrlSel");
				$(seate).css("background", "");
				var selItem = $(this).children().get(0).innerHTML;
				selSeatId.push(selItem);
			} else
				$(this).removeClass("selected ctrlSel");
		});
	}
}

//标签点击事件
function signsClick(sign) {
	//如果没有按下ctrl键
	if(!keepCtrl) {
		selSign = sign;
		$('.box div').each(function() {
			$(this).removeClass("selected ctrlSel");
		});
		$(sign).addClass("selected");
	} else {
		selSign = undefined;
		$(sign).addClass("selected ctrlSel");
		$('.box div').each(function() {
			if($(this).is('.selected')) {
				$(this).addClass("ctrlSel");
			} else
				$(this).removeClass("selected ctrlSel");
		});
	}
	setColorByAreaType(2);
}

//根据座位所属区域，设置座位颜色  1:所有的   2:选中的
function setColorByAreaType(v) {
	if(v == 1) {
		$('.box div').each(function() {
			if($(this).is('.drag')) {
				var areaType = $(this).children().get(1).textContent; //座位类型
				for(var i = 0; i < areaArr.length; i++) {
					if(areaType == areaArr[i].id) {
						$(this).css("background", areaArr[i].colour);
						break;
					}
				}
			}
		});
	} else {
		$('.box div').each(function() {
			if($(this).is('.drag')) {
				if(!$(this).is('.selected')) {
					var areaType = $(this).children().get(1).textContent; //座位类型
					for(var i = 0; i < areaArr.length; i++) {
						if(areaType == areaArr[i].id) {
							$(this).css("background", areaArr[i].colour);
							break;
						}
					}
				}
			}
		});
	}
}

//清空座位选中样式
function clearSeatSelected() {
	$('.box div').each(function() {
		if($(this).is('.drag')) {
			if($(this).is('.selected')) {
				$(this).removeClass("selected");
			}
		}
	});
}

window.addEventListener("storage", function (e) {
	if(e.key == "theme_color"){
		themeColor = e.newValue;
		changeThemeColor(e.newValue);
	}
	if(e.key == "size"){
		if(e.newValue == 'false'){
			$('.box').css("width", boxWidth + 222);
		}
		else{
			$('.box').css("width", boxWidth);
		}
	}
});

var root = document.querySelector(':root');
 //即时换色
// 设置需要换色的元素及其样式
function changeThemeColor(colo){
	$(".btn-info").css("background", colo);
	root.setAttribute('style', '--color: '+ colo);
}

$(".btn-info").hover(function(){
	$(this).css('opacity', ".6");
},
function(){
	$(this).css('opacity', "1");
});

$(".cancel").hover(function(){
	$(this).css("background", "transparent").css("border", "1px solid"+ themeColor).css("color", themeColor);
},
function(){
	$(this).css("background", "transparent").css('border', "1px solid rgba(217,217,217,1)").css("color", "#000");
});
