<template>
  <div class="zy-cascader"
       :style="{ width: width + 'px' }">
    <el-popover popper-class="zy-cascader-popover"
                :trigger="trigger"
                :disabled="disabled"
                :width="width"
                v-model="options_show">
      <el-input slot="reference"
                clearable
                @blur="blur"
                @focus="focus"
                ref="zy-cascader"
                v-model="input"
                @clear="remove"
                :disabled="disabled"
                :placeholder="inputText">
        <i slot="suffix"
           v-if="input == ''"
           :class="['zy-cascader-icon', 'el-icon-arrow-down', options_show ? 'zy-cascader-icon-a' : '']"></i>
      </el-input>
      <el-scrollbar class="zy-cascader-box">
        <zy-tree-components :tree="data"
                            v-model="id"
                            :child="child"
                            :props="props"
                            :keyword="input"
                            :node-key="nodeKey"
                            :determine="determine"
                            @on-tree-click="selectedClick"></zy-tree-components>
      </el-scrollbar>
    </el-popover>
  </div>
</template>
<script>
export default {
  name: 'zyCascader',
  data () {
    return {
      id: this.value,
      input: '',
      selectData: {},
      options_show: false,
      inputText: '',
      determine: false
    }
  },
  props: {
    value: [String, Number, Array, Object],
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 树结构配置
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label'
        }
      }
    },
    // 触发方式 click/focus/hover/manual
    trigger: {
      type: String,
      default: 'click'
    },
    placeholder: {
      type: String,
      default: '请选择内容'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 宽度
    width: {
      type: String,
      default: '296'
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    child: {
      type: Boolean,
      default: true
    }
  },
  model: {
    prop: 'value',
    event: 'id'
  },
  created () {
    this.inputText = this.placeholder
  },
  watch: {
    value (val) {
      if (val) {
        this.id = val
        this.determine = true
      } else {
        this.id = ''
        this.input = ''
        this.inputText = this.placeholder
      }
    },
    id (val) {
      this.$emit('id', val)
    },
    selectData (val) {
      this.$emit('select', val)
    },
    options_show () {
      if (this.input === '' && this.id && !this.options_show) {
        this.determine = true
        this.input = this.selectedText
        this.inputText = this.selectedText
      }
    }
  },
  methods: {
    blur () {
      if (this.input === '' && this.id && !this.options_show) {
        this.determine = true
        this.input = this.selectedText
        this.inputText = this.selectedText
      }
    },
    focus () {
      this.determine = false
      if (this.input && this.id) {
        this.input = ''
        this.inputText = this.selectedText
      }
    },
    // 下拉框选中事件
    selectedClick (data) {
      // if (this.selectedText === data[this.props.label]) {
      //   return
      // }
      this.input = data[this.props.label]
      this.selectData = data
      this.options_show = false
      this.inputText = data[this.props.label]
      this.selectedText = data[this.props.label]
      this.determine = true
      this.$refs['zy-cascader'].focus()
      setTimeout(() => {
        this.$refs['zy-cascader'].blur()
      }, 22)
    },
    remove () {
      this.id = ''
      this.input = ''
      this.inputText = this.placeholder
      this.selectData = {}
      setTimeout(() => {
        this.options_show = false
      }, 22)
    }
  }
}
</script>
<style lang="scss">
@import "./zy-cascader.scss";
</style>
