<template>
  <div class="activity-manage">
    <search-box @search-click="search" @reset-click="reset" title="活动管理筛选">
      <zy-widget label="关键字">
        <el-input v-model="searchParams.keyword" placeholder="请输入关键字" clearable @keyup.enter.native="search"></el-input>
      </zy-widget>
      <zy-widget label="活动类型">
        <zy-cascader width="222" node-key="id" clearable v-model="searchParams.meetType" :data="typeList" placeholder="请选择类型">
        </zy-cascader>
      </zy-widget>
    </search-box>
    <div class="qd-list-wrap">
      <div class="qd-btn-box">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
        <el-button type="primary" plain @click="handleAppShow(1)" v-if="selectionList.length>0">APP显示</el-button>
        <el-button type="primary" plain @click="handleAppShow(2)" v-if="selectionList.length>0">APP不显示</el-button>
        <el-button type="primary" plain @click="handleExcel" v-if="selectionList.length>0">导出文档</el-button>
        <el-button type="success" plain @click="handleWord" v-if="selectionList.length>0">导出表格</el-button>
        <el-button type="danger" icon="el-icon-delete" plain @click="handleBatchDelete">删除</el-button>
      </div>
      <div class="qd-table-box">
        <zy-table>
          <el-table :data="list" stripe border ref="table" slot="zytable" @selection-change="handleSelectionChange">
            <el-table-column type="selection" fixed="left" width="60"></el-table-column>
            <el-table-column type="index" label="序号" width="80"></el-table-column>
            <el-table-column label="活动主题" min-width="240" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-button type="text" @click="handleInfo(scope.row.id)">{{scope.row.meetName}}</el-button>
              </template>
            </el-table-column>
            <el-table-column label="活动类型" prop="meetType" width="120"></el-table-column>
            <el-table-column label="活动时间" prop="starTime" min-width="360">
              <template slot-scope="scope">{{scope.row.meetStartTime}}至{{scope.row.meetEndTime}}</template>
            </el-table-column>
            <el-table-column label="签到时间" prop="endTime" min-width="360">
              <template slot-scope="scope">{{scope.row.meetSignBeginTime}}至{{scope.row.meetSignEndTime}}</template>
            </el-table-column>
            <el-table-column label="组织部门" prop="organizer" width="120"></el-table-column>
            <el-table-column label="是否APP显示" min-width="120">
              <template slot-scope="scope">
                <i class="el-icon-check sp-icon" v-if="scope.row.isAppShow === 1"></i>
                <i class="el-icon-close" v-else></i>
              </template>
            </el-table-column>
            <el-table-column label="操作员" prop="createName" min-width="120"></el-table-column>
            <el-table-column label="活动状态" prop="state" min-width="120"></el-table-column>
            <el-table-column label="操作" min-width="600" fixed="right">
              <template slot-scope="scope">
                <el-button @click="handleSign(scope.row.id)" type="text">签到码</el-button>
                <el-button @click="handleExportSign(scope.row.id)" type="text">签到码导出</el-button>
                <el-button @click="handleLeaveVerify(scope.row.id)" type="text">请假审核</el-button>
                <el-button @click="handleAttendance(scope.row.id)" type="text">考勤情况</el-button>
                <el-button @click="handleFiles(scope.row.id)" type="text">活动资料</el-button>
                <el-button type="text" @click="handleNotice(scope.row.id,1)">报名通知</el-button>
                <el-button type="text" @click="handleNotice(scope.row.id,2)">签到通知</el-button>
              </template>
            </el-table-column>
          </el-table>
        </zy-table>
      </div>
      <div class="qd-page-box">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page.sync="page" :page-sizes="[10, 20, 50, 80, 100, 200, 500]" :page-size.sync="pageSize" background layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
      </div>
    </div>
    <zy-pop-up v-model="exportShow" title="导出">
      <zy-export :excelId="excelId" :type="550" @callback="exportShow = false"></zy-export>
    </zy-pop-up>
    <zy-pop-up v-model="isCard" title="活动签到码">
      <card :id="id"></card>
    </zy-pop-up>
    <preview-code v-if="isQrcode" @cancel="isQrcode = false" :url="codeUrl" name="签到码"></preview-code>
  </div>
</template>

<script>
import table from '@mixins/table.js'
import { filterParams, checkParams } from '@/common/handleParams'
import card from './widget/card'
export default {
  mixins: [table],
  components: { card },
  data() {
    return {
      searchParams: {
        keyword: '',
        meetType: ''
      },
      typeList: [],
      exportShow: false,
      excelId: null,
      isQrcode: false,
      codeUrl: null,
      isCard: false,
      id: null
    }
  },
  inject: ['newTab'],
  created() {
    this.getTypeList()
    this.getList()
  },
  methods: {
    // 获取所属分类
    getTypeList () {
      this.$api.microAdvice.microAdviceManageTree({ treeType: 3 }).then(res => {
        this.typeList = res.data
      })
    },
    getList() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize
      }
      params = Object.assign(params, filterParams(this.searchParams))
      this.$api.activity.list(params).then(res => {
        var { data, total } = res
        this.list = data
        this.total = total
      })
    },
    search() {
      if (!checkParams(this.searchParams)) {
        return this.$message.warning('请输入关键字或选择想要搜索的')
      }
      this.getList()
    },
    reset() {
      this.searchParams = {
        keyword: '',
        type: ''
      }
      this.getList()
    },
    handleAdd() {
      const mid = new Date().getTime().toString()
      this.newTab({
        name: '新建活动',
        menuId: mid,
        to: '/activity-add',
        params: { mid }
      })
    },
    handleDelete(ids) {
      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activity.dels(ids).then((res) => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消删除')
        return false
      })
    },
    handleBatchDelete() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要删除的项')
      }
      this.handleDelete(this.selectionList.map((v) => v.id).join(','))
    },
    handleSign(id) {
      this.$api.activity.qrcode(id).then(res => {
        this.isQrcode = true
        this.codeUrl = res.data
      })
    },
    handleNotice(id, type) {
      this.$confirm('此操作将发送短信通知选中的活动的参与人, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activity.sendMsg({ actvityId: id, type }).then((res) => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('通知成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消通知')
        return false
      })
    },
    handleAppShow(type) {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要审核的项')
      }
    },
    handleExcel() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要导出的项')
      }
      this.excelId = this.selectionList.map(item => item.id).join(',')
      this.exportShow = true
    },
    handleWord() {},
    handleLeaveVerify(id) {
      this.newTab({
        name: '请假审核',
        menuId: id,
        to: '/activity-leave',
        params: { id: id, mid: id }
      })
    },
    handleAttendance(id) {
      this.newTab({
        name: '考核情况',
        menuId: id,
        to: '/activity-attendance',
        params: { id: id, mid: id }
      })
    },
    handleInfo(id) {
      this.newTab({
        name: '编辑活动',
        menuId: id,
        to: '/activity-add',
        params: { id: id, mid: id }
      })
    },
    handleFiles(id) {
      this.newTab({
        name: '活动资料',
        menuId: id,
        to: '/activity-files',
        params: { id: id, mid: id }
      })
    },
    handleExportSign(id) {
      this.id = id
      this.isCard = true
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-manage {
  width: 100%;
  height: 100%;
}
</style>
