
import JSZipUtils from 'jszip-utils'
import docxtemplater from 'docxtemplater'
import { saveAs } from 'file-saver'
import JSZip from 'jszip'
export default {
  data () {
    return {
      zipName: '导出word.zip',
      zip: null,
      download: [],
      length: 0
    }
  },
  watch: {
    download (val) {
      if (val.length === this.length) {
        var word = this.zip.generate({ type: 'blob' })
        saveAs(word, this.zipName)
        this.zip = null
        this.download = []
      }
    }
  },
  methods: {
    create (res = '') {
      if (res) {
        this.zip = new JSZip(res)
      } else {
        this.zip = new JSZip()
      }
    },
    insert (name, file) {
      this.zip.file(name, file, { binary: true })
    },
    wordList (url, list) {
      this.length = list.length
      list.forEach((item) => {
        this.exportWords(url, item)
      })
    },
    exportWords (url, data) {
      JSZipUtils.getBinaryContent(url, (error, content) => {
        if (error) {
          throw error
        }
        const zip = new JSZip(content)
        let doc = new docxtemplater().loadZip(zip) // eslint-disable-line
        doc.setData({
          ...data
        })
        try {
          doc.render()
        } catch (error) {
          const e = {
            message: error.message,
            name: error.name,
            stack: error.stack,
            properties: error.properties
          }
          console.log(JSON.stringify({ error: e }))
          throw error
        }
        const out = doc.getZip().generate({ type: 'ArrayBuffer' })
        this.zip.file(data.docName + '.docx', out, { binary: true })
        this.download.push(data.proposalId)
      })
    }
  }
}
