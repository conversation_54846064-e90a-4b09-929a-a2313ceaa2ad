<template>
  <el-upload
    class="avatar-uploader"
    action="/#"
    accept=".jpg,.jpeg,.png,.PNG,.JPG"
    :show-file-list="false"
    :http-request="customUpload"
    :before-upload="beforeAvatarUpload"
  >
    <img v-if="photoList.length" :src="photoList[0].filePath" class="avatar" />
    <img v-else-if="files.length > 0" :src="files[0].filePath" class="avatar" />
    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
    <div slot="tip" class="el-upload__tip">{{ tip }}</div>
  </el-upload>
</template>

<script>
export default {
  name: 'qd-upload-img',
  props: {
    value: Array,
    module: {
      type: String,
      default: 'splashImg'
    },
    tip: {
      type: String,
      default: '只能上传jpg/png等图片格式的文件，且不超过10mb'
    },
    size: {
      type: Array,
      default: () => { return [] }
    },
    photoList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  model: {
    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg
    event: 'file'// 这个字段，是指父组件监听 parent-event 事件
  },
  data () {
    return {
      files: []
    }
  },
  watch: {
    value (val) {
      if (val) {
        this.files = val
      } else {
        this.files = []
      }
    },
    files (val) {
      this.$emit('file', val)
    }
  },
  methods: {
    // 校验文件类型和文件大小
    beforeAvatarUpload (file) {
      // const isJPG = file.type === 'image/jpeg'
      const isLt2M = file.size / 1024 / 1024 < 10
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      if (testmsg !== 'png' && testmsg !== 'jpg' && testmsg !== 'PNG' && testmsg !== 'jpeg' && testmsg !== 'JPG' && testmsg !== 'gif') {
        this.$message.error('图片文件格式暂时不支持!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 10MB!')
        return false
      }
      return isLt2M && testmsg
    },
    // 上传逻辑
    async customUpload (file) {
      const siteId = JSON.parse(sessionStorage.getItem('user' + this.$logo())).areaId
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', this.module)
      formData.append('siteId', siteId)
      this.$api.microAdvice.uploadFile(formData).then(res => {
        const { errcode, data } = res
        if (errcode === 200) {
          this.files = data
        }
      })
      this.$emit('initPhoto')
    }
  }
}
</script>

<style lang="scss">
.avatar-uploader {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 148px;
    height: 148px;
    line-height: 148px;
    text-align: center;
  }
  .avatar {
    width: 148px;
    height: 148px;
    display: block;
  }
}
</style>
