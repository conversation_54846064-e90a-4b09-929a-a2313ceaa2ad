<template>
  <div class="addGrouping">
    <el-form
      :model="form"
      :rules="rules"
      inline
      ref="form"
      label-position="top"
      class="newForm"
    >
      <el-form-item
        label="分组名称"
        class="form-title"
        prop="groupName"
      >
        <el-input
          placeholder="请输入分组名称"
          v-model="form.groupName"
          clearable
        >
        </el-input>
      </el-form-item>

      <el-form-item
        label="是否显示"
        class="form-input"
      >
        <el-radio-group v-model="form.isUsing">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        label="选择年份"
        class="form-input"
      >
        <el-date-picker
          v-model="form.groupYear"
          align="right"
          type="year"
          placeholder="选择年"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item
        label="备注信息"
        class="form-title"
      >
        <el-input
          placeholder="请输入备注信息"
          v-model="form.remarks"
          clearable
        >
        </el-input>
      </el-form-item>
      <div class="form-button">
        <el-button
          type="primary"
          @click="submitForm('form')"
        >确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      form: {
        groupName: '',
        isUsing: '1',
        remarks: '',
        groupYear: ''
      },
      rules: {
        groupName: [
          { required: true, message: '请输入模块名称', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    if (this.id) {
      this.getinfo()
    }
  },
  methods: {
    async getinfo () {
      const res = await this.$api.appManagement.yearsummarygroupinfo(this.id)
      // console.log(res)
      var { data } = res
      this.form = data
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/yearsummarygroup/add'
          if (this.id) {
            url = '/yearsummarygroup/edit'
          }
          this.$api.appManagement.yearsummarygroup(url, {
            id: this.id,
            groupName: this.form.groupName,
            isUsing: this.form.isUsing,
            groupYear: this.$format(this.form.groupYear, 'YYYY'),
            remarks: this.form.remarks
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.addGrouping {
    width: 692px;
    height: 100%;
    padding: 24px 40px;

    .form-icon {
        width: 296px;

        .form-icon-uploader {
            width: 128px;
            height: 128px;
            border: 1px dashed #ccc;

            &:hover {
                border-color: #199bc5;
            }

            .user-uploader-icon {
                font-size: 28px;
                color: #8c939d;
                width: 128px;
                height: 128px;
                line-height: 128px;
                text-align: center;
            }

            .user-img {
                width: 128px;
                height: 128px;
                display: block;
            }
        }
    }
}
</style>
