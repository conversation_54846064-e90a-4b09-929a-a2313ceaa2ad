<div class="apiDetail">
<div>
	<h2><span>Number</span><span class="path">setting.edit.drag.</span>borderMax</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.exedit</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>When drag a node to root, the zTree's inner border width. (Unit: px) It is valid when <span class="highlight_red">[setting.edit.enable = true]</span></p>
			<p>Default: 10</p>
			<p class="highlight_red">Please adjust the value according to needs.</p>
		</div>
	</div>
	<h3>Examples of setting</h3>
	<h4>1. adjust the inner border width is 20px</h4>
	<pre xmlns=""><code>var setting = {
	edit: {
		enable: true,
		drag: {
			borderMax: 20
		}
	}
};
......</code></pre>
</div>
</div>