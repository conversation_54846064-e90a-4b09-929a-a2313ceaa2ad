<template>
  <div class="vote-manage">
    <search-box @search-click="search" @reset-click="reset" title="筛选">
      <zy-widget label="关键字">
        <el-input
          v-model="searchParams.keyword"
          placeholder="请输入关键字"
          clearable
        ></el-input>
      </zy-widget>
    </search-box>
    <div class="qd-list-wrap">
      <ul class="qd-list-tab">
        <li
          class="tab-item"
          v-for="item in statusList"
          :class="{ active: avtiveId === item.id }"
          @click="tabChange(item)"
          :key="item.id"
        >
          {{ item.label }}
        </li>
      </ul>
      <div class="qd-btn-box">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd"
          >新增</el-button
        >
        <el-button type="danger" plain @click="handleBatchDelete"
          >删除</el-button
        >
      </div>
      <div class="qd-table-box">
        <zy-table>
          <el-table
            :data="list"
            stripe
            border
            ref="table"
            slot="zytable"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              fixed="left"
              width="60"
            ></el-table-column>
            <el-table-column
              type="index"
              label="序号"
              width="80"
            ></el-table-column>
            <el-table-column
              label="题目"
              prop="question"
              min-width="180"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="操作" fixed="right" min-width="300">
              <template slot-scope="scope">
                <el-button @click="handleEdit(scope.row)" type="text"
                  >编辑</el-button
                >
                <el-button @click="handleDelete(scope.row.id)" type="text"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </zy-table>
      </div>
      <div class="qd-page-box">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="page"
          :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
          :page-size.sync="pageSize"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <zy-pop-up v-model="showAdd" title="新建">
      <add-question
        :id="rowID"
        :type="avtiveId"
        @cloesWin="cloesWin"
      ></add-question>
    </zy-pop-up>
  </div>
</template>

<script>
import table from '@mixins/table.js'
import addQuestion from './add'
import { filterParams, checkParams } from '@/common/handleParams'
export default {
  components: { addQuestion },
  mixins: [table],
  data () {
    return {
      searchParams: {
        keyword: ''
      },
      avtiveId: 1,
      statusList: [
        { id: 1, label: '单选' },
        { id: 2, label: '多选' },
        { id: 3, label: '文本' }
      ],
      publishList: [
        { value: 1, label: '已发布' },
        { value: 0, label: '未发布' }
      ],
      excelId: null,
      isQrcode: false,
      codeUrl: null,
      showAdd: false,
      rowID: ''
    }
  },
  created () {
    this.getList()
  },
  inject: ['newTab'],
  methods: {
    cloesWin () {
      this.showAdd = false
      this.getList()
    },
    getList () {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize
      }
      params = Object.assign(params, filterParams(this.searchParams))
      params.answerType = this.avtiveId
      this.$api.appManagement.questionnairequestionbank.list(params).then(res => {
        const { data, total } = res
        this.list = data
        this.total = total
      })
    },
    search () {
      if (!checkParams(this.searchParams)) {
        return this.$message.warning('请输入关键字或选择想要搜索的')
      }
      this.getList()
    },
    reset () {
      this.searchParams = {
        keyword: ''
      }
      this.getList()
    },
    handleAdd () {
      this.rowID = null
      this.showAdd = true
    },
    handleEdit (val) {
      this.rowID = val.id
      this.showAdd = true
    },
    handleDelete (ids) {
      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.appManagement.questionnairequestionbank.del(ids).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消删除')
        return false
      })
    },
    handleBatchDelete () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要删除的项')
      }
      this.handleDeletes(this.selectionList.map(v => v.id).join(','))
    },
    handleDeletes (ids) {
      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.appManagement.questionnairequestionbank.dels({
          ids
        }).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消删除')
        return false
      })
    },
    // TODO 导出表格 更换type
    tabChange (val) {
      this.avtiveId = val.id
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
.vote-manage {
  width: 100%;
  height: 100%;
  .qd-table-box {
    height: calc(100% - 186px);
  }
}
</style>
