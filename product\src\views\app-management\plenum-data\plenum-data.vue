<template>
  <div class="plenum-data">
    <screening-box @search-click="search"
                   :resetButton="false">
      <el-input placeholder="请输入内容"
                v-model="keyword"
                clearable
                @keyup.enter.native="search">
      </el-input>
      <el-select v-model="stateId"
                 filterable
                 clearable
                 placeholder="请选择审核状态">
        <el-option v-for="item in state"
                   :key="item.id"
                   :label="item.name"
                   :value="item.id">
        </el-option>
      </el-select>
    </screening-box>
    <div class="button-box">
      <el-button type="primary"
                 icon="el-icon-plus"
                 v-permissions="'auth:zyinfodetail:add'"
                 @click="newData">新增</el-button>
      <el-button type="primary"
                 icon="el-icon-delete"
                 v-permissions="'auth:zyinfodetail:dels'"
                 @click="deleteClick">删除</el-button>
      <el-button type="primary"
                 icon="el-icon-circle-check"
                 v-permissions="'auth:zyinfodetail:batchUpdate'"
                 @click="passClick(1)">审核通过</el-button>
      <el-button type="primary"
                 icon="el-icon-remove-outline"
                 v-permissions="'auth:zyinfodetail:batchUpdate'"
                 @click="passClick(2)">审核不通过</el-button>
    </div>
    <div class="information-box">
      <div class="information-tree-box">
        <div class="information-tree-text">选择栏目</div>
        <div class="information-tree">
          <zy-tree :tree="tree"
                   v-model="treeId"
                   :props="{ children: 'children', label: 'name'}"
                   @on-tree-click="choiceClick"></zy-tree>
        </div>
      </div>
      <div class="information-data-box">
        <div class="plenum-data">
          <zy-table>
            <el-table :data="tableData"
                      stripe
                      border
                      ref="table"
                      slot="zytable"
                      @select="selected"
                      @select-all="selectedAll">
              <el-table-column type="selection"
                               fixed="left"
                               width="60"></el-table-column>
              <el-table-column label="序号"
                               width="80"
                               prop="sort">
              </el-table-column>
              <el-table-column label="标题"
                               min-width="260"
                               show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-button @click="details(scope.row)"
                             type="text">{{scope.row.title}}</el-button>
                </template>
              </el-table-column>
              <el-table-column label="所属栏目"
                               width="160"
                               prop="structureName">
              </el-table-column>
              <el-table-column label="来源"
                               width="160"
                               prop="source">
              </el-table-column>
              <el-table-column label="发布人"
                               width="160"
                               prop="createBy">
              </el-table-column>
              <el-table-column label="发布时间"
                               width="190"
                               prop="publishDate">
              </el-table-column>
              <el-table-column label="审核状态"
                               width="120"
                               prop="auditingFlag">
              </el-table-column>
              <el-table-column label="操作"
                               fixed="right"
                               v-if="$hasPermission(['auth:zyinfodetail:edit'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button @click="modify(scope.row)"
                             v-permissions="'auth:zyinfodetail:edit'"
                             type="primary"
                             plain
                             size="mini">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </zy-table>
        </div>
        <div class="paging_box">
          <el-pagination @size-change="howManyArticle"
                         @current-change="whatPage"
                         :current-page.sync="page"
                         :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                         :page-size.sync="pageSize"
                         background
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </div>

    <zy-pop-up v-model="show"
               :title="id?'编辑':'新增'">
      <plenum-data-add :id="id"
                       :parentId="treeId"
                       @callback="addCallback"></plenum-data-add>
    </zy-pop-up>

    <zy-pop-up v-model="detailsShow"
               title="详情">
      <plenum-data-details :id="id"></plenum-data-details>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import plenumDataAdd from './plenum-data-add/plenum-data-add'
import plenumDataDetails from './plenum-data-details/plenum-data-details'
export default {
  name: 'plenumData',
  data () {
    return {
      keyword: '',
      treeId: '1',
      tree: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      stateId: '',
      state: [
        { id: '1', name: '审核通过' },
        { id: '0', name: '待审核' },
        { id: '-1', name: '审核不通过' }
      ],
      id: '',
      show: false,
      detailsShow: false
    }
  },
  inject: ['newTab'],
  mixins: [tableData],
  components: {
    plenumDataAdd,
    plenumDataDetails
  },
  mounted () {
    this.informationList()
    this.informationColumnTree()
  },
  methods: {
    search () {
      this.informationList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    modify (row) {
      this.id = row.id
      this.show = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    addCallback () {
      this.informationList()
      this.show = false
    },
    passClick (auditingFlag) {
      if (this.choose.length) {
        this.$confirm(`此操作将选择的资料的状态改为${auditingFlag === 1 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.informationBatchUpdate(this.choose.join(','), auditingFlag)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async informationBatchUpdate (id, auditingFlag) {
      const res = await this.$api.appManagement.informationBatchUpdate({ ids: id, auditingFlag: auditingFlag })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.informationList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async informationColumnTree () {
      const res = await this.$api.appManagement.informationColumnTree({ module: 5 })
      var { data } = res
      var arr = [{ children: [], id: '1', name: '所有' }]
      this.tree = arr.concat(data)
    },
    choiceClick (item) {
      this.informationList()
    },
    async informationList () {
      const res = await this.$api.appManagement.informationList({
        module: 5,
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        structureId: this.treeId,
        auditingFlag: this.stateId
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.informationList()
    },
    whatPage (val) {
      this.informationList()
    },
    deleteClick (row) {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的资料, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.informationListDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async informationListDel (id) {
      const res = await this.$api.appManagement.informationListDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.informationList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
@import "./plenum-data.scss";
</style>
