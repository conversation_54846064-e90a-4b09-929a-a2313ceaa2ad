.project-associated {
  width: 1000px;
  height: 500px;
  padding: 16px 24px;

  .button-box-list {
    height: 30px;
    margin-bottom: 16px;

    .el-button {
      width: 64px;
      max-width: 64px;
      min-width: 64px;
      padding: 0;
      height: 30px;
      font-size: $textSize12;
    }

    .el-input {
      width: 268px;
      float: right;

      .el-input__inner {
        height: 30px;
        line-height: 30px;
        padding-left: 31px;
        font-size: $textSize12;
        border-radius: 2px;
      }

      .el-input__prefix {
        width: 31px;
        height: 100%;
        display: flex;
        align-items: center;
        left: 0;
        padding-left: 9px;
        box-sizing: border-box;

        .input-search {
          width: 14px;
          height: 14px;
          background: url('../../../../assets/img/input-search.png');
          background-size: 100% 100%;
        }
      }
    }
  }

  .project-associated-box {
    display: flex;
    height: calc(100% - 52px);

    .project-associated-tree {
      width: 222px;
      height: 100%;
      border: 1px solid #d9d9d9;

      .el-tree {
        display: inline-block !important;
        min-width: 100%;
        padding-bottom: 17px;

        .el-tree-node__content {
          height: 40px;
          line-height: 40px;

          &:hover {
            background-color: #e0eaf2;
          }
        }

        .is-current > .el-tree-node__content {
          background-color: #e0eaf2;
          color: #199bc5;
          font-weight: 600;
        }
      }
    }

    .project-associated-table {
      width: calc(100% - 222px);
      height: 100%;

      .information-mosaic-list {
        width: 100%;
        height: calc(100% - 52px);
        border: 1px solid #d9d9d9;
        border-left: 0;
        overflow: auto;

        .el-table {
          background-color: #f5f5f5;
        }

        .el-table th {
          background-color: #e6e5e8;
          height: 30px;
          padding: 0;
          font-size: $textSize12;

          & > .cell {
            font-weight: 500;
          }
        }

        .el-table__body td {
          height: 30px;
          padding: 0;
          font-size: $textSize12;
          background-color: #f5f5f5;
        }

        .el-table--enable-row-hover .el-table__body tr:hover > td {
          background-color: #e0eaf2;
        }
      }
    }
  }
}
