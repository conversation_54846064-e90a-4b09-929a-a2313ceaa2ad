---
---
@import "bootstrap-{{ site.bootstrap.version }}/bootstrap.less";
@import "bootstrap-{{ site.bootstrap.version }}/responsive-utilities.less";
@import "sticky-footer.less";

@import "mixins.less";
@import "variables.less";
@import "lazy.less";

.navbar .brand {
  font-family: @serifFontFamily;
  .icon-flag { padding-right: 3px; }
}

.navbar .nav > li > a { padding: 12px 10px 9px; }
.navbar .nav.pull-right { margin-right: -10px; }
//.navbar, .jumbotron, #social-buttons { min-width: 990px; } // necessary fix for non-responsive layouts

h1, h2, h3, h4, h5, h6 { font-family: @serifFontFamily; }

#iconCarousel {
  a { color: @white; }
  @size: 220px;
  font-size: @size;
  text-align: center;
  line-height: @size + 5;
  text-shadow: 2px 2px 3px @grayDarker;
  .carousel-control {
    top: @size + 33px;
    .square(30px);
    background: none;
    border-width: 0;
    font-size: 30px;
    line-height: 25px;
    left: 300/2 - 33px;
    &.right {
      left: auto;
      right: 300/2 - 33px;
    }
  }
}

.jumbotron {
  background: @red;
  border-bottom: 1px solid @redDark;
  &, h1 { color: @white; }
  #gradient > .directional(@red, mix(@red, @redDark, 90%), -40deg);
//  background-color: @red;

//  &:after {
//    content:'';
//    display:block;
//    position:absolute;
//    top:0;
//    right:0;
//    bottom:0;
//    left:0;
//    background:url(../img/grain-tm400.png);
//    opacity:.5;
//  }

  h1 {
    font-size: 80px;
    letter-spacing: -2px;
    line-height: 1;
  }
  p {
    margin-top: 15px;
    margin-bottom: 15px;
    font-size: 30px;
    line-height: 1.3;
    font-weight: lighter;
  }
  .actions { margin-top: 25px;}
  text-shadow: 2px 2px 2px @grayDark;
  ul {
    margin-left: 50px;
    li {
      &.icon-large:before {
        text-indent: -2em;
        vertical-align: baseline;
      }
      font-size: 15px;
      line-height: 30px;
      text-shadow: 1px 1px 1px @grayDark;
    }
  }
  .btn-large {
    font-family: @serifFontFamily;
    font-weight: 500;
    font-size: 24px;
    padding: 17px 30px;
    letter-spacing: -1px;
  }
  .hero-content {
    text-align: center;
  }
  .shameless-self-promotion {
    font-size: 14px;
    margin-top: 25px;
    color: mix(@white, @red, 40%);
    text-shadow: none;
    a { color: mix(@white, @red, 70%); }
    a:hover { color: mix(@white, @red, 100%); }
  }
}

.jumbotron-index {
  padding: 40px 0;
  h1 {
    font-size: 80px;
    letter-spacing: -2px;
    line-height: 1;
    margin: 0 0 15px;
  }
  p {
    margin-top: 15px;
    margin-bottom: 15px;
    font-size: 30px;
    line-height: 1.3;
  }
}

.jumbotron-ad {
  padding: 20px 0;
  h1 { margin-top: 25px; }
  p { margin-bottom: 35px; }
}

.jumbotron-icon {
  padding: 20px 0 30px;
  #gradient > .directional(@grayLighter, mix(@grayLighter, @grayLight, 90%), -40deg);
  color: @grayDarker;
  text-shadow: 1px 1px 1px @white;
  border-bottom: solid 1px mix(@grayLight, @grayLighter, 50%);

  h1 {
    color: @grayDarker;
    font-size: 40px;
    small {
      letter-spacing: normal;
      font-family: @sansFontFamily;
      font-size: @baseFontSize;
      margin-left: 20px;
    }
  }

  .info-details {
    float: left;
    p {
      margin: 25px 0;
      font-weight: bold;
    }
    .dl-horizontal {
      dt { width: @horizontalComponentOffset - 100; }
      dd { margin-left: @horizontalComponentOffset - 85; }
    }
  }
  .icon-2 { font-size: 2em; }
  .icon-3 { font-size: 4em; }
  .icon-4 { font-size: 7em; }
  .icon-5 { font-size: 12em; }
  .icon-6 { font-size: 20em; }

  .icon-1, .icon-2, .icon-3, .icon-4, .icon-5, .icon-6 { margin-right: 1/14em; }
}

//.info-ad {
//  float: right;
//  width: 154px;
//  height: 219px;
//  margin-left: 15px;
//}

.stripe-ad {
  margin-bottom: 22px;
  .lead {
    margin-top: 10px;
    margin-right: 30px;
  }
}

.btn-primary {
  color: @grayDark;
  text-shadow: 0 -1px 0 rgba(255,255,255,.25);
  &:hover {
    text-shadow: 0 -1px 0 rgba(255,255,255,.25);
    color: @grayDark;
  }
}

section { margin-top: 40px; }

.stacked {
  padding-top: 35px;
  height: 105px;
}

#social-buttons {
  ul.unstyled { margin: 0; }

  padding: 22px 0 17px;
  text-align: center;
  background-color: #f5f5f5;
  border-top: 1px solid #fff;
  border-bottom: 1px solid #ddd;
  .btn {
//    font-family: @serifFontFamily;
      font-weight: bold;
//    font-size: @baseFontSize;
    padding: 0px 5px;
    line-height: @baseLineHeight - 3;
    margin: 0;
  }
  .count.btn {
    background: @white;
    font-weight: normal;
  }
}

.the-icons {
  margin-top: 22px;
  .span3 {
    a {
      display: block;
      &, &:hover { color: @grayDarker; }

      cursor: pointer;
      line-height: 32px;
      height: 32px;
      padding-left: 10px;
      .border-radius(6px);

      [class^="icon-"],
      [class*=" icon-"] {
        width: 32px;
        font-size: 14px;
        display: inline-block;
        text-align: right;
        margin-right: 10px;
      }

      &:hover {
        background-color: @errorBackground;
        text-decoration: none;
        [class^="icon-"], [class*=" icon-"] {
          *font-size: 28px;
          *vertical-align: middle;
        }

        [class^="icon-"]:before,
        [class*=" icon-"]:before {
          font-size: 28px;
          vertical-align: -5px;
        }
      }
    }
  }
}

#why, #whats-new {
  .span4 { margin-bottom: 20px; }
  h4 {
    [class^="icon-"]:before,
    [class*=" icon-"]:before {
      vertical-align: -10%;
      font-size: 28px;
      display: inline-block;
      width: 30/28em;
      text-align: center;
      margin-right: 5px;
//      color: mix(@grayLight, @grayLighter, 70%);

      // Gradient on the icons
//      background: -webkit-linear-gradient(mix(@grayLight, @grayLighter, 50%), mix(@gray, @grayLight, 50%));
//      -webkit-background-clip: text;
//      -webkit-text-fill-color: transparent;
    }
  }
}


.rating {
  unicode-bidi: bidi-override;
  direction: rtl;

  font-size: 30px;
  span.star {
    font-family: FontAwesome;
    font-weight: normal;
    font-style: normal;
    display: inline-block;
    &:hover {
      cursor: pointer;
    }
  }
  span.star:before {
    content: "\f006"; // empty star
    padding-right: 5px;
    color: @grayLight;
  }

  span.star:hover:before, span.star:hover ~ span.star:before {
    content: "\f005"; // solid star
    color: #e3cf7a;
  }
}


.label,
.badge {
  background-color: @grayLighter;
}

.well.well-transparent {
  background-color: transparent;
}

footer {
//  #gradient > .vertical(@navbarInverseBackgroundHighlight, @navbarInverseBackground);
  background-color: @red;
  border-top: 1px solid mix(@red, @redDark, 50%);
  a {
    color: @white;
    text-shadow: 0 -1px 0 rgba(0,0,0,.25);
    &:hover {
      color: @white;
    }

  }

  color: mix(@red, @white, 35%);
  text-shadow: 0 -1px 0 rgba(0,0,0,.25);
  margin-top: 60px;
  padding-top: 45px;
  padding-bottom: 60px;
  *zoom: 1; // ie7 hack
  ul {
//    margin-left: 30px;
    line-height: 25px;
  }

  .project { margin-top: 10px; }
}

// makes dropdowns closer for split dropdown
// Links
.navbar .nav > li {
  &.dropdown-split-right > a { padding-left: 7px; }
  &.dropdown-split-left > a { padding-right: 0; }
}

@import "responsive.less";
