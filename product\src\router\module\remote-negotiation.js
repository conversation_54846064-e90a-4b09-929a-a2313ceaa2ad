export default [
  {
    path: '/remote-negotiation-add',
    name: 'remote-negotiation-add',
    component: () => import(/* 远程协商 发起会议 */ '@remote-negotiation/add/add.vue')
  },
  {
    path: '/remote-negotiation-manage',
    name: 'remote-negotiation-manage',
    component: () => import(/* 远程协商 管理 */ '@remote-negotiation/manage/manage.vue')
  },
  {
    path: '/remote-negotiation-config',
    name: 'remote-negotiation-config',
    component: () => import(/* 远程协商 配置 */ '@remote-negotiation/config/config.vue')
  },
  {
    path: '/remote-negotiation-info',
    name: 'remote-negotiation-info',
    component: () => import(/* 远程协商 详情 */ '@remote-negotiation/info/info.vue')
  },
  {
    path: '/remote-negotiation-control',
    name: 'remote-negotiation-control',
    component: () => import(/* 远程协商 会控 */ '@remote-negotiation/control/control.vue')
  }
]
