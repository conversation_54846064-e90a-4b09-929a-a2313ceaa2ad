<template>
  <div class="special-keyword-add">
    <el-form :model="form" ref="form" :rules="addRules" label-position="top">
      <el-form-item label="关键词" prop="questionContent">
        <el-input v-model="form.questionContent"></el-input>
      </el-form-item>
      <el-form-item label="排序">
        <el-input v-model="form.sort" type="number"></el-input>
      </el-form-item>
      <el-form-item label="回复类型" prop="answerType">
        <el-select v-model="form.answerType" placeholder="请选择类型">
          <el-option v-for="item in typelist" :key="item.value" :label="item.name" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="回复内容 - 文本" v-if="form.answerType==='1'">
        <el-input v-model="content"></el-input>
      </el-form-item>
      <el-form-item label="回复内容 - 超链接标题" v-if="form.answerType==='2'">
        <el-input v-model="content"></el-input>
      </el-form-item>
      <el-form-item label="回复内容 - 超链接" v-if="form.answerType==='2'">
        <el-input v-model="form.href"></el-input>
      </el-form-item>
      <el-form-item label="回复内容 - 图片" v-if="form.answerType==='3'">
        <el-upload class="form-upload-demo" drag :before-upload="beforeUpload" :http-request="customUpload"
          :before-remove="customUploadRemove" :file-list="files" :limit="1" multiple action="/">
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持jpg,png,gif,jpeg格式</div>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit('form')">提交</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      content: '',
      form: {
        questionContent: '',
        answerType: '1',
        sort: 0
      },
      addRules: {
        questionContent: [{ required: true, message: '请选择类型', trigger: 'blur' }],
        answerType: [{ required: true, message: '请选择回答', trigger: 'change' }]
      },
      typelist: [
        { name: '文本', value: '1' },
        { name: '超链接', value: '2' },
        { name: '图片', value: '3' }
      ],
      files: []
    }
  },
  mounted () {
    if (this.info) {
      this.form.questionContent = this.info.questionContent
      this.form.answerType = this.info.answerType
      this.form.sort = this.info.sort
      if (this.form.answerType === '1') {
        this.content = this.info.answerContent
      } else if (this.form.answerType === '2') {
        this.content = this.info.hrefTitle
        this.form.href = this.info.href
      } else {
        this.files = this.info.attachmentList.map(item => {
          return {
            name: item.fileName,
            size: item.fileSize,
            type: item.fileType,
            url: item.filePath,
            id: item.id,
            uid: item.id
          }
        })
      }
    }
  },
  methods: {
    // 提交
    onSubmit (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const data = this.form
          if (this.form.answerType === '1') {
            if (this.content === '') {
              return this.$message.warning('请输入文本内容')
            }
            data.answerContent = this.content
          } else if (this.form.answerType === '2') {
            if (this.content === '') {
              return this.$message.warning('请输入文本内容')
            }
            if (this.form.href === '') {
              return this.$message.warning('请输入超链接地址')
            }
            data.hrefTitle = this.content
          } else {
            if (this.files.length === 0) {
              return this.$message.warning('请选择图片类型附件')
            }
            data.attachmentIds = this.files[0].id
          }
          if (this.info) {
            data.id = this.info.id
            this.$api.appManagement.specialKeywordEdit(data).then(res => {
              if (res.errcode === 200) {
                this.$message.success('编辑关键词成功')
                this.$emit('close')
              }
            })
          } else {
            this.$api.appManagement.specialKeywordSave(data).then(res => {
              if (res.errcode === 200) {
                this.$message.success('新增关键词成功')
                this.$emit('close')
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 文件格式判断
    beforeUpload (file) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension9 = testmsg === 'png'
      const extension10 = testmsg === 'jpg'
      const extension12 = testmsg === 'jpeg'
      const extension11 = testmsg === 'gif'
      // word、excel、pdf、jpg、png
      // const isLt2M = file.size / 1024 / 1024 < 10
      if (!extension9 && !extension10 && !extension11 && !extension12) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
        return false
      }
      return extension9 || extension10 || extension11 || extension12
    },
    // 文件上传
    customUpload (file) {
      const siteId = JSON.parse(sessionStorage.getItem('user' + this.$logo())).areaId
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', 'assistantQA')
      formData.append('siteId', siteId)
      this.$api.microAdvice.uploadFile(formData).then(res => {
        const { errcode, data } = res
        if (errcode === 200) {
          const fileData = {
            name: data[0].fileName,
            size: data[0].fileSize,
            type: data[0].fileType,
            url: data[0].filePath,
            id: data[0].id,
            uid: data[0].uid
          }
          this.files.push(fileData)
        }
      })
    },
    // 文件删除
    customUploadRemove (file) {
      for (const i in this.files) {
        if (this.files[i].uid === file.uid) {
          this.files.splice(i, 1)
        }
      }
    }
  }
}
</script>

<style lang="scss">
.special-keyword-add {
  width: 600px;
  padding: 20px;
}
</style>
