// 导入封装的方法
import {
  get,
  post,
  postform
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const PartyBuilding = {

  zyinfodetailList (params) { // 党建列表
    return get('/zyinfodetail/list', params)
  },
  zyinfodetailadd (url, params) { // 党建列表新增,修改
    return post(url, params)
  },
  zyinfodetaildel (params) { // 党建列表shanchu
    return post('/zyinfodetail/dels', params)
  },
  zyinfodetailbatchUpdate (params) { // 党建列表审核
    return post('/zyinfodetail/batchUpdate', params)
  },

  zyinfodetailinfo (params) { // 党建列表详情
    return get(`/zyinfodetail/info/${params}`)
  },
  zyinfostructureltree (params) { // 党建类型管理树
    return get('/zyinfostructure/tree', params)
  },
  zyinfostructurellist (params) { // 党建类型管理列表
    return get('/zyinfostructure/list', params)
  },
  zyinfostructureladdEdit (url, params) { // 党建类型管理新增修改
    return post(url, params)
  },
  zyinfostructureinfo (params) { // 党建列表详情
    return get(`/zyinfostructure/info/${params}`)
  },
  zyinfostructureldels (params) { // 党建类型管理列表
    return get('/zyinfostructure/dels', params)
  },
  zypartyinfoanalysepartyStatus (params) { //  资讯查看状态
    return post('/zypartyinfoanalyse/partyStatus', params)
  },
  zypartyinfoanalysepartyInfoBrowseDetail (params) { //  通知查看详情
    return post('/zypartyinfoanalyse/partyInfoBrowseDetail', params)
  },
  zypartyinfoanalysepartyInfoDetail (params) { //  党员信息统计详情接口
    return post('/zypartyinfoanalyse/partyInfoDetail', params)
  },

  zypartyinfoanalysepartyStudy (params) { //  党建理论学习统计
    return post('/zypartyinfoanalyse/partyStudy', params)
  },
  zypartyinfoanalysepartyInfo (params) { //  党员信息统计：年龄、性别、学历、党员类型
    return post('/zypartyinfoanalyse/partyInfo', params)
  },
  wholeuserlist (params) { //  党员信息统计：年龄、性别、学历、党员类型
    return post('/wholeuser/list', params)
  },

  wholeuserparters (params) { //  党员信息管理
    return post('/wholeuser/parters', params)
  },
  wholeusereditadd (url, params) {
    return post(url, params)
  },
  wholeuserinfo (params) {
    return post(`/wholeuser/info/${params}`)
  },
  wholeuserbatchdel (params) { //  党员信息管理
    return post('/wholeuser/batch/del', params)
  }

}
export default PartyBuilding
