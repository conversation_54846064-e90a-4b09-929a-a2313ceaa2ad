const currentMember = () => import('@/views/memberInformation/generalCurrentMember/currentMember')
const nationalCurrentMember = () => import('@/views/memberInformation/generalCurrentMember/nationalCurrentMember')
const beforeMember = () => import('@/views/memberInformation/generalBeforeMember/beforeMember')
const nationalBeforeMember = () => import('@/views/memberInformation/generalBeforeMember/nationalBeforeMember')
const memberStatistical = () => import('@/views/memberInformation/generalMemberStatistical/memberStatistical')
const nationalMemberStatistical = () => import('@/views/memberInformation/generalMemberStatistical/nationalMemberStatistical')
const memberAudit = () => import('@/views/memberInformation/member-audit/member-audit.vue')
const memberAdd = () => import('@/views/memberInformation/generalCurrentMember/memberNew/memberAdd')
const memberDetailsQd = () => import('@/views/memberInformation/generalCurrentMember/memberDetails/memberDetailsQd')
const memberModifyRecord = () => import('@/views/memberInformation/memberModifyRecord/memberModifyRecord')

const memberInformation = [
  { // 本届委员
    path: '/currentMember',
    name: 'currentMember',
    component: currentMember
  },
  { // 本届全国委员
    path: '/nationalCurrentMember',
    name: 'nationalCurrentMember',
    component: nationalCurrentMember
  },
  { // 历届委员
    path: '/beforeMember',
    name: 'beforeMember',
    component: beforeMember
  },
  { // 历届全国委员
    path: '/nationalBeforeMember',
    name: 'nationalBeforeMember',
    component: nationalBeforeMember
  },
  { // 委员统计
    path: '/memberStatistical',
    name: 'memberStatistical',
    component: memberStatistical
  },
  { // 全国委员统计
    path: '/nationalMemberStatistical',
    name: 'nationalMemberStatistical',
    component: nationalMemberStatistical
  },
  { // 代表信息审核
    path: '/memberAudit',
    name: 'memberAudit',
    component: memberAudit
  },
  { // 新增代表信息
    path: '/memberAdd',
    name: 'memberAdd',
    component: memberAdd
  },
  { // 新增代表信息详情
    path: '/memberDetailsQd',
    name: 'memberDetailsQd',
    component: memberDetailsQd
  },
  { // 委员修改记录
    path: '/memberModifyRecord',
    name: 'memberModifyRecord',
    component: memberModifyRecord
  }
]
export default memberInformation
