.zy-calendar {
  max-width: 520px;

  .zy-calendar-selected {
    height: 98px;
    background-color: $zy-color;
    padding: 0 25px;
    padding-top: 19px;

    .years {
      font-size: $textSize16;
      line-height: 24px;
      color: #fff;
      margin-bottom: 7px;
    }

    .time {
      font-size: 24px;
      line-height: 32px;
      color: #fff;
    }

    .fadeY-enter {
      transform: translateY(30px);
      opacity: 0;
    }

    .fadeY-leave-active {
      transform: translateY(-30px);
      opacity: 0;
    }

    .fadeY-enter-active,
    .fadeY-leave-active {
      transition: all 0.3s;
    }
  }

  .zy_container {
    width: 100%;
  }

  li {
    list-style-type: none;
  }

  .zy_content_all {
    font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC',
      'Helvetica Neue', STHeiti, 'Microsoft Yahei', Tahoma, Simsun, sans-serif;
    background-color: #fff;
    width: 100%;
    overflow: hidden;
    padding-bottom: 8px;
  }

  .zy_top_changge {
    display: flex;

    li {
      cursor: pointer;
      display: flex;
      color: #fff;
      font-size: 18px;
      flex: 1;
      justify-content: center;
      align-items: center;
      height: 47px;
    }

    .zy_content_li {
      cursor: auto;
      flex: 2.5;
      color: #262626;
    }

    .zy_jiantou1 {
      width: 12px;
      height: 12px;
      border-top: 2px solid #262626;
      border-left: 2px solid #262626;
      transform: rotate(-45deg);
    }

    .zy_jiantou1:active,
    .zy_jiantou2:active {
      border-color: #ddd;
    }

    .zy_jiantou2 {
      width: 12px;
      height: 12px;
      border-top: 2px solid #262626;
      border-right: 2px solid #262626;
      transform: rotate(45deg);
    }
  }

  .zy_content {
    display: flex;
    flex-wrap: wrap;
    padding: 0 3% 0 3%;
    width: 100%;
  }

  .zy_content:first-child .zy_content_item_tag,
  .zy_content:first-child .zy_content_item {
    color: #ddd;
    font-size: $textSize16;
  }

  .zy_content_item,
  .zy_content_item_tag {
    font-size: 15px;
    width: 13.4%;
    text-align: center;
    color: #fff;
    position: relative;
  }

  .zy_top_tag {
    width: 40px;
    height: 40px;
    line-height: 40px;
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .zy_item_date {
    width: 40px;
    height: 40px;
    line-height: 40px;
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;

    &:hover {
      background: $zy-withColor;
      cursor: pointer;
      border-radius: 100px;
    }
  }

  .zy_content_item {
    height: 40px;

    & > .zy_isMark {
      margin: auto;
      border-radius: 100px;
      z-index: 2;

      &::after {
        content: '';
        position: absolute;
        bottom: 10%;
        left: 50%;
        transform: translateX(-50%);
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #b3b3b3;
      }
    }

    .zy_other_dayhide {
      color: #bfbfbf;
    }

    .zy_want_dayhide {
      color: #bfbfbf;
    }

    .zy_isToday {
      background: $zy-color;
      border-radius: 100px;
      color: #fff;
    }

    .zy_chose_day {
      background: $zy-color;
      color: #fff;
      border-radius: 100px;
    }
  }
}
