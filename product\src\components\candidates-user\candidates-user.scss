.candidates-user {
  width: 660px;
  height: 462px;
  padding-bottom: 56px;
  background-color: #f5f5f5;

  .candidates-user-box {
    width: 100%;
    height: 100%;
    padding-top: 10px;
    display: flex;
    justify-content: space-between;

    .candidates-user-content {
      position: relative;
      width: 400px;
      min-width: 400px;
      height: 100%;
      padding-top: 47px;

      .search-box {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 47px;
        padding-top: 10px;
        padding-left: 16px;
        padding-right: 45px;

        .el-input__inner {
          height: 30px;
          line-height: 30px;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.15);
          border-radius: 2px;
          padding-left: 31px;
          font-size: $textSize12;
        }

        .el-input__prefix {
          width: 31px;
          height: 100%;
          display: flex;
          align-items: center;
          left: 0;
          padding-left: 9px;
          box-sizing: border-box;

          .input-search {
            width: 14px;
            height: 14px;
            background: url('../../assets/img/input-search.png');
            background-size: 100% 100%;
          }
        }
      }

      .user-box {
        height: 100%;
        display: flex;

        .user-tree-box {
          height: 100%;
          width: 200px;
          position: relative;
          padding-top: 32px;

          .institutions-text {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            font-size: $textSize14;
            font-family: PingFang SC;
            font-weight: 400;
            color: #8c8c8c;
            line-height: 32px;
            height: 32px;
            padding-left: 16px;
            background-color: #f5f5f5;
          }

          .user-tree {
            height: 100%;

            .zy-tree {
              width: 200px;
              min-width: 200px;
              background-color: #f5f5f5;

              .zy-tree-components-item {
                background-color: #f5f5f5;

                .zy-tree-components-item-text {
                  height: 30px;
                  line-height: 30px;
                  font-size: $textSize12;
                }
              }
            }
          }
        }

        .user-personnel-box {
          height: 100%;
          width: 200px;
          border-left: 1px solid #d9d9d9;
          box-shadow: 1px 0px 0px 0px rgba(217, 217, 217, 1);
          border-radius: 2px;
          position: relative;
          padding-top: 32px;

          .el-checkbox__inner {
            border-radius: 7px;
          }

          .personnel-checkbox {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            line-height: 32px;
            height: 32px;
            padding-right: 20px;
            background-color: #f5f5f5;
            display: flex;
            justify-content: space-between;

            .el-checkbox {
              height: 100%;
              line-height: 30px;
              margin: 0;
            }

            .personnel-checkbox-text {
              height: 100%;
              color: #8c8c8c;
              padding-left: 16px;
              font-size: $textSize14;
              font-family: PingFang SC;
              font-weight: 400;
            }
          }

          .user-content-box {
            height: 100%;

            .user-content {
              height: 30px;
              display: flex;
              justify-content: space-between;

              .user-content-icon-name {
                height: 100%;
                display: flex;
                align-items: center;

                .user-content-icon {
                  width: 12px;
                  height: 12px;
                  background: url('../../assets/img/user-normal.png');
                  background-size: 100% 100%;
                  margin-left: 17px;
                }

                .user-content-name {
                  width: 138px;
                  height: 100%;
                  line-height: 30px;
                  font-size: $textSize12;
                }
              }

              .el-checkbox {
                height: 100%;
                line-height: 30px;
                margin: 0;
                padding-right: 20px;

                .el-checkbox__label {
                  display: none;
                }
              }
            }
          }
        }
      }
    }

    .selected-user-box {
      position: relative;
      width: 260px;
      min-width: 260px;
      height: 100%;
      padding-top: 32px;
      border-left: 1px solid #d9d9d9;

      .selected-user-number {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        line-height: 32px;
        height: 32px;
        padding-left: 21px;
        padding-right: 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #f5f5f5;

        .selected-user-number-text {
          font-size: $textSize14;
          font-family: PingFang SC;
          font-weight: 400;
          color: #8c8c8c;
        }

        .selected-user-icon-delete {
          width: 20px;
          height: 20px;
          cursor: pointer;
          background: #f5f5f5 url('../../assets/img/icon_close.png') no-repeat
            center center;

          &:hover {
            background-color: #e8e8e8;
          }
        }
      }

      .selected-user {
        height: 100%;
        width: 100%;

        .selected-user-content {
          width: 100%;
          height: 47px;
          display: flex;
          background-color: #f5f5f5;

          .selected-user-icon {
            width: 42px;
            min-width: 42px;
            height: 100%;
            padding-left: 20px;
            padding-top: 7px;

            .selected-user-icon-name {
              width: 12px;
              height: 12px;
              background: url('../../assets/img/user-normal.png');
              background-size: 100% 100%;
            }
          }

          .selected-user-information {
            width: 171px;
            padding-top: 5px;

            .selected-user-name {
              font-size: $textSize12;
              line-height: 17px;
            }

            .selected-user-text {
              font-size: $textSize12;
              line-height: 17px;
              color: #8c8c8c;
            }
          }

          .selected-user-delete {
            width: 46px;
            min-width: 46px;
            height: 100%;
            padding-left: 10px;
            padding-top: 3px;

            .selected-user-icon-delete {
              width: 20px;
              height: 20px;
              cursor: pointer;
              background: #f5f5f5 url('../../assets/img/icon_close.png')
                no-repeat center center;

              &:hover {
                background-color: #e8e8e8;
              }
            }
          }
        }
      }
    }
  }

  .candidates-user-button {
    width: 100%;
    height: 56px;
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    .el-button {
      height: 32px;
      line-height: 32px;
      padding: 0 16px;
      font-size: $textSize14;
    }

    .el-button + .el-button {
      margin-left: 24px;
    }
  }
}
