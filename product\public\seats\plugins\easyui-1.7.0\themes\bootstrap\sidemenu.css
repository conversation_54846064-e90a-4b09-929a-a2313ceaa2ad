.sidemenu .tree-hit {
  background-image: none;
}
.sidemenu-default-icon {
  background-image: none;
  width: 0;
}
.sidemenu .accordion .accordion-header,
.sidemenu .accordion .accordion-body {
  border-bottom-color: transparent;
  background: transparent;
}
.sidemenu .accordion .accordion-header {
  color: #777;
}
.sidemenu .accordion-header .panel-title {
  height: 30px;
  line-height: 30px;
  color: #777;
}
.sidemenu .accordion-header:hover {
  background: #e6e6e6;
  color: #777;
}
.sidemenu .tree-node-hover {
  background: #e6e6e6;
  color: #777;
}
.sidemenu .tree-node-selected {
  border-right: 2px solid #0070a9;
  color: #fff;
  background: #0081c2;
}
.sidemenu .tree-node {
  height: 40px;
}
.sidemenu .tree-title {
  margin: 11px 0;
}
.sidemenu .tree-node-nonleaf {
  position: relative;
}
.sidemenu .tree-node-nonleaf::after {
  display: inline-block;
  content: '';
  position: absolute;
  top: 50%;
  margin-top: -8px;
  background: url('images/accordion_arrows.png') no-repeat 0 0;
  width: 16px;
  height: 16px;
  right: 5px;
}
.sidemenu .tree-node-nonleaf-collapsed::after {
  background: url('images/accordion_arrows.png') no-repeat -16px 0;
}
.sidemenu-collapsed .panel-icon {
  left: 50%;
  margin-left: -8px;
}
.sidemenu-tooltip {
  padding: 0;
  margin: 0 -12px;
  border: 0;
}
.sidemenu-tooltip.tooltip-left {
  margin: 0 12px;
}
.sidemenu-tooltip .tooltip-arrow-outer,
.sidemenu-tooltip .tooltip-arrow {
  display: none;
}
