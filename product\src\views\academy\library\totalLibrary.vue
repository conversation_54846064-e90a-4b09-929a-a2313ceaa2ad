<template>
  <div class="totalLibrary">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input placeholder="请输入关键词"
                v-model="keyword"
                clearable
                @keyup.enter.native="search">
      </el-input>
      <zy-select width="222"
                 node-key="id"
                 :data="bookType"
                 v-model="bookTypeId"
                 @select="bookTypeChoose"
                 :props="{children: 'children',label: 'name'}"
                 placeholder="请选择书库分类"></zy-select>
      <div class="screening-checkbox">
        <el-checkbox v-model="notInCompany">未加入单位书库</el-checkbox>
      </div>
    </screening-box>
    <div class="button-box">
      <el-button type="primary"
                 @click="synchronous">拉取掌阅书籍</el-button>
      <el-button type="primary"
                 @click="determine">拉入单位书库</el-button>
      <el-button type="primary"
                 @click="pageSelected">本页全选</el-button>
      <el-button type="primary"
                 @click="noPageSelected">取消本页选中</el-button>
    </div>
    <el-scrollbar class="totalLibraryBox">
      <div class="totalLibraryListBox">
        <el-checkbox-group v-model="checkedCities"
                           @change="handleCheckedCitiesChange">
          <div class="totalLibraryItem"
               v-for="(item) in tableData"
               @click="bookClick(item)"
               :key="item.id">
            <div class="totalLibraryItemImg">
              <img :src="item.coverImgUrl"
                   alt="">
            </div>
            <div class="totalLibraryItemBox">
              <div class="totalLibraryItemName">{{item.bookName}}</div>
              <div class="totalLibraryItemIntroduction">{{item.bookDescription}}</div>
              <div class="totalLibraryItemAuthor"
                   @click.stop>
                <div class="totalLibraryItemAuthorText">{{item.authorName}}</div>
                <el-checkbox :value="item.id"
                             :label="item.id"></el-checkbox>
                <!-- <div class="libraryAllItemButton">
                  <el-button type="text"
                             @click="details(item)">查看详情</el-button>
                  <el-checkbox :value="item.id"
                               :label="item.id"></el-checkbox>
                </div> -->
              </div>
            </div>
          </div>
        </el-checkbox-group>
      </div>
    </el-scrollbar>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="show"
               title="选择关联分类">
      <totalLibraryType @callback="newCallback"> </totalLibraryType>
    </zy-pop-up>
    <zy-pop-up v-model="detailsShow"
               title="书籍详情">
      <libraryDetails :id="id"> </libraryDetails>
    </zy-pop-up>
  </div>
</template>
<script>
import totalLibraryType from './totalLibraryType'
import libraryDetails from './libraryDetails'
export default {
  name: 'totalLibrary',
  data () {
    return {
      keyword: '',
      bookTypeId: '',
      bookTypeFirstId: '',
      bookTypeSecondId: '',
      bookType: [],
      notInCompany: true,
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      checkedCities: [],
      storageData: [],
      selectObj: [],
      id: '',
      show: false,
      detailsShow: false
    }
  },
  inject: ['tabDel'],
  components: {
    totalLibraryType,
    libraryDetails
  },
  watch: {
    bookTypeId (val) {
      if (val === '') {
        this.bookTypeFirstId = ''
        this.bookTypeSecondId = ''
      }
    },
    notInCompany (val) {
      if (val) {
        this.page = 1
      }
      this.bookrepositoryList()
    }
  },
  mounted () {
    this.bookrepositoryTypetree()
    this.bookrepositoryList()
  },
  activated () {
    this.bookrepositoryTypetree()
    this.bookrepositoryList()
  },
  methods: {
    search () {
      this.page = 1
      this.bookrepositoryList()
    },
    reset () {
      this.keyword = ''
      this.bookTypeId = ''
      this.bookTypeFirstId = ''
      this.bookTypeSecondId = ''
      if (this.notInCompany) {
        this.bookrepositoryList()
      } else {
        this.notInCompany = true
      }
    },
    synchronous () {
      this.handreadPullBooks()
    },
    async handreadPullBooks () {
      const res = await this.$api.academy.handreadPullBooks()
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async bookrepositoryTypetree () {
      const res = await this.$api.academy.bookrepositoryTypetree()
      var { data } = res
      this.bookType = data
    },
    bookTypeChoose (data) {
      if (data.level == '1') { // eslint-disable-line
        this.bookTypeFirstId = data.id
        this.bookTypeSecondId = ''
      } else {
        this.bookTypeFirstId = ''
        this.bookTypeSecondId = data.id
      }
    },
    async bookrepositoryList () {
      const res = await this.$api.academy.bookrepositoryList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        bookTypeFirstId: this.bookTypeFirstId,
        bookTypeSecondId: this.bookTypeSecondId,
        notInCompany: this.notInCompany ? '1' : ''
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    howManyArticle () {
      this.bookrepositoryList()
    },
    whatPage () {
      this.bookrepositoryList()
    },
    handleCheckedCitiesChange (value) {
      console.log(value)
      var values = []
      value.forEach(item => {
        values[item] = item
      })
      this.tableData.forEach((item) => {
        if (Object.prototype.hasOwnProperty.call(values, item.id)) {
        } else {
          delete this.selectObj[item.id]
          this.deleteData(item)
        }
      })
      value.forEach(item => {
        if (Object.prototype.hasOwnProperty.call(this.selectObj, item)) {
        } else {
          this.selectObj[item] = item
          this.pushData(item)
        }
      })
    },
    pageSelected () {
      this.tableData.forEach((item) => {
        if (Object.prototype.hasOwnProperty.call(this.selectObj, item.id)) {
        } else {
          this.selectObj[item.id] = item.id
          this.checkedCities.push(item.id)
          this.storageData.push(item)
        }
      })
    },
    noPageSelected () {
      this.tableData.forEach((item) => {
        if (Object.prototype.hasOwnProperty.call(this.selectObj, item.id)) {
          delete this.selectObj[item.id]
          var checkedCities = this.checkedCities
          this.checkedCities = checkedCities.filter(row => row !== item.id)
          var storageData = this.storageData
          this.storageData = storageData.filter(row => row.id !== item.id)
        }
      })
    },
    bookClick (item) {
      if (Object.prototype.hasOwnProperty.call(this.selectObj, item.id)) {
        delete this.selectObj[item.id]
        var checkedCities = this.checkedCities
        this.checkedCities = checkedCities.filter(row => row !== item.id)
        var storageData = this.storageData
        this.storageData = storageData.filter(row => row.id !== item.id)
      } else {
        this.selectObj[item.id] = item.id
        this.checkedCities.push(item.id)
        this.storageData.push(item)
      }
    },
    deleteData (data) {
      const arr = this.storageData
      arr.forEach((item, index) => {
        if (item.id === data.id) {
          arr.splice(index, 1)
        }
      })
      this.storageData = arr
    },
    pushData (id) {
      this.tableData.forEach((item, index) => {
        if (item.id === id) {
          this.storageData.push(item)
        }
      })
    },
    determine () {
      if (this.checkedCities.length) {
        this.$confirm('此操作将把当前选中的书籍加入到单位书库中, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.show = true
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一本书籍！',
          type: 'warning'
        })
      }
    },
    newCallback (type, mainType, fineType) {
      this.show = false
      if (type) {
        this.bookrepositoryOutbound(this.checkedCities.join(','), mainType, fineType)
      }
    },
    async bookrepositoryOutbound (id, bookTypeFirstId, bookTypeSecondId) {
      const res = await this.$api.academy.bookrepositoryOutbound({
        bookIds: id,
        bookTypeFirstId: bookTypeFirstId,
        bookTypeSecondId: bookTypeSecondId
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.tabDel('16620092810', this.$route.query.toId)
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.totalLibrary {
  width: 100%;
  height: 100%;
  // .screening-checkbox {
  //   height: 40px;
  //   margin-left: 24px;
  //   display: flex;
  //   align-items: center;
  // }
  .totalLibraryBox {
    width: 100%;
    height: calc(100% - 180px);
    border-top: 1px solid #e6e6e6;
    border-bottom: 1px solid #e6e6e6;
    .el-checkbox-group {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
    }

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .is-vertical {
      .el-scrollbar__thumb {
        background-color: rgba(144, 147, 153, 0.8);
      }
    }

    .totalLibraryListBox {
      display: flex;
      flex-wrap: wrap;
      padding-left: 24px;
      padding-top: 24px;
      .totalLibraryItem {
        display: flex;
        justify-content: space-between;
        margin-right: 24px;
        margin-bottom: 24px;
        width: 332px;
        height: 128px;
        cursor: pointer;
        .totalLibraryItemImg {
          height: 128px;
          width: 95px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .totalLibraryItemBox {
          width: 222px;
          height: 100%;
          position: relative;
          .totalLibraryItemName {
            color: #333;
            line-height: 21px;
            font-size: $textSize16;
            margin-bottom: 7px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          .totalLibraryItemIntroduction {
            line-height: 24px;
            color: #666;
            letter-spacing: 0.93px;
            height: 72px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            font-size: 13px;
          }
          .totalLibraryItemAuthor {
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .totalLibraryItemAuthorText {
              font-size: 13px;
              color: #999;
              letter-spacing: 1px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
            .libraryAllItemButton {
              min-width: 68px;
              .el-button {
                font-size: 13px;
                padding: 0;
                margin-right: 8px;
                span {
                  display: inline-block;
                  line-height: 16px;
                }
              }
            }
            .el-checkbox__label {
              display: none;
            }
          }
        }
      }
    }
  }
}
</style>
