.pop-up {
  // position: absolute;
  // top: 0;
  // left: 0;
  // height: 100%;
  // width: 100%;
  // z-index: 999;

  .pop-up-cover {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1000;
  }

  .none {
    display: none;
  }

  .pop-up-box {
    position: absolute;
    height: 80%;
    background-color: #fff;
    z-index: 1000;
    border-radius: 10px;
    overflow: hidden;

    .pop-up-title {
      -moz-user-select: none;
      -khtml-user-select: none;
      user-select: none;
      height: 42px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      background-color: #fff;
      box-shadow: 0px -1px 0px 0px $zy-color;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      // background-color: $zy-color;

      .pop-up-text {
        // color: #fff;
        color: #262626;
        font-family: Microsoft YaHei;
        font-size: $textSize16;
      }

      .el-icon-close {
        // color: #fff;
        cursor: pointer;
      }

      .el-icon-close:hover {
        color: $zy-color;
      }
    }

    .pop-up-body {
      height: calc(100% - 42px);
      background-color: #fff;
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;

      .zy-pop-up-body {
        width: 100%;
        height: 100%;

        .el-scrollbar__wrap {
          overflow-x: hidden;
        }

        .is-vertical {
          .el-scrollbar__thumb {
            background-color: rgba(144, 147, 153, 0.8);
          }
        }
      }
    }
  }
}
