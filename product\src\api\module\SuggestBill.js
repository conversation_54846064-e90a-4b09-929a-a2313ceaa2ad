// 导入封装的方法
import {
  get,
  post,
  postform
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const SuggestBill = {
  circleBoutInfoList (params) {
    return post('/submission/suggest/circleBoutInfo/list', params)
  },
  circleBoutInfoInfo (params) {
    return post(`/submission/suggest/circleBoutInfo/info/${params}`)
  },
  yearRelationList (params) {
    return post('/submission/suggest/circleboutyear/list', params)
  },
  yearRelationInfo (params) {
    return post(`/submission/suggest/circleboutyear/info/${params}`)
  },
  yearRelationDel (params) {
    return post('/submission/suggest/circleboutyear/dels?', params)
  },
  proposalTypeList (params) {
    return post('/submission/suggest/topic/list?', params)
  },
  proposalTypeInfo (params) {
    return post(`/submission/suggest/topic/info/${params}`)
  },
  tagroupList (params) {
    return post('/flowgroup/list', params)
  },
  tagroupInfo (params) {
    return post(`/flowgroup/info/${params}`)
  },
  userList (params) {
    return post('/flowgroup/userList?', params)
  },
  chooseList (params) {
    return post('/submission/suggest/topic/chooseList', params)
  },
  businessUser (params) {
    return post('/submission/suggest/businessUser/list', params)
  },
  businessUserdels (params) {
    return post('/submission/suggest/businessUser/dels', params)
  },
  enumData (params) {
    return post('/submission/suggest/businessUser/enumData', params)
  },
  enumDatainfo (params) {
    return post(params)
  },
  businessUserInfo (params) {
    return post(`/submission/suggest/businessUser/info/${params}`)
  },
  // 设置/取消 重点建议/公开建议/优秀建议
  batchUpdateSuggest (params) {
    return post('/suggest/batchUpdateSuggest?', params)
  },
  // 获取当前届次
  currentCircleAndBout (params) {
    return post('/submission/suggest/circleBoutInfo/currentCircleAndBout', params)
  },
  // 草稿箱建议
  myDraftsSuggestList (params) {
    return post('/suggest/myDraftsSuggestList?', params)
  },
  // 草稿箱删除
  representDelete (params) {
    return post('/suggest/representDelete', params)
  },
  // 我联名的建议
  myJoinSuggestList (params) {
    return post('/suggest/myJoinSuggestList?', params)
  },
  // 我联名的建议确认联名
  confirmJoinSubmit (params) {
    return post('/suggest/confirmJoinSubmit', params)
  },
  // 我的建议
  mySuggestList (params) {
    return post('/suggest/mySuggestList?', params)
  },
  // 代表单独变更联名关系
  representChangeJoinRelation (params) {
    return post('/suggest/representChangeJoinRelation', params)
  },
  // 建议撤销
  revoke (params) {
    return post('/suggest/represent/revoke', params)
  },
  // 建议基本详情
  suggestInfo (params) {
    return post(`/suggest/info/${params}`)
  },
  // 所有建议
  suggestList (params) {
    return post('/suggest/list?', params)
  },
  // 真实删除联名记录
  realDeleteJoinUser (params) {
    return post('/suggest/realDeleteJoinUser', params)
  },
  // 所有建议删除
  powerDelete (params) {
    return post('/suggest/powerDelete', params)
  },
  // 审查期间详情
  auditDetail (params) {
    return post('/suggest/auditDetail', params)
  },
  // 获取各审查环节操作枚举数据
  getOperationList (params) {
    return post('/suggest/getOperationList', params)
  },
  // 办理单位
  chooseListTree (params) {
    return post('/flowgroup/chooseList', params)
  },
  // 我审查的建议列表
  myAuditList (params) {
    return post('/suggest/myAuditList?', params)
  },
  // 我审查的建议列表详情
  myAuditDetail (params) {
    return post('/suggest/myAuditDetail', params)
  },
  // 修改建议编号
  changeUserDefineCode (params) {
    return post('/suggest/changeUserDefineCode', params)
  },
  // 建议预交办详情
  preAssignDetail (params) {
    return post('/suggest/preAssignDetail', params)
  },
  // 解锁建议
  unlockSuggest (params) {
    return post('/suggest/unlockSuggest', params)
  },
  // 建议批量预交给党委政
  batchPreAssignAudit (params) {
    return post('/suggest/batchPreAssignAudit', params)
  },
  // 联工委待签收建议列表
  allPreAssignList (params) {
    return post('/suggest/allPreAssignList?', params)
  },
  // 联工委待签收申请调整建议列表
  preAssignReviseList (params) {
    return post('/suggest/preAssignReviseList?', params)
  },
  // 待签收建议详情
  signedSuggestDetail (params) {
    return post('/suggest/signedSuggestDetail', params)
  },
  // 办理单位待签收建议列表(待签收，已签收)
  groupPreAssignList (params) {
    return post('/suggest/groupPreAssignList?', params)
  },
  // 办理单位签收
  receiveTransact (params) {
    return post('/suggest/receiveTransact', params)
  },
  // 办理单位申请调整
  reviseTransact (params) {
    return post('/suggest/reviseTransact', params)
  },
  // 办理单位申请调整列表
  groupPreAssignReviseList (params) {
    return post('/suggest/groupPreAssignReviseList?', params)
  },
  // 办理单位申请调整列表历史
  groupPreAssignHistoryList (params) {
    return post('/suggest/groupPreAssignHistoryList?', params)
  },
  // 办理中建议详情
  transactSuggestDetail (params) {
    return post('/suggest/transactSuggestDetail', params)
  },
  // 办理单位更新办理内部流程
  updateTransactInnerStatus (params) {
    return post('/suggest/updateTransactInnerStatus', params)
  },
  // 建议沟通情况列表
  flowContactList (params) {
    return post('/suggest/flowContactList', params)
  },
  // 建议沟通情况联系人
  contactPersonList (params) {
    return post('/suggest/contactPersonList', params)
  },
  // 答复件详情(点击查看)
  suggestAnswerDetail (params) {
    return post('/suggest/suggestAnswerDetail', params)
  },
  // 答复件详情(点击添加答复件)
  flowAnswerDetail (params) {
    return post('/suggest/flowAnswerDetail', params)
  },
  // 待联工委审查延期申请建议列表
  flowDelayList (params) {
    return post('/suggest/flowDelayList?', params)
  },
  // 保存满意度测评
  saveFlowEvaluate (params) {
    return post('/suggest/saveFlowEvaluate', params)
  },
  // 满意度测评详情
  flowEvaluateDetail (params) {
    return post('/suggest/flowEvaluateDetail', params)
  },
  // 批量办结
  batchFinishSuggest (params) {
    return post('/suggest/batchFinishSuggest', params)
  },
  // 单个办结
  finishSuggest (params) {
    return post('/suggest/finishSuggest', params)
  },
  // 单个办结
  suggestCount (params) {
    return post('/suggest/suggestCount?', params)
  },
  // 议案分类列表
  BillTypeList (params) {
    return post('/submission/motion/topic/list?', params)
  },
  // 议案分类详情
  BillTypeInfo (params) {
    return post(`/submission/motion/topic/info/${params}`)
  },
  BillChooseList (params) {
    return post('/submission/motion/topic/chooseList', params)
  },
  myMotionList (params) {
    return post('/motion/myMotionList?', params)
  },
  myJoinMotionList (params) {
    return post('/motion/myJoinMotionList?', params)
  },
  myDraftsMotionList (params) {
    return post('/motion/myDraftsMotionList?', params)
  },
  BillrepresentDelete (params) {
    return post('/motion/representDelete', params)
  },
  BillInfo (params) {
    return post(`/motion/info/${params}`)
  },
  BillList (params) {
    return post('/motion/list?', params)
  },
  BillpowerDelete (params) {
    return post('/motion/powerDelete', params)
  },
  batchTransfer (params) {
    return post('/motion/batchTransfer', params)
  },
  changeState (params) {
    return post('/motion/changeState', params)
  },
  frontPageList (params) {
    return post('/suggest/frontPageList', params)
  },
  flowTransactList (params) {
    return post('/suggest/flowTransact/list', params)
  },
  flowTransactInfo (params) {
    return post(`/suggest/flowTransact/info/${params}`)
  },
  flowTransactDel (params) {
    return post(`/suggest/flowTransact/del/${params}`)
  },
  batchUpdateGroupInfo (params) {
    return post('/suggest/batchUpdateGroupInfo?', params)
  },
  wordExport (params) {
    return post('/suggest/wordExport', params)
  },
  batchAgreeJoinSubmit (params) {
    return post('/suggest/batchAgreeJoinSubmit', params)
  },
  batchReceiveTransact (params) {
    return post('/suggest/batchReceiveTransact', params)
  },
  allTransactSuggestList (params) {
    return post('/suggest/allTransactSuggestList', params)
  },
  flowBackList (params) {
    return post('/suggest/flowBackList', params)
  },
  groupChangeList (params) {
    return post('/suggest/groupChangeList', params)
  },
  superEditTransactGroup (params) {
    return post('/suggest/superEditTransactGroup', params)
  },
  reTransact (params) {
    return post('/suggest/reTransact?', params)
  },
  requestTractAnswer (params) {
    return post('/suggest/requestTractAnswer?', params)
  },
  auditRequestTractAnswer (params) {
    return post('/suggest/auditRequestTractAnswer', params)
  },
  answerList (params) {
    return post('/suggest/answerList', params)
  },
  superDeleteFlowAnswer (params) {
    return post('/suggest/superDeleteFlowAnswer', params)
  }
}
export default SuggestBill
