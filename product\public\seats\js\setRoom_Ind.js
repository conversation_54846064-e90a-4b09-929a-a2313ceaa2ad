var token = sessionStorage.getItem('token');
var areaId = sessionStorage.getItem("areaId");
var header = '{"Authorization": ' + token + ',"u-login-areaId": ' + areaId + '}'
var themeColor = window.localStorage.getItem("theme_color") != null ? window.localStorage.getItem("theme_color") : "#199BC5";
var selRid = ''; //选中的会场Id
var selRname = ''; //选择的会场名称
var selRimg = ''; //会场图片
var selSets; //选中的座位
var selSign; //选中的标签
var seatNo = ""; //座位号
var selSeatId = new Array(); //存放选中的座位的座位号数组
var maxX = 0; //座位容器内最大的x坐标
var maxY = 0; //座位容器内最大的y坐标
var areaArr = new Array(); // 区域信息
var boxWidth = 0;

$(function() {
	selRid = window.sessionStorage.getItem("meetRoomId"); //从sessionStorage拿到到选中的会场Id
	selRname = window.sessionStorage.getItem("meetRoomName"); //从sessionStorage拿到到选中的会场Name
	$('#rName').text(selRname);
	$('#setsDiv').css("width", $(this).width() - $('#seatInfo').width() - 8).css("height", $(this).height() - 84);
	$('.box').css("width", $(this).width() - $('#seatInfo').width() - 36).css("height", $('.box').height() - 56).css("overflow", "auto");
	$('.box img').css("width", $(this).width() - $('#seatInfo').width() - 36).css("height", $(this).height() - 56).css("overflow", "auto");
	$("#areaTipFixed").show();
	$("#areaTipImg").hide();
	boxWidth = $(this).width() - $('#seatInfo').width() - 36;
	changeThemeColor(themeColor);

	//座位类型下拉框值改变事件
	$("#setsT").combobox({
    	onChange:function(n,o){
	        //这里的参数n是select改变后的value,o是改变前的value
	        var areaId = n; //区域Id
			var bgColor = "";
			//遍历区域类型数组，获取区域颜色
			for(var i = 0; i < areaArr.length; i++) {
				if(areaId == areaArr[i].id) {
					bgColor = areaArr[i].colour;
					break;
				}
			}
	
			if(selSets != undefined) {
				// var isPass = true;
				// $('.box div').each(function() {
				// 	if($(this).is('.drag')) {
				// 		var sNo = $(this).children().get(0).innerHTML; //座位号
				// 		var sTp = $(this).children().get(1).innerHTML; //座位类型
				// 		//过滤掉座位号为空的座位
				// 		if(sNo != "") {
				// 			//相同座位号，相同类型的座位存在，不通过
				// 			if(sNo == $("#editSetsNum").val() && sTp == areaId) {
				// 				isPass = false;
				// 				return isPass;
				// 			} else {
				// 				isPass = true;
				// 				return isPass;
				// 			}
				// 		}
				// 	}
				// });
	
				// if(isPass == false) {
				// 	layer.msg($("#setsT").combobox("getText") + "已存在相同房间号，不能重复！");
				// 	$('.box div').each(function() {
				// 		$(this).removeClass("selected ctrlSel");
				// 	});
				// 	clearContent();
				// 	selSets = undefined;
				// 	return;
				// } else {
					$(selSets).children().get(1).textContent = areaId;
					$(selSets).children().get(6).textContent = bgColor;
				// }
			} else {
				$('.box div').each(function() {
					if($(this).is('.drag')) {
						if($(this).is('.selected')) {
							$(this).children().get(1).textContent = areaId;
							$(this).children().get(6).textContent = bgColor;
						}
					}
				});
			}
			setColorByAreaType(1);
    	}
    });
	
	//加载房间类型下拉框数据
	loadRoomType();

});

//加载排座信息
function loadSetsInfo(rId, rImg) {
	//$("#bgImg").attr("src", rImg);
	//删除容器内的座位数据
	$(".box").find("div").remove();
	
	axios.post(server.local_path + "meetroom/selSeated", Qs.stringify({
		placeid: rId
	}), {
		headers: JSON.parse(header)
	})
	.then(function(response) {
		if(response.data.errcode == 200){
			var detailInfo = response.data.data;
			var allNums = 0; //总座位数
			if(detailInfo != null && detailInfo.length > 0) {
				seatNo = Number(detailInfo.length) + 1;
				for(var i = 0; i < detailInfo.length; i++) {
					var sId = detailInfo[i].id; //座位id
					var sNo = detailInfo[i].seatNumber; //座位号
					var sTp = detailInfo[i].seatType; //座位类型
					var sSt = detailInfo[i].seatState; //座位状态
					var sRm = detailInfo[i].remarks; //座位备注
					var sH = detailInfo[i].seatH; //座位高度
					var sW = detailInfo[i].seatW; //座位宽度
					var sX = detailInfo[i].seatX; //座位x坐标
					var sY = detailInfo[i].seatY; //座位y坐标
					var sL = detailInfo[i].label; //座位/标记  seat:座位-sign:标记
					var sBC = detailInfo[i].bgColor; //座位背景色
					var sP = detailInfo[i].position; //座位几排几座
	
					if(Number(sX) > Number(maxX)) {
						maxX = sX; //给容器内的最大x坐标赋值
					}
					if(Number(sY) > Number(maxY)) {
						maxY = sY; //给容器内的最大y坐标赋值
					}
	
					if(sL == "seat") {
						allNums++;
						var sRow = "",
							sNos = ""; //座位几排几座
						if(sP != "") {
							var rowAndNos = sP.split(",");
							sRow = rowAndNos[0];
							sNos = rowAndNos[1];
						}
	
						var addStr = '<div onclick="seatesClick($(this));" class="drag" style="z-index: 1;width:' + sW + 'px;height:' + sH + 'px;top:' + sY + ';left:' + sX + ';">' +
							'<label style="color:black;">' + sNo + '</label>' + //第一个label放座位号
							'<label style="display:none; color:black;">' + sTp + '</label>' + //第二个label放座位类型
							'<label style="display:none; color:black;">' + sSt + '</label>' + //第三个label放座位状态
							'<label style="display:none; color:black;">' + sId + '</label>' + //第四个label放座位Id
							'<label style="display:none; color:black;">' + sRow + '</label>' + //第五个label放座位第几排
							'<label style="display:none; color:black;">' + sNos + '</label>' + //第六个label放座位第几座
							'<label style="display:none; color:black;">' + sBC + '</label>' + //第七个label放座位背景色
							'<label style="display:none; color:black;">' + sRm + '</label>' + //第八个label放座位备注
							'</div>';
					} else {
						var addStr = '<div id="' + sId + '" onclick="signsClick($(this));" class="signDrag" style="z-index: 1;width:' + sW + 'px;height:' + sH + 'px;top:' + sY + ';left:' + sX + ';">' + sNo + '</div>';
					}
	
					$(".box").append(addStr);
				}
				$("#seatNum").text(allNums);
				initDrag();
				setColorByAreaType(1);
			} 
		}
		else {
			seatNo = 0;
		}
		showSeatNoOrSort();
	}).catch(function(error) {

	});
}

//保存排座信息
$("#saveSets").on("click", function() {
	saveSeats();
});

function saveSeats() {
	//保存排座信息 传会场id,座位信息给后台,座位信息为数组
	//遍历排座容器内所有座位信息 封装座位信息
	var setsArr = new Array(); //座位信息数组
	var zxNum = 0; //执行次数
	var isSave = true; //是否通过保存

	$('.box div').each(function() {
		if($(this).is('.drag')) {
			var sNo = $(this).children().get(0).innerHTML; //座位号
			//过滤掉座位号为空的座位
			if(sNo != "") {
				var sTp = $(this).children().get(1).innerHTML; //座位类型
				var sSt = $(this).children().get(2).innerHTML; //座位状态
				var sId = $(this).children().get(3).innerHTML; //座位Id
				var sRow = $(this).children().get(4).innerHTML; //座位第几排
				var sNos = $(this).children().get(5).innerHTML; //座位第几座
				var sX = $(this).position().left.toFixed(2); //x坐标
				var sY = $(this).position().top.toFixed(2); //y坐标
				var sW = $(this).get(0).offsetWidth; //座位宽度
				var sH = $(this).get(0).offsetHeight; //座位高度
				var sBC = $(this).children().get(6).innerHTML; //座位背景色
				var sRm = $(this).children().get(7).innerHTML; //座位备注
				var sP = "";
				if(sRow == "") {
					sRow = "-"
				}
				if(sNos == "") {
					sNos = "-"
				}
				sP = sRow + "," + sNos;

				var setsObj = new Object();
				setsObj.id = sId;
				setsObj.seatnumber = sNo;
				setsObj.seattype = sTp;
				setsObj.seatstate = sSt;
				setsObj.position = sP;
				setsObj.seatx = Number(sX) + Number(scroW);
				setsObj.seaty = Number(sY) + Number(scroH);
				setsObj.seatw = sW;
				setsObj.seath = sH;
				setsObj.bgcolor = sBC != "null" ? sBC : "";
				setsObj.remarks = sRm != "null" ? sRm : "";
				setsObj.label = "seat";
				setsArr.push(setsObj);
				isSave = true;
			} else {
				isSave = false;
				return false;
			}
		} else {
			var sC = $(this).text(); //标记内容
			if(sC != "") {
				var sId = $(this).attr("id"); //标签Id
				var sX = $(this).position().left; //x坐标
				var sY = $(this).position().top; //y坐标
				var sW = $(this).get(0).offsetWidth; //座位宽度
				var sH = $(this).get(0).offsetHeight; //座位高度

				var setsObj = new Object();
				setsObj.id = sId;
				setsObj.seatnumber = sC;
				setsObj.seattype = "";
				setsObj.seatstate = "";
				setsObj.remarks = "";
				setsObj.seatx = Number(sX) + Number(scroW);
				setsObj.seaty = Number(sY) + Number(scroH);
				setsObj.seatw = sW;
				setsObj.seath = sH;
				setsObj.label = "sign";
				setsArr.push(setsObj);
			}
		}
	});

	if(isSave == true) {
		axios.post(server.local_path + "meetroom/insSeated", Qs.stringify({
			siteid: selRid,
			seateds: JSON.stringify(setsArr)
		}), {
			headers: JSON.parse(header)
		})
		.then(function(response) {
			var resultInfo = response.data;
			if(resultInfo.errcode == 200) {
				layer.msg("保存成功");
				setTimeout(function() {
					clearContent();
					//加载排座信息
					loadSetsInfo(selRid, selRimg);
				}, 1000);
			}
			else{
				layer.msg(resultInfo.errmsg);
			}
		}).catch(function(error) {
			layer.msg(error.response.data.message);
		});
	} else {
		layer.msg("座位编号设置不完整");
	}
}

//添加座位
$("#addSets").on("click", function() {
	clearSeatSelected();//清空座位选中样式
	var wNum = 50; //座位宽度
	var hNum = 50;//座位高度
	var areaId = $("#setsT").combobox("getValue"); //区域Id
	var bgColor = "";
	//遍历区域类型数组，获取区域颜色
	for(var i = 0; i < areaArr.length; i++) {
		if(areaId == areaArr[i].id) {
			bgColor = areaArr[i].colour;
			break;
		}
	}
	var rowNums = $("#rowNum").val(); //排
	var colNums = $("#colNum").val(); //列
	if(rowNums != "" && colNums != ""){
		for(var r=0; r<rowNums; r++){
			var rowTopNum = r * 60;
			for(var i = 0; i < colNums; i++) {
				var topNum = scroH + Number(hNum) + rowTopNum + 60;
				var leftNum = scroW + (Number(i) + 1) * (Number(wNum) + 10) + 60;
				var addStr = '<div onclick="seatesClick($(this));" class="drag isSeat selected" style="z-index: 1;width:' + wNum + 'px;height:' + hNum + 'px;top:' + topNum + ';left:' + leftNum + ';">' +
					'<label style="color:black;"></label>' + //第一个label放座位号
					'<label style="display:none; color:black;">' + $("#setsT").combobox("getValue") + '</label>' + //第二个label放座位类型
					'<label style="display:none; color:black;"></label>' + //第三个label放座位状态
					'<label style="display:none; color:black;"></label>' + //第四个label放座位Id
					'<label style="display:none; color:black;"></label>' + //第五个label放座位第几排
					'<label style="display:none; color:black;"></label>' + //第六个label放座位第几座
					'<label style="display:none; color:black;">' + bgColor + '</label>' + //第七个label放座位背景色
					'<label style="display:none; color:black;"></label>' + //第八个label放座位备注
					'</div>';
				$(".box").append(addStr);
			}
		}
		initDrag();
		setColorByAreaType(1);
		clearSeatSelected();//清空座位选中样式
	}
});

//添加标记
$("#addSigns").on("click", function() {
	var wNum = 50; //宽度
	var hNum = 50; //高度
	var sRNums = $("#rowNum").val(); //排
	var sCNums = $("#colNum").val(); //列
	for(var i = 0; i < sRNums; i++) {
		var rowTopNum = i * 60;
		var topNum = scroH + Number(hNum) + rowTopNum + 60;
		var leftNum = Number(wNum) + 5;
		var addStr = '<div id="" onclick="signsClick($(this));" class="signDrag isSign" style="z-index: 1;border: 1px solid #dce0e4;width:' + wNum + 'px;height:' + hNum + 'px;top:' + topNum + ';left:' + leftNum + ';"></div>';
		$(".box").append(addStr);
	}
	for(var i = 0; i < sCNums; i++) {
		var topNum = scroH + Number(hNum);
		var leftNum = (Number(i) + 1) * (Number(wNum) + 10) + 60;
		var addStr = '<div id="" onclick="signsClick($(this));" class="signDrag isSign" style="z-index: 1;border: 1px solid #dce0e4;width:' + wNum + 'px;height:' + hNum + 'px;top:' + topNum + ';left:' + leftNum + ';"></div>';
		$(".box").append(addStr);
	}
	initDrag();
});

//删除座位
$("#delSets").on("click", function() {
	var zxNum = 0; //执行次数
	var selList = new Array(); //选择的座位或标记

	seatNo = selSeatId[0];
	var sidStr = "";
	$('.box div').each(function() {
		if($(this).is('.selected')) {
			var sId = "";
			//座位
			if($(this).is('.drag')) {
				sId = $(this).children().get(3).innerHTML; //座位Id
				//过滤掉座位Id为空的座位
				if(sId != "") {
					sidStr += sId + ",";
				}
				selList.push($(this));
			}
			//标记
			else {
				sId = $(this).attr("id"); //标记Id
				//过滤掉标记Id为空的座位
				if(sId != "") {
					sidStr += sId + ",";
				}
				selList.push($(this));
			}
		}
	});

	clearContent();

	//组装ID字符串
	sidStr = sidStr.substring(0, sidStr.length - 1);
	if(sidStr != "") {
		layer.confirm('确定要删除吗？', {}, function(index) {
			//把选中的座位或标记在容器内删除
			for(var i = 0; i < selList.length; i++) {
				$(selList[i]).remove();
			}
			//把选中的座位或标记请求接口删除
			axios.post(server.local_path + "meetroom/dels", Qs.stringify({
				ids: sidStr
			}), {
				headers: JSON.parse(header)
			})
			.then(function(response) {
				var resultInfo = response.data;
				if(resultInfo.errcode == 200) {
					layer.msg("删除成功");
				}
			}).catch(function(error) {
				layer.msg(error.response.data.message);
			});
			//关闭
			layer.close(index);
		});
	}
	else{
		layer.confirm('确定要删除吗？', {}, function(index) {
			//把选中的座位或标记在容器内删除
			for(var i = 0; i < selList.length; i++) {
				$(selList[i]).remove();
			}
			//关闭
			layer.close(index);
		});
	}
});

//全选座位
$("#allSel").on("click", function() {
	selSeatId = new Array();
	$('.box div').each(function() {
		if(!$(this).is('.selected')) {
			$(this).addClass("selected");
			$(this).css("background", "");
		}
		//座位
		if($(this).is('.drag')) {
			var selItem = $(this).children().get(0).innerHTML;
			selSeatId.push(selItem);
		}
	});
});

//给座位设置编号
$("#editSetsNum").change(function() {
	if(selSets != undefined) {
	// 	var isPass = true;
	// 	$('.box div').each(function() {
	// 		if($(this).is('.drag')) {
	// 			var sNo = $(this).children().get(0).textContent; //座位号
	// 			var sTp = $(this).children().get(1).textContent; //座位类型
	// 			//过滤掉座位号为空的座位
	// 			if(sNo != "") {
	// 				//相同座位号，相同类型的座位存在，不通过
	// 				if(sNo == $("#editSetsNum").val() && sTp == $("#setsT").combobox("getValue")) {
	// 					isPass = false;
	// 					return isPass;
	// 				} else {
	// 					isPass = true;
	// 					return isPass;
	// 				}
	// 			}
	// 		}
	// 	});

	// 	if(isPass == false) {
	// 		layer.msg($("#setsT").combobox("getText") + "已存在相同房间号，不能重复！");
	// 		$(selSets).children().get(0).textContent = "";
	// 		return;
	// 	} else {
			$(selSets).children().get(0).textContent = $("#editSetsNum").val();
			$("#editSetsNum").blur();
	// 	}
	}
});

//选中座位，展示座位信息到指定标签
function selSeatesInfo() {
	var selSetsNum = $(selSets).children().get(0).textContent; //选中的座位号
	var selSetsType = $(selSets).children().get(1).textContent; //选中的座位类型
	var selSetsRow = $(selSets).children().get(4).textContent; //选中的座位第几排
	var selSetsNos = $(selSets).children().get(5).textContent; //选中的座位第几座
	var selSetsRmk = $(selSets).children().get(7).textContent; //选中的座位备注
	var selSetsW = selSets.get(0).offsetWidth;
	var selSetsH = selSets.get(0).offsetHeight;
	$("#editSetsNum").val(selSetsNum);
	$("#editSetsNum").select();
	$("#setsW").val(selSetsW);
	$("#setsH").val(selSetsH);
	$("#setsT").combobox("setValue", selSetsType != "null" ? selSetsType : "");
	$("#setsRow").val(selSetsRow);
	$("#setsNum").val(selSetsNos);
	$("#setsRmk").val(selSetsRmk != "null" ? selSetsRmk : "");
}

//座位点击事件
function seatesClick(seate) {
	//如果没有按下ctrl键
	if(!keepCtrl) {
		selSets = seate;
		$('.box div').each(function() {
			$(this).removeClass("selected ctrlSel");
		});
		$(seate).addClass("selected");
		$(seate).css("background", "");
		selSeatId = new Array();
		var selItem = $(seate).children().get(0).innerHTML;
		selSeatId.push(selItem);
		selSeatesInfo();
	} else {
		selSets = undefined;
		$(seate).addClass("selected ctrlSel");
		selSeatId = new Array();
		$('.box div').each(function() {
			if($(this).is('.selected')) {
				$(this).addClass("ctrlSel");
				$(seate).css("background", "");
				var selItem = $(this).children().get(0).innerHTML;
				selSeatId.push(selItem);
			} else
				$(this).removeClass("selected ctrlSel");
		});
	}
}

//标签点击事件
function signsClick(sign) {
	//如果没有按下ctrl键
	if(!keepCtrl) {
		selSign = sign;
		$('.box div').each(function() {
			$(this).removeClass("selected ctrlSel");
		});
		$(sign).addClass("selected");
	} else {
		selSign = undefined;
		$(sign).addClass("selected ctrlSel");
		$('.box div').each(function() {
			if($(this).is('.selected')) {
				$(this).addClass("ctrlSel");
			} else
				$(this).removeClass("selected ctrlSel");
		});
	}
	setColorByAreaType(2);
}

//根据座位所属区域，设置座位颜色  1:所有的   2:选中的
function setColorByAreaType(v) {
	if(v == 1) {
		$('.box div').each(function() {
			if($(this).is('.drag')) {
				var areaType = $(this).children().get(1).textContent; //座位类型
				for(var i = 0; i < areaArr.length; i++) {
					if(areaType == areaArr[i].id) {
						// $(this).css("background", areaArr[i].colour);
						$(this).css("border", "1px solid" + areaArr[i].colour);
						break;
					}
				}
			}
		});
	} else {
		$('.box div').each(function() {
			if($(this).is('.drag')) {
				if(!$(this).is('.selected')) {
					var areaType = $(this).children().get(1).textContent; //座位类型
					for(var i = 0; i < areaArr.length; i++) {
						if(areaType == areaArr[i].id) {
							// $(this).css("background", areaArr[i].colour);
							$(this).css("border", "1px solid" + areaArr[i].colour);
							break;
						}
					}
				}
			}
		});
	}
}

//清空座位选中样式
function clearSeatSelected() {
	$('.box div').each(function() {
		if($(this).is('.drag')) {
			if($(this).is('.selected')) {
				$(this).removeClass("selected");
			}
		}
	});
}

window.addEventListener("storage", function (e) {
	if(e.key == "theme_color"){
		themeColor = e.newValue;
		changeThemeColor(e.newValue);
	}
	if(e.key == "size"){
		if(e.newValue == 'false'){
			$('.box').css("width", boxWidth + 222);
		}
		else{
			$('.box').css("width", boxWidth);
		}
	}
});

var root = document.querySelector(':root');
 //即时换色
// 设置需要换色的元素及其样式
function changeThemeColor(colo){
	$(".btn-info").css("background", colo);
	root.setAttribute('style', '--color: '+ colo);
}

$(".btn-info").hover(function(){
	$(this).css('opacity', ".6");
},
function(){
	$(this).css('opacity', "1");
});

$(".cancel").hover(function(){
	$(this).css("background", "transparent").css("border", "1px solid"+ themeColor).css("color", themeColor);
},
function(){
	$(this).css("background", "transparent").css('border', "1px solid rgba(217,217,217,1)").css("color", "#000");
});
