<section id="rotated-flipped">
  <h2 class="page-header">Rotated &amp; Flipped</h2>
  <div class="row">
    <div class="span3">
      A set of classes that can be used to arbitrarily rotate and flip icons.
    </div>
    <div class="span9">
      <div class="well well-large well-transparent lead">
        <i class="icon-shield"></i>&nbsp; normal<br>
        <i class="icon-shield icon-rotate-90"></i>&nbsp; icon-rotate-90<br>
        <i class="icon-shield icon-rotate-180"></i>&nbsp; icon-rotate-180<br>
        <i class="icon-shield icon-rotate-270"></i>&nbsp; icon-rotate-270<br>
        <i class="icon-shield icon-flip-horizontal"></i>&nbsp; icon-flip-horizontal<br>
        <i class="icon-shield icon-flip-vertical"></i>&nbsp; icon-flip-vertical
      </div>
{% highlight html %}
<i class="icon-shield"></i>&nbsp; normal<br>
<i class="icon-shield icon-rotate-90"></i>&nbsp; icon-rotate-90<br>
<i class="icon-shield icon-rotate-180"></i>&nbsp; icon-rotate-180<br>
<i class="icon-shield icon-rotate-270"></i>&nbsp; icon-rotate-270<br>
<i class="icon-shield icon-flip-horizontal"></i>&nbsp; icon-flip-horizontal<br>
<i class="icon-shield icon-flip-vertical"></i>&nbsp; icon-flip-vertical
{% endhighlight %}
      <p class="alert alert-info">
        <i class="icon-info-sign"></i> Rotating and flipping icons aren't yet supported in IE7.
      </p>
    </div>
  </div>
</section>
