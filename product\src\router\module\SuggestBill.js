/**
 * 建议者
 */
// 提交建议
const AdvisorNew = () => import('@SuggestBill/advisor/AdvisorNew/AdvisorNew')
// 我领衔的建议
const ILeadSuggestion = () => import('@SuggestBill/advisor/ILeadSuggestion/ILeadSuggestion')
// 我附议的建议
const MyJointProposalRD = () => import('@SuggestBill/advisor/MyJointProposal/MyJointProposal')
// 草稿箱
const AdvisorDraftBox = () => import('@SuggestBill/advisor/AdvisorDraftBox/AdvisorDraftBox')/**
* 建议管理
*/
// 所有建议
const AllSuggestions = () => import('@SuggestBill/AdvisorManagement/AllSuggestions/AllSuggestions')
// 大会审查
// 代表团审查
const DelegationReview = () => import('@SuggestBill/AdvisorManagement/ReviewProposal/DelegationReview')
// 待拟办审查
const ProposalsToBeMade = () => import('@SuggestBill/AdvisorManagement/ReviewProposal/ProposalsToBeMade')
// 待审查建议
const ProposalsToBeReviewed = () => import('@SuggestBill/AdvisorManagement/ReviewProposal/ProposalsToBeReviewed')
// 待审定建议
const RecommendationsToBeReviewed = () => import('@SuggestBill/AdvisorManagement/ReviewProposal/RecommendationsToBeReviewed')
// 待阅建议
const ToReadAdvice = () => import('@SuggestBill/AdvisorManagement/ReviewProposal/ToReadAdvice')
// 我审查的建议
const LReviewTheProposal = () => import('@SuggestBill/AdvisorManagement/LReviewTheProposal/LReviewTheProposal')
// 不予接收的建议
const RefuseToAcceptSuggestions = () => import('@SuggestBill/AdvisorManagement/DoNotAcceptAdvice/RefuseToAcceptSuggestions')
// 参阅件的建议
const ReferenceSuggestions = () => import('@SuggestBill/AdvisorManagement/DoNotAcceptAdvice/ReferenceSuggestions')
// 人大交办
const RdSuggestionsToBeGiven = () => import('@SuggestBill/AdvisorManagement/SuggestedThatAssignedBy/SuggestionsToBeGiven/RdSuggestionsToBeGiven')
// 政府交办
const ZfSuggestionsToBeGiven = () => import('@SuggestBill/AdvisorManagement/SuggestedThatAssignedBy/SuggestionsToBeGiven/ZfSuggestionsToBeGiven')
// 党委交办
const DwSuggestionsToBeGiven = () => import('@SuggestBill/AdvisorManagement/SuggestedThatAssignedBy/SuggestionsToBeGiven/DwSuggestionsToBeGiven')
// 待签收
const ProposalToBeSigned = () => import('@SuggestBill/AdvisorManagement/SuggestedThatAssignedBy/ProposalToBeSigned/ProposalToBeSigned')
// 申请调整
const ToApplyAdjustment = () => import('@SuggestBill/AdvisorManagement/SuggestedThatAssignedBy/ToApplyAdjustment/ToApplyAdjustment')
// 办理中建议
const ProposalInProcess = () => import('@SuggestBill/AdvisorManagement/ProposalInProcess/ProposalInProcess')
// 办理中申请调整建议
const DealtWithInAdjust = () => import('@SuggestBill/AdvisorManagement/DealtWithInAdjust/DealtWithInAdjust')
// 已答复建议
const RepliesToProposals = () => import('@SuggestBill/AdvisorManagement/RepliesToProposals/RepliesToProposals')
// 申请延期
const DelayProposalRD = () => import('@SuggestBill/AdvisorManagement/DelayProposal/DelayProposal')
// 已办结建议
const ProposalBeenFinalized = () => import('@SuggestBill/AdvisorManagement/ProposalBeenFinalized/ProposalBeenFinalized')
// 统计分析
const proposalStatisticalRD = () => import('@SuggestBill/AdvisorManagement/proposal-statistical/proposal-statistical')
// 统计详情
const statisticalDetailsRD = () => import('@SuggestBill/AdvisorManagement/statistical-details/statistical-details')
// 跟踪办理
const TrackingIsDealtWith = () => import('@SuggestBill/AdvisorManagement/TrackingIsDealtWith/TrackingIsDealtWith')
/**
 * 办理单位
 */
// 办理单位所有建议
const UnitAllSuggestions = () => import('@SuggestBill/UndertakeUnit/UnitAllSuggestions/UnitAllSuggestions')
// 办理单位待签收
const UnitProposalToBeSigned = () => import('@SuggestBill/UndertakeUnit/UnitProposalToBeSigned/UnitProposalToBeSigned')
// 办理单位申请调整
const UnitToApplyAdjustment = () => import('@SuggestBill/UndertakeUnit/UnitProposalToBeSigned/UnitToApplyAdjustment')
// 办理单位调整记录
const UnitHistoricalRecord = () => import('@SuggestBill/UndertakeUnit/UnitProposalToBeSigned/UnitHistoricalRecord')
// 办理单位办理中建议
const UnitProposalInProcess = () => import('@SuggestBill/UndertakeUnit/UnitProposalInProcess/UnitProposalInProcess')
// 办理单位跟踪办理建议
const UnitTrackingIsDealtWith = () => import('@SuggestBill/UndertakeUnit/UnitTrackingIsDealtWith/UnitTrackingIsDealtWith')
// 办理单位已答复建议
const UnitRepliesToProposals = () => import('@SuggestBill/UndertakeUnit/UnitRepliesToProposals/UnitRepliesToProposals')
// 办理单位已办结建议
const UnitProposalBeenFinalized = () => import('@SuggestBill/UndertakeUnit/UnitProposalBeenFinalized/UnitProposalBeenFinalized')

/**
 * 建议配置管理
 */
// 界次编号
const theTimeSerialNumberRd = () => import('@SuggestBill/AdvisorConfiguration/the-time-serial-number/the-time-serial-number')
// 办理单位
const handleUnitRd = () => import('@SuggestBill/AdvisorConfiguration/handle-unit/handle-unit')
// 界次年份
const theTimeYearRd = () => import('@SuggestBill/AdvisorConfiguration/the-time-year/the-time-year')
// 提案分类
const advisorTypeRd = () => import('@SuggestBill/AdvisorConfiguration/advisor-type/advisor-type')
// 办理人
const transactorRd = () => import('@SuggestBill/AdvisorConfiguration/handle-unit/transactor/transactor')
// 业务关系
const betweenManagementRd = () => import('@SuggestBill/AdvisorConfiguration/between-management/between-management')

/**
 * 议案管理
 */
// 提交议案
const NewBill = () => import('@SuggestBill/BillManagement/NewBill/NewBill')
// 我的议案
const MyBill = () => import('@SuggestBill/BillManagement/MyBill/MyBill')
// 我联名的议案
const MyJointBill = () => import('@SuggestBill/BillManagement/MyJointBill/MyJointBill')
// 议案草稿
const BillAdvisorDraftBox = () => import('@SuggestBill/BillManagement/BillAdvisorDraftBox/BillAdvisorDraftBox')
// 议案管理
const AllBill = () => import('@SuggestBill/BillManagement/AllBill/AllBill')
// 议案分类管理
const BillType = () => import('@SuggestBill/BillManagement/BillType/BillType')

const SuggestBill = [
  {
    path: '/AdvisorNew',
    name: 'AdvisorNew',
    component: AdvisorNew
  },
  {
    path: '/ILeadSuggestion',
    name: 'ILeadSuggestion',
    component: ILeadSuggestion
  },
  {
    path: '/MyJointProposalRD',
    name: 'MyJointProposalRD',
    component: MyJointProposalRD
  },
  {
    path: '/AdvisorDraftBox',
    name: 'AdvisorDraftBox',
    component: AdvisorDraftBox
  },
  {
    path: '/theTimeSerialNumberRd',
    name: 'theTimeSerialNumberRd',
    component: theTimeSerialNumberRd
  },
  {
    path: '/handleUnitRd',
    name: 'handleUnitRd',
    component: handleUnitRd
  },
  {
    path: '/transactorRd',
    name: 'transactorRd',
    component: transactorRd
  },
  {
    path: '/theTimeYearRd',
    name: 'theTimeYearRd',
    component: theTimeYearRd
  },
  {
    path: '/advisorTypeRd',
    name: 'advisorTypeRd',
    component: advisorTypeRd
  },
  {
    path: '/betweenManagementRd',
    name: 'betweenManagementRd',
    component: betweenManagementRd
  },
  {
    path: '/AllSuggestions',
    name: 'AllSuggestions',
    component: AllSuggestions
  },
  {
    path: '/DelegationReview',
    name: 'DelegationReview',
    component: DelegationReview
  },
  {
    path: '/ProposalsToBeMade',
    name: 'ProposalsToBeMade',
    component: ProposalsToBeMade
  },
  {
    path: '/ProposalsToBeReviewed',
    name: 'ProposalsToBeReviewed',
    component: ProposalsToBeReviewed
  },
  {
    path: '/RecommendationsToBeReviewed',
    name: 'RecommendationsToBeReviewed',
    component: RecommendationsToBeReviewed
  },
  {
    path: '/ToReadAdvice',
    name: 'ToReadAdvice',
    component: ToReadAdvice
  },
  {
    path: '/LReviewTheProposal',
    name: 'LReviewTheProposal',
    component: LReviewTheProposal
  },
  {
    path: '/RefuseToAcceptSuggestions',
    name: 'RefuseToAcceptSuggestions',
    component: RefuseToAcceptSuggestions
  },
  {
    path: '/ReferenceSuggestions',
    name: 'ReferenceSuggestions',
    component: ReferenceSuggestions
  },
  {
    path: '/RdSuggestionsToBeGiven',
    name: 'RdSuggestionsToBeGiven',
    component: RdSuggestionsToBeGiven
  },
  {
    path: '/ZfSuggestionsToBeGiven',
    name: 'ZfSuggestionsToBeGiven',
    component: ZfSuggestionsToBeGiven
  },
  {
    path: '/DwSuggestionsToBeGiven',
    name: 'DwSuggestionsToBeGiven',
    component: DwSuggestionsToBeGiven
  },
  {
    path: '/ProposalToBeSigned',
    name: 'ProposalToBeSigned',
    component: ProposalToBeSigned
  },
  {
    path: '/ToApplyAdjustment',
    name: 'ToApplyAdjustment',
    component: ToApplyAdjustment
  },
  {
    path: '/UnitAllSuggestions',
    name: 'UnitAllSuggestions',
    component: UnitAllSuggestions
  },
  {
    path: '/UnitProposalToBeSigned',
    name: 'UnitProposalToBeSigned',
    component: UnitProposalToBeSigned
  },
  {
    path: '/UnitToApplyAdjustment',
    name: 'UnitToApplyAdjustment',
    component: UnitToApplyAdjustment
  },
  {
    path: '/UnitHistoricalRecord',
    name: 'UnitHistoricalRecord',
    component: UnitHistoricalRecord
  },
  {
    path: '/TrackingIsDealtWith',
    name: 'TrackingIsDealtWith',
    component: TrackingIsDealtWith
  },
  {
    path: '/ProposalInProcess',
    name: 'ProposalInProcess',
    component: ProposalInProcess
  },
  {
    path: '/UnitTrackingIsDealtWith',
    name: 'UnitTrackingIsDealtWith',
    component: UnitTrackingIsDealtWith
  },
  {
    path: '/DealtWithInAdjust',
    name: 'DealtWithInAdjust',
    component: DealtWithInAdjust
  },
  {
    path: '/UnitProposalInProcess',
    name: 'UnitProposalInProcess',
    component: UnitProposalInProcess
  },
  {
    path: '/RepliesToProposals',
    name: 'RepliesToProposals',
    component: RepliesToProposals
  },
  {
    path: '/UnitRepliesToProposals',
    name: 'UnitRepliesToProposals',
    component: UnitRepliesToProposals
  },
  {
    path: '/DelayProposalRD',
    name: 'DelayProposalRD',
    component: DelayProposalRD
  },
  {
    path: '/ProposalBeenFinalized',
    name: 'ProposalBeenFinalized',
    component: ProposalBeenFinalized
  },
  {
    path: '/UnitProposalBeenFinalized',
    name: 'UnitProposalBeenFinalized',
    component: UnitProposalBeenFinalized
  },
  {
    path: '/proposalStatisticalRD',
    name: 'proposalStatisticalRD',
    component: proposalStatisticalRD
  },
  {
    path: '/statisticalDetailsRD',
    name: 'statisticalDetailsRD',
    component: statisticalDetailsRD
  },
  {
    path: '/NewBill',
    name: 'NewBill',
    component: NewBill
  },
  {
    path: '/MyBill',
    name: 'MyBill',
    component: MyBill
  },
  {
    path: '/MyJointBill',
    name: 'MyJointBill',
    component: MyJointBill
  },
  {
    path: '/BillAdvisorDraftBox',
    name: 'BillAdvisorDraftBox',
    component: BillAdvisorDraftBox
  },
  {
    path: '/AllBill',
    name: 'AllBill',
    component: AllBill
  },
  {
    path: '/BillType',
    name: 'BillType',
    component: BillType
  }]
export default SuggestBill
