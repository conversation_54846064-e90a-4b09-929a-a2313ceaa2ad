<template>
  <div class="academyGroupMessage">
    <search-button-box @search-click="search"
                       @reset-click="reset">
      <template slot="button">
        <el-button icon="el-icon-delete"
                   @click="deleteClick"
                   type="primary">删除</el-button>
        <el-button icon="el-icon-upload2"
                   @click="exportList"
                   type="primary">导出</el-button>
      </template>
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
    </search-button-box>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="内容"
                           min-width="220">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="details(scope.row)">{{scope.row.messageType==1?scope.row.content:'当前消息是图片类型请点击详情查看'}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="消息类型"
                           width="120">
            <template slot-scope="scope">{{scope.row.messageType==1?'文字':'图片'}}</template>
          </el-table-column>
          <el-table-column label="来源"
                           min-width="120"
                           prop="groupName"></el-table-column>
          <el-table-column label="发布人"
                           min-width="120"
                           prop="userName"></el-table-column>
          <el-table-column label="发布时间"
                           width="200">
            <template slot-scope="scope">{{scope.row.createDate|datefmt('YYYY-MM-DD HH:mm:ss')}}</template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 30, 40]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="show"
               title="消息详情">
      <academyGroupMessageDetails :id="id"></academyGroupMessageDetails>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@mixins/tableData'
import academyGroupMessageDetails from './academyGroupMessageDetails'
export default {
  name: 'academyGroupMessage',
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      id: '',
      show: false
    }
  },
  mixins: [tableData],
  components: {
    academyGroupMessageDetails
  },
  mounted () {
    this.groupmessageList()
  },
  methods: {
    search () {
      this.groupmessageList()
    },
    reset () {
      this.keyword = ''
      this.groupmessageList()
    },
    async groupmessageList () {
      const res = await this.$api.academy.groupmessageList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    details (row) {
      this.id = row.id
      this.show = true
    },
    howManyArticle (val) {
      this.groupmessageList()
    },
    whatPage (val) {
      this.groupmessageList()
    },
    exportList () {
      if (this.choose.length) {
        this.$confirm('此操作将导出当前选中的消息, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.academy.groupmessageExport({ ids: this.choose.join(',') })
          this.choose = []
          this.selectObj = []
          this.groupmessageList()
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的消息, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.groupmessageDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async groupmessageDel (id) {
      const res = await this.$api.academy.groupmessageDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.groupmessageList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.academyGroupMessage {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
