<template>
  <div class="RelatedBooks">
    <search-button-box @search-click="search"
                       @reset-click="reset">
      <template slot="button">
        <el-button type="primary"
                   @click="determine">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </template>
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <zy-select width="222"
                   node-key="id"
                   :data="bookType"
                   twolabel="bookCount"
                   twolabelText="册"
                   v-model="bookTypeId"
                   @select="bookTypeChoose"
                   :props="{children: 'children',label: 'name'}"
                   placeholder="请选择书库分类"></zy-select>
      </template>
    </search-button-box>
    <el-scrollbar class="RelatedBooksBox">
      <div class="RelatedBooksListBox">
        <el-checkbox-group v-model="checkedCities"
                           :max="max"
                           @change="handleCheckedCitiesChange">
          <div class="RelatedBooksItem"
               v-for="(item) in tableData"
               @click="bookClick(item)"
               :key="item.id">
            <div class="RelatedBooksItemImg">
              <img :src="item.coverImgUrl"
                   alt="">
            </div>
            <div class="RelatedBooksItemBox">
              <div class="RelatedBooksItemName">{{item.bookName}}</div>
              <div class="RelatedBooksItemIntroduction">{{item.bookDescription}}</div>
              <div class="RelatedBooksItemAuthor"
                   @click.stop>
                <div class="RelatedBooksItemAuthorText">{{item.authorName}}</div>
                <el-checkbox :value="item.id"
                             :label="item.id"></el-checkbox>
              </div>
            </div>
          </div>
        </el-checkbox-group>
      </div>
    </el-scrollbar>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
export default {
  name: 'RelatedBooks',
  data () {
    return {
      keyword: '',
      bookTypeId: '',
      bookTypeFirstId: '',
      bookTypeSecondId: '',
      bookType: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      checkedCities: [],
      storageData: [],
      selectObj: []
    }
  },
  props: {
    max: {
      type: Number,
      default: 10000
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    bookTypeId (val) {
      if (val === '') {
        this.bookTypeFirstId = ''
        this.bookTypeSecondId = ''
      }
    }
  },
  mounted () {
    this.getSyTypeTree()
    this.dataMethods()
    this.syBookList()
  },
  methods: {
    dataMethods () {
      this.data.forEach(item => {
        this.checkedCities.push(item.id)
        this.selectObj[item.id] = item.id
      })
      this.storageData = this.deepCopy(this.data)
    },
    search () {
      this.page = 1
      this.syBookList()
    },
    reset () {
      this.keyword = ''
      this.bookTypeId = ''
      this.bookTypeFirstId = ''
      this.bookTypeSecondId = ''
      this.syBookList()
    },
    async getSyTypeTree () {
      const res = await this.$api.academy.getSyTypeTree({ neecCountBook: 1 })
      var { data } = res
      this.bookType = data
    },
    bookTypeChoose (data) {
      if (data.level == '1') { // eslint-disable-line
        this.bookTypeFirstId = data.id
        this.bookTypeSecondId = ''
      } else {
        this.bookTypeFirstId = ''
        this.bookTypeSecondId = data.id
      }
    },
    async syBookList () {
      const res = await this.$api.academy.syBookList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        bookTypeFirstId: this.bookTypeFirstId,
        bookTypeSecondId: this.bookTypeSecondId
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
    },
    howManyArticle () {
      this.syBookList()
    },
    whatPage () {
      this.syBookList()
    },
    handleCheckedCitiesChange (value) {
      var values = []
      value.forEach(item => {
        values[item] = item
      })
      this.tableData.forEach((item) => {
        if (Object.prototype.hasOwnProperty.call(values, item.id)) {
        } else {
          delete this.selectObj[item.id]
          this.deleteData(item)
        }
      })
      value.forEach(item => {
        if (Object.prototype.hasOwnProperty.call(this.selectObj, item)) {
        } else {
          this.selectObj[item] = item
          this.pushData(item)
        }
      })
    },
    bookClick (item) {
      if (Object.prototype.hasOwnProperty.call(this.selectObj, item.id)) {
        delete this.selectObj[item.id]
        var checkedCities = this.checkedCities
        this.checkedCities = checkedCities.filter(row => row !== item.id)
        var storageData = this.storageData
        this.storageData = storageData.filter(row => row.id !== item.id)
      } else {
        this.selectObj[item.id] = item.id
        this.checkedCities.push(item.id)
        this.storageData.push(item)
      }
    },
    deleteData (data) {
      const arr = this.storageData
      arr.forEach((item, index) => {
        if (item.id === data.id) {
          arr.splice(index, 1)
        }
      })
      this.storageData = arr
    },
    pushData (id) {
      this.tableData.forEach((item, index) => {
        if (item.id === id) {
          this.storageData.push(item)
        }
      })
    },
    determine () {
      this.$emit('callback', this.storageData, true)
    },
    cancel () {
      this.$emit('callback', this.data, false)
    },
    deepCopy (data) {
      var t = this.type(data)
      var o
      var i
      var ni
      if (t === 'array') {
        o = []
      } else if (t === 'object') {
        o = {}
      } else {
        return data
      }
      if (t === 'array') {
        for (i = 0, ni = data.length; i < ni; i++) {
          o.push(this.deepCopy(data[i]))
        }
        return o
      } else if (t === 'object') {
        for (i in data) {
          o[i] = this.deepCopy(data[i])
        }
        return o
      }
    },
    type (obj) {
      var toString = Object.prototype.toString
      var map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'undefined',
        '[object Null]': 'null',
        '[object Object]': 'object'
      }
      return map[toString.call(obj)]
    }
  }
}
</script>
<style lang="scss">
.RelatedBooks {
  width: 1092px;
  height: 520px;
  .RelatedBooksBox {
    width: 100%;
    height: calc(100% - 116px);
    border-top: 1px solid #e6e6e6;
    border-bottom: 1px solid #e6e6e6;
    .el-checkbox-group {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
    }

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .is-vertical {
      .el-scrollbar__thumb {
        background-color: rgba(144, 147, 153, 0.8);
      }
    }

    .RelatedBooksListBox {
      display: flex;
      flex-wrap: wrap;
      padding-left: 24px;
      padding-top: 24px;
      .RelatedBooksItem {
        display: flex;
        justify-content: space-between;
        margin-right: 24px;
        margin-bottom: 24px;
        width: 332px;
        height: 128px;
        cursor: pointer;
        .RelatedBooksItemImg {
          height: 128px;
          width: 95px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .RelatedBooksItemBox {
          width: 222px;
          height: 100%;
          position: relative;
          .RelatedBooksItemName {
            color: #333;
            line-height: 21px;
            font-size: $textSize16;
            margin-bottom: 7px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          .RelatedBooksItemIntroduction {
            line-height: 24px;
            color: #666;
            letter-spacing: 0.93px;
            height: 72px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            font-size: 13px;
          }
          .RelatedBooksItemAuthor {
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .RelatedBooksItemAuthorText {
              font-size: 13px;
              color: #999;
              letter-spacing: 1px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
            .el-checkbox__label {
              display: none;
            }
          }
        }
      }
    }
  }
}
</style>
