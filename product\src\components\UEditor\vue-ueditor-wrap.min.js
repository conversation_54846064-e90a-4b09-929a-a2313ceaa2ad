/* eslint-disable */
!(function (t, e) {
  "object" == typeof exports && "object" == typeof module
    ? (module.exports = e())
    : "function" == typeof define && define.amd
      ? define([], e)
      : "object" == typeof exports
        ? (exports.VueUeditorWrap = e())
        : (t.VueUeditorWrap = e());
})("undefined" != typeof self ? self : this, function () {
  return (function (t) {
    var e = {};
    function n (r) {
      if (e[r]) return e[r].exports;
      var o = (e[r] = { i: r, l: !1, exports: {} });
      return t[r].call(o.exports, o, o.exports, n), (o.l = !0), o.exports;
    }
    return (
      (n.m = t),
      (n.c = e),
      (n.d = function (t, e, r) {
        n.o(t, e) ||
          Object.defineProperty(t, e, {
            configurable: !1,
            enumerable: !0,
            get: r,
          });
      }),
      (n.n = function (t) {
        var e =
          t && t.__esModule
            ? function () {
              return t.default;
            }
            : function () {
              return t;
            };
        return n.d(e, "a", e), e;
      }),
      (n.o = function (t, e) {
        return Object.prototype.hasOwnProperty.call(t, e);
      }),
      (n.p = ""),
      n((n.s = 40))
    );
  })([
    function (t, e) {
      var n = (t.exports =
        "undefined" != typeof window && window.Math == Math
          ? window
          : "undefined" != typeof self && self.Math == Math
            ? self
            : Function("return this")());
      "number" == typeof __g && (__g = n);
    },
    function (t, e, n) {
      var r = n(28)("wks"),
        o = n(29),
        i = n(0).Symbol,
        u = "function" == typeof i;
      (t.exports = function (t) {
        return r[t] || (r[t] = (u && i[t]) || (u ? i : o)("Symbol." + t));
      }).store = r;
    },
    function (t, e) {
      var n = (t.exports = { version: "2.6.12" });
      "number" == typeof __e && (__e = n);
    },
    function (t, e, n) {
      var r = n(7);
      t.exports = function (t) {
        if (!r(t)) throw TypeError(t + " is not an object!");
        return t;
      };
    },
    function (t, e, n) {
      var r = n(0),
        o = n(2),
        i = n(11),
        u = n(5),
        s = n(9),
        c = function (t, e, n) {
          var a,
            f,
            l,
            d = t & c.F,
            p = t & c.G,
            h = t & c.S,
            v = t & c.P,
            y = t & c.B,
            m = t & c.W,
            _ = p ? o : o[e] || (o[e] = {}),
            g = _.prototype,
            b = p ? r : h ? r[e] : (r[e] || {}).prototype;
          for (a in (p && (n = e), n))
            ((f = !d && b && void 0 !== b[a]) && s(_, a)) ||
              ((l = f ? b[a] : n[a]),
                (_[a] =
                  p && "function" != typeof b[a]
                    ? n[a]
                    : y && f
                      ? i(l, r)
                      : m && b[a] == l
                        ? (function (t) {
                          var e = function (e, n, r) {
                            if (this instanceof t) {
                              switch (arguments.length) {
                                case 0:
                                  return new t();
                                case 1:
                                  return new t(e);
                                case 2:
                                  return new t(e, n);
                              }
                              return new t(e, n, r);
                            }
                            return t.apply(this, arguments);
                          };
                          return (e.prototype = t.prototype), e;
                        })(l)
                        : v && "function" == typeof l
                          ? i(Function.call, l)
                          : l),
                v &&
                (((_.virtual || (_.virtual = {}))[a] = l),
                  t & c.R && g && !g[a] && u(g, a, l)));
        };
      (c.F = 1),
        (c.G = 2),
        (c.S = 4),
        (c.P = 8),
        (c.B = 16),
        (c.W = 32),
        (c.U = 64),
        (c.R = 128),
        (t.exports = c);
    },
    function (t, e, n) {
      var r = n(13),
        o = n(31);
      t.exports = n(6)
        ? function (t, e, n) {
          return r.f(t, e, o(1, n));
        }
        : function (t, e, n) {
          return (t[e] = n), t;
        };
    },
    function (t, e, n) {
      t.exports = !n(14)(function () {
        return (
          7 !=
          Object.defineProperty({}, "a", {
            get: function () {
              return 7;
            },
          }).a
        );
      });
    },
    function (t, e) {
      t.exports = function (t) {
        return "object" == typeof t ? null !== t : "function" == typeof t;
      };
    },
    function (t, e) {
      t.exports = {};
    },
    function (t, e) {
      var n = {}.hasOwnProperty;
      t.exports = function (t, e) {
        return n.call(t, e);
      };
    },
    function (t, e) {
      var n = {}.toString;
      t.exports = function (t) {
        return n.call(t).slice(8, -1);
      };
    },
    function (t, e, n) {
      var r = n(12);
      t.exports = function (t, e, n) {
        if ((r(t), void 0 === e)) return t;
        switch (n) {
          case 1:
            return function (n) {
              return t.call(e, n);
            };
          case 2:
            return function (n, r) {
              return t.call(e, n, r);
            };
          case 3:
            return function (n, r, o) {
              return t.call(e, n, r, o);
            };
        }
        return function () {
          return t.apply(e, arguments);
        };
      };
    },
    function (t, e) {
      t.exports = function (t) {
        if ("function" != typeof t) throw TypeError(t + " is not a function!");
        return t;
      };
    },
    function (t, e, n) {
      var r = n(3),
        o = n(50),
        i = n(51),
        u = Object.defineProperty;
      e.f = n(6)
        ? Object.defineProperty
        : function (t, e, n) {
          if ((r(t), (e = i(e, !0)), r(n), o))
            try {
              return u(t, e, n);
            } catch (t) { }
          if ("get" in n || "set" in n)
            throw TypeError("Accessors not supported!");
          return "value" in n && (t[e] = n.value), t;
        };
    },
    function (t, e) {
      t.exports = function (t) {
        try {
          return !!t();
        } catch (t) {
          return !0;
        }
      };
    },
    function (t, e, n) {
      var r = n(16);
      t.exports = function (t) {
        return Object(r(t));
      };
    },
    function (t, e) {
      t.exports = function (t) {
        if (void 0 == t) throw TypeError("Can't call method on  " + t);
        return t;
      };
    },
    function (t, e, n) {
      var r = n(46),
        o = n(30);
      t.exports =
        Object.keys ||
        function (t) {
          return r(t, o);
        };
    },
    function (t, e, n) {
      var r = n(26),
        o = n(16);
      t.exports = function (t) {
        return r(o(t));
      };
    },
    function (t, e) {
      var n = Math.ceil,
        r = Math.floor;
      t.exports = function (t) {
        return isNaN((t = +t)) ? 0 : (t > 0 ? r : n)(t);
      };
    },
    function (t, e, n) {
      var r = n(28)("keys"),
        o = n(29);
      t.exports = function (t) {
        return r[t] || (r[t] = o(t));
      };
    },
    function (t, e) {
      t.exports = !0;
    },
    function (t, e, n) {
      var r = n(7),
        o = n(0).document,
        i = r(o) && r(o.createElement);
      t.exports = function (t) {
        return i ? o.createElement(t) : {};
      };
    },
    function (t, e, n) {
      var r = n(13).f,
        o = n(9),
        i = n(1)("toStringTag");
      t.exports = function (t, e, n) {
        t &&
          !o((t = n ? t : t.prototype), i) &&
          r(t, i, { configurable: !0, value: e });
      };
    },
    function (t, e, n) {
      "use strict";
      var r = n(12);
      t.exports.f = function (t) {
        return new (function (t) {
          var e, n;
          (this.promise = new t(function (t, r) {
            if (void 0 !== e || void 0 !== n)
              throw TypeError("Bad Promise constructor");
            (e = t), (n = r);
          })),
            (this.resolve = r(e)),
            (this.reject = r(n));
        })(t);
      };
    },
    function (t, e, n) {
      "use strict";
      (function (t) {
        Object.defineProperty(e, "__esModule", { value: !0 });
        var r = f(n(43)),
          o = f(n(32)),
          i = f(n(79)),
          u = f(n(86)),
          s = f(n(87)),
          c = f(n(88)),
          a = f(n(89));
        function f (t) {
          return t && t.__esModule ? t : { default: t };
        }
        var l = "UN_READY",
          d = "PENDING",
          p = "READY";
        e.default = {
          name: "VueUeditorWrap",
          data: function () {
            return {
              status: l,
              defaultConfig: {
                UEDITOR_HOME_URL:
                  void 0 !== t && t.env.BASE_URL
                    ? t.env.BASE_URL + "UEditor/"
                    : "/static/UEditor/",
              },
            };
          },
          props: {
            mode: {
              type: String,
              default: "observer",
              validator: function (t) {
                return -1 !== ["observer", "listener"].indexOf(t);
              },
            },
            value: { type: String, default: "" },
            config: {
              type: Object,
              default: function () {
                return {};
              },
            },
            init: { type: Function, default: function () { } },
            destroy: { type: Boolean, default: !0 },
            name: { type: String, default: "" },
            observerDebounceTime: {
              type: Number,
              default: 50,
              validator: function (t) {
                return t >= 20;
              },
            },
            observerOptions: {
              type: Object,
              default: function () {
                return {
                  attributes: !0,
                  attributeFilter: ["src", "style", "type", "name"],
                  characterData: !0,
                  childList: !0,
                  subtree: !0,
                };
              },
            },
            forceInit: { type: Boolean, default: !1 },
            editorId: { type: String },
            editorDependencies: Array,
            editorDependenciesChecker: Function,
          },
          computed: {
            mixedConfig: function () {
              return (0, i.default)({}, this.defaultConfig, this.config);
            },
          },
          methods: {
            registerButton: function (t) {
              var e = t.name,
                n = t.icon,
                r = t.tip,
                o = t.handler,
                i = t.index,
                u = t.UE,
                s = void 0 === u ? window.UE : u;
              s.registerUI(
                e,
                function (t, e) {
                  t.registerCommand(e, {
                    execCommand: function () {
                      o(t, e);
                    },
                  });
                  var i = new s.ui.Button({
                    name: e,
                    title: r,
                    cssRules:
                      "background-image: url(" +
                      n +
                      ") !important;background-size: cover;",
                    onclick: function () {
                      t.execCommand(e);
                    },
                  });
                  return (
                    t.addListener("selectionchange", function () {
                      var n = t.queryCommandState(e);
                      -1 === n
                        ? (i.setDisabled(!0), i.setChecked(!1))
                        : (i.setDisabled(!1), i.setChecked(n));
                    }),
                    i
                  );
                },
                i,
                this.id
              );
            },
            _initEditor: function () {
              var t = this;
              (this.$refs.container.id = this.id =
                this.editorId || "editor_" + (0, a.default)(8)),
                this.init(),
                this.$emit("before-init", this.id, this.mixedConfig),
                this.$emit("beforeInit", this.id, this.mixedConfig),
                (this.editor = window.UE.getEditor(this.id, this.mixedConfig)),
                this.editor.addListener("ready", function () {
                  t.status === p
                    ? t.editor.setContent(t.value)
                    : ((t.status = p),
                      t.$emit("ready", t.editor),
                      t.value && t.editor.setContent(t.value)),
                    "observer" === t.mode && window.MutationObserver
                      ? t._observerChangeListener()
                      : t._normalChangeListener();
                });
            },
            _loadScript: function (t) {
              return new o.default(function (e, n) {
                if (
                  (window.$loadEventBus.on(t, e),
                    !1 === window.$loadEventBus.listeners[t].requested)
                ) {
                  window.$loadEventBus.listeners[t].requested = !0;
                  var r = document.createElement("script");
                  (r.src = t),
                    (r.onload = function () {
                      window.$loadEventBus.emit(t);
                    }),
                    (r.onerror = n),
                    document.getElementsByTagName("head")[0].appendChild(r);
                }
              });
            },
            _loadCss: function (t) {
              return new o.default(function (e, n) {
                if (
                  (window.$loadEventBus.on(t, e),
                    !1 === window.$loadEventBus.listeners[t].requested)
                ) {
                  window.$loadEventBus.listeners[t].requested = !0;
                  var r = document.createElement("link");
                  (r.type = "text/css"),
                    (r.rel = "stylesheet"),
                    (r.href = t),
                    (r.onload = function () {
                      window.$loadEventBus.emit(t);
                    }),
                    (r.onerror = n),
                    document.getElementsByTagName("head")[0].appendChild(r);
                }
              });
            },
            _loadEditorDependencies: function () {
              var t = this;
              window.$loadEventBus || (window.$loadEventBus = new u.default());
              var e = ["ueditor.config.js", "ueditor.all.js"];
              return new o.default(function (n, i) {
                if (
                  t.editorDependencies &&
                  t.editorDependenciesChecker &&
                  t.editorDependenciesChecker()
                )
                  n();
                else if (
                  !t.editorDependencies &&
                  window.UE &&
                  window.UE.getEditor &&
                  window.UEDITOR_CONFIG &&
                  0 !== (0, r.default)(window.UEDITOR_CONFIG).length
                )
                  n();
                else {
                  var u = (t.editorDependencies || e).reduce(
                    function (e, n) {
                      return (
                        /^((https?:)?\/\/)?[-a-zA-Z0-9]+(\.[-a-zA-Z0-9]+)+\//.test(
                          n
                        ) || (n = (t.mixedConfig.UEDITOR_HOME_URL || "") + n),
                        ".js" === n.slice(-3)
                          ? e.jsLinks.push(n)
                          : ".css" === n.slice(-4) && e.cssLinks.push(n),
                        e
                      );
                    },
                    { jsLinks: [], cssLinks: [] }
                  ),
                    s = u.jsLinks,
                    a = u.cssLinks;
                  o.default
                    .all([
                      o.default.all(
                        a.map(function (e) {
                          return t._loadCss(e);
                        })
                      ),
                      (0, c.default)(
                        s.map(function (e) {
                          return function () {
                            return t._loadScript(e);
                          };
                        })
                      ),
                    ])
                    .then(function () {
                      return n();
                    })
                    .catch(i);
                }
              });
            },
            _contentChangeHandler: function () {
              (this.innerValue = this.editor.getContent()),
                this.$emit("input", this.innerValue);
            },
            _normalChangeListener: function () {
              this.editor.addListener(
                "contentChange",
                this._contentChangeHandler
              );
            },
            _observerChangeListener: function () {
              var t = this;
              (this.observer = new MutationObserver(
                (0, s.default)(function () {
                  t.editor.document.getElementById("baidu_pastebin") ||
                    ((t.innerValue = t.editor.getContent()),
                      t.$emit("input", t.innerValue));
                }, this.observerDebounceTime)
              )),
                this.observer.observe(this.editor.body, this.observerOptions);
            },
          },
          deactivated: function () {
            this.editor &&
              this.editor.removeListener(
                "contentChange",
                this._contentChangeHandler
              ),
              this.observer && this.observer.disconnect();
          },
          beforeDestroy: function () {
            this.destroy &&
              this.editor &&
              this.editor.destroy &&
              this.editor.destroy(),
              this.observer &&
              this.observer.disconnect &&
              this.observer.disconnect();
          },
          watch: {
            value: {
              handler: function (t) {
                var e = this;
                this.status === l
                  ? ((this.status = d),
                    (this.forceInit || "undefined" != typeof window) &&
                    this._loadEditorDependencies()
                      .then(function () {
                        e.$refs.container
                          ? e._initEditor()
                          : e.$nextTick(function () {
                            return e._initEditor();
                          });
                      })
                      .catch(function () {
                        throw new Error(
                          "[vue-ueditor-wrap] UEditor 资源加载失败！请检查资源是否存在，UEDITOR_HOME_URL 是否配置正确！"
                        );
                      }))
                  : this.status === p &&
                  (t === this.innerValue || this.editor.setContent(t || ""));
              },
              immediate: !0,
            },
          },
        };
      }.call(e, n(42)));
    },
    function (t, e, n) {
      var r = n(10);
      t.exports = Object("z").propertyIsEnumerable(0)
        ? Object
        : function (t) {
          return "String" == r(t) ? t.split("") : Object(t);
        };
    },
    function (t, e, n) {
      var r = n(19),
        o = Math.min;
      t.exports = function (t) {
        return t > 0 ? o(r(t), 9007199254740991) : 0;
      };
    },
    function (t, e, n) {
      var r = n(2),
        o = n(0),
        i = o["__core-js_shared__"] || (o["__core-js_shared__"] = {});
      (t.exports = function (t, e) {
        return i[t] || (i[t] = void 0 !== e ? e : {});
      })("versions", []).push({
        version: r.version,
        mode: n(21) ? "pure" : "global",
        copyright: "© 2020 Denis Pushkarev (zloirock.ru)",
      });
    },
    function (t, e) {
      var n = 0,
        r = Math.random();
      t.exports = function (t) {
        return "Symbol(".concat(
          void 0 === t ? "" : t,
          ")_",
          (++n + r).toString(36)
        );
      };
    },
    function (t, e) {
      t.exports =
        "constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(
          ","
        );
    },
    function (t, e) {
      t.exports = function (t, e) {
        return {
          enumerable: !(1 & t),
          configurable: !(2 & t),
          writable: !(4 & t),
          value: e,
        };
      };
    },
    function (t, e, n) {
      t.exports = { default: n(52), __esModule: !0 };
    },
    function (t, e, n) {
      "use strict";
      var r = n(21),
        o = n(4),
        i = n(56),
        u = n(5),
        s = n(8),
        c = n(57),
        a = n(23),
        f = n(60),
        l = n(1)("iterator"),
        d = !([].keys && "next" in [].keys()),
        p = function () {
          return this;
        };
      t.exports = function (t, e, n, h, v, y, m) {
        c(n, e, h);
        var _,
          g,
          b,
          x = function (t) {
            if (!d && t in j) return j[t];
            switch (t) {
              case "keys":
              case "values":
                return function () {
                  return new n(this, t);
                };
            }
            return function () {
              return new n(this, t);
            };
          },
          w = e + " Iterator",
          E = "values" == v,
          O = !1,
          j = t.prototype,
          T = j[l] || j["@@iterator"] || (v && j[v]),
          S = T || x(v),
          C = v ? (E ? x("entries") : S) : void 0,
          L = ("Array" == e && j.entries) || T;
        if (
          (L &&
            (b = f(L.call(new t()))) !== Object.prototype &&
            b.next &&
            (a(b, w, !0), r || "function" == typeof b[l] || u(b, l, p)),
            E &&
            T &&
            "values" !== T.name &&
            ((O = !0),
              (S = function () {
                return T.call(this);
              })),
            (r && !m) || (!d && !O && j[l]) || u(j, l, S),
            (s[e] = S),
            (s[w] = p),
            v)
        )
          if (
            ((_ = {
              values: E ? S : x("values"),
              keys: y ? S : x("keys"),
              entries: C,
            }),
              m)
          )
            for (g in _) g in j || i(j, g, _[g]);
          else o(o.P + o.F * (d || O), e, _);
        return _;
      };
    },
    function (t, e, n) {
      var r = n(0).document;
      t.exports = r && r.documentElement;
    },
    function (t, e, n) {
      var r = n(10),
        o = n(1)("toStringTag"),
        i =
          "Arguments" ==
          r(
            (function () {
              return arguments;
            })()
          );
      t.exports = function (t) {
        var e, n, u;
        return void 0 === t
          ? "Undefined"
          : null === t
            ? "Null"
            : "string" ==
              typeof (n = (function (t, e) {
                try {
                  return t[e];
                } catch (t) { }
              })((e = Object(t)), o))
              ? n
              : i
                ? r(e)
                : "Object" == (u = r(e)) && "function" == typeof e.callee
                  ? "Arguments"
                  : u;
      };
    },
    function (t, e, n) {
      var r = n(3),
        o = n(12),
        i = n(1)("species");
      t.exports = function (t, e) {
        var n,
          u = r(t).constructor;
        return void 0 === u || void 0 == (n = r(u)[i]) ? e : o(n);
      };
    },
    function (t, e, n) {
      var r,
        o,
        i,
        u = n(11),
        s = n(71),
        c = n(34),
        a = n(22),
        f = n(0),
        l = f.process,
        d = f.setImmediate,
        p = f.clearImmediate,
        h = f.MessageChannel,
        v = f.Dispatch,
        y = 0,
        m = {},
        _ = function () {
          var t = +this;
          if (m.hasOwnProperty(t)) {
            var e = m[t];
            delete m[t], e();
          }
        },
        g = function (t) {
          _.call(t.data);
        };
      (d && p) ||
        ((d = function (t) {
          for (var e = [], n = 1; arguments.length > n;)
            e.push(arguments[n++]);
          return (
            (m[++y] = function () {
              s("function" == typeof t ? t : Function(t), e);
            }),
            r(y),
            y
          );
        }),
          (p = function (t) {
            delete m[t];
          }),
          "process" == n(10)(l)
            ? (r = function (t) {
              l.nextTick(u(_, t, 1));
            })
            : v && v.now
              ? (r = function (t) {
                v.now(u(_, t, 1));
              })
              : h
                ? ((i = (o = new h()).port2),
                  (o.port1.onmessage = g),
                  (r = u(i.postMessage, i, 1)))
                : f.addEventListener &&
                  "function" == typeof postMessage &&
                  !f.importScripts
                  ? ((r = function (t) {
                    f.postMessage(t + "", "*");
                  }),
                    f.addEventListener("message", g, !1))
                  : (r =
                    "onreadystatechange" in a("script")
                      ? function (t) {
                        c.appendChild(a("script")).onreadystatechange =
                          function () {
                            c.removeChild(this), _.call(t);
                          };
                      }
                      : function (t) {
                        setTimeout(u(_, t, 1), 0);
                      })),
        (t.exports = { set: d, clear: p });
    },
    function (t, e) {
      t.exports = function (t) {
        try {
          return { e: !1, v: t() };
        } catch (t) {
          return { e: !0, v: t };
        }
      };
    },
    function (t, e, n) {
      var r = n(3),
        o = n(7),
        i = n(24);
      t.exports = function (t, e) {
        if ((r(t), o(e) && e.constructor === t)) return e;
        var n = i.f(t);
        return (0, n.resolve)(e), n.promise;
      };
    },
    function (t, e, n) {
      "use strict";
      Object.defineProperty(e, "__esModule", { value: !0 });
      var r = n(25),
        o = n.n(r);
      for (var i in r)
        "default" !== i &&
          (function (t) {
            n.d(e, t, function () {
              return r[t];
            });
          })(i);
      var u = n(90),
        s = n(41)(o.a, u.a, !1, null, null, null);
      (s.options.__file = "src/components/vue-ueditor-wrap.vue"),
        (e.default = s.exports);
    },
    function (t, e) {
      t.exports = function (t, e, n, r, o, i) {
        var u,
          s = (t = t || {}),
          c = typeof t.default;
        ("object" !== c && "function" !== c) || ((u = t), (s = t.default));
        var a,
          f = "function" == typeof s ? s.options : s;
        if (
          (e &&
            ((f.render = e.render),
              (f.staticRenderFns = e.staticRenderFns),
              (f._compiled = !0)),
            n && (f.functional = !0),
            o && (f._scopeId = o),
            i
              ? ((a = function (t) {
                (t =
                  t ||
                  (this.$vnode && this.$vnode.ssrContext) ||
                  (this.parent &&
                    this.parent.$vnode &&
                    this.parent.$vnode.ssrContext)) ||
                  "undefined" == typeof __VUE_SSR_CONTEXT__ ||
                  (t = __VUE_SSR_CONTEXT__),
                  r && r.call(this, t),
                  t &&
                  t._registeredComponents &&
                  t._registeredComponents.add(i);
              }),
                (f._ssrRegister = a))
              : r && (a = r),
            a)
        ) {
          var l = f.functional,
            d = l ? f.render : f.beforeCreate;
          l
            ? ((f._injectStyles = a),
              (f.render = function (t, e) {
                return a.call(e), d(t, e);
              }))
            : (f.beforeCreate = d ? [].concat(d, a) : [a]);
        }
        return { esModule: u, exports: s, options: f };
      };
    },
    function (t, e) {
      var n,
        r,
        o = (t.exports = {});
      function i () {
        throw new Error("setTimeout has not been defined");
      }
      function u () {
        throw new Error("clearTimeout has not been defined");
      }
      function s (t) {
        if (n === setTimeout) return setTimeout(t, 0);
        if ((n === i || !n) && setTimeout)
          return (n = setTimeout), setTimeout(t, 0);
        try {
          return n(t, 0);
        } catch (e) {
          try {
            return n.call(null, t, 0);
          } catch (e) {
            return n.call(this, t, 0);
          }
        }
      }
      !(function () {
        try {
          n = "function" == typeof setTimeout ? setTimeout : i;
        } catch (t) {
          n = i;
        }
        try {
          r = "function" == typeof clearTimeout ? clearTimeout : u;
        } catch (t) {
          r = u;
        }
      })();
      var c,
        a = [],
        f = !1,
        l = -1;
      function d () {
        f &&
          c &&
          ((f = !1), c.length ? (a = c.concat(a)) : (l = -1), a.length && p());
      }
      function p () {
        if (!f) {
          var t = s(d);
          f = !0;
          for (var e = a.length; e;) {
            for (c = a, a = []; ++l < e;) c && c[l].run();
            (l = -1), (e = a.length);
          }
          (c = null),
            (f = !1),
            (function (t) {
              if (r === clearTimeout) return clearTimeout(t);
              if ((r === u || !r) && clearTimeout)
                return (r = clearTimeout), clearTimeout(t);
              try {
                r(t);
              } catch (e) {
                try {
                  return r.call(null, t);
                } catch (e) {
                  return r.call(this, t);
                }
              }
            })(t);
        }
      }
      function h (t, e) {
        (this.fun = t), (this.array = e);
      }
      function v () { }
      (o.nextTick = function (t) {
        var e = new Array(arguments.length - 1);
        if (arguments.length > 1)
          for (var n = 1; n < arguments.length; n++) e[n - 1] = arguments[n];
        a.push(new h(t, e)), 1 !== a.length || f || s(p);
      }),
        (h.prototype.run = function () {
          this.fun.apply(null, this.array);
        }),
        (o.title = "browser"),
        (o.browser = !0),
        (o.env = {}),
        (o.argv = []),
        (o.version = ""),
        (o.versions = {}),
        (o.on = v),
        (o.addListener = v),
        (o.once = v),
        (o.off = v),
        (o.removeListener = v),
        (o.removeAllListeners = v),
        (o.emit = v),
        (o.prependListener = v),
        (o.prependOnceListener = v),
        (o.listeners = function (t) {
          return [];
        }),
        (o.binding = function (t) {
          throw new Error("process.binding is not supported");
        }),
        (o.cwd = function () {
          return "/";
        }),
        (o.chdir = function (t) {
          throw new Error("process.chdir is not supported");
        }),
        (o.umask = function () {
          return 0;
        });
    },
    function (t, e, n) {
      t.exports = { default: n(44), __esModule: !0 };
    },
    function (t, e, n) {
      n(45), (t.exports = n(2).Object.keys);
    },
    function (t, e, n) {
      var r = n(15),
        o = n(17);
      n(49)("keys", function () {
        return function (t) {
          return o(r(t));
        };
      });
    },
    function (t, e, n) {
      var r = n(9),
        o = n(18),
        i = n(47)(!1),
        u = n(20)("IE_PROTO");
      t.exports = function (t, e) {
        var n,
          s = o(t),
          c = 0,
          a = [];
        for (n in s) n != u && r(s, n) && a.push(n);
        for (; e.length > c;) r(s, (n = e[c++])) && (~i(a, n) || a.push(n));
        return a;
      };
    },
    function (t, e, n) {
      var r = n(18),
        o = n(27),
        i = n(48);
      t.exports = function (t) {
        return function (e, n, u) {
          var s,
            c = r(e),
            a = o(c.length),
            f = i(u, a);
          if (t && n != n) {
            for (; a > f;) if ((s = c[f++]) != s) return !0;
          } else
            for (; a > f; f++)
              if ((t || f in c) && c[f] === n) return t || f || 0;
          return !t && -1;
        };
      };
    },
    function (t, e, n) {
      var r = n(19),
        o = Math.max,
        i = Math.min;
      t.exports = function (t, e) {
        return (t = r(t)) < 0 ? o(t + e, 0) : i(t, e);
      };
    },
    function (t, e, n) {
      var r = n(4),
        o = n(2),
        i = n(14);
      t.exports = function (t, e) {
        var n = (o.Object || {})[t] || Object[t],
          u = {};
        (u[t] = e(n)),
          r(
            r.S +
            r.F *
            i(function () {
              n(1);
            }),
            "Object",
            u
          );
      };
    },
    function (t, e, n) {
      t.exports =
        !n(6) &&
        !n(14)(function () {
          return (
            7 !=
            Object.defineProperty(n(22)("div"), "a", {
              get: function () {
                return 7;
              },
            }).a
          );
        });
    },
    function (t, e, n) {
      var r = n(7);
      t.exports = function (t, e) {
        if (!r(t)) return t;
        var n, o;
        if (e && "function" == typeof (n = t.toString) && !r((o = n.call(t))))
          return o;
        if ("function" == typeof (n = t.valueOf) && !r((o = n.call(t))))
          return o;
        if (!e && "function" == typeof (n = t.toString) && !r((o = n.call(t))))
          return o;
        throw TypeError("Can't convert object to primitive value");
      };
    },
    function (t, e, n) {
      n(53), n(54), n(61), n(65), n(77), n(78), (t.exports = n(2).Promise);
    },
    function (t, e) { },
    function (t, e, n) {
      "use strict";
      var r = n(55)(!0);
      n(33)(
        String,
        "String",
        function (t) {
          (this._t = String(t)), (this._i = 0);
        },
        function () {
          var t,
            e = this._t,
            n = this._i;
          return n >= e.length
            ? { value: void 0, done: !0 }
            : ((t = r(e, n)), (this._i += t.length), { value: t, done: !1 });
        }
      );
    },
    function (t, e, n) {
      var r = n(19),
        o = n(16);
      t.exports = function (t) {
        return function (e, n) {
          var i,
            u,
            s = String(o(e)),
            c = r(n),
            a = s.length;
          return c < 0 || c >= a
            ? t
              ? ""
              : void 0
            : (i = s.charCodeAt(c)) < 55296 ||
              i > 56319 ||
              c + 1 === a ||
              (u = s.charCodeAt(c + 1)) < 56320 ||
              u > 57343
              ? t
                ? s.charAt(c)
                : i
              : t
                ? s.slice(c, c + 2)
                : u - 56320 + ((i - 55296) << 10) + 65536;
        };
      };
    },
    function (t, e, n) {
      t.exports = n(5);
    },
    function (t, e, n) {
      "use strict";
      var r = n(58),
        o = n(31),
        i = n(23),
        u = {};
      n(5)(u, n(1)("iterator"), function () {
        return this;
      }),
        (t.exports = function (t, e, n) {
          (t.prototype = r(u, { next: o(1, n) })), i(t, e + " Iterator");
        });
    },
    function (t, e, n) {
      var r = n(3),
        o = n(59),
        i = n(30),
        u = n(20)("IE_PROTO"),
        s = function () { },
        c = function () {
          var t,
            e = n(22)("iframe"),
            r = i.length;
          for (
            e.style.display = "none",
            n(34).appendChild(e),
            e.src = "javascript:",
            (t = e.contentWindow.document).open(),
            t.write("<script>document.F=Object</script>"),
            t.close(),
            c = t.F;
            r--;

          )
            delete c.prototype[i[r]];
          return c();
        };
      t.exports =
        Object.create ||
        function (t, e) {
          var n;
          return (
            null !== t
              ? ((s.prototype = r(t)),
                (n = new s()),
                (s.prototype = null),
                (n[u] = t))
              : (n = c()),
            void 0 === e ? n : o(n, e)
          );
        };
    },
    function (t, e, n) {
      var r = n(13),
        o = n(3),
        i = n(17);
      t.exports = n(6)
        ? Object.defineProperties
        : function (t, e) {
          o(t);
          for (var n, u = i(e), s = u.length, c = 0; s > c;)
            r.f(t, (n = u[c++]), e[n]);
          return t;
        };
    },
    function (t, e, n) {
      var r = n(9),
        o = n(15),
        i = n(20)("IE_PROTO"),
        u = Object.prototype;
      t.exports =
        Object.getPrototypeOf ||
        function (t) {
          return (
            (t = o(t)),
            r(t, i)
              ? t[i]
              : "function" == typeof t.constructor && t instanceof t.constructor
                ? t.constructor.prototype
                : t instanceof Object
                  ? u
                  : null
          );
        };
    },
    function (t, e, n) {
      n(62);
      for (
        var r = n(0),
        o = n(5),
        i = n(8),
        u = n(1)("toStringTag"),
        s =
          "CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(
            ","
          ),
        c = 0;
        c < s.length;
        c++
      ) {
        var a = s[c],
          f = r[a],
          l = f && f.prototype;
        l && !l[u] && o(l, u, a), (i[a] = i.Array);
      }
    },
    function (t, e, n) {
      "use strict";
      var r = n(63),
        o = n(64),
        i = n(8),
        u = n(18);
      (t.exports = n(33)(
        Array,
        "Array",
        function (t, e) {
          (this._t = u(t)), (this._i = 0), (this._k = e);
        },
        function () {
          var t = this._t,
            e = this._k,
            n = this._i++;
          return !t || n >= t.length
            ? ((this._t = void 0), o(1))
            : o(0, "keys" == e ? n : "values" == e ? t[n] : [n, t[n]]);
        },
        "values"
      )),
        (i.Arguments = i.Array),
        r("keys"),
        r("values"),
        r("entries");
    },
    function (t, e) {
      t.exports = function () { };
    },
    function (t, e) {
      t.exports = function (t, e) {
        return { value: e, done: !!t };
      };
    },
    function (t, e, n) {
      "use strict";
      var r,
        o,
        i,
        u,
        s = n(21),
        c = n(0),
        a = n(11),
        f = n(35),
        l = n(4),
        d = n(7),
        p = n(12),
        h = n(66),
        v = n(67),
        y = n(36),
        m = n(37).set,
        _ = n(72)(),
        g = n(24),
        b = n(38),
        x = n(73),
        w = n(39),
        E = c.TypeError,
        O = c.process,
        j = O && O.versions,
        T = (j && j.v8) || "",
        S = c.Promise,
        C = "process" == f(O),
        L = function () { },
        P = (o = g.f),
        M = !!(function () {
          try {
            var t = S.resolve(1),
              e = ((t.constructor = {})[n(1)("species")] = function (t) {
                t(L, L);
              });
            return (
              (C || "function" == typeof PromiseRejectionEvent) &&
              t.then(L) instanceof e &&
              0 !== T.indexOf("6.6") &&
              -1 === x.indexOf("Chrome/66")
            );
          } catch (t) { }
        })(),
        k = function (t) {
          var e;
          return !(!d(t) || "function" != typeof (e = t.then)) && e;
        },
        R = function (t, e) {
          if (!t._n) {
            t._n = !0;
            var n = t._c;
            _(function () {
              for (
                var r = t._v,
                o = 1 == t._s,
                i = 0,
                u = function (e) {
                  var n,
                    i,
                    u,
                    s = o ? e.ok : e.fail,
                    c = e.resolve,
                    a = e.reject,
                    f = e.domain;
                  try {
                    s
                      ? (o || (2 == t._h && U(t), (t._h = 1)),
                        !0 === s
                          ? (n = r)
                          : (f && f.enter(),
                            (n = s(r)),
                            f && (f.exit(), (u = !0))),
                        n === e.promise
                          ? a(E("Promise-chain cycle"))
                          : (i = k(n))
                            ? i.call(n, c, a)
                            : c(n))
                      : a(r);
                  } catch (t) {
                    f && !u && f.exit(), a(t);
                  }
                };
                n.length > i;

              )
                u(n[i++]);
              (t._c = []), (t._n = !1), e && !t._h && D(t);
            });
          }
        },
        D = function (t) {
          m.call(c, function () {
            var e,
              n,
              r,
              o = t._v,
              i = A(t);
            if (
              (i &&
                ((e = b(function () {
                  C
                    ? O.emit("unhandledRejection", o, t)
                    : (n = c.onunhandledrejection)
                      ? n({ promise: t, reason: o })
                      : (r = c.console) &&
                      r.error &&
                      r.error("Unhandled promise rejection", o);
                })),
                  (t._h = C || A(t) ? 2 : 1)),
                (t._a = void 0),
                i && e.e)
            )
              throw e.v;
          });
        },
        A = function (t) {
          return 1 !== t._h && 0 === (t._a || t._c).length;
        },
        U = function (t) {
          m.call(c, function () {
            var e;
            C
              ? O.emit("rejectionHandled", t)
              : (e = c.onrejectionhandled) && e({ promise: t, reason: t._v });
          });
        },
        I = function (t) {
          var e = this;
          e._d ||
            ((e._d = !0),
              ((e = e._w || e)._v = t),
              (e._s = 2),
              e._a || (e._a = e._c.slice()),
              R(e, !0));
        },
        B = function (t) {
          var e,
            n = this;
          if (!n._d) {
            (n._d = !0), (n = n._w || n);
            try {
              if (n === t) throw E("Promise can't be resolved itself");
              (e = k(t))
                ? _(function () {
                  var r = { _w: n, _d: !1 };
                  try {
                    e.call(t, a(B, r, 1), a(I, r, 1));
                  } catch (t) {
                    I.call(r, t);
                  }
                })
                : ((n._v = t), (n._s = 1), R(n, !1));
            } catch (t) {
              I.call({ _w: n, _d: !1 }, t);
            }
          }
        };
      M ||
        ((S = function (t) {
          h(this, S, "Promise", "_h"), p(t), r.call(this);
          try {
            t(a(B, this, 1), a(I, this, 1));
          } catch (t) {
            I.call(this, t);
          }
        }),
          ((r = function (t) {
            (this._c = []),
              (this._a = void 0),
              (this._s = 0),
              (this._d = !1),
              (this._v = void 0),
              (this._h = 0),
              (this._n = !1);
          }).prototype = n(74)(S.prototype, {
            then: function (t, e) {
              var n = P(y(this, S));
              return (
                (n.ok = "function" != typeof t || t),
                (n.fail = "function" == typeof e && e),
                (n.domain = C ? O.domain : void 0),
                this._c.push(n),
                this._a && this._a.push(n),
                this._s && R(this, !1),
                n.promise
              );
            },
            catch: function (t) {
              return this.then(void 0, t);
            },
          })),
          (i = function () {
            var t = new r();
            (this.promise = t),
              (this.resolve = a(B, t, 1)),
              (this.reject = a(I, t, 1));
          }),
          (g.f = P =
            function (t) {
              return t === S || t === u ? new i(t) : o(t);
            })),
        l(l.G + l.W + l.F * !M, { Promise: S }),
        n(23)(S, "Promise"),
        n(75)("Promise"),
        (u = n(2).Promise),
        l(l.S + l.F * !M, "Promise", {
          reject: function (t) {
            var e = P(this);
            return (0, e.reject)(t), e.promise;
          },
        }),
        l(l.S + l.F * (s || !M), "Promise", {
          resolve: function (t) {
            return w(s && this === u ? S : this, t);
          },
        }),
        l(
          l.S +
          l.F *
          !(
            M &&
            n(76)(function (t) {
              S.all(t).catch(L);
            })
          ),
          "Promise",
          {
            all: function (t) {
              var e = this,
                n = P(e),
                r = n.resolve,
                o = n.reject,
                i = b(function () {
                  var n = [],
                    i = 0,
                    u = 1;
                  v(t, !1, function (t) {
                    var s = i++,
                      c = !1;
                    n.push(void 0),
                      u++,
                      e.resolve(t).then(function (t) {
                        c || ((c = !0), (n[s] = t), --u || r(n));
                      }, o);
                  }),
                    --u || r(n);
                });
              return i.e && o(i.v), n.promise;
            },
            race: function (t) {
              var e = this,
                n = P(e),
                r = n.reject,
                o = b(function () {
                  v(t, !1, function (t) {
                    e.resolve(t).then(n.resolve, r);
                  });
                });
              return o.e && r(o.v), n.promise;
            },
          }
        );
    },
    function (t, e) {
      t.exports = function (t, e, n, r) {
        if (!(t instanceof e) || (void 0 !== r && r in t))
          throw TypeError(n + ": incorrect invocation!");
        return t;
      };
    },
    function (t, e, n) {
      var r = n(11),
        o = n(68),
        i = n(69),
        u = n(3),
        s = n(27),
        c = n(70),
        a = {},
        f = {};
      ((e = t.exports =
        function (t, e, n, l, d) {
          var p,
            h,
            v,
            y,
            m = d
              ? function () {
                return t;
              }
              : c(t),
            _ = r(n, l, e ? 2 : 1),
            g = 0;
          if ("function" != typeof m) throw TypeError(t + " is not iterable!");
          if (i(m)) {
            for (p = s(t.length); p > g; g++)
              if (
                (y = e ? _(u((h = t[g]))[0], h[1]) : _(t[g])) === a ||
                y === f
              )
                return y;
          } else
            for (v = m.call(t); !(h = v.next()).done;)
              if ((y = o(v, _, h.value, e)) === a || y === f) return y;
        }).BREAK = a),
        (e.RETURN = f);
    },
    function (t, e, n) {
      var r = n(3);
      t.exports = function (t, e, n, o) {
        try {
          return o ? e(r(n)[0], n[1]) : e(n);
        } catch (e) {
          var i = t.return;
          throw (void 0 !== i && r(i.call(t)), e);
        }
      };
    },
    function (t, e, n) {
      var r = n(8),
        o = n(1)("iterator"),
        i = Array.prototype;
      t.exports = function (t) {
        return void 0 !== t && (r.Array === t || i[o] === t);
      };
    },
    function (t, e, n) {
      var r = n(35),
        o = n(1)("iterator"),
        i = n(8);
      t.exports = n(2).getIteratorMethod = function (t) {
        if (void 0 != t) return t[o] || t["@@iterator"] || i[r(t)];
      };
    },
    function (t, e) {
      t.exports = function (t, e, n) {
        var r = void 0 === n;
        switch (e.length) {
          case 0:
            return r ? t() : t.call(n);
          case 1:
            return r ? t(e[0]) : t.call(n, e[0]);
          case 2:
            return r ? t(e[0], e[1]) : t.call(n, e[0], e[1]);
          case 3:
            return r ? t(e[0], e[1], e[2]) : t.call(n, e[0], e[1], e[2]);
          case 4:
            return r
              ? t(e[0], e[1], e[2], e[3])
              : t.call(n, e[0], e[1], e[2], e[3]);
        }
        return t.apply(n, e);
      };
    },
    function (t, e, n) {
      var r = n(0),
        o = n(37).set,
        i = r.MutationObserver || r.WebKitMutationObserver,
        u = r.process,
        s = r.Promise,
        c = "process" == n(10)(u);
      t.exports = function () {
        var t,
          e,
          n,
          a = function () {
            var r, o;
            for (c && (r = u.domain) && r.exit(); t;) {
              (o = t.fn), (t = t.next);
              try {
                o();
              } catch (r) {
                throw (t ? n() : (e = void 0), r);
              }
            }
            (e = void 0), r && r.enter();
          };
        if (c)
          n = function () {
            u.nextTick(a);
          };
        else if (!i || (r.navigator && r.navigator.standalone))
          if (s && s.resolve) {
            var f = s.resolve(void 0);
            n = function () {
              f.then(a);
            };
          } else
            n = function () {
              o.call(r, a);
            };
        else {
          var l = !0,
            d = document.createTextNode("");
          new i(a).observe(d, { characterData: !0 }),
            (n = function () {
              d.data = l = !l;
            });
        }
        return function (r) {
          var o = { fn: r, next: void 0 };
          e && (e.next = o), t || ((t = o), n()), (e = o);
        };
      };
    },
    function (t, e, n) {
      var r = n(0).navigator;
      t.exports = (r && r.userAgent) || "";
    },
    function (t, e, n) {
      var r = n(5);
      t.exports = function (t, e, n) {
        for (var o in e) n && t[o] ? (t[o] = e[o]) : r(t, o, e[o]);
        return t;
      };
    },
    function (t, e, n) {
      "use strict";
      var r = n(0),
        o = n(2),
        i = n(13),
        u = n(6),
        s = n(1)("species");
      t.exports = function (t) {
        var e = "function" == typeof o[t] ? o[t] : r[t];
        u &&
          e &&
          !e[s] &&
          i.f(e, s, {
            configurable: !0,
            get: function () {
              return this;
            },
          });
      };
    },
    function (t, e, n) {
      var r = n(1)("iterator"),
        o = !1;
      try {
        var i = [7][r]();
        (i.return = function () {
          o = !0;
        }),
          Array.from(i, function () {
            throw 2;
          });
      } catch (t) { }
      t.exports = function (t, e) {
        if (!e && !o) return !1;
        var n = !1;
        try {
          var i = [7],
            u = i[r]();
          (u.next = function () {
            return { done: (n = !0) };
          }),
            (i[r] = function () {
              return u;
            }),
            t(i);
        } catch (t) { }
        return n;
      };
    },
    function (t, e, n) {
      "use strict";
      var r = n(4),
        o = n(2),
        i = n(0),
        u = n(36),
        s = n(39);
      r(r.P + r.R, "Promise", {
        finally: function (t) {
          var e = u(this, o.Promise || i.Promise),
            n = "function" == typeof t;
          return this.then(
            n
              ? function (n) {
                return s(e, t()).then(function () {
                  return n;
                });
              }
              : t,
            n
              ? function (n) {
                return s(e, t()).then(function () {
                  throw n;
                });
              }
              : t
          );
        },
      });
    },
    function (t, e, n) {
      "use strict";
      var r = n(4),
        o = n(24),
        i = n(38);
      r(r.S, "Promise", {
        try: function (t) {
          var e = o.f(this),
            n = i(t);
          return (n.e ? e.reject : e.resolve)(n.v), e.promise;
        },
      });
    },
    function (t, e, n) {
      "use strict";
      e.__esModule = !0;
      var r,
        o = n(80),
        i = (r = o) && r.__esModule ? r : { default: r };
      e.default =
        i.default ||
        function (t) {
          for (var e = 1; e < arguments.length; e++) {
            var n = arguments[e];
            for (var r in n)
              Object.prototype.hasOwnProperty.call(n, r) && (t[r] = n[r]);
          }
          return t;
        };
    },
    function (t, e, n) {
      t.exports = { default: n(81), __esModule: !0 };
    },
    function (t, e, n) {
      n(82), (t.exports = n(2).Object.assign);
    },
    function (t, e, n) {
      var r = n(4);
      r(r.S + r.F, "Object", { assign: n(83) });
    },
    function (t, e, n) {
      "use strict";
      var r = n(6),
        o = n(17),
        i = n(84),
        u = n(85),
        s = n(15),
        c = n(26),
        a = Object.assign;
      t.exports =
        !a ||
          n(14)(function () {
            var t = {},
              e = {},
              n = Symbol(),
              r = "abcdefghijklmnopqrst";
            return (
              (t[n] = 7),
              r.split("").forEach(function (t) {
                e[t] = t;
              }),
              7 != a({}, t)[n] || Object.keys(a({}, e)).join("") != r
            );
          })
          ? function (t, e) {
            for (
              var n = s(t), a = arguments.length, f = 1, l = i.f, d = u.f;
              a > f;

            )
              for (
                var p,
                h = c(arguments[f++]),
                v = l ? o(h).concat(l(h)) : o(h),
                y = v.length,
                m = 0;
                y > m;

              )
                (p = v[m++]), (r && !d.call(h, p)) || (n[p] = h[p]);
            return n;
          }
          : a;
    },
    function (t, e) {
      e.f = Object.getOwnPropertySymbols;
    },
    function (t, e) {
      e.f = {}.propertyIsEnumerable;
    },
    function (t, e, n) {
      "use strict";
      Object.defineProperty(e, "__esModule", { value: !0 }),
        (e.default = function () {
          (this.listeners = {}),
            (this.on = function (t, e) {
              void 0 === this.listeners[t] &&
                (this.listeners[t] = { triggered: !1, requested: !1, cbs: [] }),
                this.listeners[t].triggered && e(),
                this.listeners[t].cbs.push(e);
            }),
            (this.emit = function (t) {
              this.listeners[t] &&
                ((this.listeners[t].triggered = !0),
                  this.listeners[t].cbs.forEach(function (t) {
                    return t();
                  }));
            });
        });
    },
    function (t, e, n) {
      "use strict";
      Object.defineProperty(e, "__esModule", { value: !0 }),
        (e.default = function (t, e) {
          var n = null;
          return function () {
            var r = this,
              o = arguments;
            n && clearTimeout(n),
              (n = setTimeout(function () {
                t.apply(r, o);
              }, e));
          };
        });
    },
    function (t, e, n) {
      "use strict";
      Object.defineProperty(e, "__esModule", { value: !0 });
      var r,
        o = n(32),
        i = (r = o) && r.__esModule ? r : { default: r };
      e.default = function (t) {
        return t.reduce(function (t, e) {
          return t.then(e);
        }, i.default.resolve());
      };
    },
    function (t, e, n) {
      "use strict";
      Object.defineProperty(e, "__esModule", { value: !0 }),
        (e.default = function (t) {
          for (var e = "abcdefghijklmnopqrstuvwxyz", n = "", r = 0; r < t; r++)
            n += e.charAt(Math.floor(Math.random() * e.length));
          return n;
        });
    },
    function (t, e, n) {
      "use strict";
      var r = function () {
        var t = this.$createElement,
          e = this._self._c || t;
        return e("div", [
          e("div", { ref: "container", attrs: { name: this.name } }),
        ]);
      };
      r._withStripped = !0;
      var o = { render: r, staticRenderFns: [] };
      e.a = o;
    },
  ]).default;
});

