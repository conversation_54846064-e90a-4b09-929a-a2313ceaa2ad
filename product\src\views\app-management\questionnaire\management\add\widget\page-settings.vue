<template>
  <el-form
    :model="form"
    class="qd-form vote-add-page-form"
    ref="form"
    label-position="right"
    label-width="150px"
    inline
    :rules="rules"
  >
    <div class="base-form-item">
      <div class="base-form-item-label">页面样式</div>
      <div class="base-form-item-content">
        <el-form-item label="大张图" prop="files" class="form-item-wd100">
          <qd-upload-img
            v-model="form.files"
            module="bigImageforQS"
            class="big-img"
            :size="[750, 360]"
            tip="请上传750*360px的图片"
          ></qd-upload-img>
        </el-form-item>
        <el-form-item label="背景图" class="form-item-wd100">
          <qd-upload-img v-model="form.files1" module="bigImageforQSBG">
          </qd-upload-img>
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script>
export default {
  data () {
    return {
      form: {
        files: [],
        files1: []
      },
      rules: {
        files: [{ required: true, message: '请上传图片', trigger: 'file' }]
      }
    }
  },
  created () {
  },
  methods: {
    // 获取字典
    validForm () {
      let result = false
      this.$refs.form.validate((valid) => { result = valid })
      return result
    },
    reset () {
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style lang="scss">
.vote-add-page-form {
  margin: 25px auto;
}
.big-img {
  .avatar-uploader-icon {
    width: 308px !important;
  }
}
</style>
