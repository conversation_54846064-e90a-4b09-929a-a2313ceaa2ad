<template>
  <div class="interactive-file-new">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="文件归类"
                    prop="fileClass"
                    class="form-input">
        <el-select v-model="form.fileClass"
                   filterable
                   placeholder="请选择文件归类">
          <el-option v-for="item in fileClass"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否公开"
                    prop="isPublic"
                    class="form-input">
        <el-radio-group v-model="form.isPublic">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="上传附件"
                    class="form-upload">
        <el-upload class="form-upload-demo"
                   drag
                   action="/"
                   :before-remove="beforeRemove"
                   :before-upload="handleFile"
                   :http-request="fileUpload"
                   :file-list="file"
                   multiple>
          <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>
        </el-upload>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'interactiveFileNew',
  data () {
    return {
      form: {
        fileClass: '',
        isPublic: '0'
      },
      rules: {
        fileClass: [
          { required: true, message: '请选择文件归类', trigger: 'blur' }
        ],
        isPublic: [
          { required: true, message: '请选择是否公开', trigger: 'blur' }
        ]
      },
      fileClass: [],
      file: []
    }
  },
  mounted () {
    this.dictionaryPubkvs()
  },
  methods: {
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'file_info_class'
      })
      var { data } = res
      this.fileClass = data.file_info_class
    },
    /**
   * 限制上传附件的文件类型
  */
    handleFile (file, fileList) {
    },
    /**
   * 上传附件请求方法
  */
    fileUpload (files) {
      this.file = []
      this.file.push(files.file)
    },
    /**
   * 删除附件
  */
    beforeRemove (file, fileList) {
      var fileData = this.file
      this.file = fileData.filter(item => item.uid !== file.uid)
    },
    /**
   * 提交提案
  */
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const param = new FormData()
          param.append('fileClass', this.form.fileClass)
          param.append('isPublic', this.form.isPublic)
          param.append('file', this.file[0])
          this.$api.appManagement.fileinfoAdd(param).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    /**
   * 取消按钮
  */
    cancel () {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.interactive-file-new {
  width: 680px;
  height: 100%;
  padding: 24px;
}
</style>
