<section id="web-application">
  <h2 class="page-header">Web Application Icons</h2>

  <div class="row the-icons">
    {% assign icons_web_application = icons | expand_aliases | category:"Web Application Icons" | sort_by:'class' %}

    {% for icon in icons_web_application %}
      <div class="span3"><a href="{{ page.relative_path }}icon/{{ icon.id }}"><i class="icon-{{ icon.class }}"></i> icon-{{ icon.class }}{% if icon.alias_of %} <span class="muted">(alias)</span>{% endif %}</a></div>
    {% endfor %}
  </div>

</section>
