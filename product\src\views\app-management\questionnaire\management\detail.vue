<template>
  <div class="question-detail">
    <div class="title">{{ mainData.title }}</div>
    <div class="tips"
         v-html="mainData.description" />
    <div class="line"></div>
    <div class="for-box"
         v-for="(item, index) in mainData.questionListVo"
         :key="item.id">
      <div class="qs-title">
        <div class="num-box">
          <div class="qs-must"
               v-if="item.isMust">*</div>
          <div>{{ index + 1 }}</div>
        </div>
        <div class="qs-title-box">{{ item.question }}</div>
      </div>
      <div class="qs-item">
        <div v-if="item.answerType === 1">
          <el-radio-group v-model="item.answer">
            <el-radio v-for="items in item.answersListVo"
                      :label="items.choiceText"
                      :key="items.id"></el-radio>
          </el-radio-group>
        </div>
        <div v-if="item.answerType === 2">
          <el-checkbox-group v-model="input">
            <el-checkbox v-for="items in item.answersListVo"
                         :key="items.id"
                         :label="items.choiceText"></el-checkbox>
          </el-checkbox-group>
        </div>
        <div>
          <el-input v-if="item.answerType === 3"
                    style="width: 60%"
                    v-model="item.answer"
                    type="textarea"
                    :rows="2"></el-input>
        </div>
      </div>
    </div>
    <div class="bottom-btn">
      <el-button type="primary"
                 disabled>提 交</el-button>
    </div>
    <div class="zy-info">正宇软件 提供技术支持</div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      input: '',
      mainData: {},
      id: this.$route.query.id
    }
  },
  created () {
    this.getData()
  },
  methods: {
    getData () {
      this.$api.appManagement.questionnaire.info(this.id).then(res => {
        this.mainData = res.data
      })
    }
  }
}
</script>

<style lang="scss">
.question-detail {
  width: 80%;
  margin: 58px auto;
  background-color: #fff;
  box-sizing: border-box;
  padding: 80px;
  .title {
    font-size: 30px;
    font-family: PingFang SC;
    font-weight: 800;
    color: #007bff;
    margin-bottom: 50px;
    text-align: center;
  }
  .tips {
    margin-bottom: 24px;
  }
  .line {
    border: 2px dashed #cccccc;
    margin-bottom: 70px;
  }
  .for-box {
    margin-bottom: 60px;
    .qs-title {
      display: flex;
      align-items: center;
      margin-bottom: 40px;
      .num-box {
        display: flex;
        align-items: center;
        margin-right: 30px;
        width: 30px;
        .qs-must {
          width: 15px;
          color: red;
        }
      }
    }
    .qs-item {
      margin-left: 60px;
    }
  }
  .bottom-btn {
    display: flex;
    justify-content: center;
    margin-top: 40px;
    margin-bottom: 136px;
    .el-button {
      width: 144px;
      height: 50px;
      font-size: 18px;
    }
  }
  .zy-info {
    font-size: $textSize16;
    font-family: PingFang SC;
    font-weight: 500;
    color: #999999;
    text-align: center;
  }
  .el-radio,
  .el-checkbox {
    display: block;
    margin-bottom: 20px;
  }
}
</style>
