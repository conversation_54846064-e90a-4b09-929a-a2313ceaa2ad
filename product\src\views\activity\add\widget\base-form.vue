<template>
  <el-form :model="form"
           class="qd-form active-add-form"
           ref="form"
           label-position="right"
           label-width="150px"
           inline
           :rules="rules">
    <div class="base-form-item">
      <div class="base-form-item-label">基本信息</div>
      <div class="base-form-item-content">
        <el-form-item label="活动主题"
                      prop="meetName"
                      class="form-item-wd100">
          <el-input v-model="form.meetName"
                    style="width: 90%"></el-input>
        </el-form-item>
        <el-form-item label="活动类型"
                      prop="meetType"
                      class="form-item-wd100">
          <zy-cascader width="222"
                       node-key="id"
                       clearable
                       v-model="form.meetType"
                       :data="typeList"
                       placeholder="请选择类型">
          </zy-cascader>
        </el-form-item>
        <el-form-item label="组织部门"
                      prop="organizer"
                      class="form-item-wd100">
          <el-cascader clearable
                       placeholder="请选择部门"
                       v-model="form.organizer"
                       :options="orgList"
                       filterable
                       :props="{
              checkStrictly: true,
              emitPath: false,
              expandTrigger: true,
              label: 'name',
              value: 'id'
            }"
                       :show-all-levels="false"
                       ref="cascader"></el-cascader>
        </el-form-item>
        <el-form-item label="活动地址"
                      prop="address"
                      class="form-item-wd100">
          <el-input v-model="form.address"
                    maxlength="200"
                    style="width: 90%"
                    show-word-limit></el-input>
        </el-form-item>
        <el-form-item v-if="type !== 'make-up'"
                      label="活动报名截止时间"
                      prop="signEndTime"
                      class="form-item-wd100">
          <el-date-picker v-model="form.signEndTime"
                          placeholder="请选择时间"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item v-if="type !== 'make-up'"
                      label="签到时间"
                      prop="time1"
                      class="form-item-wd100">
          <el-date-picker v-model="form.time1"
                          type="datetimerange"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          range-separator="至"
                          start-placeholder="开始时间"
                          end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="活动时间"
                      prop="time2"
                      class="form-item-wd100">
          <el-date-picker v-model="form.time2"
                          type="datetimerange"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          range-separator="至"
                          start-placeholder="开始时间"
                          end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
      </div>
    </div>
    <div class="base-form-item border-top">
      <div class="base-form-item-label">开关设置</div>
      <div class="base-form-item-content">
        <el-form-item label="是否APP显示"
                      class="form-item-wd100">
          <el-radio-group v-model="form.isAppShow">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="短信通知邀请人"
                      class="form-item-wd100">
          <el-radio-group v-model="form.isNotice">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="是否发布通知" class="form-item-wd100">
          <el-radio-group v-model="form.isRelease">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </div>
    </div>
    <wang-editor v-model="form.content"></wang-editor>
  </el-form>
</template>

<script>
export default {
  props: {
    type: String
  },
  data () {
    return {
      form: {
        meetName: '',
        meetType: '',
        organizer: '',
        address: '',
        signEndTime: '',
        time1: [],
        time2: [],
        isAppShow: 0,
        isNotice: 0,
        isRelease: 0,
        content: ''
      },
      typeList: [],
      orgList: [],
      rules: {
        meetName: [{ required: true, message: '请输入活动主题', trigger: 'blur' }],
        meetType: [{ required: true, message: '请选择活动类型', trigger: 'change' }],
        organizer: [{ required: true, message: '请选择组织部门', trigger: 'change' }],
        address: [{ required: true, message: '请输入活动地址', trigger: 'blur' }],
        signEndTime: [{ required: true, message: '请选择签到截止时间', trigger: 'change' }],
        time1: [{ required: true, message: '请选择签到时间', trigger: 'change' }],
        time2: [{ required: true, message: '请选择活动时间', trigger: 'change' }]
      }
    }
  },
  created () {
    this.getTypeList()
  },
  methods: {
    // 获取所属分类
    getTypeList () {
      this.$api.microAdvice.microAdviceManageTree({ treeType: 1 }).then(res => {
        this.orgList = res.data
        this.filterList(this.orgList)
      })
      this.$api.microAdvice.microAdviceManageTree({ treeType: 3 }).then(res => {
        const filteredData = res.data.filter(function (item) {
          return item.label !== '三双活动' && item.label !== '自然保护区问题整治'
        })
        console.log('filteredData===>', filteredData)
        this.typeList = filteredData
      })
    },
    filterList (arr) {
      arr.forEach(item => {
        if (!item.children.length) {
          item.children = null
        } else {
          this.filterList(item.children)
        }
      })
    },
    validForm () {
      let result = false
      this.$refs.form.validate((valid) => { result = valid })
      return result
    },
    reset () {
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style lang="scss">
.active-add-form {
  margin: 25px auto;
  .el-cascader-node.is-selectable.in-active-path {
    color: #409eff;
  }
}
</style>
