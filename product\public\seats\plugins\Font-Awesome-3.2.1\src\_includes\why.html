<div id="why">
  <div class="row">
    <div class="span4">
      <h4><i class="icon-flag"></i> One Font, {{ icons | size }} Icons</h4>
      In a single collection, Font Awesome is a pictographic language of web-related actions.
    </div>
    <div class="span4">
      <h4><i class="icon-pencil"></i> CSS Control</h4>
      Easily style icon color, size, shadow, and anything that's possible with CSS.
    </div>
    <div class="span4">
      <h4><i class="icon-fullscreen"></i> Infinite Scalability</h4>
      Scalable vector graphics means every icon looks awesome at any size.
    </div>
    <div class="span4">
      <h4><i class="icon-microphone"></i> Free, as in Speech</h4>
      Font Awesome is completely free for commercial use. Check out the <a href="{{ page.relative_path }}license/">license</a>.
    </div>
    <div class="span4">
      <h4><i class="icon-ok"></i> IE7 Support</h4>
      Font Awesome supports IE7. If you need it, you have my condolences.
    </div>
    <div class="span4">
      <h4><i class="icon-eye-open"></i> Perfect on Retina Displays</h4>
      Font Awesome icons are vectors, which mean they're gorgeous on high-resolution displays.
    </div>
    <div class="span4">
      <h4><i class="icon-thumbs-up-alt"></i> Made for Bootstrap</h4>
      Designed from scratch to be fully compatible with <a href="{{ site.bootstrap.url }}">Bootstrap {{ site.bootstrap.version }}</a>.
    </div>
    <div class="span4">
      <h4><i class="icon-desktop"></i> Desktop Friendly</h4>
      To use on the desktop or for a complete set of vectors,
      check out the <a href="{{ page.relative_path }}cheatsheet/">cheatsheet</a>.
    </div>
    <div class="span4">
      <h4><i class="icon-search"></i> Screen Reader Compatible</h4>
      Font Awesome won't trip up screen readers, unlike other icon fonts.
    </div>
  </div>
</div>
