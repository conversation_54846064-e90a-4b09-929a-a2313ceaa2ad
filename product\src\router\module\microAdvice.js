const microAdvice = [{
  path: '/myMicroAdvice',
  name: 'myMicroAdvice',
  component: () => import(/** 我的微建议 */ '@microAdvice/myMicroAdvice/myMicroAdvice')
},
{
  path: '/microAdviceList',
  name: 'microAdviceList',
  component: () => import(/** 统计微建议列表 */ '@microAdvice/microAdvice-count/microAdviceList')
},
{
  path: '/microAdvice-reply',
  name: 'microAdvice-reply',
  component: () => import(/** 我的微建议 */ '@microAdvice/myMicroAdvice/adminReply')
},
{
  path: '/myLeadMicroAdvice',
  name: 'myLeadMicroAdvice',
  component: () => import(/** 我的领办微建议 */ '@microAdvice/myMicroAdvice/myLeadMicroAdvice')
},
{
  path: '/microAdvice-draftbox',
  name: 'microAdvice-draftbox',
  component: () => import(/** 草稿箱 */ '@microAdvice/microAdvice-draftbox/microAdvice-draftbox')
},
{
  path: '/microAdvice-management',
  name: 'microAdvice-management',
  component: () => import(/** 微建议管理 */ '@microAdvice/microAdvice-management/microAdvice-management')
},
{
  path: '/microAdvice-verify',
  name: 'microAdvice-verify',
  component: () => import(/** 微建议审核 */ '@microAdvice/microAdvice-verify/microAdvice-verify')
},
{
  path: '/microAdvice-conduct',
  name: 'microAdvice-conduct',
  component: () => import(/** 微建议办理 */ '@microAdvice/microAdvice-conduct/microAdvice-conduct')
},
{
  path: '/microAdvice-count',
  name: 'microAdvice-count',
  component: () => import(/** 微建议统计 */ '@microAdvice/microAdvice-count/microAdvice-count')
},
{
  path: '/Assigned',
  name: 'Assigned',
  component: () => import(/** 交办、办理单位 */ '@microAdvice/Assigned/Assigned')
}

]

export default microAdvice
