const menuManagement = () => import('@systemSettings/menu-management/menu-management')
const permissionsManagement = () => import('@systemSettings/permissions-management/permissions-management')
const roleManagement = () => import('@systemSettings/role-management/role-management')
const userManagement = () => import('@systemSettings/user-management/user-management')
const Tourist = () => import('@systemSettings/Tourist/Tourist')
const dictionaryManagement = () => import('@systemSettings/dictionary-management/dictionary-management')
const treeManagement = () => import('@systemSettings/tree-management/tree-management')
const logManagement = () => import('@systemSettings/log-management/log-management')
const loginLogManagement = () => import('@systemSettings/log-management/login-log-management')
const errorLogManagement = () => import('@systemSettings/log-management/error-log-management')
const taskManagement = () => import('@systemSettings/task-management/task-management')
const systemParameters = () => import('@systemSettings/system-parameters/system-parameters')
const wordLibrary = () => import('@systemSettings/word-library/')
const sensitiveWordDetection = () => import('@systemSettings/sensitiveWordDetection/')
const candidatesManagement = () => import('@systemSettings/candidates-management/candidates-management')
const labelManagement = () => import('@systemSettings/label-management/label-management')
const userList = () => import('@systemSettings/user-list/user-list')
const userRelationship = () => import('@systemSettings/user-relationship/user-relationship')
const historySmsList = () => import('@systemSettings/sms-management/history-sms-list/history-sms-list')
const historySms = () => import('@systemSettings/sms-management/history-sms/history-sms')
const historySmsc = () => import('@systemSettings/sms-management/history-sms/history-sms-c')
const smstemplate = () => import('@systemSettings/sms-management/sms-template/sms-template')
const upsidesms = () => import('@systemSettings/sms-management/upside-sms/upside-sms')
const TheKeyWork = () => import('@/views/general/TheKeyWork.vue')
const postman = () => import('@systemSettings/postman/postman')
const ShufflingFigure = () => import('@systemSettings/ShufflingFigure/ShufflingFigure')
const undertakeUnitUser = () => import('@systemSettings/user-management/undertakeUnitUser')
const templatePage = () => import('@systemSettings/templatePage/templatePage')
const templatePageNew = () => import('@systemSettings/templatePage/templatePageNew')
const templatePageDetails = () => import('@systemSettings/templatePage/templatePageDetails')
const apkManage = () => import('@systemSettings/apkManage/apkManage')
const userFeedback = () => import('@/views/system-settings/userFeedback/userFeedback')

const systemSettings = [
  { // 菜单管理
    path: '/templatePage',
    name: 'templatePage',
    component: templatePage
  },
  { // 菜单管理
    path: '/templatePageNew',
    name: 'templatePageNew',
    component: templatePageNew
  },
  { // 菜单管理
    path: '/templatePageDetails',
    name: 'templatePageDetails',
    component: templatePageDetails
  },
  { // 菜单管理
    path: '/ShufflingFigure',
    name: 'ShufflingFigure',
    component: ShufflingFigure
  },
  { // 菜单管理
    path: '/postman',
    name: 'postman',
    component: postman
  },
  { // 菜单管理
    path: '/TheKeyWork',
    name: 'TheKeyWork',
    component: TheKeyWork
  },
  { // 菜单管理
    path: '/menuManagement',
    name: 'menuManagement',
    component: menuManagement
  },
  { // 权限管理
    path: '/permissionsManagement',
    name: 'permissionsManagement',
    component: permissionsManagement
  },
  { // 字典管理
    path: '/dictionaryManagement',
    name: 'dictionaryManagement',
    component: dictionaryManagement
  },
  { // 角色管理
    path: '/roleManagement',
    name: 'roleManagement',
    component: roleManagement
  },
  { // 用户反馈
    path: '/userFeedback',
    name: 'userFeedback',
    component: userFeedback
  },
  { // 树管理
    path: '/treeManagement',
    name: 'treeManagement',
    component: treeManagement
  },
  { // 办理单位用户
    path: '/undertakeUnitUser',
    name: 'undertakeUnitUser',
    component: undertakeUnitUser
  },
  { // 普通用户管理
    path: '/userManagement',
    name: 'userManagement',
    component: userManagement
  },
  { // 普通用户管理
    path: '/Tourist',
    name: 'Tourist',
    component: Tourist
  },
  { // 日志管理
    path: '/logManagement',
    name: 'logManagement',
    component: logManagement
  },
  { // 日志管理
    path: '/loginLogManagement',
    name: 'loginLogManagement',
    component: loginLogManagement
  },
  { // 日志管理
    path: '/errorLogManagement',
    name: 'errorLogManagement',
    component: errorLogManagement
  },
  { // 任务管理
    path: '/taskManagement',
    name: 'taskManagement',
    component: taskManagement
  },
  { // 配置管理
    path: '/systemParameters',
    name: 'systemParameters',
    component: systemParameters
  },
  { // 配置管理
    path: '/word-library',
    name: 'wordLibrary',
    component: wordLibrary
  },
  { // 配置管理
    path: '/sensitiveWordDetection',
    name: 'sensitiveWordDetection',
    component: sensitiveWordDetection
  },
  { // 选人管理
    path: '/candidatesManagement',
    name: 'candidatesManagement',
    component: candidatesManagement
  },
  { // 标签管理
    path: '/labelManagement',
    name: 'labelManagement',
    component: labelManagement
  },
  { // 用户列表
    path: '/userList',
    name: 'userList',
    component: userList
  },
  { // 用户关系管理
    path: '/userRelationship',
    name: 'userRelationship',
    component: userRelationship
  },
  { // 已发集合
    path: '/historySmsList',
    name: 'historySmsList',
    component: historySmsList
  },
  { // 已发集xian
    path: '/historySmsc',
    name: 'historySmsc',
    component: historySmsc
  },
  { // 已发
    path: '/historySms',
    name: 'historySms',
    component: historySms
  },
  { // 上集
    path: '/upsidesms',
    name: 'upsidesms',
    component: upsidesms
  },
  { // 短信模板
    path: '/smstemplate',
    name: 'smstemplate',
    component: smstemplate
  },
  { // apk管理
    path: '/apkManage',
    name: 'apkManage',
    component: apkManage
  }]
export default systemSettings
