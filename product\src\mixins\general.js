export default {
  data () {
    return {
      systemShow: [],
      helpShow: [],
      menuId: '',
      menuData: JSON.parse(sessionStorage.getItem('menuChild')),
      crumbsId: '',
      crumbsData: [],
      pathParams: {},
      includes: []
    }
  },
  watch: {
    crumbsData (val) {
      var arr = []
      val.forEach(item => {
        arr.push(item.to.substring(1))
      })
      this.includes = arr
    }
  },
  created () {
    this.systemData()
    this.helpData()
    this.menuSelected()
  },
  mounted () {
    if (window.history && window.history.pushState) {
      // 向历史记录中插入了当前页
      history.pushState(null, null, document.URL)
      window.addEventListener('popstate', this.goBack, false)
    }
  },
  destroyed () {
    window.removeEventListener('popstate', this.goBack, false)
  },
  methods: {
    goBack () {
      if (this.crumbsData.length !== 1) {
        this.tabDelJump()
      } else {
        this.returnClick()
      }
      history.pushState(null, null, document.URL)
    },
    menuSelected () {
      if (this.$route.query.obj) {
        this.crumbsData = JSON.parse(this.$route.query.obj)
      } else {
        this.crumbsData = this.menuDataList(this.menuData)
      }
      this.menuId = this.crumbsData[0].id
      // this.$router.push({ path: this.crumbsData[0].to })
    },
    menuDataList (data) {
      let arr = []
      data.forEach((item, index) => {
        if (index === 0) {
          if (item.children.length === 0) {
            arr.push({ id: item.menuId, name: item.name, to: item.to, params: {} })
          } else {
            arr = arr.concat(this.menuDataList(item.children))
          }
        }
      })
      return arr
    },
    systemData () {
      var name = JSON.parse(sessionStorage.getItem('name'))
      if (name === '系统管理') return
      var menus = JSON.parse(sessionStorage.getItem('menus' + this.$logo()))
      menus.forEach((item) => {
        if (item.name === '系统管理') {
          this.systemShow = item.children
        }
      })
    },
    helpData () {
      var name = JSON.parse(sessionStorage.getItem('name'))
      if (name === '帮助中心') return
      var menus = JSON.parse(sessionStorage.getItem('menus' + this.$logo()))
      menus.forEach((item) => {
        if (item.name === '帮助中心') {
          this.helpShow = item.children
        }
      })
    },
    help () {
      sessionStorage.setItem('name', JSON.stringify('帮助中心'))
      sessionStorage.setItem('menuChild', JSON.stringify(this.helpShow))
      this.name = '帮助中心'
      this.menuId = ''
      this.menuData = this.helpShow
      this.helpShow = []
      this.menuSelected()
      this.systemData()
    },
    // 点击的菜单
    menuSelect (data) {
      this.crumbsData = [{ id: data.menuId, name: data.name, to: data.to }]
      this.$router.push({ path: data.to, query: { ...data.params, ...this.pathParams } || {} })
      this.pathParams = {}
    },
    newTab (data) {
      var index = false
      var crumbsData = this.crumbsData
      crumbsData.forEach(item => {
        if (item.id === data.menuId) {
          index = true
          item.to = data.to
          item.name = data.name
          item.params = data.params || {}
        }
      })
      if (index) {
        this.crumbsData = crumbsData
      } else {
        this.crumbsData.push({ id: data.menuId, name: data.name, to: data.to, params: data.params || {} })
      }
      this.$router.push({ path: data.to, query: data.params || {} })
    },
    matchingMenu (mune, data, params = {}) {
      mune.forEach(item => {
        if (item.to == data) { // eslint-disable-line 
          this.pathParams = params
          this.menuId = item.menuId + ''
          // this.crumbsData = [{ id: item.menuId, name: item.name, to: item.to, params: params }]
          // this.$router.push({ path: item.to, query: params })
        } else {
          this.matchingMenu(item.children, data, params)
        }
      })
    },
    crumbsClcik (data, i) {
      var arr = []
      this.crumbsData.forEach((item, index) => {
        if (index <= i) {
          arr.push(item)
        }
      })
      this.crumbsData = arr
    },
    tabDelJump () {
      var arr = []
      var data = {}
      this.crumbsData.forEach((item, index) => {
        if (index <= this.crumbsData.length - 2) {
          arr.push(item)
        }
        if (index === this.crumbsData.length - 2) {
          data = item
        }
      })
      this.crumbsData = arr
      this.$router.push({ path: data.to, query: data.params || {} })
    },
    tabNameDelete () {
      var arr = []
      var data = {}
      this.crumbsData.forEach((item, index) => {
        if (index <= this.crumbsData.length - 2) {
          arr.push(item)
        }
        if (index === this.crumbsData.length - 2) {
          data = item
        }
      })
      this.crumbsData = arr
      this.$router.push({ path: data.to, query: data.params || {} })
    },
    jumpMenu () {
    },
    system () {
      sessionStorage.setItem('name', JSON.stringify('系统管理'))
      sessionStorage.setItem('menuChild', JSON.stringify(this.systemShow))
      this.name = '系统管理'
      this.menuId = ''
      this.menuData = this.systemShow
      this.systemShow = []
      this.menuSelected()
      this.helpData()
    },
    returnClick () {
      localStorage.removeItem('name')
      sessionStorage.removeItem('menuChild')
      this.$router.push({ name: 'home' })
    }
  }
}
