<template>
  <div class="zy-sliding"
       ref="zy-sliding">
    <div class="zy-sliding-box">
      <div class="zy-sliding-item-box">
        <div :class="['zy-sliding-item',item.class?'zy-sliding-item-a':'']"
             v-for="(item, index) in slidingList"
             @click="slidingClick(item)"
             :key="index">
          {{item.name}}
        </div>
        <div class="zy-sliding-item-sliding"></div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'zySliding',
  data () {
    return {
      slidingId: this.value,
      slidingList: []
    }
  },
  props: {
    value: {
      type: String
    },
    sliding: {
      type: Array,
      default: () => []
    },
    props: {
      type: Object,
      default: () => { }
    }
  },
  model: {
    prop: 'value',
    event: 'id'
  },
  mounted () {
    this.slidingData(this.deepCopy(this.sliding))
  },
  watch: {
    value (val) {
      if (val) {
        this.slidingList.forEach(item => {
          if (item.id === this.value) {
            this.slidingClick(item, true)
          }
        })
      }
    },
    sliding (val) {
      this.slidingData(this.deepCopy(this.sliding))
    },
    slidingList (val) {
      if (val.length) {
        this.slidingList.forEach(item => {
          if (item.id === this.value) {
            this.slidingClick(item)
          }
        })
      }
    }
  },
  methods: {
    slidingClick (data, type) {
      if (this.value !== data.id) {
        this.$emit('id', data.id)
        this.returnData(data)
      }
      if (type) {
        this.returnData(data)
      }
      this.slidingList.forEach(item => {
        item.class = false
        if (item.id === data.id) {
          item.class = true
        }
      })
      this.$nextTick(() => {
        this.slidingBox()
        this.slidingIocation()
      })
    },
    returnData (data) {
      var arr = []
      this.sliding.forEach(item => {
        if (this.props) {
          if (data.id === item[this.props.id]) {
            arr = item
          }
        } else {
          if (data.id === item.id) {
            arr = item
          }
        }
      })
      this.$emit('sliding-click', arr)
    },
    slidingIocation () {
      const slidingItem = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-a')
      const sliding = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-sliding')
      sliding.style.width = `${slidingItem.offsetWidth}px`
      sliding.style.transform = `translateX(${slidingItem.offsetLeft}px)`
      sliding.style.transitionDuration = '.3s'
    },
    slidingBox () {
      var sliding = this.$refs['zy-sliding'].querySelector('.zy-sliding-box')
      var itemBox = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-box')
      var item = this.$refs['zy-sliding'].querySelector('.zy-sliding-item-a')
      if (sliding.offsetWidth < itemBox.offsetWidth) {
        itemBox.style.transform = 'translateX(0px)'
        itemBox.style.transitionDuration = '.4s'
        if (itemBox.offsetWidth === item.offsetLeft + item.offsetWidth) {
          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - sliding.offsetWidth}px)`
        } else if (sliding.offsetWidth / 2 > itemBox.offsetWidth - item.offsetLeft) {
          itemBox.style.transform = `translateX(-${itemBox.offsetWidth - sliding.offsetWidth}px)`
        } else {
          itemBox.style.transform = `translateX(-${item.offsetLeft - sliding.offsetWidth / 2}px)`
        }
      }
    },
    slidingData (data) {
      this.initData(data)
      this.slidingList = data
    },
    initData (items) {
      items.forEach((item, index) => {
        if (this.props) {
          if ((typeof item.id) === 'undefined') { // eslint-disable-line
            item.id = item[this.props.id]
          }
          if ((typeof item.name) === 'undefined') { // eslint-disable-line
            item.name = item[this.props.name]
          }
        }
        if ((typeof item.class) === 'undefined') { // eslint-disable-line
          item.class = false
        }
        if (this.value === item.id) {
          item.class = true
          this.$emit('sliding-click', item)
        }
      })
    },
    deepCopy (data) {
      var t = this.type(data)
      var o
      var i
      var ni
      if (t === 'array') {
        o = []
      } else if (t === 'object') {
        o = {}
      } else {
        return data
      }
      if (t === 'array') {
        for (i = 0, ni = data.length; i < ni; i++) {
          o.push(this.deepCopy(data[i]))
        }
        return o
      } else if (t === 'object') {
        for (i in data) {
          o[i] = this.deepCopy(data[i])
        }
        return o
      }
    },
    type (obj) {
      var toString = Object.prototype.toString
      var map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'undefined',
        '[object Null]': 'null',
        '[object Object]': 'object'
      }
      return map[toString.call(obj)]
    },
    makeData (data) {
      const t = this.type(data)
      let o
      if (t === 'array') {
        o = []
      } else if (t === 'object') {
        o = {}
      } else {
        return data
      }
      if (t === 'array') {
        for (let i = 0; i < data.length; i++) {
          o.push(this.makeData(data[i]))
        }
      } else if (t === 'object') {
        for (const i in data) {
          if (i != 'class') {// eslint-disable-line
            o[i] = this.makeData(data[i])
          }
        }
      }
      return o
    }
  }
}
</script>
<style lang="scss">
@import "./zy-sliding.scss";
</style>
