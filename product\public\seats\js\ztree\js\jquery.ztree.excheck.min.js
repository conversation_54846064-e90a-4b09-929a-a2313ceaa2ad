/*
 * J<PERSON><PERSON>y zTree excheck v3.5.29
 * http://treejs.cn/
 *
 * Copyright (c) 2010 Hunter.z
 *
 * Licensed same as jquery - MIT License
 * http://www.opensource.org/licenses/mit-license.php
 *
 * Date: 2017-06-19
 */
(function (m) {
  var p, q, r, o = { event: { CHECK: "ztree_check" }, id: { CHECK: "_check" }, checkbox: { STYLE: "checkbox", DEFAULT: "chk", DISABLED: "disable", FALSE: "false", TRUE: "true", FULL: "full", PART: "part", FOCUS: "focus" }, radio: { STYLE: "radio", TYPE_ALL: "all", TYPE_LEVEL: "level" } }, v = { check: { enable: !1, autoCheckTrigger: !1, chkStyle: o.checkbox.STYLE, nocheckInherit: !1, chkDisabledInherit: !1, radioType: o.radio.TYPE_LEVEL, chkboxType: { Y: "ps", N: "ps" } }, data: { key: { checked: "checked" } }, callback: { beforeCheck: null, onCheck: null } }; p = function (c,
    a) { if (a.chkDisabled === !0) return !1; var b = g.getSetting(c.data.treeId), d = b.data.key.checked; if (k.apply(b.callback.beforeCheck, [b.treeId, a], !0) == !1) return !0; a[d] = !a[d]; e.checkNodeRelation(b, a); d = n(a, j.id.CHECK, b); e.setChkClass(b, d, a); e.repairParentChkClassWithSelf(b, a); b.treeObj.trigger(j.event.CHECK, [c, b.treeId, a]); return !0 }; q = function (c, a) { if (a.chkDisabled === !0) return !1; var b = g.getSetting(c.data.treeId), d = n(a, j.id.CHECK, b); a.check_Focus = !0; e.setChkClass(b, d, a); return !0 }; r = function (c, a) {
      if (a.chkDisabled ===
        !0) return !1; var b = g.getSetting(c.data.treeId), d = n(a, j.id.CHECK, b); a.check_Focus = !1; e.setChkClass(b, d, a); return !0
    }; m.extend(!0, m.fn.zTree.consts, o); m.extend(!0, m.fn.zTree._z, {
      tools: {}, view: {
        checkNodeRelation: function (c, a) {
          var b, d, h, i = c.data.key.children, l = c.data.key.checked; b = j.radio; if (c.check.chkStyle == b.STYLE) {
            var f = g.getRadioCheckedList(c); if (a[l]) if (c.check.radioType == b.TYPE_ALL) {
              for (d = f.length - 1; d >= 0; d--)b = f[d], b[l] && b != a && (b[l] = !1, f.splice(d, 1), e.setChkClass(c, n(b, j.id.CHECK, c), b), b.parentTId !=
                a.parentTId && e.repairParentChkClassWithSelf(c, b)); f.push(a)
            } else { f = a.parentTId ? a.getParentNode() : g.getRoot(c); for (d = 0, h = f[i].length; d < h; d++)b = f[i][d], b[l] && b != a && (b[l] = !1, e.setChkClass(c, n(b, j.id.CHECK, c), b)) } else if (c.check.radioType == b.TYPE_ALL) for (d = 0, h = f.length; d < h; d++)if (a == f[d]) { f.splice(d, 1); break }
          } else a[l] && (!a[i] || a[i].length == 0 || c.check.chkboxType.Y.indexOf("s") > -1) && e.setSonNodeCheckBox(c, a, !0), !a[l] && (!a[i] || a[i].length == 0 || c.check.chkboxType.N.indexOf("s") > -1) && e.setSonNodeCheckBox(c,
            a, !1), a[l] && c.check.chkboxType.Y.indexOf("p") > -1 && e.setParentNodeCheckBox(c, a, !0), !a[l] && c.check.chkboxType.N.indexOf("p") > -1 && e.setParentNodeCheckBox(c, a, !1)
        }, makeChkClass: function (c, a) {
          var b = c.data.key.checked, d = j.checkbox, h = j.radio, i = "", i = a.chkDisabled === !0 ? d.DISABLED : a.halfCheck ? d.PART : c.check.chkStyle == h.STYLE ? a.check_Child_State < 1 ? d.FULL : d.PART : a[b] ? a.check_Child_State === 2 || a.check_Child_State === -1 ? d.FULL : d.PART : a.check_Child_State < 1 ? d.FULL : d.PART, b = c.check.chkStyle + "_" + (a[b] ? d.TRUE : d.FALSE) +
            "_" + i, b = a.check_Focus && a.chkDisabled !== !0 ? b + "_" + d.FOCUS : b; return j.className.BUTTON + " " + d.DEFAULT + " " + b
        }, repairAllChk: function (c, a) { if (c.check.enable && c.check.chkStyle === j.checkbox.STYLE) for (var b = c.data.key.checked, d = c.data.key.children, h = g.getRoot(c), i = 0, l = h[d].length; i < l; i++) { var f = h[d][i]; f.nocheck !== !0 && f.chkDisabled !== !0 && (f[b] = a); e.setSonNodeCheckBox(c, f, a) } }, repairChkClass: function (c, a) { if (a && (g.makeChkFlag(c, a), a.nocheck !== !0)) { var b = n(a, j.id.CHECK, c); e.setChkClass(c, b, a) } }, repairParentChkClass: function (c,
          a) { if (a && a.parentTId) { var b = a.getParentNode(); e.repairChkClass(c, b); e.repairParentChkClass(c, b) } }, repairParentChkClassWithSelf: function (c, a) { if (a) { var b = c.data.key.children; a[b] && a[b].length > 0 ? e.repairParentChkClass(c, a[b][0]) : e.repairParentChkClass(c, a) } }, repairSonChkDisabled: function (c, a, b, d) { if (a) { var h = c.data.key.children; if (a.chkDisabled != b) a.chkDisabled = b; e.repairChkClass(c, a); if (a[h] && d) for (var i = 0, l = a[h].length; i < l; i++)e.repairSonChkDisabled(c, a[h][i], b, d) } }, repairParentChkDisabled: function (c,
            a, b, d) { if (a) { if (a.chkDisabled != b && d) a.chkDisabled = b; e.repairChkClass(c, a); e.repairParentChkDisabled(c, a.getParentNode(), b, d) } }, setChkClass: function (c, a, b) { a && (b.nocheck === !0 ? a.hide() : a.show(), a.attr("class", e.makeChkClass(c, b))) }, setParentNodeCheckBox: function (c, a, b, d) {
              var h = c.data.key.children, i = c.data.key.checked, l = n(a, j.id.CHECK, c); d || (d = a); g.makeChkFlag(c, a); a.nocheck !== !0 && a.chkDisabled !== !0 && (a[i] = b, e.setChkClass(c, l, a), c.check.autoCheckTrigger && a != d && c.treeObj.trigger(j.event.CHECK, [null, c.treeId,
                a])); if (a.parentTId) { l = !0; if (!b) for (var h = a.getParentNode()[h], f = 0, k = h.length; f < k; f++)if (h[f].nocheck !== !0 && h[f].chkDisabled !== !0 && h[f][i] || (h[f].nocheck === !0 || h[f].chkDisabled === !0) && h[f].check_Child_State > 0) { l = !1; break } l && e.setParentNodeCheckBox(c, a.getParentNode(), b, d) }
            }, setSonNodeCheckBox: function (c, a, b, d) {
              if (a) {
                var h = c.data.key.children, i = c.data.key.checked, l = n(a, j.id.CHECK, c); d || (d = a); var f = !1; if (a[h]) for (var k = 0, m = a[h].length; k < m; k++) {
                  var o = a[h][k]; e.setSonNodeCheckBox(c, o, b, d); o.chkDisabled ===
                    !0 && (f = !0)
                } if (a != g.getRoot(c) && a.chkDisabled !== !0) { f && a.nocheck !== !0 && g.makeChkFlag(c, a); if (a.nocheck !== !0 && a.chkDisabled !== !0) { if (a[i] = b, !f) a.check_Child_State = a[h] && a[h].length > 0 ? b ? 2 : 0 : -1 } else a.check_Child_State = -1; e.setChkClass(c, l, a); c.check.autoCheckTrigger && a != d && a.nocheck !== !0 && a.chkDisabled !== !0 && c.treeObj.trigger(j.event.CHECK, [null, c.treeId, a]) }
              }
            }
      }, event: {}, data: {
        getRadioCheckedList: function (c) {
          for (var a = g.getRoot(c).radioCheckedList, b = 0, d = a.length; b < d; b++)g.getNodeCache(c, a[b].tId) || (a.splice(b,
            1), b--, d--); return a
        }, getCheckStatus: function (c, a) { if (!c.check.enable || a.nocheck || a.chkDisabled) return null; var b = c.data.key.checked; return { checked: a[b], half: a.halfCheck ? a.halfCheck : c.check.chkStyle == j.radio.STYLE ? a.check_Child_State === 2 : a[b] ? a.check_Child_State > -1 && a.check_Child_State < 2 : a.check_Child_State > 0 } }, getTreeCheckedNodes: function (c, a, b, d) {
          if (!a) return []; for (var h = c.data.key.children, i = c.data.key.checked, e = b && c.check.chkStyle == j.radio.STYLE && c.check.radioType == j.radio.TYPE_ALL, d = !d ? [] : d,
            f = 0, k = a.length; f < k; f++) { if (a[f].nocheck !== !0 && a[f].chkDisabled !== !0 && a[f][i] == b && (d.push(a[f]), e)) break; g.getTreeCheckedNodes(c, a[f][h], b, d); if (e && d.length > 0) break } return d
        }, getTreeChangeCheckedNodes: function (c, a, b) { if (!a) return []; for (var d = c.data.key.children, h = c.data.key.checked, b = !b ? [] : b, i = 0, e = a.length; i < e; i++)a[i].nocheck !== !0 && a[i].chkDisabled !== !0 && a[i][h] != a[i].checkedOld && b.push(a[i]), g.getTreeChangeCheckedNodes(c, a[i][d], b); return b }, makeChkFlag: function (c, a) {
          if (a) {
            var b = c.data.key.children,
            d = c.data.key.checked, h = -1; if (a[b]) for (var i = 0, e = a[b].length; i < e; i++) {
              var f = a[b][i], g = -1; if (c.check.chkStyle == j.radio.STYLE) if (g = f.nocheck === !0 || f.chkDisabled === !0 ? f.check_Child_State : f.halfCheck === !0 ? 2 : f[d] ? 2 : f.check_Child_State > 0 ? 2 : 0, g == 2) { h = 2; break } else g == 0 && (h = 0); else if (c.check.chkStyle == j.checkbox.STYLE) if (g = f.nocheck === !0 || f.chkDisabled === !0 ? f.check_Child_State : f.halfCheck === !0 ? 1 : f[d] ? f.check_Child_State === -1 || f.check_Child_State === 2 ? 2 : 1 : f.check_Child_State > 0 ? 1 : 0, g === 1) { h = 1; break } else if (g ===
                2 && h > -1 && i > 0 && g !== h) { h = 1; break } else if (h === 2 && g > -1 && g < 2) { h = 1; break } else g > -1 && (h = g)
            } a.check_Child_State = h
          }
        }
      }
    }); var m = m.fn.zTree, k = m._z.tools, j = m.consts, e = m._z.view, g = m._z.data, n = k.$; g.exSetting(v); g.addInitBind(function (c) { c.treeObj.bind(j.event.CHECK, function (a, b, d, h) { a.srcEvent = b; k.apply(c.callback.onCheck, [a, d, h]) }) }); g.addInitUnBind(function (c) { c.treeObj.unbind(j.event.CHECK) }); g.addInitCache(function () { }); g.addInitNode(function (c, a, b, d) {
      if (b) {
        a = c.data.key.checked; typeof b[a] == "string" && (b[a] =
          k.eqs(b[a], "true")); b[a] = !!b[a]; b.checkedOld = b[a]; if (typeof b.nocheck == "string") b.nocheck = k.eqs(b.nocheck, "true"); b.nocheck = !!b.nocheck || c.check.nocheckInherit && d && !!d.nocheck; if (typeof b.chkDisabled == "string") b.chkDisabled = k.eqs(b.chkDisabled, "true"); b.chkDisabled = !!b.chkDisabled || c.check.chkDisabledInherit && d && !!d.chkDisabled; if (typeof b.halfCheck == "string") b.halfCheck = k.eqs(b.halfCheck, "true"); b.halfCheck = !!b.halfCheck; b.check_Child_State = -1; b.check_Focus = !1; b.getCheckStatus = function () {
            return g.getCheckStatus(c,
              b)
          }; c.check.chkStyle == j.radio.STYLE && c.check.radioType == j.radio.TYPE_ALL && b[a] && g.getRoot(c).radioCheckedList.push(b)
      }
    }); g.addInitProxy(function (c) {
      var a = c.target, b = g.getSetting(c.data.treeId), d = "", h = null, e = "", l = null; if (k.eqs(c.type, "mouseover")) { if (b.check.enable && k.eqs(a.tagName, "span") && a.getAttribute("treeNode" + j.id.CHECK) !== null) d = k.getNodeMainDom(a).id, e = "mouseoverCheck" } else if (k.eqs(c.type, "mouseout")) {
        if (b.check.enable && k.eqs(a.tagName, "span") && a.getAttribute("treeNode" + j.id.CHECK) !== null) d =
          k.getNodeMainDom(a).id, e = "mouseoutCheck"
      } else if (k.eqs(c.type, "click") && b.check.enable && k.eqs(a.tagName, "span") && a.getAttribute("treeNode" + j.id.CHECK) !== null) d = k.getNodeMainDom(a).id, e = "checkNode"; if (d.length > 0) switch (h = g.getNodeCache(b, d), e) { case "checkNode": l = p; break; case "mouseoverCheck": l = q; break; case "mouseoutCheck": l = r }return { stop: e === "checkNode", node: h, nodeEventType: e, nodeEventCallback: l, treeEventType: "", treeEventCallback: null }
    }, !0); g.addInitRoot(function (c) { g.getRoot(c).radioCheckedList = [] });
  g.addBeforeA(function (c, a, b) { c.check.enable && (g.makeChkFlag(c, a), b.push("<span ID='", a.tId, j.id.CHECK, "' class='", e.makeChkClass(c, a), "' treeNode", j.id.CHECK, a.nocheck === !0 ? " style='display:none;'" : "", "></span>")) }); g.addZTreeTools(function (c, a) {
    a.checkNode = function (a, b, c, g) {
      var f = this.setting.data.key.checked; if (a.chkDisabled !== !0 && (b !== !0 && b !== !1 && (b = !a[f]), g = !!g, (a[f] !== b || c) && !(g && k.apply(this.setting.callback.beforeCheck, [this.setting.treeId, a], !0) == !1) && k.uCanDo(this.setting) && this.setting.check.enable &&
        a.nocheck !== !0)) a[f] = b, b = n(a, j.id.CHECK, this.setting), (c || this.setting.check.chkStyle === j.radio.STYLE) && e.checkNodeRelation(this.setting, a), e.setChkClass(this.setting, b, a), e.repairParentChkClassWithSelf(this.setting, a), g && this.setting.treeObj.trigger(j.event.CHECK, [null, this.setting.treeId, a])
    }; a.checkAllNodes = function (a) { e.repairAllChk(this.setting, !!a) }; a.getCheckedNodes = function (a) { var b = this.setting.data.key.children; return g.getTreeCheckedNodes(this.setting, g.getRoot(this.setting)[b], a !== !1) };
    a.getChangeCheckedNodes = function () { var a = this.setting.data.key.children; return g.getTreeChangeCheckedNodes(this.setting, g.getRoot(this.setting)[a]) }; a.setChkDisabled = function (a, b, c, g) { b = !!b; c = !!c; e.repairSonChkDisabled(this.setting, a, b, !!g); e.repairParentChkDisabled(this.setting, a.getParentNode(), b, c) }; var b = a.updateNode; a.updateNode = function (c, g) {
      b && b.apply(a, arguments); if (c && this.setting.check.enable && n(c, this.setting).get(0) && k.uCanDo(this.setting)) {
        var i = n(c, j.id.CHECK, this.setting); (g == !0 || this.setting.check.chkStyle ===
          j.radio.STYLE) && e.checkNodeRelation(this.setting, c); e.setChkClass(this.setting, i, c); e.repairParentChkClassWithSelf(this.setting, c)
      }
    }
  }); var s = e.createNodes; e.createNodes = function (c, a, b, d, g) { s && s.apply(e, arguments); b && e.repairParentChkClassWithSelf(c, d) }; var t = e.removeNode; e.removeNode = function (c, a) { var b = a.getParentNode(); t && t.apply(e, arguments); a && b && (e.repairChkClass(c, b), e.repairParentChkClass(c, b)) }; var u = e.appendNodes; e.appendNodes = function (c, a, b, d, h, i, j) {
    var f = ""; u && (f = u.apply(e, arguments));
    d && g.makeChkFlag(c, d); return f
  }
})(jQuery);
