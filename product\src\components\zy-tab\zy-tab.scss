.zy-tab {
  position: relative;
  height: 40px;
  width: 100%;
  background-color: #fff;
  overflow: hidden;
  box-shadow: -1px 0px 0px 0px rgba(230, 230, 230, 1),
    0px 1px 0px 0px rgba(230, 230, 230, 1);
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;

  .zy-tab-left {
    position: absolute;
    top: 0;
    left: 0;
    height: 40px;
    line-height: 40px;
    width: 32px;
    padding-left: 9px;
    text-align: left;
    cursor: pointer;
    background-color: #fff;
    box-shadow: -10px 0px 50px 52px rgba(255, 255, 255, 0.8);
    z-index: 2;
  }

  .zy-tab-right {
    position: absolute;
    top: 0;
    right: 0;
    height: 40px;
    line-height: 40px;
    width: 32px;
    padding-right: 9px;
    text-align: right;
    cursor: pointer;
    background-color: #fff;
    box-shadow: 10px 0px 50px 52px rgba(255, 255, 255, 0.8);
    z-index: 2;
  }

  .zy-tab-box {
    width: 100%;
    height: 40px;
    overflow: hidden;

    .zy-tab-item-list {
      height: 100%;
      white-space: nowrap;
      float: left;

      .zy-tab-item {
        height: 100%;
        display: inline-block;
        box-shadow: 1px 0px 0px 0px rgba(230, 230, 230, 1),
          0px 0px 1px 0px rgba(230, 230, 230, 1);
        cursor: pointer;
        overflow: hidden;
        padding: 0 16px;
        line-height: 40px;
        position: relative;

        &::after {
          position: absolute;
          content: '';
          top: 0;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: transparent;
        }

        .zy-tab-item-label {
          display: inline-block;
        }

        .zy-tab-item-del-box {
          width: 16px;
          height: 100%;
          margin-left: 12px;
          display: inline-block;
          // vertical-align: middle;
          position: relative;

          .zy-tab-item-refresh {
            width: 16px;
            height: 16px;

            .el-icon-refresh {
              font-size: $textSize14;

              &:hover {
                background-color: #daeefb;
                border-radius: 50%;
              }
            }
          }

          .zy-tab-item-del {
            width: 16px;
            height: 16px;

            .el-icon-close {
              font-size: $textSize14;

              &:hover {
                background-color: #daeefb;
                border-radius: 50%;
              }
            }
          }

          // .zy-tab-item-del {
          //   position: absolute;
          //   top: 50%;
          //   left: 50%;
          //   transform: translate(-50%, -50%);
          //   width: 16px;
          //   height: 16px;
          //   display: inline-block;
          //   background: url('../../assets/img/delete.png') center no-repeat;
          //   background-size: 10px 10px;
          //   cursor: pointer;

          //   &:hover {
          //     background-color: #DAEEFB;
          //     border-radius: 50%;
          //   }
          // }
        }
      }

      .zy-tab-item-active {
        &::after {
          background-color: $zy-color;
        }

        .zy-tab-item-label {
          color: $zy-color;
        }
      }
    }
  }
}
