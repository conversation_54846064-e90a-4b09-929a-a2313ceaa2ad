<template>
  <div class="activity-leave-verify">
    <search-box @search-click="search" @reset-click="reset" title="考核情况筛选">
      <zy-widget label="关键字">
        <el-input v-model="searchParams.keyword" placeholder="请输入关键字" clearable @keyup.enter.native="search"></el-input>
      </zy-widget>
    </search-box>
    <div class="qd-list-wrap">
      <ul class="qd-list-tab">
        <li class="tab-item" v-for="item in auditSituationList" :class="{'active':code === item.code }" @click="tabChange(item)" :key="item.code">
          {{item.name}}({{item.num}})
        </li>
      </ul>
      <div class="qd-btn-box" v-if="code !=='1'">
        <el-button type="primary" v-if="code === '2'" plain @click="handleBatchSignCancel('signUp')">取消报名</el-button>
        <el-button type="primary" v-if="code === '3'" plain @click="handleBatchSignIn">报名</el-button>
        <el-button type="primary" v-if="code === '3'" plain @click="handleBatchSignNotice(1)">报名通知</el-button>
        <el-button type="primary" v-if="code === '4'" plain @click="handleBatchSignCancel('signIn')">取消签到</el-button>
        <el-button type="primary" v-if="code === '5'" plain @click="handleBatchSignUp">签到</el-button>
        <el-button type="primary" v-if="code === '5'" plain @click="handleBatchSignNotice(2)">签到通知</el-button>
        <el-button type="primary" v-if="code === '7'" plain @click="handleBatchVerify(1)">审核通过</el-button>
        <el-button type="primary" v-if="code === '8'" plain @click="handleBatchVerify(1)">审核通过</el-button>
        <el-button type="primary" v-if="code === '8'" plain @click="handleBatchVerify(2)">审核未通过</el-button>
        <el-button type="success" plain @click="handleExcel">导出excel</el-button>
      </div>
      <div class="qd-table-box" :style="{height:'calc(100% - '+ (code ==='1' ? 118:186) + 'px)'}">
        <zy-table>
          <el-table :data="list" stripe border ref="table" slot="zytable" @selection-change="handleSelectionChange">
            <el-table-column type="selection" fixed="left" width="60"></el-table-column>
            <el-table-column label="姓名" prop="userName" min-width="120"></el-table-column>
            <el-table-column label="联系号码" prop="phone" min-width="120"></el-table-column>
            <el-table-column label="界别" prop="dele" min-width="120" show-overflow-tooltip></el-table-column>
            <el-table-column label="单位及职务" prop="position" min-width="240"></el-table-column>
            <el-table-column v-if="code === '2'" label="报名时间" prop="date" min-width="240"></el-table-column>
            <el-table-column v-if="code === '2'" label="操作人" prop="createName" min-width="240"></el-table-column>
            <el-table-column v-if="code === '3'" label="操作" min-width="240">
              <template slot-scope="scope">
                <el-button type="text" @click="handleSignIn(scope.row.userId)">报名</el-button>
                <el-button type="text" @click="handleLeave(scope.row)">请假</el-button>
              </template>
            </el-table-column>
            <el-table-column v-if="code === '4'" label="签到方式" prop="singInWay" min-width="240"></el-table-column>
            <el-table-column v-if="code === '4'" label="签到时间" prop="date" min-width="240"></el-table-column>
            <el-table-column v-if="code === '4'" label="操作人" prop="createName" min-width="240"></el-table-column>
          </el-table>
        </zy-table>
      </div>
      <div class="qd-page-box">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page.sync="page" :page-sizes="[10, 20, 50, 80, 100, 200, 500]" :page-size.sync="pageSize" background layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
      </div>
      <zy-pop-up v-model="isSign" title="请假">
        <leave :info="info" :aid="id" :status="1" @callback="handleCallback"></leave>
      </zy-pop-up>
    </div>
  </div>
</template>

<script>
import table from '@mixins/table.js'
import { filterParams, checkParams } from '@/common/handleParams'
import leave from './widget/leave'
export default {
  components: { leave },
  mixins: [table],
  data() {
    return {
      searchParams: {
        keyword: ''
      },
      code: 1,
      auditSituationList: [],
      id: null,
      isSign: false,
      info: null
    }
  },
  created() {
    this.id = this.$route.query.id
    this.getSituation()
  },
  methods: {
    getList() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        activityId: this.id,
        code: this.code
      }
      params = Object.assign(params, filterParams(this.searchParams))
      this.$api.activity.attendanceList(params).then(res => {
        var { data, total } = res
        this.list = data
        this.total = total
      })
    },
    search() {
      if (!checkParams(this.searchParams)) {
        return this.$message.warning('请输入关键字或选择想要搜索的')
      }
      this.getList()
    },
    reset() {
      this.searchParams = {
        keyword: '',
        sector: ''
      }
      this.getList()
    },
    async getSituation() {
      this.auditSituationList = (await this.$api.activity.attendanceType(this.id)).data
      this.getList()
    },
    tabChange(val) {
      this.code = val.code
      this.selectionList = []
      this.reset()
    },
    // 批量取消报名或签到
    handleBatchSignCancel(type) {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要取消的项')
      }
      this.$confirm(`此操作将取消选中的项${this.code === '2' ? '报名' : '签到'}, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activity.cancelSign({ ids: this.selectionList.map((v) => v.id).join(','), type }).then((res) => {
          if (res.errcode === 200) {
            this.getSituation()
            this.$message.success('取消成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消删除')
        return false
      })
    },
    // 批量报名
    handleBatchSignIn() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要报名的项')
      }
      this.handleSignIn(this.selectionList.map((v) => v.userId).join(','))
    },
    // 报名处理
    handleSignIn(ids) {
      this.$confirm('此操作将报名选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activity.signUp({ activityId: this.id, userIds: ids }).then((res) => {
          this.$message.success(res.errmsg)
          if (res.errcode === 200) {
            this.getSituation()
          }
        })
      }).catch(() => {
        this.$message.info('取消报名')
        return false
      })
    },
    // 报名通知
    handleBatchSignNotice(type) {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要通知的项')
      }
      this.$confirm('此操作将发送短信选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activity.attendanceSendMsg({
          type,
          activityId: this.id,
          userIds: this.selectionList.map((v) => v.userId).join(',')
        }).then((res) => {
          if (res.errcode === 200) {
            this.getSituation()
            this.$message.success('发送报名通知成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消报名通知')
        return false
      })
    },
    // 批量签到
    handleBatchSignUp() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要签到的项')
      }
      this.$confirm('此操作将签到选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activity.signIn({ userIds: this.selectionList.map((v) => v.userId).join(','), activityId: this.id }).then((res) => {
          if (res.errcode === 200) {
            this.getSituation()
            this.$message.success('签到成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消签到')
        return false
      })
    },
    // 请假审核
    handleBatchVerify(type) {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要审核的项')
      }
      this.$confirm('此操作将修改选中的项的审核状态, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activity.leave.verify({ ids: this.selectionList.map((v) => v.id).join(','), status: type }).then((res) => {
          if (res.errcode === 200) {
            this.getSituation()
            this.$message.success('审核成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消审核')
        return false
      })
    },
    // TODO 导出excel
    handleExcel() {
      this.$message.warning('暂未开发')
    },
    // 请假
    handleLeave(val) {
      this.info = val
      this.isSign = true
    },
    handleCallback() {
      this.isSign = false
      this.getSituation()
    }
  }
}
</script>
<style lang="scss" scoped>
.activity-leave-verify {
  width: 100%;
  height: 100%;
}
</style>
