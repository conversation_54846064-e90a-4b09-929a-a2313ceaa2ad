<template>
  <div class="announcementActivity">
    <div class="button-box">
      <el-button @click="newData"
                 type="primary">关联活动</el-button>
      <el-button @click="deleteClick"
                 type="primary">删除</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="标题"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="details(scope.row)">{{scope.row.title}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="活动类型"
                           width="120"
                           prop="schemeType"></el-table-column>
          <el-table-column label="组织部门"
                           width="160"
                           prop="officeName"></el-table-column>
          <el-table-column label="活动状态"
                           width="120"
                           prop="schemeStatus"></el-table-column>
          <el-table-column label="活动时间"
                           width="360">
            <template slot-scope="scope">{{scope.row.startTime|datefmt('YYYY-MM-DD HH:mm:ss')}} — {{scope.row.endTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <zy-pop-up v-model="detailsShow"
               title="读书活动详情">
      <readingActivityDetails :id="uid"></readingActivityDetails>
    </zy-pop-up>
    <zy-pop-up v-model="show"
               title="关联活动">
      <associatedActivity :id="id"
                          @callback="callback"></associatedActivity>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import readingActivityDetails from '../readingActivity/readingActivityDetails'
import associatedActivity from './associatedActivity'
export default {
  name: 'announcementActivity',
  data () {
    return {
      tableData: [],
      uid: '',
      show: false,
      detailsShow: false
    }
  },
  props: ['id'],
  mixins: [tableData],
  components: {
    readingActivityDetails,
    associatedActivity
  },
  mounted () {
    this.readschemeList()
  },
  methods: {
    newData () {
      this.uid = ''
      this.show = true
    },
    details (row) {
      this.uid = row.id
      this.detailsShow = true
    },
    callback () {
      this.readschemeList()
      this.show = false
    },
    async readschemeList () {
      const res = await this.$api.academy.readschemeList({
        noticeId: this.id
      })
      var { data } = res
      this.tableData = data
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前关联的读书活动, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var ids = []
          this.tableData.forEach((row, index) => {
            if (Object.prototype.hasOwnProperty.call(this.selectObj, row.id)) {
            } else {
              ids.push(row.id)
            }
          })
          this.syNoticeJoinScheme(ids.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async syNoticeJoinScheme (id) {
      const res = await this.$api.academy.syNoticeJoinScheme({
        id: this.id,
        schemeIds: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.readschemeList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.announcementActivity {
  height: 460px;
  width: 990px;
  .tableData {
    padding: 0 22px;
    height: calc(100% - 76px) !important;
  }
  .paging_box {
    padding: 0 22px;
  }
}
</style>
