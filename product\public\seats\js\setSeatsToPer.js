var token = sessionStorage.getItem('token');
var areaId = sessionStorage.getItem("areaId");
var header = '{"Authorization": ' + token + ',"u-login-areaId": ' + areaId + '}'
var themeColor = window.localStorage.getItem("theme_color") != null ? window.localStorage.getItem("theme_color") : "#199BC5";
var mId = ""; //会议id
var rId; //会场id
var personArr; //存放了座位id的人员数组
var seatesArr; //存放已经保存的座位消息
var selDeptId = ""; //当前选中的组织部门id
var selDeptName = ""; //当前选中的组织部门Name
var selTreeNode = ""; //选中的树节点
var selSets; //选中的座位数组
var selSeatId; //存放选中的座位的座位号数组
var areaArr; // 区域信息
var impPerArr; //存放已导入的人员数组
var seatInfo; //人员对应座位信息
var maxX = 0; //座位容器内最大的x坐标
var maxY = 0; //座位容器内最大的y坐标
var maxSeatNo = 1; //座位容器内最大的座位号
var boxWidth = 0;

//初始化
$(function () {

  mId = window.sessionStorage.getItem("meetId");
  $('#perDiv').css("height", $(this).height() - 275);
  $('#setsDiv').css("height", $(this).height() - 87);
  $('.box').css("width", $(this).width() - $('#perList').width() - 65).css("height", $('.box').height() - 67).css("overflow", "auto");
  boxWidth = $(this).width() - $('#perList').width() - 65;

  changeThemeColor(themeColor);

  //layui导入人员部分代码
  layui.use('upload', function () {
    var $ = layui.jquery,
      upload = layui.upload;
    //指定允许上传的文件类型
    upload.render({
      elem: '#importPer_a',
      method: "post",
      headers: JSON.parse(header),
      contentType: 'application/json',
      url: server.local_path + "meetseatplan/import",
      accept: 'file', //普通文件
      exts: 'xls|xlsx',
      before: function (obj) {
        this.data = {
          "meetid": mId,
          "placeId": rId,
          "type": 1
        }; //关键代码
      },
      done: function (res) {
        if (res.errcode == 200) {
          layer.msg("导入成功");
          //根据条件加载人员与座位信息
          loadPersonAndSeatsInfo();
        }
        else {
          $("#infoModal").modal("show");
          $(".form-textarea").val(res.errmsg == "null" ? "" : res.errmsg);
        }
      }
    });

  });

  //layui导入人员部分代码
  layui.use('upload', function () {
    var $ = layui.jquery,
      upload = layui.upload;
    //指定允许上传的文件类型
    upload.render({
      elem: '#importPer_b',
      method: "post",
      headers: JSON.parse(header),
      contentType: 'application/json',
      url: server.local_path + "meetseatplan/import",
      accept: 'file', //普通文件
      exts: 'xls|xlsx',
      before: function (obj) {
        this.data = {
          "meetid": mId,
          "placeId": rId,
          "type": 0
        }; //关键代码
      },
      done: function (res) {
        if (res.errcode == 200) {
          layer.msg("导入成功");
          //根据条件加载人员与座位信息
          loadPersonAndSeatsInfo();
        }
        else {
          $("#infoModal").modal("show");
          $(".form-textarea").val(res.errmsg == "null" ? "" : res.errmsg);
        }
      }
    });
  });

  //layui导入人员部分代码 常委会议室模板
  layui.use('upload', function () {
    var $ = layui.jquery,
      upload = layui.upload;
    //指定允许上传的文件类型
    upload.render({
      elem: '#importPer_c',
      method: "post",
      headers: JSON.parse(header),
      contentType: 'application/json',
      url: server.local_path + "meetseatplan/import",
      accept: 'file', //普通文件
      exts: 'xls|xlsx',
      before: function (obj) {
        this.data = {
          "meetid": mId,
          "placeId": rId,
          "type": 2
        }; //关键代码
      },
      done: function (res) {
        if (res.errcode == 200) {
          layer.msg("导入成功");
          //根据条件加载人员与座位信息
          loadPersonAndSeatsInfo();
        }
        else {
          $("#infoModal").modal("show");
          $(".form-textarea").val(res.errmsg == "null" ? "" : res.errmsg);
        }
      }
    });
  });

  //layui导入人员部分代码 人民会堂模板
  layui.use('upload', function () {
    var $ = layui.jquery,
      upload = layui.upload;
    //指定允许上传的文件类型
    upload.render({
      elem: '#importPer_d',
      method: "post",
      headers: JSON.parse(header),
      contentType: 'application/json',
      url: server.local_path + "meetseatplan/import",
      accept: 'file', //普通文件
      exts: 'xls|xlsx',
      before: function (obj) {
        this.data = {
          "meetid": mId,
          "placeId": rId,
          "type": 3
        }; //关键代码
      },
      done: function (res) {
        if (res.errcode == 200) {
          layer.msg("导入成功");
          //根据条件加载人员与座位信息
          loadPersonAndSeatsInfo();
        }
        else {
          $("#infoModal").modal("show");
          $(".form-textarea").val(res.errmsg == "null" ? "" : res.errmsg);
        }
      }
    });
  });
  //已报名人员、出席列席人员单选按钮切换
  $('input:radio[name="perRadio"]').click(function () {
    //根据条件加载人员与座位信息
    loadPersonAndSeatsInfo();
  });

  //按部门排序/姓氏笔画排序下拉框值改变事件
  $("#sortType").combobox({
    onChange: function (n, o) {
      //根据条件加载人员与座位信息
      loadPersonAndSeatsInfo();
    }
  });

  //根据会议ID查会议详情
  loadMeetDetailById();
  //加载界别下拉框
  loadCircles();
  //加载组别下拉框数据
  loadGroup();
});

//加载界别下拉框数据
function loadCircles () {
  //调用查询接口
  axios.post(server.local_path + "meetapply/deleList", Qs.stringify({}), {
    headers: JSON.parse(header)
  })
    .then(function (response) {
      var circlesInfo = response.data.data;
      var options = [{ text: "请选择选项", value: "" }];
      if (circlesInfo != null && circlesInfo.length > 0) {
        for (var i = 0; i < circlesInfo.length; i++) {
          options.push({
            text: circlesInfo[i].value,//界别名称
            value: circlesInfo[i].id  //界别id
          });
        }
      }
      $("#circlesSelect").combobox("loadData", options);
      $('#circlesSelect').combobox('select', options[0].value);
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
}

//加载组别下拉框数据
function loadGroup () {
  //调用查询接口
  axios({
    method: "post",
    url: server.local_path + "meetgroup/groupList?meetId=" + mId,
    headers: JSON.parse(header),
  })
    .then(function (response) {
      var resInfo = response.data.data;
      var options = [{ text: "请选择选项", value: "" }];
      if (resInfo != null && resInfo.length > 0) {
        for (var i = 0; i < resInfo.length; i++) {
          options.push({
            text: resInfo[i].value,//组别名称
            value: resInfo[i].id  //组别id
          });
        }
      }
      $("#groupSelect").combobox("loadData", options);
      $('#groupSelect').combobox('select', options[0].value);
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
}

//根据会议ID查会议详情
function loadMeetDetailById () {
  //调用接口
  axios({
    method: "post",
    url: server.local_path + 'meetMeeting/info?id=' + mId,
    headers: JSON.parse(header)
  })
    .then(function (response) {
      var resultInfo = response.data.data;
      //获取会场id
      rId = resultInfo.meetPlace;
      //会议名称
      $("#meetName").text(resultInfo.name);
      loadAreaInfo(rId); //加载区域信息
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
}

//根据会场ID加载区域数据
function loadAreaInfo () {
  //调用查询接口
  axios({
    method: "post",
    url: server.local_path + "meetPlace/info/" + rId,
    headers: JSON.parse(header)
  })
    .then(function (response) {
      $(".areaTip").empty();
      var areaInfo = response.data.data.seatAreaList;
      //会场名称
      $("#roomName").text(response.data.data.name);
      areaArr = new Array();
      areaArr = areaInfo;
      //座位类型信息
      if (areaInfo != null && areaInfo.length > 0) {
        for (var i = 0; i < areaInfo.length; i++) {
          var areaName = areaInfo[i].name; //区域名称
          var areaColor = areaInfo[i].colour; //区域颜色
          $(".areaTip").append("<p style='margin-right: 5px; width: 20px; height:20px; margin-button: 0; border-radius: 3px; border:1px solid " + areaColor + ";color: #FFFFFF'>空</p><label style='margin-right: 10px; position: relative; top: -10px;'>" + areaName + "</label>");
        }
      }
      //根据条件加载人员与座位信息
      loadPersonAndSeatsInfo();
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
}

//根据条件加载人员与座位信息
function loadPersonAndSeatsInfo () {
  var perName = $.trim($("#personName").val()); //人员姓名
  var perPhone = $.trim($("#phone").val()); //手机、联系电话
  var circles = $("#circlesSelect").combobox("getValue"); //界别
  var perType = $("#pTypeSelect").combobox("getValue"); //是否列席人员  空:全部、 1:列席人员、0:非列席人员
  var isSignUp = $("input:radio[name='perRadio']:checked").val();//1:已报名人员  0:受邀人员
  var sortType = $("#sortType").val(); //1:按姓氏笔画排序  0:按部门排序

  axios({
    method: "post",
    url: server.local_path + "meetseatplan/getTheMeet",
    headers: JSON.parse(header),
    data: {
      "meetId": mId, //会议id
      "personName": perName,//人员姓名
      "personMobile": perPhone,//手机
      "personDeptId": selDeptId,//部门id
      "personDeleId": circles, //界别
      "personApply": isSignUp,//已报名人员、受邀人员
      "personType": perType, //空:全部、 1:列席人员、0:非列席人员
      "sortByX": sortType//按姓氏笔画排序、按部门排序
    }
  }).then(function (response) {
    var perInfo = response.data.data.people;
    var seatInfo = response.data.data.arrange;
    var treeInfoArr = new Array();

    if (perInfo != null && perInfo.length > 0) {

      var dataArr = perInfo;

      if (dataArr != null && dataArr.length > 0) {
        //遍历数组，组装树节点数据
        for (var i = 0; i < dataArr.length; i++) {
          var tNode = dataArr[i];
          var tObj = new Object();
          if (tNode.dele == true && tNode.user == false) {
            tObj.id = tNode.deleId;//id
            tObj.pId = tNode.deleParentId; //父级id
            tObj.name = tNode.deleName; //界别名称
          }
          else if (tNode.dept == true && tNode.user == false) {
            tObj.id = tNode.officeId;//id
            tObj.pId = tNode.officeParentId; //父级id
            tObj.name = tNode.officeName; //部门名称
          } else if (tNode.atten == true && tNode.user == false) {
            tObj.id = tNode.attenId;//id
            tObj.pId = tNode.attenId; //父级id
            tObj.name = tNode.attenName; //部门名称
          }
          else if (tNode.dele == true && tNode.user == true) {
            tObj.id = tNode.inviteId;//id
            tObj.pId = tNode.deleId; //父级id
            tObj.uid = tNode.peopleId; //人员id
            tObj.umobile = tNode.peopleMobile; //人员电话
            tObj.name = tNode.peopleName; //人员名称
            tObj.job = tNode.job; //人员职务
          } else if (tNode.dept == true && tNode.user == true) {
            tObj.id = tNode.inviteId;//id
            tObj.pId = tNode.officeId; //父级id
            tObj.uid = tNode.peopleId; //人员id
            tObj.umobile = tNode.peopleMobile; //人员电话
            tObj.name = tNode.peopleName; //人员名称
            tObj.job = tNode.job; //人员职务
          } else if (tNode.atten == true && tNode.user == true) {
            tObj.id = tNode.inviteId;//id
            tObj.pId = tNode.attenParentId; //父级id
            tObj.uid = tNode.peopleId; //人员id
            tObj.umobile = tNode.peopleMobile; //人员电话
            tObj.name = tNode.peopleName; //人员名称
            tObj.job = tNode.job; //人员职务
          }
          else {
            if (tNode.dele == true) {
              tObj.id = tNode.deleId;//id
              tObj.pId = tNode.deleParentId; //父级id
            }
            else {
              tObj.id = tNode.officeId;//id
              tObj.pId = tNode.officeParentId; //父级id
            }
            tObj.uid = tNode.peopleId; //人员id
            tObj.umobile = tNode.peopleMobile; //人员电话
            tObj.name = tNode.peopleName; //人员名称
            tObj.job = tNode.job; //人员职务
          }
          tObj.isPer = tNode.user; //是否是人员

          if (seatInfo != null && seatInfo.length > 0) {
            for (var k = 0; k < seatInfo.length; k++) {
              var spId = seatInfo[k].userId != undefined ? seatInfo[k].userId : ""; //座位对应的人员id 用手机号代替
              var sId = seatInfo[k].id != undefined ? seatInfo[k].id : ""; //座位id
              var sNo = seatInfo[k].seatNumber != undefined ? seatInfo[k].seatNumber : ""; //座位号

              if (tNode.user == true) {

                if (spId == tNode.peopleMobile) {
                  tObj.seatesId = sId; //座位id
                  tObj.seatesNo = sNo; //座位号
                  break;
                } else {
                  tObj.seatesId = ""; //座位id
                  tObj.seatesNo = ""; //座位号
                  continue;
                }
              }
            }
            treeInfoArr.push(tObj);
          } else {
            tObj.seatesId = ""; //座位id
            tObj.seatesNo = ""; //座位号
            treeInfoArr.push(tObj);
          }
        }
      }
    }
    //创建树
    createPerTree(treeInfoArr);
    personArr = treeInfoArr;
    //删除容器内的座位数据
    $(".box").find("div").remove();
    if (seatInfo != null && seatInfo.length > 0) {
      seatesArr = new Array();
      seatesArr = seatInfo;

      //对“座位信息”对象数组进行排序
      tool_sortByKey(seatesArr, "seatY");
      //最小Y坐标偏移60像素的差值
      var diffY = 60 - Number(seatInfo[0].seatY);

      for (var i = 0; i < seatInfo.length; i++) {
        var sId = seatInfo[i].id; //座位id
        var sNo = seatInfo[i].seatNumber; //座位号
        var sTp = seatInfo[i].seatType; //座位类型
        var sSt = seatInfo[i].seatState; //座位状态
        var sH = seatInfo[i].seatH; //座位高度
        var sW = seatInfo[i].seatW; //座位宽度
        var sX = seatInfo[i].seatX; //座位x坐标
        var sY = Number(seatInfo[i].seatY) + diffY; //座位y坐标
        var sL = seatInfo[i].label; //座位/标记  seat:座位-sign:标记
        var sBC = seatInfo[i].bgColor; //座位背景色
        var sP = seatInfo[i].position; //座位几排几座

        var bdColor = "";
        //遍历区域数组，设置座位的边框颜色
        if (areaArr != null && areaArr.length > 0) {
          for (var a = 0; a < areaArr.length; a++) {
            if (sTp == areaArr[a].id) {
              bdColor = areaArr[a].colour;
              break;
            } else {
              bdColor = "";
              continue;
            }
          }
        }

        if (Number(sNo) > Number(maxSeatNo)) {
          maxSeatNo = sNo; //给容器内的最大座位号赋值
        }
        if (Number(sX) > Number(maxX)) {
          maxX = sX; //给容器内的最大x坐标赋值
        }
        if (Number(sY) > Number(maxY)) {
          maxY = sY; //给容器内的最大y坐标赋值
        }

        if (sL == "seat") {

          var sRow = "", sNos = ""; //座位几排几座
          if (sP != "") {
            var rowAndNos = sP.split(",");
            sRow = rowAndNos[0];
            sNos = rowAndNos[1];
          }

          var pId = ""; //人员Id
          var pName = ""; //人员姓名

          //遍历人员数组，对比座位id,找到指定了座位的人员，把人员id，人员姓名字段放到座位上
          if (personArr != null && personArr.length > 0) {
            for (var k = 0; k < personArr.length; k++) {
              if (personArr[k].isPer == true) {

                if (sId == personArr[k].seatesId) {
                  pId = personArr[k].umobile; //人员id 用手机号代替
                  pName = personArr[k].name; //人员姓名
                  break;
                } else {
                  pId = "";
                  pName = "";
                  continue;
                }
              }
            }
            var addStr = '<div onclick="seatesClick($(this));" class="drag" style="z-index: 1;width:' + sW + 'px;height:' + sH + 'px;top:' + sY + 'px;left:' + sX + 'px;border: 1px solid ' + bdColor + ';">' +
              '<label style="height:10px; color: #000000;">' + sNo + '</label>' + //第一个label放座位号
              '<label style="display:none; height:10px; color: #000000;">' + sNos + '</label>' + //第二个label放座位第几座 即 顺序号
              '<label style="display:none;">' + sTp + '</label>' + //第三个label放座位类型
              '<label style="display:none;">' + sSt + '</label>' + //第四个label放座位状态
              '<label style="display:none;">' + sId + '</label>' + //第五个label放座位Id
              '<label style="display:none;">' + pId + '</label>' + //第六个label放人员Id
              '<label style="text-indent: -1px;font-size: 11px !important; color: #666666;">' + pName + '</label>' + //第七个label放人员姓名
              '<label style="display:none;">' + sBC + '</label>' + //第八个label放座位背景色
              '<label style="display:none;">' + sRow + '</label>' + //第九个label放座位第几排
              '<label style="display:none;"></label>' + //第十个label放人员电话号码
              '</div>';
            $(".box").append(addStr);
          }
          else {
            var addStr = '<div onclick="seatesClick($(this));" class="drag" style="z-index: 1;width:' + sW + 'px;height:' + sH + 'px;top:' + sY + 'px;left:' + sX + 'px;border: 1px solid ' + bdColor + ';">' +
              '<label style="height:10px; color: #000000;">' + sNo + '</label>' + //第一个label放座位号
              '<label style="display:none; height:10px; color: #000000;">' + sNos + '</label>' + //第二个label放座位第几座 即 顺序号
              '<label style="display:none;">' + sTp + '</label>' + //第三个label放座位类型
              '<label style="display:none;">' + sSt + '</label>' + //第四个label放座位状态
              '<label style="display:none;">' + sId + '</label>' + //第五个label放座位Id
              '<label style="display:none;"></label>' + //第六个label放人员Id
              '<label style="text-indent: -1px;"></label>' + //第七个label放人员姓名
              '<label style="display:none;">' + sBC + '</label>' + //第八个label放座位背景色
              '<label style="display:none;">' + sRow + '</label>' + //第九个label放座位第几排
              '<label style="display:none;"></label>' + //第十个label放人员电话号码
              '</div>';
            $(".box").append(addStr);
          }
        }
        else {
          var addStr = '<div class="signDrag" style="z-index: 1;width:' + sW + 'px;height:' + sH + 'px;top:' + sY + 'px;left:' + sX + 'px;">' + sNo + '</div>';
          $(".box").append(addStr);
        }
      }
      //设置座位选中状态
      setSeatSelected();
      //根据选择类型 显示顺序号/座位号
      showInfo();
    }
  }).catch(function (error) {
    layer.msg(error.response.data.message);
  });
}

//创建参会人员树
function createPerTree (treeData) {
  var setting = {

    view: {
      addDiyDom: addDiyDom,
      showIcon: false
    },
    data: {
      simpleData: {
        enable: true
      }
    },
    check: {
      enable: true
    }
  };

  //给节点添加文本框
  function addDiyDom (treeId, treeNode) {
    if (treeNode.parentNode && treeNode.parentNode.id != 2) return;
    var aObj = $("#" + treeNode.tId);
    if (treeNode.isPer == true) {
      var editStr = "<input type='text' id='" + treeNode.tId + "_input'' value='" + treeNode.seatesNo + "' style='width:40px;margin-left:15px;position: relative;margin-top: -1px;text-align: center;color: red;'/>";
      $(aObj.children().get(1)).append(editStr);
      $(aObj.children().get(2)).css("margin-left", "40px");
      if (treeNode.umobile != null && treeNode.umobile != "") {
        var tmb = treeNode.umobile.substring(0, 3) + '****' + treeNode.umobile.substring(7, 11);
        $(aObj.children().get(2)).append("<br/><span style='color: #8C8C8C;'>" + tmb + "</span>");
      }
      var inp = $("#" + treeNode.tId + "_input");
      if (inp) {
        inp.bind("click", function () {
          selNode(treeNode);
          setFocus(inp);
        });
      }
    }
  }

  //设置选中树节点
  function selNode (tNode) {
    selTreeNode = tNode;
    var treeObj = $.fn.zTree.getZTreeObj("perTree");
    var node = treeObj.getNodeByParam("umobile", selTreeNode.umobile);
    treeObj.cancelSelectedNode(); //先取消所有的选中状态
    treeObj.selectNode(node, true); //将指定ID的节点选中
    treeObj.expandNode(node, true, false); //将指定ID节点展开
  }

  //设置文本框焦点
  function setFocus (ipt) {
    ipt.focus();
  }

  var zNodes = treeData;

  $(document).ready(function () {
    $.fn.zTree.init($("#perTree"), setting, zNodes);
    var treeObj = $.fn.zTree.getZTreeObj("perTree");
    treeObj.expandAll(true);
  });
}

//保存人员座位信息 把座位号，人员id封装到数组
$("#saveSeats").on("click", function () {
  var setsArr = new Array(); //座位信息数组
  //遍历所有座位
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      var pId = $(this).children().get(5).textContent; //人员id 用手机号代替
      var pName = $(this).children().get(6).textContent; //人员姓名
      var sId = $(this).children().get(4).textContent; //座位id
      var sNo = $(this).children().get(0).textContent; //座位号
      //过滤掉人员为空的座位
      //			if(pId != "") {
      var setsObj = new Object();
      setsObj.meetId = mId; //会议id
      setsObj.placeId = rId; //会场id
      setsObj.seatId = sId; //座位id
      setsObj.seatNumber = sNo; //座位号/顺序号
      setsObj.name = pName != null ? pName : ""; //人员姓名
      setsObj.phone = pId != null ? pId : ""; //人员电话
      setsObj.userId = pId != null ? pId : ""; //人员id 用手机号代替
      setsArr.push(setsObj);
      //			}
    }
  });

  axios.post(server.local_path + "meetseatplan/insertArrange", Qs.stringify({
    seatedArrange: JSON.stringify(setsArr)
  }), {
    headers: JSON.parse(header)
  })
    .then(function (response) {
      var resultInfo = response.data;
      if (resultInfo.errcode == 200) {
        setTimeout(function () {
          layer.msg("操作成功");
          //根据条件加载人员与座位信息
          loadPersonAndSeatsInfo();
        }, 1000);
      }
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
});

//删除座位
$("#deleteSeats").on("click", function () {
  var delSelStr = "";
  //遍历所有座位 获取选中的座位信息
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      if ($(this).is('.selected')) {
        var sId = $(this).children().get(5).textContent; //座位上的人员id
        if (sId != "") {
          delSelStr += sId + ",";
        }
      }
    }
  });

  delSelStr = delSelStr.substring(0, delSelStr.length - 1);

  if (delSelStr.length > 0) {
    layer.confirm('确定要删除吗？', {}, function (index) {
      axios.post(server.local_path + "meetseatplan/delSeatPlan", Qs.stringify({
        meetId: mId,
        placeId: rId,
        peopleId: delSelStr
      }), {
        headers: JSON.parse(header)
      })
        .then(function (response) {
          var resultInfo = response.data;
          if (resultInfo.errcode == 200) {
            setTimeout(function () {
              layer.msg("操作成功");
              //根据条件加载人员与座位信息
              loadPersonAndSeatsInfo();
            }, 1000);
          }
        }).catch(function (error) {
          layer.msg(error.response.data.message);
        });
      //关闭
      layer.close(index);
    });
  }
  else {
    layer.msg("请选择座位有人员信息的座位进行删除");
  }
});

//存放座位对调选中的座位
var swapSeatsArr;
//座位对调
$("#swapSeats").on("click", function () {
  swapSeatsArr = new Array();
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      if ($(this).is('.selected')) {
        swapSeatsArr.push($(this));
        $(this).removeClass("selected");
      }
    }
  });
  if (swapSeatsArr.length > 1 && swapSeatsArr.length < 3) {
    var firstPid = $(swapSeatsArr[0]).children().get(5).textContent; //第一个座位的人员id
    var secondPid = $(swapSeatsArr[1]).children().get(5).textContent; //第二个座位的人员id
    var firstPname = $(swapSeatsArr[0]).children().get(6).textContent; //第一个座位的人员姓名
    var secondPname = $(swapSeatsArr[1]).children().get(6).textContent; //第二个座位的人员姓名

    $(swapSeatsArr[0]).children().get(5).textContent = secondPid; //将第二个座位的人员id赋给第一个座位
    $(swapSeatsArr[0]).children().get(6).textContent = secondPname; //将第二个座位的人员姓名赋给第一个座位
    $(swapSeatsArr[1]).children().get(5).textContent = firstPid; //将第一个座位的人员id赋给第二个座位
    $(swapSeatsArr[1]).children().get(6).textContent = firstPname; //将第一个座位的人员姓名赋给第二个座位

    updateTreeNodeInfo();//将座位信息同步更新到树节点上
  }
  else {
    layer.msg("请选择两个座位进行对调");
  }
});

/**
 * @desc   对象数组排序
 * @param   {array} 数组
 * @param   {key} 对象中的key
 */
function tool_sortByKey (array, key) {
  return array.sort(function (a, b) {
    var x = Number(a[key]);
    var y = Number(b[key]);
    return x < y ? -1 : x > y ? 1 : 0;
  });
}

//自动补位
$("#autoPatch").on("click", function () {
  var seatsArr_Patch = new Array(); //存放自动补位选中的座位
  var allSeatsArr_Patch = new Array(); //所有座位信息
  var havePerSeatsArr_Patch = new Array(); //已经排有人员信息的座位
  $('.box div').each(function () {
    if ($(this).is('.drag')) {

      //将座位放入座位数组进行排序用
      var obj = new Object();
      obj.item = $(this);
      obj.value = $(this).children().get(0).textContent; //座位号
      allSeatsArr_Patch.push(obj);//所有座位信息

      var pId = $(this).children().get(5).textContent; //人员id
      var pName = $(this).children().get(6).textContent; //人员姓名
      var sNo = $(this).children().get(0).textContent; //座位号
      //过滤掉人员为空的座位
      if (pId != "") {
        var pobj = new Object();
        pobj.sNo = sNo;
        pobj.pId = pId;
        pobj.pName = pName;
        havePerSeatsArr_Patch.push(pobj); //有人员信息的座位
      }

      if ($(this).is('.selected')) {
        seatsArr_Patch.push($(this)); //选中的座位
        $(this).removeClass("selected");
      }
    }
  });

  //对“所有座位信息”对象数组进行排序
  tool_sortByKey(allSeatsArr_Patch, "value");
  //对“已经排有人员信息的座位”对象数组进行排序
  tool_sortByKey(havePerSeatsArr_Patch, "sNo");

  if (seatsArr_Patch.length > 0 && seatsArr_Patch.length < 2) {

    var selSeatNo = $(seatsArr_Patch[0]).children().get(0).textContent;//获取选中座位的座位号
    var selSeatUid = $(seatsArr_Patch[0]).children().get(5).textContent;//获取选中座位的人员Id

    //将座位号小于选中座位号的已经排有人员的座位过滤
    havePerSeatsArr_Patch = $.grep(havePerSeatsArr_Patch, function (hps) {
      if (Number(hps.sNo) >= Number(selSeatNo)) {
        return hps;
      }
    });

    //选中没有人员信息的座位才执行补位操作
    if (selSeatUid == "") {
      var passPatchNum = Number(maxSeatNo) - Number(selSeatNo) + 1; //可供自动补位的座位数
      //如果当前已经排有人员的座位数小于等于可供自动补位的座位数 可以自动补位
      if (havePerSeatsArr_Patch.length <= passPatchNum) {

        for (var i = 0; i < allSeatsArr_Patch.length; i++) {
          var sNo = $(allSeatsArr_Patch[i].item).children().get(0).textContent;

          if (Number(sNo) >= Number(selSeatNo)) {
            $(allSeatsArr_Patch[i].item).children().get(5).textContent = "";
            $(allSeatsArr_Patch[i].item).children().get(6).textContent = "";
          }

          if (Number(selSeatNo) == Number(sNo)) {
            if (havePerSeatsArr_Patch.length > 0) {
              var ps = havePerSeatsArr_Patch.shift();
              $(allSeatsArr_Patch[i].item).children().get(5).textContent = ps.pId;
              $(allSeatsArr_Patch[i].item).children().get(6).textContent = ps.pName;
              selSeatNo = Number(selSeatNo) + 1;
            }
          }
        }
        updateTreeNodeInfo();//将座位信息同步更新到树节点上
      }
      else {
        layer.msg("没有足够的座位可以补位");
      }
    }
    else {
      layer.msg("请选择空座位进行补位");
    }
  }
  else {
    layer.msg("请选择一个座位");
  }
});

//自动排座
$("#autoSort").on("click", function () {
  var autoSortArr = new Array(); //自动排座的座位信息
  var autoPersArr = new Array(); //自动排座的人员信息
  var autoSortType = $('input:radio[name="autoSortType"]:checked').val(); //自动排座类型

  //获取人员树所有选中的节点
  var treeObj = $.fn.zTree.getZTreeObj("perTree");
  var nodes = treeObj.getCheckedNodes(true);
  for (var i = 0; i < nodes.length; i++) {
    //将人员节点放入数组
    if (nodes[i].isPer == true) {
      var perObj = new Object();
      perObj.userId = nodes[i].uid; //树节点选中的人员id
      perObj.name = nodes[i].name; //树节点选中的人员姓名
      perObj.phone = nodes[i].umobile; //树节点选中的人员电话
      autoPersArr.push(perObj);
    }
  }

  //遍历所有座位，获取已设置人员的座位和已选中的座位信息
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      var pId = $(this).children().get(5).textContent; //人员id 用手机号代替
      var pName = $(this).children().get(6).textContent; //人员姓名
      var sId = $(this).children().get(4).textContent; //座位id
      var sNo = $(this).children().get(0).textContent; //座位号
      //过滤掉未选中的座位
      if ($(this).is('.selected')) {
        var stobj = new Object();
        stobj.meetId = mId; //会议id
        stobj.placeId = rId; //会场id
        stobj.seatId = sId; //座位id
        stobj.seatNumber = sNo; //座位号/顺序号
        stobj.name = pName != null ? pName : ""; //人员姓名
        stobj.phone = pId != null ? pId : ""; //人员电话
        stobj.userId = pId != null ? pId : ""; //人员id 用手机号代替
        autoSortArr.push(stobj);
      }
    }
  });
  //对“已选中的座位信息”对象数组进行排序
  tool_sortByKey(autoSortArr, "seatNumber");
  if (autoPersArr.length == 0) {
    layer.msg("请选择人员");
    return;
  }
  if (autoSortArr.length == 0) {
    layer.msg("请选择座位");
    return;
  }
  axios.post(server.local_path + "meetseatplan/autoArraySeat", Qs.stringify({ arrayType: autoSortType, seatJson: JSON.stringify(autoSortArr), peopleJson: JSON.stringify(autoPersArr) }),
    { headers: JSON.parse(header) })
    .then(function (response) {
      var resultInfo = response.data;
      if (resultInfo.errcode == 200) {
        setTimeout(function () {
          layer.msg("操作成功");
          //根据条件加载人员与座位信息
          loadPersonAndSeatsInfo();
        }, 1000);
      }
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
});

/**
 * 插入座位
 * insetType
 * 1:自动补齐 优先插入没有人员的座位，有人的座位依次类推
 * 2:座位类推 无视没有人员的座位，所有座位依次类推
 */
function insertSeatsByType (insertType) {
  var insetMaxSno = 1; //插入座位的已排人员座位的最大座位号
  var seatsArr_Inset = new Array(); //存放插入座位选中的座位
  var allSeatsArr_Inset = new Array(); //所有座位信息
  var havePerSeatsArr_Inset = new Array(); //已经排有人员信息的座位
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      //将座位放入座位数组进行排序用
      var obj = new Object();
      obj.item = $(this);
      obj.value = $(this).children().get(0).textContent; //座位号
      allSeatsArr_Inset.push(obj);//所有座位信息

      var pId = $(this).children().get(5).textContent; //人员id
      var pName = $(this).children().get(6).textContent; //人员姓名
      var sNo = $(this).children().get(0).textContent; //座位号
      //过滤掉人员为空的座位
      if (pId != "") {
        var pobj = new Object();
        pobj.sNo = sNo;
        pobj.pId = pId;
        pobj.pName = pName;
        havePerSeatsArr_Inset.push(pobj); //有人员信息的座位

        if (Number(sNo) > Number(insetMaxSno)) {
          insetMaxSno = sNo; //给插入座位的已排人员座位的最大座位号赋值
        }
      }

      if ($(this).is('.selected')) {
        seatsArr_Inset.push($(this)); //选中的座位
      }
    }
  });

  //对“所有座位信息”对象数组进行排序
  tool_sortByKey(allSeatsArr_Inset, "value");
  //对“已经排有人员信息的座位”对象数组进行排序
  tool_sortByKey(havePerSeatsArr_Inset, "sNo");

  if (seatsArr_Inset.length > 0 && seatsArr_Inset.length < 2) {

    var selSeatNo = $(seatsArr_Inset[0]).children().get(0).textContent; //获取选中座位的座位号
    var selSeatUid = $(seatsArr_Inset[0]).children().get(5).textContent;//获取选中座位的人员Id

    //将座位号小于选中座位号的已经排有人员的座位过滤
    havePerSeatsArr_Inset = $.grep(havePerSeatsArr_Inset, function (hps) {
      if (Number(hps.sNo) >= Number(selSeatNo)) {
        return hps;
      }
    });
    //对有人员的座位进行操作
    if (selSeatUid != "") {
      //自动补齐
      if (insertType == 1) {
        if (havePerSeatsArr_Inset.length > 0) {
          //循环起始值
          var startIndex = Number(selSeatNo);

          if (startIndex != allSeatsArr_Inset.length) {
            var num = 0; //可供插入的座位数
            for (var s = 0; s < allSeatsArr_Inset.length; s++) {
              var sno = $(allSeatsArr_Inset[s].item).children().get(0).textContent; //座位号
              if (Number(selSeatNo) < Number(sno)) {
                num++
              }
            }
            //如果要插入的座位数>可供插入的座位数
            if (havePerSeatsArr_Inset.length > num) {
              layer.msg("没有足够的座位");
              $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
            }
            else {
              $(seatsArr_Inset[0]).children().get(5).textContent = ""; //清空选中座位的人员id
              $(seatsArr_Inset[0]).children().get(6).textContent = ""; //清空选中座位的人员姓名

              for (var p = startIndex; p < allSeatsArr_Inset.length; p++) {
                var apname = $(allSeatsArr_Inset[p].item).children().get(6).textContent;
                $(allSeatsArr_Inset[p].item).children().get(5).textContent = ""; //清空座位的人员id
                $(allSeatsArr_Inset[p].item).children().get(6).textContent = ""; //清空座位的人员姓名
                //如果下一个座位的人员信息为空 赋值后跳出循环
                if (apname == "") {
                  var sno = $(allSeatsArr_Inset[p].item).children().get(0).textContent; //座位号
                  var ps = havePerSeatsArr_Inset.shift();
                  if ((Number(ps.sNo) + 1) == Number(sno)) {
                    $(allSeatsArr_Inset[p].item).children().get(5).textContent = ps.pId;
                    $(allSeatsArr_Inset[p].item).children().get(6).textContent = ps.pName;
                    break;
                  }
                }
                else {
                  var sno = $(allSeatsArr_Inset[p].item).children().get(0).textContent; //座位号
                  var ps = havePerSeatsArr_Inset.shift();
                  if ((Number(ps.sNo) + 1) == Number(sno)) {
                    $(allSeatsArr_Inset[p].item).children().get(5).textContent = ps.pId;
                    $(allSeatsArr_Inset[p].item).children().get(6).textContent = ps.pName;
                  }
                }
              }
              $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
              updateTreeNodeInfo();//将座位信息同步更新到树节点上
            }
          }
          else {
            layer.msg("没有足够的座位");
            $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
          }
        }
      }
      //座位类推
      else {
        if (Number(insetMaxSno) >= Number(maxSeatNo)) {
          layer.msg("没有足够的座位");
          $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
        }
        else {
          if (havePerSeatsArr_Inset.length > 0) {

            $(seatsArr_Inset[0]).children().get(5).textContent = ""; //清空选中座位的人员id
            $(seatsArr_Inset[0]).children().get(6).textContent = ""; //清空选中座位的人员姓名

            for (var i = 0; i < havePerSeatsArr_Inset.length; i++) {
              //循环起始值
              var startIndex = Number(selSeatNo);
              for (var p = startIndex; p < allSeatsArr_Inset.length; p++) {
                var pname = havePerSeatsArr_Inset[i].pName;
                var apname = $(allSeatsArr_Inset[p].item).children().get(6).textContent;
                if (pname == apname && apname != "") {
                  $(allSeatsArr_Inset[p].item).children().get(5).textContent = ""; //清空座位的人员id
                  $(allSeatsArr_Inset[p].item).children().get(6).textContent = ""; //清空座位的人员姓名
                }
                var sno = $(allSeatsArr_Inset[p].item).children().get(0).textContent; //座位号
                if ((Number(havePerSeatsArr_Inset[i].sNo) + 1) == Number(sno)) {
                  $(allSeatsArr_Inset[p].item).children().get(5).textContent = havePerSeatsArr_Inset[i].pId;
                  $(allSeatsArr_Inset[p].item).children().get(6).textContent = havePerSeatsArr_Inset[i].pName;
                  break;
                }
              }
            }
          }
        }
        $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
        updateTreeNodeInfo();//将座位信息同步更新到树节点上
      }
    }
    else {
      layer.msg("请选择有人员的座位进行操作");
      $(seatsArr_Inset[0]).removeClass("selected"); //清空座位的选中样式
    }
  }
  else {
    layer.msg("请选择一个座位");
  }
}

//将座位信息同步更新到树节点上
function updateTreeNodeInfo () {
  $('.box div').each(function () {
    if ($(this).is('.drag')) {
      var pId = $(this).children().get(5).textContent; //人员id
      var sId = $(this).children().get(4).textContent; //座位id
      var sNo = $(this).children().get(0).textContent; //座位号

      //过滤掉人员为空的座位
      if (pId != "") {
        var treeObj = $.fn.zTree.getZTreeObj("perTree");
        var treeNode = treeObj.getNodes();
        var treeNodes = treeObj.transformToArray(treeNode); //获取树所有节点
        for (var i = 0; i < treeNodes.length; i++) {
          var pId = treeNodes[i].umobile; //树节点人员id 用手机号代替
          var sId = $(this).children().get(5).textContent; //座位上的人员id
          //对比树节点人员id与座位上绑定的人员id
          if (pId == sId) {
            treeNodes[i].seatesId = sId; //设置树节点上座位id的值
            treeNodes[i].seatesNo = sNo; //设置树节点上座位号的值
            $("#" + treeNodes[i].tId + "_input").val(sNo); //设置树节点上文本框的值
          }
        }
      }
    }
  });
}

//查询点击事件
$("#query").click(function () {
  $("#winModal").modal("show")
});

//查询条件弹框确定
$("#confirmBtn").click(function () {
  //根据条件加载人员与座位信息
  loadPersonAndSeatsInfo();
  $("#winModal").modal("hide");
});

//查询条件重置
$("#resetParam").click(function () {
  $("#personName").val("");
  $("#phone").val("");
  $("#deptId").val("");
  $("#deptName").val("");
  selDeptId = "";
  $("#pTypeSelect").combobox("setValue", $('#pTypeSelect').combobox('getData')[0].value);
  $("#circlesSelect").combobox("setValue", $('#circlesSelect').combobox('getData')[0].value);
  $("#groupSelect").combobox("setValue", $('#groupSelect').combobox('getData')[0].value);
});

window.addEventListener("storage", function (e) {
  if (e.key == "theme_color") {
    themeColor = e.newValue;
    changeThemeColor(e.newValue);
  }
  if (e.key == "size") {
    if (e.newValue == 'false') {
      $('.box').css("width", boxWidth + 222);
    }
    else {
      $('.box').css("width", boxWidth);
    }
  }
});

var root = document.querySelector(':root');
//即时换色
// 设置需要换色的元素及其样式
function changeThemeColor (colo) {
  $("#meetName").css("color", colo);
  $(".btn-info").css("background", colo);
  $(".modal-body span").css("color", colo);
  root.setAttribute('style', '--color: ' + colo);
}

$(".btn-info").hover(function () {
  $(this).css('opacity', ".6");
},
  function () {
    $(this).css('opacity', "1");
  });

$(".cancel").hover(function () {
  $(this).css("background", "transparent").css("border", "1px solid" + themeColor).css("color", themeColor);
},
  function () {
    $(this).css("background", "transparent").css('border', "1px solid rgba(217,217,217,1)").css("color", "#000");
  });

$(".dropdown-item").hover(function () {
  $(this).css("background", "transparent").css("color", themeColor);
},
  function () {
    $(this).css("background", "transparent").css("color", "#72B9D0");
  });
