// 导入封装的方法
import { post, get } from '../http'
export default {
  // 在线诉求分类
  appealType: {
    list(params) {
      return post('/oaservicetype/list', params)
    },
    add(params) {
      return post('/oaservicetype/add', params)
    },
    edit(params) {
      return post('/oaservicetype/edit', params)
    },
    dels(ids) {
      return post('/oaservicetype/dels', { ids: ids })
    },
    info(id) {
      return get(`/oaservicetype/info/${id}`)
    }
  },
  // 服务在线委员
  member: {
    list(params) {
      return post('/oaserviceuser/list', params)
    },
    add(params) {
      return post('/oaserviceuser/add', params)
    },
    edit(params) {
      return post('/oaserviceuser/edit', params)
    },
    dels(ids) {
      return post('/oaserviceuser/dels', { ids: ids })
    },
    info(id) {
      return get(`/oaserviceuser/info/${id}`)
    }
  },
  // 在线诉求管理
  appealManage: {
    list(params) {
      return post('/oaservice/list', params)
    },
    add(params) {
      return post('/oaservice/add', params)
    },
    edit(params) {
      return post('/oaservice/edit', params)
    },
    dels(ids) {
      return post('/oaservice/dels', { ids: ids })
    },
    info(id) {
      return get(`/oaservice/info/${id}`)
    },
    verify(params) {
      return post('/oaservice/updStatus', params)
    }
  },
  // 在线意见征集
  solicitOpinions: {
    list(params) {
      return post('/oaserviceopinion/list', params)
    },
    add(params) {
      return post('/oaserviceopinion/add', params)
    },
    edit(params) {
      return post('/oaserviceopinion/edit', params)
    },
    dels(ids) {
      return post('/oaserviceopinion/dels', { ids: ids })
    },
    info(id) {
      return get(`/oaserviceopinion/info/${id}`)
    }
  }
}
