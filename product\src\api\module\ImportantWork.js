import {
  post
} from '../http'
const ImportantWork = {
  columnList (params) {
    return post('focuscolumnmodule/list', params)
  },
  columnInfo (params) {
    return post(`/focuscolumnmodule/info/${params}`)
  },
  columnDels (params) {
    return post('focuscolumnmodule/dels', params)
  },
  list (params) {
    return post('focuscontentmodule/list', params)
  },
  info (params) {
    return post(`/focuscontentmodule/info/${params}`)
  },
  dels (params) {
    return post('focuscontentmodule/dels', params)
  }
}
export default ImportantWork
