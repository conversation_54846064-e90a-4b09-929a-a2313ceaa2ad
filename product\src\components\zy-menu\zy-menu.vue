<template>
  <el-scrollbar class="zy-menu">
    <el-menu
      :default-active="key"
      :background-color="backgroundColor"
      :text-color="textColor"
      :active-text-color="activeTextColor"
      @select="select"
    >
      <zy-menu-children
        :menu="menu"
        :value="key"
        :props="props"
      ></zy-menu-children>
    </el-menu>
  </el-scrollbar>
</template>
<script>
import zyMenuChildren from './zy-menu-children'
export default {
  name: 'zyMenu',
  data () {
    return {
      key: ''
    }
  },
  props: {
    value: [String, Number, Array, Object],
    menu: {
      type: Array,
      default: () => []
    },
    textColor: {
      type: String,
      default: ''
    },
    backgroundColor: {
      type: String,
      default: ''
    },
    activeTextColor: {
      type: String,
      default: ''
    },
    // 树结构配置
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label',
          id: 'id',
          to: 'to',
          icon: 'iconUrl',
          isShow: 'isShow',
          showValue: true
        }
      }
    }
  },
  emits: ['select'],
  components: {
    zyMenuChildren
  },
  watch: {
    value (val) {
      if (val) {
        this.key = val
        this.selectData(this.menu, val)
      }
    }
  },
  mounted () {
    this.key = this.value
    this.selectData(this.menu, this.value)
  },
  methods: {
    select (key) {
      this.key = key
      this.$emit('input', key)
      this.selectData(this.menu, key)
    },
    selectData (data, id) {
      data.forEach(item => {
        if (item[this.props.children].length === 0) {
          if (item[this.props.id] === id) {
            this.$emit('select', item)
          }
        } else {
          this.selectData(item[this.props.children], id)
        }
      })
    }
  }
}
</script>
<style lang="scss">
@import './zy-menu.scss';
</style>
