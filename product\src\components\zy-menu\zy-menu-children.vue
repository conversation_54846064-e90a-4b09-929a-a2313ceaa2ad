<template>
  <div>
    <div v-for="(item) in menu"
         :key="item[props.id]">
      <el-submenu :index="item[props.id]"
                  v-if="isShowChildren(item)">
        <template #title>
          <div class="zy-menu-icon"
               v-if="level==1">
            <img :src="item[props.icon]"
                 alt="">
          </div>
          <span :class="[level==1?'menu-color':'']">{{item[props.label]}}</span>
        </template>
        <zy-menu-children :menu="item[props.children]"
                          :level="level+1"
                          :value="value"
                          :props="props"></zy-menu-children>
      </el-submenu>
      <el-menu-item :index="item[props.id]"
                    v-else>
        <div class="zy-menu-icon"
             v-if="level==1">
          <img :src="item[props.icon]"
               alt="">
        </div>
        <span slot="title"
              :class="[level==1?'menu-color':'']">{{item[props.label]}}</span>
      </el-menu-item>
    </div>
  </div>
</template>
<script>
export default {
  name: 'zyMenuChildren',
  props: {
    value: [String, Number, Array, Object],
    menu: {
      type: Array,
      default: () => []
    },
    level: {
      type: Number,
      default: 1
    },
    // 树结构配置
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label',
          id: 'id',
          to: 'to',
          isShow: 'isShow',
          showValue: true
        }
      }
    }
  },
  methods: {
    isShowChildren (menu) {
      let isShow = false
      if (menu[this.props.children].length) {
        isShow = menu[this.props.children].some(item => item[this.props.isShow] === this.props.showValue)
      }
      return isShow
    }
  }
}
</script>
