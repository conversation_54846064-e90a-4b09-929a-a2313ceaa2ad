<div class="apiDetail">
<div>
	<h2><span>Boolean / Function(treeId, treeNodes, targetNode)</span><span class="path">setting.edit.drag.</span>inner</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.exedit</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>When drag one node to the target node, set whether to allow the node to be the target node's child. It is valid when <span class="highlight_red">[setting.edit.enable = true]</span></p>
			<p class="highlight_red">If the target node is root, so z<PERSON><PERSON> will only trigger 'inner' and not trigger 'prev / next'.</p>
			<p class="highlight_red">This function mainly for the appropriate limit drag and drop (auxiliary arrow), it requires a combination of 'prev, next' together, to achieve full functionality.</p>
			<p>Default: true</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p> true means: allow the node to be the target node's child.</p>
	<p> false means: don't allow the node to be the target node's child.</p>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>.</p>
	<h4 class="topLine"><b>treeNodes</b><span>Array(JSON)</span></h4>
	<p>A collection of the nodes which has been dragged</p>
	<h4 class="topLine"><b>targetNode</b><span>JSON</span></h4>
	<p>JSON data object of the target node which treeNodes are draged over.</p>
	<p class="highlight_red">If the treeNodes will be root node, the targetNode = null</p>
	<h4 class="topLine"><b>Return </b><span>Boolean</span></h4>
	<p>return true or false</p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. disable to drag the node to the target node's inner.</h4>
	<pre xmlns=""><code>var setting = {
	edit: {
		enable: true,
		drag: {
			prev: true,
			next: true,
			inner: false
		}
	}
};
......</code></pre>
	<h4>2. disable to drag the node to be root node's child.</h4>
	<pre xmlns=""><code>function canInner(treeId, nodes, targetNode) {
	return !(targetNode && targetNode.level === 0);
}
var setting = {
	edit: {
		enable: true,
		drag: {
			prev: true,
			next: true,
			inner: canInner
		}
	}
};
......</code></pre>
</div>
</div>