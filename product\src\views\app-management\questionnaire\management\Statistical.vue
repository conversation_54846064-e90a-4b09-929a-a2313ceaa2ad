<template>
  <div class="card-item">
    <div class="qs-title">{{ title }}</div>
    <div v-for="(item, index) in info" :key="item.id">
      <div class="card-title-box">
        <div class="title-num">第{{ index + 1 }}题</div>
        <div class="main-title">
          {{ item.question }}({{
            item.answerType === 1
              ? '单选'
              : item.answerType === 2
              ? '多选'
              : '文本'
          }})
        </div>
      </div>

      <el-table
        v-if="item.answerType !== 3"
        :data="item.answerStatisticsListVo"
        border
        style="width: 100%"
      >
        <el-table-column
          v-if="item.answerType !== 3"
          prop="answer"
          label="选项"
        >
        </el-table-column>
        <el-table-column
          v-if="item.answerType !== 3"
          prop="num"
          label="选择人数"
        >
        </el-table-column>
        <el-table-column v-if="item.answerType !== 3" label="占比">
          <template slot-scope="scope">
            <el-progress
              :percentage="getProgress(scope.row, item.totalNum)"
              :stroke-width="18"
            ></el-progress>
          </template>
        </el-table-column>
      </el-table>
      <el-table
        v-if="item.answerType === 3"
        :data="item.textStatisticsListVo"
        border
        style="width: 100%"
      >
        <el-table-column prop="name" label="答题人"> </el-table-column>
        <el-table-column prop="count" label="回答详情"> </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      tableData: [{
        date: '2016-05-02',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1518 弄'
      }, {
        date: '2016-05-04',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1517 弄'
      }, {
        date: '2016-05-01',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1519 弄'
      }, {
        date: '2016-05-03',
        name: '王小虎',
        address: '上海市普陀区金沙江路 1516 弄'
      }],
      info: {},
      title: this.$route.query.title
    }
  },
  created () {
    this.getInfo()
  },
  methods: {
    getInfo () {
      this.$api.appManagement.questionnaire.statisticsData({
        questionnaireId: this.$route.query.id
      }).then(res => {
        this.info = res.data
      })
    },
    getProgress (row, sum) {
      if (sum === 0) {
        return 0
      } else {
        return Number(((row.num / sum) * 100).toFixed(0))
      }
    }
  }
}
</script>
<style lang="scss">
.card-item {
  padding: 20px;
  background-color: #fff;
  .el-progress-bar {
    padding-right: 70px;
  }
  .card-title-box {
    display: flex;
    align-items: center;
    margin-bottom: 40px;
    margin-top: 50px;
    .title-num {
      font-size: 20px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #333333;
      margin-right: 20px;
    }
    .main-title {
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #333333;
    }
  }
  .el-table__header-wrapper tr th {
    background: #f2f2f2;
    div {
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #333333;
    }
  }
  .qs-title {
    padding: 20px 0;
    text-align: center;
    font-size: 30px;
    font-family: PingFang SC;
    font-weight: 800;
    color: #007bff;
    line-height: 36px;
  }
}
</style>
