<template>
  <div class="booksNew">
    <booksAdd :id="''"
              v-if="show"
              @callback="callback"></booksAdd>
  </div>
</template>
<script>
import booksAdd from './booksAdd'
export default {
  // 提交建议
  name: 'booksNew',
  data () {
    return {
      show: true
    }
  },
  components: {
    booksAdd
  },
  methods: {
    callback () {
      this.show = false
      setTimeout(() => {
        this.show = true
      }, 200)
    }
  }
}
</script>
