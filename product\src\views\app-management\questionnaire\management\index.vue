<template>
  <div class="vote-manage">
    <search-box @search-click="search"
                @reset-click="reset"
                title="问卷调查筛选">
      <zy-widget label="关键字">
        <el-input v-model="searchParams.keyword"
                  placeholder="请输入关键字"
                  clearable></el-input>
      </zy-widget>
      <zy-widget label="时间段">
        <el-date-picker v-model="time"
                        type="daterange"
                        range-separator="至"
                        start-placeholder="开始时间"
                        value-format="yyyy-MM-dd"
                        end-placeholder="结束时间"></el-date-picker>
      </zy-widget>
      <zy-widget label="发布状态">
        <el-select v-model="searchParams.isPublish"
                   placeholder="发布状态">
          <el-option v-for="item in publishList"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value">
          </el-option>
        </el-select>
      </zy-widget>
      <zy-widget label="问卷调查状态">
        <el-select v-model="searchParams.questionnaireStatus"
                   placeholder="问卷调查状态">
          <el-option v-for="item in statusList"
                     :key="item.id"
                     :label="item.label"
                     :value="item.id">
          </el-option>
        </el-select>
      </zy-widget>
    </search-box>
    <div class="qd-list-wrap">
      <div class="qd-btn-box">
        <el-button type="primary"
                   icon="el-icon-plus"
                   @click="handleAdd">新增</el-button>
        <!-- <el-button type="success" plain @click="handleExcel"
          >导出表格</el-button
        > -->
        <el-button type="danger"
                   plain
                   @click="handleBatchDelete">删除</el-button>
      </div>
      <div class="qd-table-box">
        <zy-table>
          <el-table :data="list"
                    stripe
                    border
                    ref="table"
                    slot="zytable"
                    @selection-change="handleSelectionChange">
            <el-table-column type="selection"
                             fixed="left"
                             width="60"></el-table-column>
            <el-table-column label="标题"
                             prop="theme"
                             min-width="180"
                             show-overflow-tooltip>
              <template slot-scope="scope">
                <el-button @click="handlePreview(scope.row.id)"
                           type="text">{{
                  scope.row.title
                }}</el-button>
              </template>
            </el-table-column>
            <el-table-column label="开始时间"
                             min-width="360">
              <template slot-scope="scope">{{ scope.row.startTime }}至{{ scope.row.endTime }}</template>
            </el-table-column>
            <el-table-column label="发起人"
                             prop="createBy"
                             min-width="120"></el-table-column>
            <el-table-column label="发布状态"
                             min-width="120">
              <template slot-scope="scope">
                <div :style="{
                    color:
                      scope.row.isPublishValue === '已发布'
                        ? '#333333'
                        : '#FF5064'
                  }">
                  {{ scope.row.isPublishValue }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="问卷调查状态"
                             prop="questionnaireStatusValue"
                             min-width="120">
              <template slot-scope="scope">
                <div :style="{
                    color:
                      scope.row.questionnaireStatusValue === '进行中'
                        ? '#007BFF'
                        : scope.row.questionnaireStatusValue === '未开始'
                        ? '#FF5064'
                        : '#999999'
                  }">
                  {{ scope.row.questionnaireStatusValue }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作"
                             fixed="right"
                             min-width="300">
              <template slot-scope="scope">
                <el-popover placement="bottom"
                            width="80"
                            trigger="hover">
                  <div class="hover-edit-botton">
                    <el-button v-if="scope.row.questionnaireStatusValue === '未开始'"
                               @click="handleEdit(scope.row)"
                               type="text">编辑</el-button>
                    <el-button @click="handlePreview(scope.row.id)"
                               type="text">预览</el-button>
                    <el-button @click="handleStatistical(scope.row)"
                               type="text">统计</el-button>
                    <el-button @click="handleDelete(scope.row.id)"
                               type="text">删除</el-button>
                  </div>
                  <el-button type="text"
                             style="color: #007bff"
                             slot="reference">设置</el-button>
                </el-popover>
              </template>
            </el-table-column>
          </el-table>
        </zy-table>
      </div>
      <div class="qd-page-box">
        <el-pagination @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"
                       :current-page.sync="page"
                       :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                       :page-size.sync="pageSize"
                       background
                       layout="total, sizes, prev, pager, next, jumper"
                       :total="total">
        </el-pagination>
      </div>
    </div>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export :excelId="excelId"
                 :type="550"
                 @callback="exportShow = false"></zy-export>
    </zy-pop-up>
    <preview-code v-if="isQrcode"
                  @cancel="isQrcode = false"
                  :url="codeUrl"
                  name="签到码"></preview-code>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import table from '@mixins/table.js'
import { filterParams } from '@/common/handleParams'
export default {
  components: {},
  mixins: [table],
  data () {
    return {
      searchParams: {
        keyword: '',
        isPublish: '',
        questionnaireStatus: ''
      },
      time: [],
      statusList: [
        { id: '', label: '全部' },
        { id: 1, label: '未开始' },
        { id: 2, label: '进行中' },
        { id: 3, label: '已结束' }
      ],
      publishList: [
        { value: 1, label: '已发布' },
        { value: 0, label: '未发布' }
      ],
      exportShow: false,
      excelId: null,
      isQrcode: false,
      codeUrl: null
    }
  },
  created () {
    this.getList()
  },
  inject: ['newTab'],
  methods: {
    getList () {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize
      }
      params = Object.assign(params, filterParams(this.searchParams))
      if (this.time.length > 0) {
        params.startTime = this.time[0]
        params.endTime = this.time[1]
      }
      this.$api.appManagement.questionnaire.list(params).then(res => {
        const { data, total } = res
        this.list = data
        this.total = total
      })
    },
    search () {
      this.getList()
    },
    reset () {
      this.searchParams = {
        keyword: '',
        isPublish: '',
        questionnaireStatus: ''
      }
      this.time.splice(0, this.time.length)
      this.getList()
    },
    handleAdd () {
      const mid = new Date().getTime().toString()
      this.newTab({
        name: '新建问卷调查',
        menuId: mid,
        to: '/questionnaire-add',
        params: { mid }
      })
    },
    handleEdit (val) {
      if (dayjs().isBefore(val.startTime)) {
        this.newTab({
          name: '编辑问卷调查',
          menuId: val.id,
          to: '/questionnaire-add',
          params: { id: val.id, mid: val.id }
        })
      } else {
        this.$message.warning('问卷调查已经开始,禁止修改')
      }
      this.newTab({
        name: '编辑问卷调查',
        menuId: val.id,
        to: '/questionnaire-add',
        params: { id: val.id, mid: val.id }
      })
    },
    handleDelete (ids) {
      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.appManagement.questionnaire.del(ids).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消删除')
        return false
      })
    },
    handleBatchDelete () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要删除的项')
      }
      this.handleDeletes(this.selectionList.map(v => v.id).join(','))
    },
    handleDeletes (ids) {
      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.appManagement.questionnaire.dels({
          ids
        }).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消删除')
        return false
      })
    },
    // TODO 导出表格 更换type
    handleExcel () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要导出的项')
      }
      this.excelId = this.selectionList.map(item => item.id).join(',')
      this.exportShow = true
    },
    handleEnable (id, type) {
      this.$confirm(`此操作将${type === 1 ? '发布' : '取消发布'}选中的项, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.appManagement.questionnaire.publish({ id: id, isPublish: type }).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('修改发布状态成功')
          }
        })
      }).catch(() => {
        return false
      })
    },
    handleVoteItem (id) {
      this.newTab({
        name: '问卷调查项管理',
        menuId: id,
        to: '/vote-item',
        params: { id: id, mid: id }
      })
    },
    handlePreview (id) {
      this.newTab({
        name: '问卷调查详情',
        menuId: id,
        to: '/question-detail',
        params: { id: id, mid: id }
      })
    },
    handleStatistical (row) {
      this.newTab({
        name: '问卷调查统计',
        menuId: row.id,
        to: '/questionnaire-Statistical',
        params: { id: row.id, mid: row.id, title: row.title }
      })
    }
  }
}
</script>
<style lang="scss">
.vote-manage {
  width: 100%;
  height: 100%;
  .qd-table-box {
    height: calc(100% - 186px);
  }
  .el-table .cell {
    overflow-y: visible !important;
  }
}
.hover-edit-botton .el-button {
  display: block;
  width: 100%;
  margin-left: 0px;
}
.el-popover {
  min-width: 100px;
}
</style>
