import JSZip from 'jszip'
import { saveAs } from 'file-saver'
import nbsp from '@SuggestBill/AdvisorManagement/AllSuggestions/nbsp'
export default {
  data () {
    return {
      pdflist: [],
      pdf: [],
      zip: null,
      index: null,
      pdfName: '建议导出PDF.zip',
      pdfIdIndex: null,
      pdfIdList: []
    }
  },
  inject: ['loadingprovide'],
  components: {
    nbsp
  },
  watch: {
    index (val) {
      if (val === this.pdflist.length) {
        var zipFile = this.zip.generate({ type: 'blob' })
        saveAs(zipFile, this.pdfName)
        this.zip = null
        this.index = null
        this.pdflist = []
        this.pdf = []
        this.loadingprovide(false)
        if (this.pdfIdIndex !== null) {
          setTimeout(() => {
            this.$message('正在继续生成PDF下载请不要关闭页面')
            this.loadingprovide(true)
            this.zip = new JSZip()
            this.pdfIdIndex = this.pdfIdIndex + 1
            this.pdfName = `第${(this.pdfIdIndex * 16) + 1}条---${(this.pdfIdIndex * 16) + this.pdfIdList[this.pdfIdIndex].length}条建议导出PDF.zip`
            this.pdflist = this.pdfIdList[this.pdfIdIndex]
            this.pdf = [this.pdflist[0]]
          }, 222)
        }
      } else if (val !== null) {
        this.pdf = [this.pdflist[this.index]]
      }
    }
  },
  methods: {
    PdfExport () {
      if (this.choose.length) {
        this.$confirm('此操作将把当前选中的建议导出为PDF, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.zip = new JSZip()
          this.loadingprovide(true)
          this.wordExport(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消导出'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据导出',
          type: 'warning'
        })
      }
    },
    async wordExport (ids) {
      const res = await this.$api.SuggestBill.wordExport({ ids: ids })
      console.log(res)
      var { data } = res
      var arr = data
      if (arr.length > 16) {
        var array = this.group(arr, 16)
        this.$confirm('当前共导出' + arr.length + '条提案，因生成的PDF过大，将为您分为' + array.length + '个压缩包导出，是否继续', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.pdfIdIndex = 0
          this.pdfIdList = array
          this.pdfName = `第${(this.pdfIdIndex * 16) + 1}条---${(this.pdfIdIndex * 16) + this.pdfIdList[this.pdfIdIndex].length}条建议导出PDF.zip`
          this.pdflist = this.pdfIdList[this.pdfIdIndex]
          this.pdf = [this.pdflist[0]]
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消导出'
          })
        })
      } else {
        this.pdflist = data
        this.pdf = [data[0]]
      }
    },
    group (array, subGroupLength) {
      let index = 0
      const newArray = []
      while (index < array.length) {
        newArray.push(array.slice(index, index += subGroupLength))
      }
      return newArray
    },
    callbackadd (base64, name) {
      this.index = this.index === null ? 1 : this.index + 1
      this.zip.file(`${name}.pdf`, base64, { base64: true })
    }
  }
}
