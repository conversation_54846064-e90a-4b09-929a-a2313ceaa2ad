jQuery Tree Plugin ---- zTree  
============
last verson :  3.5.35


**Donate to zTree** : http://www.treejs.cn/v3/donate.php


zTree API : http://www.treejs.cn/v3/api.php


zTree Demo : http://www.treejs.cn/v3/demo.php


Introduction of zTree (简介)
============
* zTree is a multi-functional "tree plug-ins." based on jQuery. The main advantages of zTree includes excellent performance, flexible configuration, and the combination of multiple functions.
(zTree 是一个依靠 jQuery 实现的多功能 “树插件”。优异的性能、灵活的配置、多种功能的组合是 zTree 最大优点。)

* zTree is a free tree plug-in and uses the MIT license. 
(zTree 是开源免费的软件, 使用 MIT 许可证)

* The code of zTree v3.x has been seperated according to the various functions. You can only load the code you need. 
(zTree v3.x 将核心代码按照功能进行了分割，不需要的代码可以不用加载)
* zTree v3.x uses delay loading technique, which can easily load tens of thousands of nodes in seconds even in IE6 browser. 
(采用了 延迟加载 技术，上万节点轻松加载，即使在 IE6 下也能基本做到秒杀)
* Compatible with IE, FireFox?, Chrome, Opera, Safari and other browsers. 
(兼容 IE、FireFox?、Chrome、Opera、Safari 等浏览器)
* Support for JSON data. 
(支持 JSON 数据)
* Support for static and asynchronous data loading node. 
(支持静态 和 Ajax 异步加载节点数据)
* Replace the skin / custom icon flexibllly. 
(支持任意更换皮肤 / 自定义图标)
* Support extremely flexible checkbox or radio selection function. 
(支持极其灵活的 checkbox 或 radio 选择功能)
* Provide enough incident response callback. 
(提供多种事件响应回调)
* Flexible editing (add / delete / change / search) functions, such as drag and drop nodes,you can even drag and drop multiple nodes. 
(灵活的编辑（增/删/改/查）功能，可随意拖拽节点，还可以多节点拖拽哟)
* Enable to generate multiple instances of zTree in one page. 
(在一个页面内可同时生成多个 Tree 实例)
* Simple parameters to achieve flexible configuration capabilities. 
(简单的参数配置实现 灵活多变的功能)
* To enhance performance, zTree transforms the js & css structure to provide excellent browser compatibility and make the development more easily
(zTree v3.x（JQuery Tree 插件），性能全面提升，js & css 架构全面调整，提供更好的兼容性和易开发性)


