<template>
  <div class="custom-project-details">
    <div class="custom-project-details-title">{{details.title}}</div>
    <div class="custom-project-details-xx">
      <div class="custom-project-details-source">发布人：{{name}}</div>
      <div class="custom-project-details-tiem">发布时间：{{details.publishTime}}</div>
    </div>
    <div class="cover">
      <div class="cover-text">封面图</div>
      <div class="cover-img"
           v-if="details.coverImg">
        <img :src="details.coverImg.fullUrl"
             alt="">
      </div>
    </div>
    <div class="theme">
      <div class="theme-text">主题图</div>
      <div class="theme-img"
           v-if="details.themeImg">
        <img :src="details.themeImg.fullUrl"
             alt="">
      </div>
    </div>
    <div class="custom-project-details-content"
         v-html="details.content"></div>
  </div>
</template>
<script>
export default {
  name: 'customTopicDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id', 'name'],
  mounted () {
    if (this.id) {
      this.customTopicListInfo()
    }
  },
  methods: {
    async customTopicListInfo () {
      const res = await this.$api.appManagement.customTopicListInfo(this.id)
      var { data } = res
      this.details = data
      // if (data.externalLinks) {
      //   window.open(data.externalLinks, '_blank')
      // }
    }
  }
}
</script>
<style lang="scss">
@import "./custom-topic-details.scss";
</style>
