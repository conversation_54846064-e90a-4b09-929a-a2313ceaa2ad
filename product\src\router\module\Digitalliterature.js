const Digitalliterature = [
  {
    path: '/numhome',
    name: 'numhome',
    component: () => import('../../views/Digitalliterature/Numhome/numhome.vue')
  },
  {
    path: '/Generalform',
    name: 'Generalform',
    component: () => import('../../views/Digitalliterature/Calligraphymanagement/Generalform')
  },
  {
    path: '/addgen',
    name: 'addgen',
    component: () => import('../../views/Digitalliterature/Calligraphymanagement/Generalform/addgen')
  },
  {
    path: '/Generalform-info',
    name: 'Generalform-info',
    component: () => import('../../views/Digitalliterature/Calligraphymanagement/Generalform/Generalform-info')
  },
  {
    path: '/Calligraphyinspection',
    name: 'Calligraphyinspection',
    component: () => import('../../views/Digitalliterature/Calligraphymanagement/Calligraphyinspection')
  },
  {
    path: '/Borrowinginformation',
    name: 'Borrowinginformation',
    component: () => import('../../views/Digitalliterature/Calligraphymanagement/Borrowinginformation')
  },
  {
    path: '/CalligraphyStoragelocation',
    name: 'CalligraphyStoragelocation',
    component: () => import('../../views/Digitalliterature/Calligraphymanagement/CalligraphyStoragelocation')
  },
  {
    path: '/CalligraphyClassification',
    name: 'CalligraphyClassification',
    component: () => import('../../views/Digitalliterature/Calligraphymanagement/CalligraphyClassification')
  },
  {
    path: '/Bookgeneralledger',
    name: 'Bookgeneralledger',
    component: () => import('../../views/Digitalliterature/Librarymanagement/Bookgeneralledger')
  },
  {
    path: '/books-info',
    name: 'books-info',
    component: () => import('../../views/Digitalliterature/Librarymanagement/Bookgeneralledger/books-info')
  },
  {
    path: '/addbooks',
    name: 'addbooks',
    component: () => import('../../views/Digitalliterature/Librarymanagement/Bookgeneralledger/addbooks')
  },
  {
    path: '/Bookinspection',
    name: 'Bookinspection',
    component: () => import('../../views/Digitalliterature/Librarymanagement/Bookinspection')
  },
  {
    path: '/Bookborrowing',
    name: 'Bookborrowing',
    component: () => import('../../views/Digitalliterature/Librarymanagement/Bookborrowing')
  },
  {
    path: '/BookStoragelocation',
    name: 'BookStoragelocation',
    component: () => import('../../views/Digitalliterature/Librarymanagement/BookStoragelocation')
  },
  {
    path: '/Bookclassification',
    name: 'Bookclassification',
    component: () => import('../../views/Digitalliterature/Librarymanagement/Bookclassification')
  },
  {
    path: '/Receptiontask',
    name: 'Receptiontask',
    component: () => import('../../views/Digitalliterature/Appointment/Receptiontask')
  },
  {
    path: '/Literaturehistoryappointment',
    name: 'Literaturehistoryappointment',
    component: () => import('../../views/Digitalliterature/Appointment/Literaturehistoryappointment')
  },
  // {
  //   path: '/Statisticalanalysis',
  //   name: 'Statisticalanalysis',
  //   component: () => import('../../views/Digitalliterature/Appointment/Statisticalanalysis')
  // },
  {
    path: '/Solicitationnotice',
    name: 'Solicitationnotice',
    component: () => import('../../views/Digitalliterature/Collectionmanagement/Solicitationnotice')
  },
  {
    path: '/addnotice',
    name: 'addnotice',
    component: () => import('../../views/Digitalliterature/Collectionmanagement/Solicitationnotice/addnotice')
  },
  {
    path: '/Literaturecollectionclues',
    name: 'Literaturecollectionclues',
    component: () => import('../../views/Digitalliterature/Collectionmanagement/Literaturecollectionclues')
  },
  {
    path: '/Visualization',
    name: 'Visualization',
    component: () => import('../../views/Digitalliterature/Visualization/')
  },
  // 资讯路由
  {
    path: '/Literatureinformation-list',
    name: '/Literatureinformation-list',
    component: () => import('../../views/Digitalliterature/Literatureinformation/Literatureinformation-list/Literatureinformation-list')
  },
  {
    path: '/Literatureinformation-column',
    name: '/Literatureinformation-column',
    component: () => import('../../views/Digitalliterature/Literatureinformation/Literatureinformation-column/Literatureinformation-column')
  },
  // 通知公告路由
  {
    path: '/noticemanagement',
    name: '/noticemanagement',
    component: () => import('../../views/Digitalliterature/notice/noticemanagement')
  },
  {
    path: '/noticeaddition',
    name: '/noticeaddition',
    component: () => import('../../views/Digitalliterature/notice/noticemanagement-news/noticeaddition')
  },
  // 文史馆可视化管理
  {
    path: '/historicalProposalManagement',
    name: '/historicalProposalManagement',
    component: () => import('../../views/Digitalliterature/literatureHistoryVisualization/historicalProposalManagement/historicalProposalManagement')
  },
  {
    path: '/historicalProposalManagementNew',
    name: '/historicalProposalManagementNew',
    component: () => import('../../views/Digitalliterature/literatureHistoryVisualization/historicalProposalManagement/historicalProposalManagementNew')
  },
  {
    path: '/CPPCCEncyclopediaManagement',
    name: '/CPPCCEncyclopediaManagement',
    component: () => import('../../views/Digitalliterature/literatureHistoryVisualization/CPPCCEncyclopediaManagement/CPPCCEncyclopediaManagement')
  },
  {
    path: '/CPPCCEncyclopediaManagementNew',
    name: '/CPPCCEncyclopediaManagementNew',
    component: () => import('../../views/Digitalliterature/literatureHistoryVisualization/CPPCCEncyclopediaManagement/CPPCCEncyclopediaManagementNew')
  },
  {
    path: '/proposalKnowledgeManagement',
    name: '/proposalKnowledgeManagement',
    component: () => import('../../views/Digitalliterature/literatureHistoryVisualization/proposalKnowledgeManagement/proposalKnowledgeManagement')
  },
  {
    path: '/proposalKnowledgeManagementNew',
    name: '/proposalKnowledgeManagementNew',
    component: () => import('../../views/Digitalliterature/literatureHistoryVisualization/proposalKnowledgeManagement/proposalKnowledgeManagementNew')
  },
  {
    path: '/visualizationConfiguration',
    name: '/visualizationConfiguration',
    component: () => import('../../views/Digitalliterature/literatureHistoryVisualization/visualizationConfiguration/visualizationConfiguration')
  }
]
export default Digitalliterature
