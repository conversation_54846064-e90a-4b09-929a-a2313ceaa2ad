.zy-menu-tree {
  background-color: #fff;
  width: 248px;
  height: 100%;
  min-width: 248px;
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;

  .menu-item {
    padding: 18px 0;
    padding-left: 24px;
    padding-right: 36px;
    font-size: $textSize16;
    line-height: 24px;
    display: block;
    border-left: 4px solid transparent;
    color: #000;
    cursor: pointer;

    &:hover {
      background-color: $zy-withColor;
    }
  }

  .menu-item-active {
    border-left: 4px solid $zy-color;
    background-color: $zy-withColor;
    color: $zy-color;
  }

  .menu-item-title {
    position: relative;

    .menu-icon {
      position: absolute;
      top: 50%;
      right: 12px;
      width: 24px;
      height: 24px;
      background: url('../../assets/img/menu-icon.png');
      transform: translateY(-50%) rotate(0deg);
      transition-duration: 0.4s;
    }

    .menu-icon-active {
      transform: translateY(-50%) rotate(-180deg);
      transition-duration: 0.4s;
    }
  }
}
