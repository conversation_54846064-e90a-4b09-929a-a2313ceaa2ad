<template>
  <div class="myBookcase">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input placeholder="请输入关键词"
                v-model="keyword"
                clearable
                @keyup.enter.native="search">
      </el-input>
      <el-select v-model="typeId"
                 filterable
                 clearable
                 placeholder="请选择分组">
        <el-option v-for="item in typeIdData"
                   :key="item.id"
                   :label="item.typeName"
                   :value="item.id">
        </el-option>
      </el-select>
    </screening-box>
    <el-scrollbar class="myBookcaseBox"
                  wrap-class="scrollbar-wrapper">
      <div class="myBookcaseList"
           v-infinite-scroll="load"
           infinite-scroll-distance="52"
           infinite-scroll-delay="520">
        <div class="myBookcaseListBox">
          <div class="myBookcaseItem"
               v-for="(item) in tableData"
               :key="item.id"
               @click="details(item)">
            <div class="myBookcaseItemImg">
              <img :src="item.coverImgUrl"
                   alt="">
            </div>
            <div class="myBookcaseItemBox">
              <div class="myBookcaseItemName">{{item.bookName}}</div>
              <div class="myBookcaseItemIntroduction">{{item.bookDescription}}</div>
              <div class="myBookcaseItemAuthor">
                <div class="myBookcaseItemAuthorText">{{item.authorName}}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="myBookcaseTetx"
             v-if="infinite">正在加载中...</div>
        <div class="myBookcaseTetx"
             v-else>没有更多了</div>
      </div>
    </el-scrollbar>
    <zy-pop-up v-model="detailsShow"
               title="书籍详情">
      <libraryDetails :id="id"> </libraryDetails>
    </zy-pop-up>
  </div>
</template>
<script>
import libraryDetails from '../library/libraryDetails'
export default {
  name: 'myBookcase',
  data () {
    return {
      keyword: '',
      typeId: '',
      typeIdData: [],
      infinite: false,
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      id: '',
      detailsShow: false
    }
  },
  components: {
    libraryDetails
  },
  mounted () {
    this.syMyTypeList()
    this.syMyBookList()
  },
  methods: {
    search () {
      this.page = 1
      this.pageSize = 10
      this.tableData = []
      this.syMyBookList()
    },
    reset () {
      this.keyword = ''
      this.typeId = ''
      this.page = 1
      this.pageSize = 10
      this.tableData = []
      this.syMyBookList()
    },
    async syMyTypeList () {
      const res = await this.$api.academy.syMyTypeList({})
      var { data } = res
      this.typeIdData = data
    },
    async syMyBookList () {
      const res = await this.$api.academy.syMyBookList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        typeId: this.typeId
      })
      var { data, total } = res
      this.tableData = this.tableData.concat(data || [])
      this.total = total
      this.infinite = false
    },
    load () {
      if (this.tableData.length < this.total) {
        this.infinite = true
        this.page = this.page + 1
        this.syMyBookList()
      }
    },
    details (row) {
      this.id = row.bookId
      this.detailsShow = true
    }
  }
}
</script>
<style lang="scss">
.myBookcase {
  width: 100%;
  height: 100%;
  .scrollbar-wrapper {
    overflow-x: hidden !important;
  }
  .myBookcaseBox {
    width: 100%;
    height: calc(100% - 64px);

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .is-vertical {
      .el-scrollbar__thumb {
        background-color: rgba(144, 147, 153, 0.8);
      }
    }

    .myBookcaseTetx {
      height: 52px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ccc;
      border-top: 1px solid #ededed;
      cursor: pointer;
    }

    .myBookcaseList {
      padding-left: 24px;
      padding-top: 24px;
      .myBookcaseListBox {
        display: flex;
        flex-wrap: wrap;
        .myBookcaseItem {
          display: flex;
          justify-content: space-between;
          margin-right: 24px;
          margin-bottom: 24px;
          width: 332px;
          height: 128px;
          cursor: pointer;
          .myBookcaseItemImg {
            height: 128px;
            width: 95px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .myBookcaseItemBox {
            width: 222px;
            height: 100%;
            position: relative;
            .myBookcaseItemName {
              color: #333;
              line-height: 21px;
              font-size: $textSize16;
              margin-bottom: 7px;
            }
            .myBookcaseItemIntroduction {
              line-height: 24px;
              color: #666;
              letter-spacing: 0.93px;
              height: 72px;
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 3;
              -webkit-box-orient: vertical;
              font-size: 13px;
            }
            .myBookcaseItemAuthor {
              position: absolute;
              left: 0;
              bottom: -2px;
              width: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;
              .myBookcaseItemAuthorText {
                font-size: 13px;
                color: #999;
                letter-spacing: 1px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
  }
}
</style>
