:root {
    --color: #72B9D0;
}

html{
	background: #fff;
}
*{
	box-sizing: border-box;
}
body {
	overflow: auto;
	margin: 0;
	padding: 32px 24px 24px 24px;
	box-sizing: border-box;
}

li {
	list-style: none outside none;
}

.box {
	width: 100%;
	height: 100%;
	position: relative;
	text-align: center;
}

p {
	font-size: 20px;
	color: #333;
	text-align: center;
	margin: 0 0 20px;
}

i.hander {
	display: block;
	width: 100%;
	height: 25px;
	background: #ccc;
	text-align: center;
	font-size: 12px;
	color: #333;
	line-height: 25px;
	font-style: normal;
}

.meetUl {
	margin: 0;
	color: #2E2E2E;
	padding: 0px 5px;
	text-align: center;
	background-color: #F2F2F2;
}

.meetUl li {
	margin-top: 0px;
	height: 50px;
	line-height: 50px;
	background-color: #F2F2F2;
	list-style-type: none;
	margin: 8px 0px;
	font-weight: bold;
	font-size: 15px;
	text-align: left;
	white-space: nowrap;
}

#addSets {
	width: 80px;
	height: 35px;
	margin-top: 15px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: white;
}

#setNum {
	width: 100px;
	height: 35px;
	margin-top: 40px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: white;
}

#reset {
	position: relative;
	top: 10px;
	left: 0;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: white;
}

#addSigns {
	float: left;
	margin-left: 5px;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #FFFFFF;
	text-align: center;
	cursor: pointer;
	color: #262626;
	border: 1px solid #D9D9D9;
}

#delSets {
	float: left;
	margin-left: 5px;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: white;
}

#saveSets {
	float: left;
	margin-left: 5px;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: white;
}

#allSel {
	float: left;
	margin-left: 5px;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #FFFFFF;
	text-align: center;
	cursor: pointer;
	color: #262626;
	border: 1px solid #D9D9D9;
}

#resortNo {
	float: left;
	margin-bottom: 10px;
	width: 110px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: #fff;
	border: 0px solid #D9D9D9;
}

#resortNumber {
	float: right;
	margin-bottom: 10px;
	width: 110px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #199BC5;
	text-align: center;
	cursor: pointer;
	color: #fff;
	border: 0px solid #D9D9D9;
}

#makePic {
	float: left;
	margin-left: 5px;
	width: 80px;
	height: 35px;
	line-height: 32px;
	border-radius: 2px;
	background-color: #FFFFFF;
	text-align: center;
	cursor: pointer;
	color: #262626;
	border: 1px solid #D9D9D9;
}

.btn-info:hover{
	background: var(--color) !important;
	border-color: var(--color) !important;
	color: #FFF !important;
}

.cancel:hover{
	border-color: var(--color) !important;
	color: var(--color) !important;
}

.textbox-focused{
	border-color:var(--color) !important;
}

.drag {
	position: absolute;
	/* border: 0px solid #000; */
	border-radius: 5px;
	/* background: #00A0EA; */
	font-size: 12px;
	cursor: move;
	display: flex;
	justify-content: center;
	align-items: center;
	/*background-image: url(../img/border.svg);
	background-repeat: no-repeat;
	background-size: 100% 100%;*/
}

.signDrag {
	position: absolute;
	border: 0px solid #FFFFFF;
	border-radius: 0px;
	background: #FFFFFF;
	cursor: move;
	display: flex;
	justify-content: center;
	align-items: center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.selected {
	background-color: #e46424;
	border-color: #e46424;
}

.dot {
	position: absolute;
	width: 20px;
	height: 20px;
	background: #F00;
}

.set_content {
	display: none;
	position: absolute;
	width: 240px;
	height: 200px;
	border: 2px solid #bdcbd4;
	background-color: #d5d8e0;
	opacity: 0.9;
	z-index: 1002;
	overflow-x: hidden;
	overflow-y: auto;
}

.signs_content {
	display: none;
	position: absolute;
	width: 260px;
	height: 80px;
	border: 2px solid #e1e9ec;
	background-color: #e1e9ec;
	padding: 5px;
	opacity: 1;
	z-index: 1002;
	text-align: center;
	overflow-x: hidden;
	overflow-y: auto;
}

#areaTipFixed {
	position: fixed;
	top: 68px;
	right: 21px;
	z-index: 100;
	text-align: right;
	width: 81%;
	background: #FFF;
}

.areaTip {
	position: absolute;
	top: 0;
	right: 30px;
	z-index: 100;
	text-align: right;
}

.inputText{
	background:linear-gradient(0deg,rgba(255,255,255,1) 0%,rgba(249,250,251,1) 100%);
	border:1px solid rgba(217, 217, 217, 1);
	box-shadow:0px 1px 2px 0px rgba(0, 0, 0, 0.15);
	border-radius:2px;
	outline: none;
}

.rmkBox{
	width:20px;
 	height:25px; 
 	cursor: pointer;
	background: var(--color);
	margin: 4px 8px;
	border-radius: 3px;
}

.opt {
    height: 30px;
    line-height: 20px;
}

.magic-radio, .magic-checkbox {
    position: absolute;
    display: none;
}

.magic-radio + label, .magic-checkbox + label {
    position: relative;
    display: block;
    padding-left: 30px;
    cursor: pointer;
}

.magic-radio:checked + label:before {
    border: 1px solid var(--color);
}

.magic-radio:checked + label:before, .magic-checkbox:checked + label:before {
    animation-name: none;
}
.magic-radio + label:before {
    border-radius: 50%;
}
.magic-radio + label:before, .magic-checkbox + label:before {
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block;
    width: 20px;
    height: 20px;
    content: '';
    border: 1px solid #c0c0c0;
}

.magic-radio:checked + label:after, .magic-checkbox:checked + label:after {
    display: block;
}
.magic-radio + label:after {
    top: 7px;
    left: 7px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color);
}
.magic-radio + label:after, .magic-checkbox + label:after {
    position: absolute;
    display: none;
    content: '';
}