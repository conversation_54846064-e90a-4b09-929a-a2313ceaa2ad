.screening-box {
  height: 68px;
  min-height: 68px;
  display: flex;
  flex-wrap: wrap;
  padding-top: 14px;
  // border-bottom: 1px solid #e6e6e6;
  box-sizing: border-box;
  border-radius: 10px;
  background-color: #fff;
  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);
  overflow: hidden;

  .el-input {
    width: 222px;
  }

  .el-input {
    margin-left: 24px;
    margin-bottom: 12px;
  }

  .el-select {
    margin-left: 24px;
    margin-bottom: 12px;

    .el-input {
      margin-left: 0;
      margin-bottom: 0;
    }
  }

  .zy-tree-select {
    width: 222px;
    margin-left: 24px;
    margin-bottom: 12px;

    .el-input {
      margin-left: 0;
      margin-bottom: 0;
    }
  }

  .zy-select {
    margin-left: 24px;
    margin-bottom: 12px;

    .el-input {
      margin-left: 0;
      margin-bottom: 0;
    }
  }

  .el-date-editor {
    margin-left: 24px;
    margin-bottom: 12px;
  }

  .screening-checkbox {
    width: 222px;
    height: 40px;
    margin-bottom: 12px;
    margin-left: 24px;
    display: flex;
    align-items: center;
  }

  .screening-button {
    width: 246px;
    flex-shrink: 0;

    .el-button {
      height: 40px;
      margin-left: 24px;
      padding: 0 16px;
    }

    .el-button + .el-button {
      margin-left: 16px;
    }

    .el-button--text {
      margin-left: 9px;
      font-size: $textSize14;
      padding: 0;

      .el-icon-arrow-down {
        transition-duration: 0.4s;
        transform: rotate(0);
      }

      .el-icon-arrow-down-a {
        transition-duration: 0.4s;
        transform: rotate(-180deg);
      }
    }
  }
}
