const wisdomUserManagement = () => import('@/views/wisdomWarehouse/user-management/user-management')
const sinceSituation = () => import('@/views/wisdomWarehouse/SinceSituation/SinceSituation')
const memberDetailsQd = () => import('@/views/wisdomWarehouse/user-management/memberDetailsQd')
const committeeDataCustomTopic = () => import('@/views/wisdomWarehouse/general-custom-topic/general-custom-topic')
const CompilationColumn = () => import('@/views/wisdomWarehouse/CompilationColumn/CompilationColumn')

const systemSettings = [
  { // 人员管理
    path: '/wisdomUserManagement',
    name: 'wisdomUserManagement',
    component: wisdomUserManagement
  },
  { // 人员详情
    path: '/memberDetailsQd-ww',
    name: 'memberDetailsQd-ww',
    component: memberDetailsQd
  },
  { // 履职情况
    path: '/sinceSituation-ww',
    name: 'sinceSituation-ww',
    component: sinceSituation
  },
  { // 汇编详情
    path: '/committeeDataCustomTopic-ww',
    name: 'committeeDataCustomTopic-ww',
    component: committeeDataCustomTopic
  },
  { // 汇编栏目
    path: '/compilationColumn',
    name: 'compilationColumn',
    component: CompilationColumn
  }
]
export default systemSettings
