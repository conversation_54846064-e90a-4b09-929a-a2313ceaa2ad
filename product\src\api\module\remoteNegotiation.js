// 导入封装的方法
import { post, get } from '../http'
export default {
  meetting: {
    list(params) {
      return post('/zyMeeting/list', params)
    },
    add(params) {
      return post('/zyMeeting/add', params)
    },
    edit(params) {
      return post('/zyMeeting/edit', params)
    },
    info(id) {
      return get(`/zyMeeting/info/${id}`)
    },
    dels(ids) {
      return post('/zyMeeting/dels', { ids })
    },
    addJoinUser(params) {
      return post('/zyMeeting/saveParticipants', params)
    },
    // 是否全场静音
    allMute(params) {
      return post('/zyMeeting/muteConferences', params)
    },
    timeInfo(id) {
      return post('/zyMeeting/realTimeInfo', { id })
    },
    sendMsg(ids) {
      return post('/zyMeeting/toSendSms', { ids })
    },
    // 是否启用录制
    record(params) {
      return post('/zyMeeting/recordConferences', params)
    },
    // 否允许与会者自己解除静音
    canUserMute(params) {
      return post('/zyMeeting/guestUnMute', params)
    },
    // 用户单个静音
    userSelfMute(params) {
      return post('/zyMeeting/muteAccount', params)
    }
  },
  terminal: {
    list(params) {
      return post('/meeting/terminal/list', params)
    },
    add(params) {
      return post('/meeting/terminal/add', params)
    },
    edit(params) {
      return post('/meeting/terminal/edit', params)
    },
    info(id) {
      return get(`/meeting/terminal/info/${id}`)
    },
    dels(ids) {
      return post('/meeting/terminal/dels', { ids })
    }
  }
}
