.zy-cascader-checkbox {
  width: 100%;
  height: 40px;

  .zy-cascader-checkbox-input {
    width: 100%;
    height: 40px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    padding-right: 40px;
    overflow: hidden;
    position: relative;

    .zy-cascader-checkbox-input-text {
      color: #999;
      font-size: $textSize14;
    }

    .zy-cascader-checkbox-input-icon {
      position: absolute;
      top: -1px;
      right: -1px;
      z-index: 2;
      height: 40px;
      width: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;

      .el-icon-arrow-down {
        color: #999;
        transition-duration: 0.4s;
        transform: rotate(0);
      }
    }

    .zy-cascader-checkbox-input-icon-a {
      .el-icon-arrow-down {
        transition-duration: 0.4s;
        transform: rotate(-180deg);
      }
    }

    .el-tag + .el-tag {
      margin-left: 12px;
    }

    &:hover {
      border-color: #c0c4cc;
    }

    &:focus {
      border-color: $zy-color;
    }
  }

  .zy-cascader-checkbox-input-disabled {
    background-color: #e6e5e8;
    border-color: #e4e7ed !important;
    cursor: not-allowed;

    .zy-cascader-checkbox-input-icon {
      background-color: #e6e5e8;
    }
  }
}

.zy-cascader-checkbox-popover {
  padding: 0;

  .zy-cascader-checkbox-box-box {
    height: 260px;
    width: 100%;

    .el-scrollbar__wrap {
      overflow-x: hidden;

      .el-input {
        width: calc(100% - 0.5px);
      }
    }
  }

  .zy-cascader-checkbox-box {
    height: 220px;
    width: 100%;

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}

.zy-cascader-checkbox-tree {
  display: inline-block !important;
  min-width: 100%;

  .el-tree-node__content {
    height: 40px;
    line-height: 40px;

    &:hover {
      background-color: #e0eaf2;
    }
  }
}
