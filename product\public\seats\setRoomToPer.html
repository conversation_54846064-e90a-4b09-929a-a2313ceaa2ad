<html>

	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>会议排座</title>
		<!-- 引用css -->
		<link href="plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
		<link href="css/style.css" rel="stylesheet">
		<link rel="stylesheet" href="css/base.css">
		<link rel="stylesheet" href="plugins/layui/css/layui.css">
		<link rel="stylesheet" href="js/icheck/skins/all.css">
		<link rel="stylesheet" href="js/ztree/zTreeStyle/zTreeStyle.css">
		<link rel="stylesheet" href="plugins/easyui-1.7.0/themes/default/easyui.css">
		<link rel="stylesheet" href="plugins/easyui-1.7.0/themes/icon.css">
		<link rel="stylesheet" href="css/setRoomToPer.css">
		<style type="text/css">
			* {
				font-size: 14px !important;
			}
		</style>
	</head>

	<body>

		<div id="setsDiv" style="height: 100%;background: rgb(255, 255, 255);min-width: 734px;">
			<div style="width: 100%;padding: 20px; text-align: center;">
				<select id="ruleSelect" class="easyui-combobox custom-select" data-options="editable:false"
					panelHeight="240px" name="state" style="width:208px; height: 44px; margin-left: 20px;">
					<option value="">请选择排座方式</option>
					<option value="1">连续排座</option>
					<option value="2">隔座排座</option>
				</select>
				<div class="btn-group show toRight" style="margin-left: 20px;">
					<button id="checkPer" type="button" class="btn cancel dropdown-toggle"
						aria-haspopup="true" aria-expanded="true">
						选择人员
					</button>
				</div>
				<div class="btn-group show toRight">
					<button id="addOutSidePer" type="button" class="btn cancel dropdown-toggle" data-toggle="dropdown"
						aria-haspopup="true" aria-expanded="true">
						添加系统外人员
					</button>
				</div>
				<button type="button" class="btn btn-info" style="margin-left: 20px;" id="bw">补位</button>
				<button type="button" class="btn btn-info" id="dd">座位对调</button>
				<button type="button" class="btn cancel toRight" style="margin-left: 20px;" id="ylSeats">座位预留</button>
				<button type="button" class="btn cancel toRight" id="ylCancel">取消预留</button>
				<div class="btn-group show toRight">
				  <button id="downloadBtn" type="button" class="btn cancel dropdown-toggle" data-toggle="dropdown"
				    aria-haspopup="true" aria-expanded="true">
				    下载导入模版
				    <img src="img/minus.png">
				  </button>
				  <div class="dropdown-menu" x-placement="bottom-start"
				    style="position: absolute; transform: translate3d(0px, 33px, 0px); top: 0px; left: 0px; will-change: transform; text-align: center;min-width: 136px;">
				    <a class="dropdown-item showSortNo" onclick="downTemplateByType(1);">报名人员</a>
				    <a class="dropdown-item showSeatNo" onclick="downTemplateByType(0);">邀请人员</a>
				  </div>
				</div>
				<div class="btn-group show toRight">
					<button id="importPer_a" type="button" class="btn cancel dropdown-toggle" data-toggle="dropdown"
						aria-haspopup="true" aria-expanded="true">
						导入
					</button>
				</div>
				<button type="button" class="btn toRight" style="margin-left: 20px;" id="deleteSeats">清空</button>
				<button type="button" class="btn cancel toRight" id="clearAll">全部清空</button>
				<button type="button" class="btn btn-info" style="margin-left: 20px;" id="exportSeatInfo">导出排座信息</button>
				<button type="button" class="btn btn-info" style="margin-left: 20px;" id="saveSeats">保存</button>
				<button type="button" class="btn btn-info" id="release">发布</button>
			</div>
			<div class="box" onmousedown="boxOnMouse();"
				style="position: relative; overflow: hidden;text-align: center;">
				<p class="bjBox"></p>
				<span class="releaseTip">发布状态</span>
				<label id="meetName"></label>
				<span class="areaTip"></span>
			</div>
		</div>

		<!--begain 部门选择弹出框-->
		<div id="deptWin" class="dept_content">
			<ul id="treeDemo" class="ztree" style="overflow: hidden;"></ul>
		</div>
		<!--end-->

		<div id="cloneDiv"></div>
		<div id="images" style="display: none;"></div>

		<!----------搜索--------->
		<div class="modal fade" id="winModal" tabindex="-1" role="dialog" aria-labelledby="winModalLabel"
			aria-hidden="true" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<span class="modal-title" id="winModalLabel" style="font-size: 14px;">搜索</span>
						<div class="close" data-dismiss="modal" aria-label="Close"><img src="img/icon_close.png" alt="">
						</div>
					</div>
					<div class="modal-body">
						<div>
							<span>姓名</span>
							<input type="text" id="personName" placeholder="请输入姓名" style="width: 504px;"
								autocomplete="off">
						</div>
						<div>
							<span>联系电话</span>
							<input type="text" id="phone" placeholder="请输入手机号码" style="width: 504px;"
								autocomplete="off" />
						</div>
						<div>
							<span>部门</span>
							<div style="position: relative; right: 28px;">
								<input id="deptId" type="hidden" />
								<input id="deptName" class="form-control meetInfo" style="width: 296px;" type="text"
									readonly="readonly" placeholder="请选择选项">
								<img src="img/select.png" id="organDel" class="selectIco"></img>
							</div>
						</div>
						<div>
							<span>界别</span>
							<div style="position: relative; right: 28px;">
								<select id="circlesSelect" class="easyui-combobox custom-select"
									data-options="editable:false" panelHeight="240px" name="state"
									style="width:296px; height: 44px;">

								</select>
							</div>
						</div>
						<div>
							<span>组别</span>
							<div style="position: relative; right: 28px;">
								<select id="groupSelect" class="easyui-combobox custom-select"
									data-options="editable:false" panelHeight="240px" name="state"
									style="width:296px; height: 44px;">

								</select>
							</div>
						</div>
						<div>
							<span>人员类型</span>
							<div style="position: relative; right: 28px;">
								<select id="pTypeSelect" class="easyui-combobox custom-select"
									data-options="editable:false" panelHeight="140px" name="state"
									style="width:296px; height: 44px;">
									<option value="">全部人员</option>
									<option value="1">列席人员</option>
									<option value="0">非列席人员</option>
								</select>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<label id="confirmBtn" class="btn-info">确定</label>
						<label id="resetParam" class="cancel">重置</label>
					</div>
				</div>
			</div>
		</div>

		<!----------发送通知--------->
		<div class="modal fade" id="msgModal" tabindex="-1" role="dialog" aria-labelledby="winModalLabel"
			aria-hidden="true" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<span class="modal-title" id="winModalLabel" style="font-size: 14px;">发送通知</span>
						<div class="close" data-dismiss="modal" aria-label="Close"><img src="img/icon_close.png" alt="">
						</div>
					</div>
					<div class="modal-body">
						<div>
							<span><i style="color: red; padding-right: 10px;">*</i>发送方式</span>
							<input type="checkbox" name="vehicle" value="Car" checked="checked" disabled />
							<label style="line-height: 43px; margin-left: 10px;">短信</label>
						</div>
						<div>
							<span><i style="color: red; padding-right: 10px;">*</i>请选择模版</span>
							<div>
								<select id="tempSelect" class="easyui-combobox custom-select"
									data-options="editable:false" panelHeight="240px" name="state"
									style="width:296px; height: 44px;">

								</select>
							</div>
						</div>
						<div>
							<span><i style="color: red; padding-right: 10px;">*</i>内容</span>
							<textarea class="form-textarea" rows="5" id="msgContent"
								style="width: 80%; border: 1px solid rgba(217, 217, 217, 1); padding: 5px; color: rgb(154 154 154); border-radius: 5px;"></textarea>
						</div>
					</div>
					<div class="modal-footer">
						<label id="confirmSendBtn" class="btn-info">确定</label>
						<label id="closeModal" class="cancel">取消</label>
					</div>
				</div>
			</div>
		</div>

		<!----------下载模版--------->
		<div class="modal fade" id="tempModal" tabindex="-1" role="dialog" aria-labelledby="winModalLabel"
			aria-hidden="true" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<span class="modal-title" id="winModalLabel" style="font-size: 14px;">下载模版</span>
						<div class="close" data-dismiss="modal" aria-label="Close"><img src="img/icon_close.png" alt="">
						</div>
					</div>
					<div class="modal-body tempBody" style="display: inline-block;">

					</div>
					<div class="modal-footer">
						<label id="downBtn" class="btn-info">确定</label>
						<label id="closeTempModal" class="cancel">取消</label>
					</div>
				</div>
			</div>
		</div>

		<!--begain 关联驻地弹出框-->
		<div id="rsdWin" class="rsd_content">
			<div id="rsdList">

			</div>
			<div class="modal-footer btn-footer">
				<label id="confirmRsd" class="btn-info">确定</label>
				<label id="cancelRsd" class="cancel">关闭</label>
			</div>
		</div>
		<!--end-->

		<div class="modal fade" id="infoModal" tabindex="-1" role="dialog" aria-labelledby="winModalLabel"
			aria-hidden="true" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<span class="modal-title" id="winModalLabel" style="font-size: 14px;"><img src="img/notice.png">提示</span>
						<div class="close" data-dismiss="modal" aria-label="Close"><img src="img/icon_close.png" alt="">
						</div>
					</div>
					<div class="modal-body">
						<div style="width: 100%; justify-content: center;"><span ><img src="img/sucsses.png"><label id="noticeInfo"></label></span></div>
						<label class="infoTitle">以下人员非本次会议邀请人员，无法安排座位。</label>
						<div id="noMeetList">
							
						</div>
						<label class="infoTitle">以下人员重名，请填写手机号，否则无法安排座位。</label>
						<div id="nameRepeat">
							
						</div>
						<label class="infoTitle">以下人员导入是座位号不存在，无法安排座位。</label>
						<div id="noSeatNo">
							
						</div>
						<label class="infoTitle">以下人员为系统外人员，无法安排座位。</label>
						<div id="noNameList">
							
						</div>
					</div>
					<div class="modal-footer">
						<label id="confirmBtn" class="btn-info" data-dismiss="modal">确定</label>
						<label id="resetParam" class="cancel" data-dismiss="modal">取消</label>
					</div>
				</div>
			</div>
		</div>

		<!-- 授权查看弹出框 -->
		<div class="modal fade" id="grantModal" tabindex="-1" role="dialog" aria-labelledby="winModalLabel"
			aria-hidden="true" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<span class="modal-title" id="winModalLabel" style="font-size: 14px;">授权查看人员</span>
						<div class="close" data-dismiss="modal" aria-label="Close"><img src="img/icon_close.png" alt="">
						</div>
					</div>
					<div class="modal-body itemBody">
						<div class="bodyItem" style="display: inline-flex;">
							<div style="width: 45%; margin-right: 15px;">
								<p style=" text-align: left; margin-bottom: 5px;">被授权人</p>
								<input id="firstGrant" type="text" class="gPer" placeholder="被授权人" style="width: 100%;"
									readonly="readonly">
							</div>
							<div style="width: 45%; margin-right: 15px;">
								<p style=" text-align: left; margin-bottom: 5px;">可查看范围</p>
								<input id="firstRange" type="text" class="gRPer" style="width: 100%;"
									readonly="readonly">
							</div>
							<label class="addItem"
								style="padding-top: 28px;font-size: 24px !important;padding-right: 10px;">+</label>
							<label class="delItem" style="padding-top: 27px;font-size: 35px !important;">-</label>
						</div>
					</div>
					<div class="modal_btn">
						<label id="saveGrantBtn" class="btn-info">确定</label>
						<label id="cancelGrant" class="cancel" data-dismiss="modal">取消</label>
					</div>
				</div>
			</div>
		</div>

		<!-- 选择人员弹出框 -->
		<div class="modal fade" id="perModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel"
			aria-hidden="true" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h4 class="modal-title" id="modalLabel">常用人员列表</h4>
						<div class="close perClose" data-dismiss="modal" aria-label="Close" style="font-size: 18px;">
							<img src="img/icon_close.png" alt="">
						</div>
					</div>
					<div class="modal-body">
						<div class="modal_left">
							<div class="modal_search">
								<input type="text" id="perModalSearch" placeholder="搜索人员名字" autocomplete="off">
								<img src="img/search.png" alt="">
							</div>
							<div class="modal_type" style="display: flex;">
								<div class="radioBox" style="display: flex;width: 280px;height: 30px;">
									<div class="radio radio-info" style="padding-left: 0;">
										<input type="radio" name="treeType" id="isJg_b" value="1" checked="checked"
											style="height: 30px;">
										<label class="radioLabel" for="isJg_b"> 按组织机构显示 </label>
									</div>
									<div class="radio radio-info" id="zb_btn">
										<input type="radio" name="treeType" id="isZb_b" value="2" style="height: 30px;">
										<label class="radioLabel" for="isZb_b"> 按组别显示 </label>
									</div>
								</div>
							</div>
							<div class="modal_main">
								<div class="modal_main_l">
									<p style="padding-left:16px" class="modal_title">选择机构</p>
									<ul id="treeLeft" class="ztree"
										style="overflow-y: auto;width: auto;min-width: 200px;">
									</ul>
								</div>
								<div class="modal_main_r">
									<p class="modal_title">人员列表</p>
									<label id="allcheck" class="unCheck"><i id="allCheckChild" class=""></i></label>
									<div class="modal_main_r_box" style="overflow-y: auto;">
									</div>
								</div>
							</div>
						</div>
						<div class="modal_right">
							<p class="modal_title">已选择（<label id="userNum">0</label>）人</p>
							<img class="allDel" src="img/icon_close.png">
							<div class="checkUsrBox" style="overflow-y: auto;height: 360px;">
							</div>
						</div>
					</div>
				</div>
				<div class="modal_btn">
					<label id="saveUser" class="btn-info">确认</label>
					<label data-dismiss="modal" class="cancel perClose">取消</label>
				</div>
			</div>
		</div>
		<!-- 选择人员弹出框 end -->

		<!-- 引用js -->
		<script src="plugins/axios/axios.min.js"></script>
		<script src="plugins/axios/qs.min.js"></script>
		<script src="plugins/jquery/jquery.min.js "></script>
		<script src="plugins/bootstrap/js/popper.min.js "></script>
		<!-- <script src="plugins/bootstrap/js/bootstrap.min.js "></script> -->
		<!-- <script type="text/javascript" src="js/drag/jquery.event.drag-2.2.js"></script>
		<script type="text/javascript" src="js/drag/jquery.event.drag.live-2.2.js"></script>
		<script type="text/javascript" src="js/drag/jquery.event.drop-2.2.js"></script>
		<script type="text/javascript" src="js/drag/jquery.event.drop.live-2.2.js"></script> -->
		<script type="text/javascript" src="js/drag/excanvas.min.js"></script>
		<script type="text/javascript" src="js/ztree/js/jquery.ztree.core.js"></script>
		<script type="text/javascript" src="js/ztree/js/jquery.ztree.excheck.js"></script>
		<script type="text/javascript" src="js/icheck/icheck.js"></script>
		<script type="text/javascript" src="plugins/layui/layer/layer.js"></script>
		<script type="text/javascript" src="js/layui/layui.js"></script>
		<script src="plugins/easyui-1.7.0/jquery.easyui.min.js"></script>
		<script type="text/javascript" src="js/base.js"></script>
		<script type="text/javascript" src="js/setRoomToPer.js"></script>
		<script type="text/javascript" src="js/setRoomToPerMin.js"></script>
		<script type="text/javascript" src="js/setRoomToPerMinChkPer.js"></script>
		<!-- html2canvas将Dom节点在Canvas里边画出来 -->
		<script src="js/createPic/html2canvas.js"></script>
		<!-- 将canvas图片保存成图片 -->
		<!-- <script src="js/createPic/moment.js"></script> -->
		<script src="js/createPic/canvas2image.js"></script>
		<script src="js/createPic/base64.js"></script>
	</body>

</html>
