@import './reset.scss';

.fl {
  float: left;
}

.fr {
  float: right;
}

.clearfix:after {
  content: '';
  display: block;
  clear: both;
}

.clearfix {
  zoom: 1;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

html,
body,
#app {
  height: 100%;
  overflow: hidden;
}

div {
  color: #262626;
  box-sizing: border-box;
}

.mt-15 {
  margin-top: 15px;
}

// 所有浏览器隐藏滚动条样式
.scrollBar {
  /*IE下隐藏滚动条*/
  -ms-scroll-chaining: chained;
  -ms-overflow-style: none;
  -ms-content-zooming: zoom;
  -ms-scroll-rails: none;
  -ms-content-zoom-limit-min: 100%;
  -ms-content-zoom-limit-max: 500%;
  // -ms-scroll-snap-type: proximity;
  -ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);
  -ms-overflow-style: none;
  overflow: auto;
  /*火狐下隐藏滚动条*/
  scrollbar-width: none;

  /*谷歌下隐藏滚动条*/
  &::-webkit-scrollbar {
    display: none;
  }
}

.el-date-table td span {
  border-radius: 0 !important;
}

.el-date-table td.today span {
  border: 1px solid #0473de;
}

// 由于优先等级问题写在这里 自定义 el-tooltip 样式
.el-tooltip__popper.is-light.tooltip {
  width: 177px;
  min-height: 60px;
  padding: 16px;
  box-sizing: border-box;
  background: #f2f6f9;
  border-radius: 2px;
  border-color: #dbdbdb;
}

// 箭头向上
.el-tooltip__popper.is-light[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: #dbdbdb;
  &::after {
    border-bottom-color: #f2f6f9;
  }
}

// 箭头向下
.el-tooltip__popper.is-light[x-placement^='top'] .popper__arrow {
  border-top-color: #dbdbdb;
  &::after {
    border-top-color: #f2f6f9;
  }
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.beyond {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.beyond3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

::-webkit-input-placeholder {
  color: #8c8c8c !important;
}

::-webkit-textarea-placeholder {
  color: #8c8c8c !important;
}

:-moz-placeholder {
  color: #8c8c8c !important;
}

::-moz-placeholder {
  color: #8c8c8c !important;
}

:-ms-input-placeholder {
  color: #8c8c8c !important;
}

:-ms-textarea-placeholder {
  color: #8c8c8c !important;
}

.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}

.flex-y-center {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -ms-grid-row-align: center;
  align-items: center;
}

// 防止多次点击选中
.cannotselect {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.hover-active:hover {
  opacity: 0.7;
}

.blue-btn {
  background: #0473de;
  border-radius: 2px;
}

.gray-btn {
  border-radius: 2px;
  border: 1px solid rgba(217, 217, 217, 1);
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(249, 250, 251, 1) 100%
  );
  border-radius: 2px;
}

.router-fade-enter-active,
.router-fade-leave-active {
  transition: opacity 0.3s;
}

.router-fade-enter,
.router-fade-leave-active {
  opacity: 0;
}

.el-select-dropdown__item {
  height: 40px !important;
  line-height: 40px !important;

  &:hover {
    background-color: #e0eaf2 !important;
  }
}

.proposal-home {
  .el-progress {
    .el-progress-bar__outer {
      background-color: #f9cbba;
    }
  }
}
.xyl-sliding {
  width: 100%;
  margin: auto;

  .xyl-sliding-wrap {
    width: 100%;
    overflow: hidden;

    &:after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 2px;
      background-color: #e4e7ed;
      z-index: 2;
    }

    .xyl-sliding-prev,
    .xyl-sliding-next {
      position: absolute;
      top: 2px;
      height: calc(100% - 4px);
      width: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      cursor: pointer;
      z-index: 9;
    }

    .xyl-sliding-prev {
      left: 0;
    }

    .xyl-sliding-next {
      right: 0;
    }

    .xyl-sliding-scroll {
      white-space: nowrap;
      position: relative;
      transition: transform 0.3s;
      float: left;
      display: block;
      z-index: 3;

      .xyl-sliding-active-bar {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 2px;
        background-color: $zy-color;
        transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        list-style: none;
      }

      .xyl-sliding-item {
        display: inline-block;
        height: 40px;
        line-height: 40px;
        cursor: pointer;
        margin-right: 38px;
      }

      .is-active {
        color: $zy-color;
      }
    }
  }
}
