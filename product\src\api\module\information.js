// 导入封装的方法
import {
  get,
  post,
  postform,
  exportFile
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
export const exe = params => exportFile('', params)

const information = {
  // 信息列表
  infomanagelist (params) {
    return post('/infomanage/list', params)
  },
  // 我的信息列表
  myInformationList (params) {
    return post('/infomanage/myInfoList', params)
  },
  infomanageinfo (params) { // 信息详情
    return post(`/infomanage/info/${params}`)
  },
  infomanageeditInfo (params) { // 处理信息
    return post('/infomanage/editInfo', params)
  },
  infomanageeditdels (params) { // 删除信息
    return post('/infomanage/dels', params)
  },
  infomanageeditadd (params) { // 新增信息
    return post('/infomanage/add', params)
  },
  infomanageeditedit (params) { // 新增信息
    return post('/infomanage/edit', params)
  },
  getHandleType (params) { // 新增信息
    return post('/infomanage/getHandleType', params)
  },
  infomanagereportName (params) { // 自动获取报送人姓名或报送单位接口
    return post('/infomanage/reportName', params)
  },
  analyseyear (params) { // 查询信息管理年度统计列表接口
    // return post('/infomanage/analyse/year', params)
    return post('/infomanage/analyse/analyseYearDetail', params)
  },
  analysetype (params) { // 获取信息管理处理类型统计列表接口
    return post('/infomanage/analyse/type', params)
  },

  analysereportType (params) { // 获取信息管理报送类型（单位|用户）统计列表接口
    return post('/infomanage/analyse/reportTypeByOrg', params)
    // return post('/infomanage/analyse/reportType', params)
  },
  // 信息管理用户统计列表
  analysereportTypeByUser (params) {
    return post('/infomanage/analyse/reportTypeByUser', params)
  },
  // 推送信息列表
  infomanitionPushlist (params) {
    return get('/infomanagejoinuser/list', params)
  },
  // 获取已推送用户列表
  informationPushUserList (id) {
    return get('/infomanagejoinuser/getPushInfoManageUsers', { dataId: id })
  },
  // 信息推送
  informationPushAdd (params) {
    return post('/infomanagejoinuser/pushInfoManage', params)
  },
  // 信息发布
  informationPublish (params) {
    return post('/infomanage/publish', params)
  },
  // 信息接收
  informationAccept (id) {
    return post('/infomanagejoinuser/getPushInfoManage', { dataId: id })
  },
  // 信息存储
  infomationSaveFile(params) {
    return post('/infomanage/saveFile', params)
  }
}

export default information
