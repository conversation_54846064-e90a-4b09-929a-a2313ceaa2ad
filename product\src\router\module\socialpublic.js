const socialpublicoption = [

  {
    path: '/socialIndex',
    name: 'socialIndex'
  },
  {
    path: '/detailComment',
    name: 'detailComment'
  },
  {
    path: '/submitSocialpublic', // 提交
    name: 'submitSocialpublic',
    parent: 'info'
  },
  {
    path: '/submitSocialpublicOne', // 编辑
    name: 'submitSocialpublicOne',
    parent: 'info'
  },
  {
    path: '/newspapersSubmit', // 上报
    name: 'newspapersSubmit',
    parent: 'info'
  },
  {
    path: '/provinceSubmitSocialpublic', // 省提交
    name: 'provinceSubmitSocialpublic',
    parent: 'info'
  },
  {
    path: '/provinceCompanysocialpulics', // 省我的社情民意信息
    name: 'provinceCompanysocialpulics',
    parent: 'info'
  },
  {
    path: '/provinceMysocialpubics', // 省单位社情民意信息
    name: 'provinceMysocialpubics',
    parent: 'info'
  },
  {
    path: '/socialpublicdrafts', // 草稿箱
    name: 'socialpublicdrafts',
    parent: 'info'
  },
  {
    path: '/Mysocialpubic', // 我的社情民意信息
    name: 'Mysocialpubic',
    parent: 'info'
  },
  {
    path: '/statisticalAnalysis', // 统计分析
    name: 'statisticalAnalysis',
    parent: 'info'
  },
  {
    path: '/ScoreStatistics', // 来稿得分统计
    name: 'ScoreStatistics',
    parent: 'info'
  },
  {
    path: '/StatisticalManagement',
    name: 'StatisticalManagement'
  },

  {
    path: '/editconfig', //  编辑人配置
    name: 'editconfig'
  },

  {
    path: '/Companysocialpulic',
    name: 'Companysocialpulic',
    parent: 'info'
  },
  {
    path: '/Allsocialpublic', // 所有社情民意信息
    name: 'Allsocialpublic',
    parent: 'socialpulicManage'
  },
  {
    path: '/excellent', // 优秀社情民意信息
    name: 'excellent',
    parent: 'socialpulicManage'
  },
  {
    path: '/InformationAcquisition', // 信息采编
    name: 'InformationAcquisition',
    parent: 'socialpulicManage'
  },
  {
    path: '/ProvincialSubmit', // 省政协提交
    name: 'ProvincialSubmit',
    parent: 'Provincial'
  },
  {
    path: '/Provincialsccial', // 省政协社情民意
    name: 'Provincialsccial',
    parent: 'Provincial'
  },
  {
    path: '/ProvincialsccialDW', // 省政协单位社情民意
    name: 'ProvincialsccialDW',
    parent: 'Provincial'
  },
  {
    path: '/szxScoreStatistics', // 省政协来稿得分统计
    name: 'szxScoreStatistics',
    parent: 'Provincial'
  },
  {
    path: '/szxnotice', // 省政协通知
    name: 'szxnotice',
    parent: 'Provincial'
  },
  {
    path: '/documentCorrection', // 公文检索
    name: 'documentCorrection'
  }
]
const socialpublic = socialpublicoption.map(route => {
  if (!route.parent) {
    route = {
      ...route,
      component: () => import(/* webpackChunkName: "[request]" */'@/views/socialpublic-qd/' + route.name + '/' + route.name + '.vue')
    }
  } else {
    route = {
      ...route,
      component: () => import(/* webpackChunkName: "[request]" */'@/views/socialpublic-qd/' + route.parent + '/' + route.name + '/' + route.name + '.vue')
    }
  }

  // console.log(route)
  return route
})
export default socialpublic
