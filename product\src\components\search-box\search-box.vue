<template>
  <div class="search-box"
       ref="searchBox">
    <span class="search-title"
          v-if="title">{{ title }}</span>
    <div class="search-box-slot"
         ref="searchBoxSlot">
      <slot></slot>
    </div>
    <div class="search-box-btn"
         :style="{ width: (isMore ? 220 : 184) + 'px' }">
      <el-button type="text"
                 v-if="isMore"
                 @click="moreClick">
        <i :class="['el-icon-arrow-down', isShow ? '' : 'el-icon-arrow-down-a']"></i>{{ isShow ? '展开' : '收起' }}
      </el-button>
      <el-button type="primary"
                 @click="search"
                 v-if="isSearch">查询</el-button>
      <el-button @click="reset"
                 v-if="isReset">重置</el-button>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
export default {
  name: 'search-box',
  props: {
    isSearch: {
      type: Boolean,
      default: true
    },
    isReset: {
      type: Boolean,
      default: true
    },
    title: String
  },
  mounted () {
    this.$nextTick(() => {
      this.collapse()
    })
    this.resize = window.onresize = _.debounce(() => {
      this.collapse()
    }, 500)
  },
  data () {
    return {
      isMore: false,
      isShow: true,
      resize: null
    }
  },
  destroyed () {
    this.resize = null
  },
  methods: {
    search () {
      this.$emit('search-click')
    },
    reset () {
      this.$emit('reset-click')
    },
    collapse () {
      var searchBoxSlot = this.$refs.searchBoxSlot
      if (searchBoxSlot) {
        var width = 0
        for (let index = 0; index < searchBoxSlot.childNodes.length; index++) {
          if (searchBoxSlot.childNodes[index].offsetWidth !== undefined) {
            width += searchBoxSlot.childNodes[index].offsetWidth + 24
          }
        }
        if (searchBoxSlot.offsetWidth < width) {
          this.isMore = true
        } else {
          this.isMore = false
        }
      }
    },
    moreClick () {
      var searchBoxSlot = this.$refs.searchBoxSlot
      if (this.isShow) {
        searchBoxSlot.style.height = 'auto'
      } else {
        searchBoxSlot.style.height = '52px'
      }
      this.isShow = !this.isShow
      this.$emit('more-click', searchBoxSlot.offsetHeight, this.isShow)
    }
  }
}
</script>

<style lang="scss"
       scoped>
      .search-box {
        box-sizing: border-box;
        border-radius: 10px;
        background-color: #fff;
        box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);
        display: flex;
        justify-content: flex-end;
        padding: 12px 10px 0;

        .search-title {
          min-width: 120px;
          flex-shrink: 0;
          color: #444;
          line-height: 40px;
          height: 40px;
        }

        .el-input {
          width: 222px;
          // margin-left: 24px;
          margin-bottom: 12px;
        }

        .el-select {
          // margin-left: 24px;
          margin-bottom: 12px;

          .el-input {
            margin-left: 0;
            margin-bottom: 0;
          }
        }

        .zy-select {
          // margin-left: 24px;
          margin-bottom: 12px;

          .el-input {
            margin-left: 0;
            margin-bottom: 0;
          }
        }

        .el-date-editor {
          // margin-left: 24px;
          margin-bottom: 12px;
        }

        .search-box-slot {
          width: calc(100% - 246px);
          display: flex;
          justify-content: flex-end;
          flex-wrap: wrap;
          overflow: hidden;
          height: 52px;
        }

        .search-box-btn {
          flex-shrink: 0;

          .el-button {
            height: 40px;
            margin-left: 24px;
            padding: 0 16px;
          }

          .el-button+.el-button {
            margin-left: 16px;
          }

          .el-button--text {
            margin-left: 9px;
            font-size: $textSize14;
            padding: 0;

            .el-icon-arrow-down {
              transition-duration: 0.4s;
              transform: rotate(0);
            }

            .el-icon-arrow-down-a {
              transition-duration: 0.4s;
              transform: rotate(-180deg);
            }
          }
        }
      }
    </style>
