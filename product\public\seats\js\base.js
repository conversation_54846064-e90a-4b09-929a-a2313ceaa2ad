//加载对Promise的依赖
function isIE () {
  if (!!window.ActiveXObject || "ActiveXObject" in window) {
    return true;
  } else {
    return false;
  }
}
if (isIE()) {
  var script = document.createElement('script');
  script.type = 'text/javaScript';
  script.src = '../../../js/bluebird.min.js';
  document.getElementsByTagName('head')[0].appendChild(script);
}
$(function () {
  $(".preloader").fadeOut();
  //	$('[data-toggle="tooltip"]').tooltip();
  //	$('[data-toggle="popover"]').popover();
  // 点击子页面关闭顶部导航
  // $(document).click(function() {
  // 	$(window.parent.document)
  // 		.find(".bounceInDown")
  // 		.removeClass("show");
  // });
  // 开启表单验证
  formCheck();
  // 开启图片预览
  //	imageZoom(600, 400);
});
var urlName = document.location.hostname;
//后台服务的路径
var server = {
  "local_path": "",
};
console.log(urlName)
switch (urlName) {
  case "xtbg.gdzwfw.gov.cn":
    server.local_path = "https://xtbg.gdzwfw.gov.cn/rz_sszzllz/lzt/";
    break;
  case "zhzx.hnzx.gov.cn":
    server.local_path = "http://zhzx.hnzx.gov.cn:8081/lzt/";
    break;
  case "xtbgtest.digitalgd.com.cn":
    server.local_path = "https://xtbgtest.digitalgd.com.cn/rz_lzmh/lzt/";
    break;
  case "***************":
    server.local_path = "http://***************:19130/lzt/";
    break;
  case "**************":
    server.local_path = "http://**************:8081/lzt/";
    break;
  default:
    server.local_path = "http://test.dc.cszysoft.com:21408/lzt/";
    break;
}
//手机端用到 start
//会议报名状态  0:未回复 1:已报名 2:请假 3:审核中 5:未批准
var signUpStatus = {
  "untreated": "0",
  "signUp": "1",
  "leave": "2",
  "examine": "3",
  "unratified": "5"
};

//会议签到状态  0:未签到  1:已签到  2:迟到 3:已请假
var signInStatus = {
  "absent": "0",
  "signIn": "1",
  "late": "2",
  "leave": "3"
};

//会管人员是否可编辑标志 0允许修改 1只能查看 2不显示按钮
var editable = {
  "edit": "0",
  "unEdit": "1",
  "unShow": "2"
};
//手机端用到 end

//时间段快速选择 自定义
var timeSlot = ["09:00", "09:30", "14:00", "14:30", "15:00"]

/***********jquery 添加实例方法************/

//数据范围转换为名字
function dataScopeToName (options) {
  if (options == 1) {
    return "所有数据"
  } else if (options == 2) {
    return "所在公司及以下数据"
  } else if (options == 3) {
    return "所在公司数据"
  } else if (options == 4) {
    return "所在部门及以下数据"
  } else if (options == 5) {
    return "所在部门数据"
  } else if (options == 8) {
    return "仅本人数据"
  } else if (options == 9) {
    return "按明细设置"
  } else {
    return ""
  }
}

//角色类型转换为名字
function roleTypeToName (options) {
  if (options == "assignment") {
    return "任务分配"
  } else if (options == "security-role") {
    return "管理角色"
  } else if (options == "user") {
    return "普通角色"
  } else {
    return ""
  }
}

//预计会期转换为名字
function planTimeToName (options) {
  if (options == "0.5") {
    return "半天"
  } else if (options == "1") {
    return "一天"
  } else if (options == "1.5") {
    return "一天半"
  } else if (options == "2") {
    return "两天"
  } else if (options == "2.5") {
    return "两天半"
  } else if (options == "3") {
    return "三天"
  } else if (options == "3.5") {
    return "三天半"
  } else if (options == "4") {
    return "四天"
  } else if (options == "4.5") {
    return "四天半"
  } else if (options == "5") {
    return "五天"
  } else if (options == "5.5") {
    return "五天半"
  } else if (options == "6") {
    return "六天"
  } else if (options == "6.5") {
    return "六天半"
  } else if (options == "7") {
    return "七天"
  } else {
    return ""
  }
}

// 验证手机号
function isPhoneNo (phone) {
  var pattern = /^(0|86|17951)?(13[0-9]|15[012356789]|17[678]|18[0-9]|14[57])[0-9]{8}$/;
  return pattern.test(phone);
}

/**
 * @desc 关闭提示信息
 */
function closeLayerInfo (item) {
  $(item).parent().parent().remove();
}

$.fn.extend({
  /**
   *datatables 表格
   *
   * @param {*} options
   * @returns datatable对象
   */
  buildTable: function (options) {
    var defaults = {
      destroy: true, // 摧毁现有结构。重新创建新的datatable
      serverSide: true,
      autoWidth: false, // 自适应宽度
      stateSave: false, // 保存状态到cookie ******很重要 ，
      // 当搜索的时候页面一刷新会导致搜索的消失。使用这个属性设置为true就可避免了
      paging: true, // 是否使用分页
      searching: false,
      lengthChange: false, // 是否启用设置每页显示记录数
      pageLength: 10, // 默认每页显示的记录数
      scrollCollapse: false, // 指定适当的时候缩起滚动视图
      ordering: true, // 是否使用排序
      "bJQueryUI": false, //页面风格使用jQuery.
      sPaginationType: "full_numbers", //分页样式
      processing: true,
      language: {
        sLengthMenu: '&nbsp;&nbsp;&nbsp;&nbsp;每页显示 _MENU_ 条',
        sZeroRecords: '抱歉，没有找到',
        sEmptyTable: '抱歉，没有找到',
        sInfo: '显示 _START_ 到 _END_ 条　共 _TOTAL_ 条',
        sInfoEmpty: '没有记录',
        sInfoFiltered: '(从 _MAX_ 条数据中检索)',
        sProcessing: null,//'正在加载中...',
        sSearch: '搜索：',
        oPaginate: {
          sFirst: '首页',
          sPrevious: '上页',
          sNext: '下页',
          sLast: '末页'
        }
      }
    };
    options = $.extend(true, {}, defaults, options);
    var load;
    this.on('preXhr.dt', function () {
      load = layer.msg('加载中', {
        icon: 16
      })
    }).on('xhr.dt', function () {
      layer.close(load);
    })
    return this.dataTable(options);
  },
  /**
   *table 全选和反选方法，点击行选中
   *
   * @param {*} allcheck 全选按钮id
   */
  tableSelect: function (allcheck) {
    var table = this;
    $(allcheck).on('change', function () {
      var checkAll = $(this).prop('checked');
      table.find('input[class="check"]').prop('checked', checkAll);
    });
    this.on('change', '.check', function () {
      var length1 = table.find('tbody .check').length;
      var length2 = table.find('tbody .check:checked').length;
      $(allcheck).prop('checked', length1 === length2 ? true : false);
    });
  }
});

/***************工具函数***************/


(function (window) {
  function Tool () { }
  Tool.prototype = {
    dateFormat: tool_dateFormat,
    parseQueryString: tool_parseQueryString,
    fixTime: tool_fixTime,
  }
  window.tools = new Tool();

  /* 添加template方法 */
  if (typeof (template) != 'undefined') {
    template.defaults.imports.dateFormat = tool_dateFormat;
    template.defaults.imports.preViewFile = function (id) {
      return UPLOADFILE + id;
    }
  }
  /**
   * 根据时间戳转换成特定格式
   *
   * @param {any} date 时间戳
   * @param {any} format 格式
   * @returns
   */
  function tool_dateFormat (date, format) {
    format = format || 'yyyy-MM-dd hh:mm:ss';
    date = new Date(date) || new Date(); //新建日期对象
    /*日期字典*/
    var map = {
      M: date.getMonth() + 1, //月份
      d: date.getDate(), //日
      h: date.getHours(), //小时
      m: date.getMinutes(), //分
      s: date.getSeconds(), //秒
      q: Math.floor((date.getMonth() + 3) / 3), //季度
      S: date.getMilliseconds() //毫秒
    };
    /*正则替换*/
    format = format.replace(/([yMdhmsqS])+/g, function (all, t) {
      var v = map[t];
      if (v !== undefined) {
        if (all.length > 1) {
          v = '0' + v;
          v = v.substr(v.length - 2);
        }
        return v;
      } else if (t === 'y') {
        return (date.getFullYear() + '').substr(4 - all.length);
      }
      return all;
    });
    /*返回自身*/
    return format;
  }
  /**
   *
   * @desc   url参数转对象
   * @param  {String} url  default: window.location.href
   * @return {Object}
   */
  function tool_parseQueryString (url) {
    url = url == null ? window.location.href : url;
    if (url.lastIndexOf("?") == -1) {
      return {};
    }
    var search = url.substring(url.lastIndexOf("?") + 1);
    if (!search) {
      return {};
    }
    return JSON.parse('{"' + decodeURIComponent(search).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
  }

  /**
   * [tool_fixTime description] 时间字符串格式转换时间戳
   * @param  {[type]} timeStr [description] 字符串
   * @return {[type]}         [description] 时间戳
   */
  function tool_fixTime (timeStr) {
    timeStr = timeStr.replace(/(-)/g, '/'); // 把所有'-'转换为'/'
    return new Date(timeStr).getTime();
  }
})(window)


/***********公共方法************/

/**
 * [formCheck description] 表单验证
 * @return {[type]} [description]
 */
var checkResult = true;
function formCheck () {
  var rexOption = {
    tel: {
      rex: /^(0|86|17951)?(13[0-9]|15[012356789]|17[678]|18[0-9]|14[57])[0-9]{8}$/,
      msg: '手机号格式错误'
    }, //手机号码
    email: {
      rex: /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/,
      msg: '邮箱格式错误'
    }, // 邮箱
    idcard: {
      rex: /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/,
      msg: '身份证格式错误'
    },
    date: {
      rex: /^[1-2][0-9][0-9][0-9]-[0-1]{0,1}[0-9]-[0-3]{0,1}[0-9]$/,
      msg: '日期格式错误'
    }, // 日期
    zh: {
      rex: /[\u4e00-\u9fa5]/gm,
      msg: '请输入中文'
    }, // 中文
    en: {
      rex: /^[a-zA-Z]+$/,
      msg: '请输入英文'
    }, // 英文
    number: {
      rex: /^\d+$/,
      msg: '请输入数字'
    }, // 数字
    lgt: {
      rex: /^(((\d|[0-9]\d|1[0-7]\d|0)\.\d{0,6})|(\d|[0-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/,
      msg: '请输入正确的经度格式'
    },
    ltt: {
      rex: /^([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/,
      msg: '请输入正确的纬度格式'
    }
  }

  $('[data-rex]').on('blur', function () {
    var rex = $(this).attr('data-rex');
    var empty = $(this).attr('empty');
    var content = $.trim($(this).val());
    var rule, msg;
    if (!/^[a-zA-Z]+$/.test(rex)) { // 如果rex是正则表达式
      rule = new RegExp(rex);
      msg = $(this).attr('data-msg');
    } else if (rex) {
      rule = rexOption[rex].rex;
      msg = rexOption[rex].msg;
    }
    if (!empty) { // 验证是否为空
      if (!content) {
        $(this).removeClass("border-success").addClass("border-danger").val("")/*.attr("placeholder", '输入内容不能为空')*/;
        $(this).next().removeClass("mdi-check-circle successIcon").addClass("mdi-close-circle failIcon");
        $(this).parent().find("span").text(window.localStorage.getItem("lang") == "tw" ? $font.traditionalized("输入内容不能为空") : $font.simplized("输入内容不能为空"));
        checkResult = false;
        return;
      }
      if (rex) {
        if (!rule.test(content)) {
          $(this).removeClass("border-success").addClass("border-danger").val("")/*.attr("placeholder", msg)*/;
          $(this).next().removeClass("mdi-check-circle successIcon").addClass("mdi-close-circle failIcon");
          $(this).parent().find("span").text(msg);
          checkResult = false;
          return;
        }
      }
    } else {
      if (content) {
        if (rex) {
          if (!rule.test(content)) {
            $(this).removeClass("border-success").addClass("border-danger").val("")/*.attr("placeholder", msg)*/;
            $(this).next().removeClass("mdi-check-circle successIcon").addClass("mdi-close-circle failIcon");
            $(this).parent().find("span").text(msg);
            checkResult = false;
            return;
          }
        }
      }
    }
    $(this).removeClass("border-danger").addClass("border-success")/*.attr("placeholder", "")*/;
    $(this).next().removeClass("mdi-close-circle failIcon").addClass("mdi-check-circle successIcon");
    $(this).parent().find("span").text("");
    checkResult = true;
  })
}

/**
 * [imageZoom description] 图片缩放
 * @param  {[number]} width  [description]
 * @param  {[number]} height [description]
 * @return {[type]}        [description]
 */
function imageZoom (width, height) {
  $(document).on("click", "img[data-zoom]", function () {
    var thisSrc = $(this).attr("src");
    var originSrc = $(this).attr('data-zoom');
    var showSrc = originSrc ? originSrc : thisSrc;
    if (showSrc) {
      $(".img-shade,.preview-zoom").remove();
      var previewStr = '<div class="img-shade">' +
        '<div class="preview-zoom is-loading" style="width:' + width + 'px;height:' + height + 'px"><img src="' + showSrc + '" ></div>' +
        '</div >';
      $("body").append(previewStr);
      $(".img-shade,.preview-zoom").fadeIn();
      // 图片加载过渡
      $(".preview-zoom>img").get(0).onload = function () {
        $(this).parent().removeClass("is-loading");
      };
    }
    return false; //阻止事件冒泡
  }).click(function () {
    $(".img-shade,.preview-zoom").fadeOut();
  });
}
/*********表格超过长度字体处理***********/
function tableRow (str, iswidth) {
  // 在GBK编码里，除了ASCII字符，其它都占两个字符宽
  var fontNum = str.replace(/[^\x00-\xff]/g, 'xx').length
  var contentHtml = '';
  if (fontNum > Math.ceil(iswidth / 7.8125)) {                //7.8125 为每个字节的宽度       iswidth：单元格宽度
    contentHtml = '<span class="textMore">' + str + '</span>'
  } else {
    contentHtml = str
  }
  return contentHtml
}

//权限集合
var permissionsList = new Array();

//公共获取权限的方法
function getPermissionsByMenuName (menuName) {
  var menuList = menuList = JSON.parse(window.sessionStorage.getItem("menuId"));
  if (menuList.children.length > 0) {
    for (var i = 0; i < menuList.children.length; i++) {
      var menuItem = menuList.children[i];
      //递归解析菜单权限
      getChildNode(menuItem);
    }
    if (permissionsList.length > 0) {
      for (var p = 0; p < permissionsList.length; p++) {
        var pItem = permissionsList[p];
        var htmlHref = pItem.to;
        htmlHref = htmlHref.replace(/^http:\/\/[^/]+/, "");
        var addr = htmlHref.substr(htmlHref.lastIndexOf('/', htmlHref.lastIndexOf('/') - 1) + 1);
        var index = addr.lastIndexOf("\/");
        //js 获取字符串中最后一个斜杠后面的内容
        addrLast = decodeURI(addr.substring(index + 1, addr.length - 5));
        if (addrLast == menuName) {
          var pmsList = new Array();
          //返回权限集合
          if (pItem.pms.length > 0) {
            for (var j = 0; j < pItem.pms.length; j++) {
              var index = pItem.pms[j].lastIndexOf(":");
              var pmsItem = pItem.pms[j].substring(index + 1, pItem.pms[j].length);
              pmsList.push(pmsItem);
            }
            return pmsList;
          }
        }
      }
    }
  }
}

//递归解析菜单权限
function getChildNode (node) {
  //先找到子结点
  var nodeList = node.children;
  if (nodeList.length > 0) {
    for (var i = 0; i < nodeList.length; i++) {
      var childNode = nodeList[i];
      var obj = new Object();
      obj.name = childNode.name;
      obj.to = childNode.to;
      obj.pms = childNode.permissions;
      permissionsList.push(obj);
      //判断是否有子级
      if (childNode.children.length > 0) {
        getChildNode(childNode);
      }
    }
  }
  else {
    var obj = new Object();
    obj.name = node.name;
    obj.to = node.to;
    obj.pms = node.permissions;
    permissionsList.push(obj);
  }
}