// 导入封装的方法
import {
  get,
  post,
  postform
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const PartyBuildingQd = {

  findOrgPartys (params) { // 党建列表
    return get('/party/findOrgPartys', params)
  },
  partylList (params) { // 党建列表
    return get('/party/list', params)
  },
  partyShow (params) { // APP显示
    return get('/party/appshow', params)
  },
  partyHide (params) { // APP不显示
    return get('/party/apphide', params)
  },
  findPartyConfigTypeAndName (params) { // 党建类型
    return get('/partyconfig/findPartyConfigTypeAndName', params)
  },
  partyadd (url, params) { // 党建列表新增,修改
    return post(url, params)
  },
  partyinfo (params) { // 党建列表详情
    return post(`/party/info/${params}`)
  },

  partydels (params) { // 党建列表shanchu
    return post('/party/dels', params)
  },
  partyconfiglist (params) { // 党建配置列表
    return post('/partyconfig/list', params)
  },
  getOrgName (params) { // 获取部门名称
    return post('/party/getOrgName', params)
  },
  findPartyConfigTrees (params) { // 获取部门名称
    return post('/partyconfig/findPartyConfigTrees', params)
  },
  partyconfig (url, params) { // 党建配置新增
    return post(`/partyconfig/${url}`, params)
  },
  partyconfigdel (params) { // 党建配置删除
    return post('/partyconfig/dels', params)
  },
  partyconfiginfo (params) { // 党建配置详情
    return post(`/partyconfig/info/${params}`)
  },
  partyyearlist (params) { // 年份配置
    return post('/partyyear/list', params)
  },
  partyyear (url, params) { // 年份新增
    return post(url, params)
  },
  partyyeardels (params) { // 年份配置
    return post('/partyyear/dels', params)
  },

  partyassesslist (params) { // 部门用户列表
    return post('partyassess/list', params)
  },

  partyassess (url, params) { // 部门用户新增,修改
    return post(url, params)
  },
  partyassessdels (params) { // 部门用户删除
    return post('partyassess/dels', params)
  },
  treelist (params) { // 部门用户列表
    return post('tree/list ', params)
  },

  partyorgrecordlist (params) { // 建部门调动记录列表
    return post('partyorgrecord/list', params)
  },
  partyorgrecorddels (params) { // 建部门调动记录列表
    return post('partyorgrecord/dels', params)
  },

  getUserPartyInfo (params) { // 获取党建个人考核结果详情
    return post('party/getUserPartyInfo', params)
  },

  getUserPartyHeaders (params) { // 获取党建资讯弹框的tab栏id
    return post('party/getUserHeaders', params)
  },
  getOrgPartyInfo (params) { // 获取党建部门考核结果详情
    return post('party/getOrgPartyInfo', params)
  },

  getUserEvaluationResult (params) { // 个人考核结果列表
    return post('/party/getUserEvaluationResult', params)
  },

  getOrgEvaluationResult (params) { // 部门考核结果列表
    return post('/party/getOrgEvaluationResult', params)
  },
  auditPartyInfo (params) { // 审核
    return post('/party/auditPartyInfo', params)
  },
  auditPartys (params) { // 审核
    return post('/party/auditPartys', params)
  },
  getUserNumAndOrgNum (params) { // 个人部门得分
    return post('/party/getUserNumAndOrgNum', params)
  },
  getPartyTypeNum (params) { // 类型统计
    return post('/party/getPartyTypeNum', params)
  },
  /**
   *
   *
   *
   */
  zyinfodetailbatchUpdate (params) { // 党建列表审核
    return post('/zyinfodetail/batchUpdate', params)
  },

  zyinfodetailinfo (params) { // 党建列表详情
    return get(`/zyinfodetail/info/${params}`)
  },
  zyinfostructureltree (params) { // 党建类型管理树
    return get('/zyinfostructure/tree', params)
  },
  zyinfostructurellist (params) { // 党建类型管理列表
    return get('/zyinfostructure/list', params)
  },
  zyinfostructureladdEdit (url, params) { // 党建类型管理新增修改
    return post(url, params)
  },
  zyinfostructureinfo (params) { // 党建列表详情
    return get(`/zyinfostructure/info/${params}`)
  },
  zyinfostructureldels (params) { // 党建类型管理列表
    return get('/zyinfostructure/dels', params)
  },
  zypartyinfoanalysepartyStatus (params) { //  资讯查看状态
    return post('/zypartyinfoanalyse/partyStatus', params)
  },
  zypartyinfoanalysepartyInfoBrowseDetail (params) { //  通知查看详情
    return post('/zypartyinfoanalyse/partyInfoBrowseDetail', params)
  },
  zypartyinfoanalysepartyInfoDetail (params) { //  党员信息统计详情接口
    return post('/zypartyinfoanalyse/partyInfoDetail', params)
  },

  zypartyinfoanalysepartyStudy (params) { //  党建理论学习统计
    return post('/zypartyinfoanalyse/partyStudy', params)
  },
  zypartyinfoanalysepartyInfo (params) { //  党员信息统计：年龄、性别、学历、党员类型
    return post('/zypartyinfoanalyse/partyInfo', params)
  },
  wholeuserlist (params) { //  党员信息统计：年龄、性别、学历、党员类型
    return post('/wholeuser/list', params)
  },

  wholeuserparters (params) { //  党员信息管理
    return post('/wholeuser/parters', params)
  },
  wholeusereditadd (url, params) {
    return post(url, params)
  },
  wholeuserinfo (params) {
    return post(`/wholeuser/info/${params}`)
  },
  wholeuserbatchdel (params) { //  党员信息管理
    return post('/wholeuser/batch/del', params)
  }

}
export default PartyBuildingQd
