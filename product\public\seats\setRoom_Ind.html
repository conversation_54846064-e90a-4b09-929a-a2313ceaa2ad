<html>
  <!--单个的会场排座-->

  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>会场排座</title>
    <!-- 引用css -->
    <link rel="stylesheet" href="js/icheck/skins/all.css" />
    <link rel="stylesheet" href="css/setRoom.css" />
    <link rel="stylesheet" href="plugins/easyui-1.7.0/themes/default/easyui.css" />
    <link rel="stylesheet" href="plugins/easyui-1.7.0/themes/icon.css" />
  </head>

  <body>
    <div id="seatInfo" style="position: relative; float: left; width: 260px; height: 100%; overflow-x: hidden; overflow-y: auto; background: #ffffff">
      <h3 id="rName" style="margin: 0 0 23px 0"></h3>
      <div>
        <span style="line-height: 100%">
          <label>房间总数:</label>
          <label id="seatNum"></label>
          <label>个</label>
        </span>
      </div>
      <div style="height: 40px; line-height: 36px; margin-top: 15px">
        <span>
          <input id="rowNum" class="inputText" type="text" onchange="num(this)" style="position: relative; top: 2px; left: 2px; width: 50px; height: 30px; text-align: center" />
          <label style="position: relative; top: 2px; left: 2px">排</label>
          <input id="colNum" class="inputText" type="text" onchange="num(this)" style="position: relative; top: 2px; left: 2px; width: 50px; height: 30px; text-align: center" />
          <label style="position: relative; top: 2px; left: 2px">列</label>
        </span>
      </div>
      <div id="addSets" class="btn btn-info">添加房间</div>
      <div style="height: 40px; line-height: 36px; margin-top: 20px">
        <select id="setsT" class="easyui-combobox custom-select" data-options="editable:false" panelHeight="240px" name="state" style="width: 95%; height: 44px"></select>
      </div>
      <label style="position: relative; top: 15px; left: 2px">房间号生成方式：</label>
      <div style="display: flex; position: relative; top: 25px">
        <div class="opt">
          <input class="magic-radio" type="radio" name="setRoomNum" id="byOrder" value="1" checked="checked" />
          <label for="byOrder">按顺序生成</label>
        </div>
        <div class="opt" style="margin-left: 25px">
          <input class="magic-radio" type="radio" name="setRoomNum" id="bySingular" value="2" />
          <label for="bySingular">按单数生成</label>
        </div>
      </div>
      <div style="display: flex; position: relative; top: 30px; left: -25px">
        <div class="opt" style="margin-left: 25px">
          <input class="magic-radio" type="radio" name="setRoomNum" id="byDouble" value="3" />
          <label for="byDouble">按双数生成</label>
        </div>
      </div>
      <div id="setNum" class="btn btn-info">设置房间号</div>
      <div style="height: 40px; line-height: 36px; margin-top: 15px">
        <span>
          <label style="position: relative; top: 2px; left: 2px">房&nbsp;间&nbsp;号：</label>
          <input id="editSetsNum" class="inputText" type="text" onchange="num(this)" style="position: relative; left: -2px; top: 2px; width: 50px; height: 30px; text-align: center" />
        </span>
      </div>
      <div style="height: 40px; line-height: 36px; margin-top: 10px">
        <span style="display: inline-flex">
          <label>备&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;注：</label>
          <input id="setsRmk" type="text" class="inputText" style="position: relative; top: 1px; width: 170px; height: 30px; text-align: left" />
        </span>
      </div>
    </div>
    <div id="setsDiv" style="position: absolute; left: 280px; height: 100%">
      <div style="width: 100%; height: 36px; display: inline-flex; white-space: nowrap">
        <div id="addSigns" class="btn cancel">添加标记</div>
        <div id="makePic" class="btn cancel">生成图片</div>
        <div id="allSel" class="btn cancel">全选</div>
        <div id="delSets" class="btn btn-info">删除</div>
        <div id="saveSets" class="btn btn-info">保存</div>
      </div>
      <div class="box" onmousedown="boxOnMouse(event);">
        <span id="areaTipFixed" class="areaTip"></span>
        <span id="areaTipImg" class="areaTip"></span>
        <!--<img id="bgImg">-->
      </div>
    </div>

    <!--begain 标签内容弹出框-->
    <div id="inputWin" class="signs_content">
      <input type="text" id="sgContent" style="width: 100%; height: 30px; text-align: left; margin-bottom: 5px" />
      <label style="position: relative; left: -25px">宽</label>
      <input id="signW" type="number" min="15" style="width: 75px; height: 25px; text-align: center; position: relative; left: -27px" />
      <label style="position: relative; left: -29px">高</label>
      <input id="signH" type="number" min="15" style="width: 76px; height: 25; text-align: center; position: relative; left: -31px" />
      <div id="confirm" style="position: absolute; top: 39px; right: 5px"><button style="height: 28px">确定</button></div>
    </div>
    <!--end 标签内容弹出框-->
    <div id="cloneDiv"></div>
    <div id="images" style="display: none"></div>

    <!-- 引用js -->
    <script src="plugins/axios/axios.min.js"></script>
    <script src="plugins/axios/qs.min.js"></script>
    <script src="plugins/jquery/jquery.min.js "></script>
    <script type="text/javascript" src="js/drag/jquery.event.drag-2.2.js"></script>
    <script type="text/javascript" src="js/drag/jquery.event.drag.live-2.2.js"></script>
    <script type="text/javascript" src="js/drag/jquery.event.drop-2.2.js"></script>
    <script type="text/javascript" src="js/drag/jquery.event.drop.live-2.2.js"></script>
    <script type="text/javascript" src="js/drag/excanvas.min.js"></script>
    <script type="text/javascript" src="js/icheck/icheck.js"></script>
    <script type="text/javascript" src="plugins/easyui-1.7.0/jquery.easyui.min.js"></script>
    <script type="text/javascript" src="plugins/layui/layer/layer.js"></script>
    <script type="text/javascript" src="js/base.js"></script>
    <script type="text/javascript" src="js/setRoom_Ind.js"></script>
    <script type="text/javascript" src="js/setRoomMin_Ind.js"></script>
    <!-- html2canvas将Dom节点在Canvas里边画出来 -->
    <script src="js/createPic/html2canvas.js"></script>
    <!-- 将canvas图片保存成图片 -->
    <!-- <script src="js/createPic/moment.js"></script> -->
    <script src="js/createPic/canvas2image.js"></script>
    <script src="js/createPic/base64.js"></script>
  </body>
</html>
