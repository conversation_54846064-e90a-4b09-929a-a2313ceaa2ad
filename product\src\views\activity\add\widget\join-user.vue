<template>
  <div class="add-join-user">
    <div class="base-form-item">
      <div class="base-form-item-label">参与人员</div>
      <div class="base-form-item-content">
        <div class="add-user-form">
          <div class="add-user-form-label">邀请人</div>
          <div class="add-user-form-content">
            <el-button
              size="small"
              plain
              icon="el-icon-plus"
              @click="isShow = true"
            ></el-button>
            <div
              class="user-list"
              ref="userRef"
              :style="{ paddingRight: (isMore ? 60 : 0) + 'px' }"
            >
              <el-tag
                style="margin-right: 10px; margin-bottom: 7px"
                class="tag"
                v-for="(item, index) in userData"
                :key="index"
                @close="deleteMan(index)"
                closable
              >
                {{ item.name }}
              </el-tag>
              <div class="ellipsis" v-if="isMore">
                <span></span><span></span><span></span>
              </div>
            </div>
            <el-button
              v-if="userData.length > 0"
              type="primary"
              @click="handleUser"
              >查看全部</el-button
            >
          </div>
        </div>
      </div>
    </div>
    <zy-pop-up v-model="isShow" title="添加参与人员">
      <candidates-user
        point="point_21"
        :data="userData"
        @userCallback="userCallback"
      ></candidates-user>
    </zy-pop-up>
    <zy-pop-up v-model="isVisible" title="全部人员">
      <zy-filter-user :userList.sync="userData"></zy-filter-user>
    </zy-pop-up>
  </div>
</template>

<script>
export default {
  data () {
    return {
      isShow: false,
      isMore: false,
      isVisible: false,
      userData: []
    }
  },
  methods: {
    userCallback (data, type) {
      if (type) {
        this.userData = data
      }
      this.isShow = false
      this.$nextTick(() => {
        this.collapse()
      })
    },
    deleteMan (index) {
      this.userData.splice(index, 1)
    },
    collapse () {
      var userRef = this.$refs.userRef
      if (userRef) {
        var width = 0
        for (let index = 0; index < userRef.childNodes.length; index++) {
          if (userRef.childNodes[index].offsetWidth !== undefined) {
            width += userRef.childNodes[index].offsetWidth + 10
          }
        }
        if (userRef.offsetWidth < width) {
          this.isMore = true
        } else {
          this.isMore = false
        }
      }
    },
    handleUser () {
      this.isVisible = true
    }
  }
}
</script>

<style lang="scss">
.add-join-user {
  .base-form-item {
    display: flex;
    margin: 25px auto;
    .base-form-item-label {
      width: 150px;
      text-align: left;
      flex-shrink: 0;
      font-size: 18px;
      color: #333;
    }
    .base-form-item-content {
      width: calc(100% - 150px);
    }
  }
  .add-user-form {
    display: flex;
    .add-user-form-label {
      width: 120px;
      text-align: right;
      margin-right: 10px;
      line-height: 36px;
    }
    .add-user-form-content {
      width: calc(100% - 130px);
    }
  }
}
.user-list {
  margin: 20px 0px;
}
</style>
