<template>
  <div class="information-reports">
    <div class="button-box-list">
      <el-button
        type="primary"
        size="mini"
        v-permissions="'auth:zyinforeportpic:scrolling:report:add'"
        @click="newData"
      >新增</el-button>
      <el-button
        size="mini"
        type="primary"
        v-permissions="'auth:zyinforeportpic:dels'"
        @click="deleteClick"
      >删除</el-button>
      <el-button
        type="primary"
        size="mini"
        icon="el-icon-circle-check"
        @click="editIsCheck(1)"
      >审核通过</el-button>
      <el-button
        type="primary"
        size="mini"
        icon="el-icon-remove-outline"
        @click="editIsCheck(2)"
      >审核不通过</el-button>
    </div>
    <div class="information-mosaic-list scrollBar">
      <el-table
        ref="multipleTable"
        :data="tableData"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="48"
        >
        </el-table-column>
        <!-- <el-table-column
          label="序号"
          width="80"
          prop="sort"
        >
        </el-table-column> -->
        <el-table-column
          label="标题"
          prop="title"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="审核状态"
          prop="isCheck"
        >
        </el-table-column>
        <el-table-column
          label="创建人"
          prop="createBy"
          width="120"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="发布时间"
          width="160"
          prop="publishDate"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          label="操作"
          v-if="$hasPermission(['auth:zyinforeportpic:scrolling:report:edit'])"
          width="80"
        >
          <template slot-scope="scope">
            <el-button
              @click="handleClick(scope.row)"
              type="text"
              size="small"
            >编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <zy-pop-up
      v-model="show"
      :title="mosaicId==''?'新增':'编辑'+'滚动报道'"
    >
      <reports-new
        :id="id"
        :mosaicId="mosaicId"
        @newCallback="newCallback"
      ></reports-new>
    </zy-pop-up>

  </div>
</template>
<script>
import reportsNew from './reports-new/reports-new'
export default {
  name: 'informationReports',
  data () {
    return {
      tableData: [],
      multipleSelection: [],
      mosaicId: '',
      show: false
    }
  },
  props: ['id'],
  components: {
    reportsNew
  },
  mounted () {
    if (this.id) {
      this.reportList()
    }
  },
  methods: {
    async reportList () {
      const res = await this.$api.appManagement.reportList({ detailId: this.id, pageNo: 1, pageSize: 1000 })
      var { data } = res
      this.tableData = data
    },
    handleClick (row) {
      this.mosaicId = row.id
      this.show = true
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    newData () {
      this.mosaicId = ''
      this.show = true
    },
    newCallback () {
      this.reportList()
      this.show = false
    },

    async editIsCheck (isCheck) {
      if (this.multipleSelection.length < 1) {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
        return
      }

      var arr = []
      this.multipleSelection.forEach(item => {
        arr.push(item.id)
      })
      const res = await this.$api.appManagement.editIsCheck(
        { ids: arr.join(','), isCheck: isCheck }
      )
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.reportList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    deleteClick () {
      if (this.multipleSelection.length) {
        this.$confirm('此操作将删除当前选中的滚动报道, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var arr = []
          this.multipleSelection.forEach(item => {
            arr.push(item.id)
          })
          var idSets = arr.join(',')
          this.picDel(idSets)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async picDel (id) {
      const res = await this.$api.appManagement.picDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.reportList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
@import './information-reports.scss';
</style>
