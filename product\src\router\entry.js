import Vue from 'vue'
import Router from 'vue-router'
import router, {
  routes,
  resetRouter
} from './index'
import api from '../api'
import {
  addRoute
} from './addRoute'
import {
  changeThemeColor
} from '@/theme'

var theme = ''
var isMoreProject = ''
// 加载页面获取配置
async function refresh (to, next, index, token) {
  const res = await api.general.nologin({
    codes: 'theme,mainText,router,isMoreProject'
  })
  var {
    data
  } = res
  if (data) {
    if (JSON.stringify(data) !== '{}') {
      theme = data.theme
      sessionStorage.setItem('theme', theme)
      if (data.isMoreProject) {
        isMoreProject = data.isMoreProject === 'true'
      } else {
        isMoreProject = false
      }
      generate(data.router, data.mainText, index)
    }
  }
  // alert(to.name)
  if (to.name !== 'login' && to.name !== 'login-help') {
    if (to.query.qdzxtoken) {
      sessionStorage.setItem('token' + theme, token)
      next({
        name: 'supervise-notice-card-add-back'
      })
    } else {
      next({
        name: 'login'
      })
    }
  } else {
    if (to.name === 'login' || to.name === 'login-help') {
      const route = sessionStorage.getItem('route') || ''
      if (route) {
        const fullPath = JSON.parse(route)
        next({
          path: fullPath
        })
      } else {
        next()
      }
    } else {
      next({
        name: 'login'
      })
    }
  }
}
// 切换系统获取配置
async function refreshs (token, projects, callback, index) {
  const res = await api.general.nologin({
    codes: 'theme,mainText,router'
  })
  var {
    data
  } = res
  theme = ''
  if (data) {
    if (JSON.stringify(data) !== '{}') {
      theme = data.theme
      sessionStorage.setItem('token' + theme, JSON.stringify(token))
      sessionStorage.setItem('projects' + theme, JSON.stringify(projects))
      sessionStorage.setItem('theme', theme)
      generate(data.router, data.mainText, index)
    }
  }
  callback()
}
// 根据配置生成系统路由和系统文字
function generate (router, mainText, index) {
  if (router) {
    sessionStorage.setItem('router' + theme, router)
    if (index === 1) {
      addRoute()
    }
  } else {
    sessionStorage.setItem('router' + theme, JSON.stringify({
      login: 'login',
      home: 'home'
    }))
    if (index === 1) {
      addRoute()
    }
  }
  if (mainText) {
    var array = JSON.parse(mainText)
    sessionStorage.setItem('generalName' + theme, JSON.stringify(array.generalName))
    sessionStorage.setItem('system' + theme, JSON.stringify(array.system))
    sessionStorage.setItem('position' + theme, JSON.stringify(array.position))
    document.title = array.generalName
    if (array.system === '人大') { // eslint-disable-line
      changeThemeColor()
    } else if (array.system === '政协') { // 智慧政协
      changeThemeColor('#3279F4')
    }
  } else {
    sessionStorage.setItem('generalName' + theme, '智慧人大信息管理系统')
    sessionStorage.setItem('system' + theme, '人大')
    sessionStorage.setItem('position' + theme, '代表')
    document.title = '智慧人大信息管理系统'
    changeThemeColor()
  }
}
// 解决路由切换报错
const originalPush = Router.prototype.push
Router.prototype.push = function push (location) {
  return originalPush.call(this, location).catch(err => err)
}
// 菜单过滤
function filterMenu (menuList) {
  return menuList.filter(item => {
    return item.isShow
  }).map(item => {
    item = Object.assign({}, item)
    if (item.children) {
      item.children = filterMenu(item.children)
    }
    return item
  })
}
// 切换系统登录
async function changearea () {
  const res = await api.general.changearea({
    isOutSideNet: `${window.location.protocol}//${window.location.host}:21408` === 'http://*************:21408'
  })
  var {
    data: {
      token,
      user,
      menus,
      areas
    }
  } = res
  var menu = filterMenu(menus)
  sessionStorage.setItem('menus' + theme, JSON.stringify(menu))
  sessionStorage.setItem('token' + theme, JSON.stringify(token))
  sessionStorage.setItem('user' + theme, JSON.stringify(user))
  sessionStorage.setItem('areas' + theme, JSON.stringify(areas))
  sessionStorage.setItem('areaId' + theme, JSON.stringify(user.areaId))
  router.push({
    path: '/home'
  })
  readBigDataConfig()
}
// 切换系统
function switchClick (type) {
  const token = JSON.parse(sessionStorage.getItem('token' + theme)) || ''
  const projects = JSON.parse(sessionStorage.getItem('projects' + theme)) || ''
  sessionStorage.clear()
  sessionStorage.setItem('switchpage', JSON.stringify(type))
  resetRouter()
  refreshs(token, projects, changearea, 1)
}
//  获取大数据配置
async function readBigDataConfig () {
  const res = await api.general.readonfig({
    codes: 'BigDataUser,BigDataUrl,BigDataLiveShowUrl'
  })
  sessionStorage.setItem('BigDataUrl' + theme, JSON.stringify(res.data.BigDataUrl))
  sessionStorage.setItem('BigDataUser' + theme, JSON.stringify(res.data.BigDataUser))
  sessionStorage.setItem('BigDataLiveShowUrl' + theme, JSON.stringify(res.data.BigDataLiveShowUrl))
}
// 主题
Vue.prototype.$logo = () => theme
// 主题
Vue.prototype.$isMoreProject = () => isMoreProject
// 切换系统
Vue.prototype.$switchClick = switchClick
// 获取大数据配置
Vue.prototype.$readonfig = readBigDataConfig
// 区分是人大还是政协
Vue.prototype.$system = () => {
  return JSON.parse(sessionStorage.getItem('system' + theme)) || ''
}
// 区分是委员还是代表
Vue.prototype.$position = () => {
  return JSON.parse(sessionStorage.getItem('position' + theme)) || ''
}
// 系统名称
Vue.prototype.$generalName = () => {
  return JSON.parse(sessionStorage.getItem('generalName' + theme)) || ''
}
if (!JSON.parse(localStorage.getItem('fontSize'))) {
  localStorage.setItem('fontSize', JSON.stringify(3))
}
Vue.prototype.$fontSize = (type) => {
  localStorage.setItem('fontSize', JSON.stringify(type))
  if (type === 1) {
    document.getElementsByTagName('body')[0].style.setProperty('--test16', '21px')
    document.getElementsByTagName('body')[0].style.setProperty('--test14', '19px')
    document.getElementsByTagName('body')[0].style.setProperty('--test12', '17px')
  }
  if (type === 2) {
    document.getElementsByTagName('body')[0].style.setProperty('--test16', '18px')
    document.getElementsByTagName('body')[0].style.setProperty('--test14', '16px')
    document.getElementsByTagName('body')[0].style.setProperty('--test12', '14px')
  }
  if (type === 3) {
    document.getElementsByTagName('body')[0].style.setProperty('--test16', '16px')
    document.getElementsByTagName('body')[0].style.setProperty('--test14', '14px')
    document.getElementsByTagName('body')[0].style.setProperty('--test12', '12px')
  }
}
// 阻止浏览器的返回
window.addEventListener('popstate', function () {
  history.pushState(null, null, document.URL)
})
// 重置路由
var index = 0

function resetRouters () {
  index = 0
  router.options.routes = routes
  resetRouter()
}
// 重置路由全局方法
Vue.prototype.$resetRouter = resetRouters
router.beforeEach((to, from, next) => {
  if (to.name === 'app') {
    next()
    return
  }
  const user = sessionStorage.getItem('token' + theme)
  if (to.query.route) {
    sessionStorage.setItem('route', JSON.stringify(to.fullPath))
  }
  if (user) {
    if (to.name === 'login' || to.name === 'login-help') {
      const route = sessionStorage.getItem('route') || ''
      if (route) {
        const fullPath = JSON.parse(route)
        next({
          path: fullPath
        })
      } else {
        const menu = sessionStorage.getItem('menuChild')
        if (menu) {
          next({
            name: 'general'
          })
        } else {
          next({
            name: 'home'
          })
        }
      }
    } else {
      next()
    }
  } else {
    index += 1
    if (to.query.qdzxtoken) {
      to.query.qdzxtoken = encodeURI(to.query.qdzxtoken)
      sessionStorage.setItem('qdzxtoken', JSON.stringify(to.query))
    }
    refresh(to, next, index, to.query.qdzxtoken)
  }
})
