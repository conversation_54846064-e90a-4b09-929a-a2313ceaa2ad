<template>
  <div class="content-data-column-new">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="栏目名称"
                    class="form-input"
                    prop="name">
        <el-input placeholder="请输入栏目名称"
                  v-model="form.name"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="上级栏目">
        <zy-select width="296"
                   node-key="id"
                   :props="{label:'name',children:'children'}"
                   v-model="form.superior"
                   :data="menu"
                   placeholder="请选择上级栏目"></zy-select>
      </el-form-item>
      <el-form-item label="图标"
                    class="form-icon">
        <el-upload class="form-icon-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <img v-if="file.fullUrl"
               :src="file.fullUrl"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="选中图标"
                    class="form-icon">
        <el-upload class="form-icon-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="selectImgUpload"
                   :show-file-list="false">
          <img v-if="selectFile.fullUrl"
               :src="selectFile.fullUrl"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-input"
                    prop="sort">
        <el-input placeholder="请输入排序"
                  v-model="form.sort"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="是否置顶"
                    class="form-input">
        <el-radio-group v-model="form.isTop">
          <el-radio label="1">置顶</el-radio>
          <el-radio label="0">不置顶</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否APP显示"
                    class="form-input">
        <el-radio-group v-model="form.isPushApp">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'contentDataColumnNew',
  data () {
    return {
      menu: [],
      form: {
        name: '',
        superior: '',
        sort: '',
        isTop: '1',
        isPushApp: '1'
      },
      rules: {
        name: [
          { required: true, message: '请输入栏目名称', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ]
      },
      file: {},
      selectFile: {}
    }
  },
  props: ['id'],
  mounted () {
    this.informationColumnTree()
    if (this.id) {
      this.informationColumnInfo()
    }
  },
  methods: {
    async informationColumnTree () {
      const res = await this.$api.appManagement.informationColumnTree({ module: 4 })
      var { data } = res
      this.menu = data
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const param = new FormData()
      param.append('file', files.file)
      this.$api.general.fileImg(param).then(res => {
        var { data } = res
        this.file = data
      })
    },
    selectImgUpload (files) {
      const param = new FormData()
      param.append('file', files.file)
      this.$api.general.fileImg(param).then(res => {
        var { data } = res
        this.selectFile = data
      })
    },
    async informationColumnInfo () {
      const res = await this.$api.appManagement.informationColumnInfo(this.id)
      var { data: { parentId, sort, name, isTop, isPushApp, iconUrl, selectIconUrl } } = res
      this.form.superior = parentId
      this.form.name = name
      this.form.sort = sort
      this.form.isTop = isTop
      this.form.isPushApp = isPushApp
      if (iconUrl) {
        this.file = iconUrl
      }
      if (selectIconUrl) {
        this.selectFile = selectIconUrl
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.form.superior === '') {
            this.form.superior = 1
          }
          var url = '/zyinfostructure/add'
          if (this.id) {
            url = '/zyinfostructure/edit'
          }
          this.$api.systemSettings.generalAdd(url, {
            id: this.id,
            parentId: this.form.superior,
            name: this.form.name,
            sort: this.form.sort,
            isTop: this.form.isTop,
            isPushApp: this.form.isPushApp,
            iconUrl: this.file.shortName,
            selectIconUrl: this.selectFile.shortName,
            module: 4
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
@import "./content-data-column-new.scss";
</style>
