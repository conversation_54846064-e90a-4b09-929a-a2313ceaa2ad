<template>
  <div class="vote-item-list">
    <search-box @search-click="search"
                @reset-click="reset"
                title="问题筛选">
      <zy-widget label="关键字">
        <el-input v-model="keyword"
                  placeholder="请输入关键字"
                  clearable
                  @keyup.enter.native="search"></el-input>
      </zy-widget>
    </search-box>
    <div v-for="(item, index) in formList"
         :key="index">
      <div class="radio-item">
        <div class="nav-box">
          <div class="nav-left">
            <span class="indexNum"><span class="red-dot">*</span>{{ index + 1 }}</span>
          </div>
          <div class="nav-right">
            <div class="radio-header">
              <el-input class="title-input"
                        v-model="item.question"
                        placeholder="请输入问题题目"></el-input>
              <div class="edit-list">
                <img v-if="index !== 0"
                     @click="swapArray(formList, index - 1, index)"
                     src="../../../../../../assets/images/up-two-s.png" />
                <img v-if="index !== formList.length - 1"
                     @click="swapArray(formList, index, index + 1)"
                     src="../../../../../../assets/images/down-two-s.png" />
                <img @click="swapArray(formList, index, index)"
                     src="../../../../../../assets/images/delete-s.png" />
              </div>
            </div>
            <div v-if="item.answerType === 1 || item.answerType === 2">
              <div class="radio-box"
                   v-for="(items, indexs) in item.choiceText"
                   :key="indexs">
                <div class="radio-box-width">
                  <div v-if="item.answerType === 1"
                       class="o-box" />
                  <div v-if="item.answerType === 2"
                       class="square-box" />
                  <el-input v-model="formList[index].choiceText[indexs]"
                            clearable
                            :placeholder="'选项 ' + (indexs + 1)" />
                </div>
                <div class="edit-list">
                  <img v-if="indexs !== 0"
                       @click="swapArray(item.choiceText, indexs - 1, indexs)"
                       src="../../../../../../assets/images/up-two-d.png" />
                  <img v-if="indexs !== item.choiceText.length - 1"
                       @click="swapArray(item.choiceText, indexs, indexs + 1)"
                       src="../../../../../../assets/images/down-two-d.png" />
                  <img v-if="item.choiceText.length > 2"
                       @click="swapArray(item.choiceText, indexs, indexs)"
                       src="../../../../../../assets/images/delete-d.png" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card-buttom">
          <div class="add-button flex-between">
            <div v-if="item.answerType === 1 || item.answerType === 2"
                 style="color: #007bff; width: 100%"
                 class="flex-between"
                 @click="addItems(item)">
              <i class="el-icon-plus"></i>添加单个选项
            </div>
          </div>
          <div class="flex-between">
            <div class="swith-box flex-between">
              <div class="swith-text">此项必答</div>
              <el-switch v-model="item.isMust"
                         :active-value="1"
                         :inactive-value="0" />
            </div>
            <div style="width: 170px">
              <div class="flex-between"
                   v-if="item.answerType === 1 || item.answerType === 2">
                <div style="margin-right: 20px">题型</div>
                <el-radio-group v-model="item.answerType"
                                size="small">
                  <el-radio-button :label="1">单选</el-radio-button>
                  <el-radio-button :label="2">多选</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="add-box">
      <div class="main-text">添加题型</div>
      <div class="btn-list">
        <div class="btn-box"
             @click="addForm(1)">
          <img src="../../../../../../assets/images/radio-two.png" />
          <span>单选</span>
        </div>
        <div class="btn-box"
             @click="addForm(2)">
          <img src="../../../../../../assets/images/check-correct.png" />
          <span>多选</span>
        </div>
        <div class="btn-box"
             @click="addForm(3)">
          <img src="../../../../../../assets/images/text.png" />
          <span>文本</span>
        </div>
        <div class="btn-box"
             @click="addForm(4)">
          <img src="../../../../../../assets/images/report.png" />
          <span>从题库添加</span>
        </div>
      </div>
    </div>
    <zy-pop-up v-model="showAddFormBank"
               title="从题库添加">
      <add-question @callBack="callBack"
                    :formList="formList"></add-question>
    </zy-pop-up>
  </div>
</template>
<script>
import addQuestion from './widget/add-question.vue'
export default {
  components: { addQuestion },
  data () {
    return {
      keyword: '',
      showAddFormBank: false,
      formList: []
    }
  },
  computed: {
    srcList: function () {
      return this.list.map(v => v.imgPath)
    }
  },
  methods: {
    callBack (items) {
      this.showAddFormBank = false
      items.forEach(item => {
        delete item.id
        delete item.checked
        this.$set(item, 'isMust', 1)
        this.formList.push(item)
      })
    },
    addForm (val) {
      switch (val) {
        case 1:
          this.formList.push({
            question: '',
            answerType: 1,
            choiceText: ['', ''],
            isMust: 1
          })
          break
        case 2:
          this.formList.push({
            question: '',
            answerType: 2,
            choiceText: ['', ''],
            isMust: 1
          })
          break
        case 3:
          this.formList.push({
            question: '',
            answerType: 3,
            isMust: 1
          })
          break
        case 4:
          this.showAddFormBank = true
          break
      }
    },
    search () {
      if (this.keyword === '') {
        return this.$message.warning('请输入想要搜索的')
      }
    },
    reset () {
      this.keyword = ''
    },
    deleteList (type, kid) {
      this[type].splice(this[type].findIndex(v => v.kid === kid), 1)
    },
    swapArray (arr, index1, index2) {
      if (index1 === index2) {
        arr = arr.splice(index1, 1)
        return arr
      }
      arr[index1] = arr.splice(index2, 1, arr[index1])[0]
      return arr
    },
    addItems (item) {
      item.choiceText.push('')
    },
    validForm () {
      return this.formList.every(item => {
        if (item.answerType === 1 || item.answerType === 2) {
          return item.question && item.choiceText.every(val => {
            return val
          })
        } else {
          return item.question
        }
      })
    }
  }
}
</script>
<style lang="scss">
.vote-item-list {
  .search-box {
    box-shadow: none;
    margin: 10px 0;
  }
  .qd-btn-box {
    height: 54px;
    display: flex;
    align-items: center;
  }
  .add-box {
    height: 160px;
    background: #f9fcff;
    border: 1px dashed #007bff;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .main-text {
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: 500;
      color: #333333;
      margin-bottom: 40px;
    }
    .btn-list {
      display: flex;
      .btn-box {
        padding: 0px 40px;
        display: flex;
        align-items: center;
        cursor: pointer;
        img {
          width: 18px;
          margin-right: 7px;
        }
        span {
          font-weight: 500;
          color: #007bff;
          font-size: $textSize16;
        }
      }
    }
  }
  .radio-item {
    border: 2px solid transparent;
    box-shadow: 0px 0px 10px 0px #e3e3e3;
    border-radius: 10px;
    margin-bottom: 23px;
    transition: all 0.2s;
    &:hover {
      border: 2px solid #007bff;
      .nav-box .nav-right .radio-header .edit-list {
        visibility: visible;
      }
    }
    .nav-box {
      display: flex;
      padding: 26px 42px 0px 42px;

      .nav-left {
        margin-right: 40px;
        flex-shrink: 0;
        width: 24px;
        .indexNum {
          font-size: 18px;
          font-family: PingFang SC;
          font-weight: 500;
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 40px;
          .red-dot {
            color: red;
            display: inline-block;
          }
        }
      }
      .nav-right {
        width: 100%;
        .radio-header {
          box-sizing: border-box;
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          transition: all 0.2s;
          .title-input {
            width: 60%;
          }
          .edit-list {
            visibility: hidden;
            display: flex;
            justify-content: flex-end;
            img {
              width: 25px;
              cursor: pointer;
              margin-left: 38px;
            }
          }
        }
        .radio-box {
          width: 100%;
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          transition: all 0.2s;
          &:hover {
            .radio-box-width {
              border: 2px dashed #ebebeb;
            }
            .edit-list {
              visibility: visible;
            }
          }
          .radio-box-width {
            border: 2px solid transparent;
            width: 60%;
            display: flex;
            align-items: center;
            box-sizing: border-box;
            padding-left: 12px;
            border-radius: 4px;
            .el-input__inner {
              border: none;
            }
            .o-box,
            .square-box {
              width: 18px;
              height: 18px;
              background: #ffffff;
              border: 1px solid #cccccc;
              margin-right: 9px;
              flex-shrink: 0;
            }
            .o-box {
              border-radius: 50%;
            }
            .square-box {
              border-radius: 4px;
            }
          }
          .edit-list {
            visibility: hidden;
            display: flex;
            width: 120px;
            justify-content: space-between;
            margin-left: 20px;
            img {
              width: 30px;
              cursor: pointer;
            }
          }
        }
      }
    }
    .card-buttom {
      border-top: 1px solid #ebebeb;
      padding: 29px 42px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 18px;
      font-weight: 400;
      color: #333333;
      .add-button {
        width: 130px;
        cursor: pointer;
      }
      .swith-box {
        margin-right: 90px;
        .swith-text {
          margin-right: 19px;
        }
      }
    }
  }
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
