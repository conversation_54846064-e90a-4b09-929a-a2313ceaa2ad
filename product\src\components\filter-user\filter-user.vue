<template>
  <div class="user-filter">
    <div class="search-box">
      <el-input v-model="keyword" placeholder="搜索" size="mini" @keyup.enter.native="search">
        <i class="el-icon-search el-icon-input" slot="prefix"> </i>
      </el-input>
      <el-button type="primary" icon="el-icon-search" size="mini" @click="search"></el-button>
      <el-button icon="el-icon-refresh" size="mini" @click="reset"></el-button>
    </div>
    <div class="select-all">
      <el-checkbox v-model="isAll" :indeterminate="isInde" @change="handleAll">全选</el-checkbox>
    </div>
    <el-checkbox-group v-model="checkList" @change="handleCheckItem">
      <ul class="check-user">
        <li v-for="item in searchList" :key="item.userId">
          <el-checkbox :label="item.userId">{{item.name}}</el-checkbox>
        </li>
      </ul>
    </el-checkbox-group>
    <div class="user-filter-btn">
      <el-button type="primary" size="mini" @click="handleDelete">删除</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    userList: Array
  },
  name: 'zy-filter-user',
  data() {
    return {
      keyword: '',
      searchList: [],
      isAll: false,
      isVisible: false,
      checkList: [],
      isInde: false
    }
  },
  created() {
    this.searchList = this.userList.map(v => {
      v.check = false
      return v
    })
  },
  methods: {
    search() {
      if (this.keyword === '') {
        return this.$message.warning('请选择想要筛选的项')
      }
      this.searchList = this.userList.filter(v => v.name.indexOf(this.keyword) !== -1)
      this.checkList = []
      this.isInde = false
      this.isAll = false
    },
    reset() {
      this.keyword = ''
      this.searchList = this.userList.map(v => {
        v.check = false
        return v
      })
      this.checkList = []
      this.isInde = false
      this.isAll = false
    },
    handleDelete() {
      const arr = this.userList
      this.checkList.map(v => {
        this.searchList.splice(this.userList.findIndex(item => item.userId === v), 1)
        arr.splice(arr.findIndex(item => item.userId === v), 1)
      })
      // 删除的时候 也处理一下
      this.handleCheckItem()
      this.$emit('update:userList', arr)
    },
    // 处理全选/全不选
    handleAll() {
      if (this.isAll) {
        this.checkList = this.searchList.map(v => v.userId)
      } else {
        this.checkList = []
      }
      this.isInde = false
    },
    // 处理 checkbox 的不确定状态
    handleCheckItem() {
      if (this.checkList.length > 0 && this.checkList.length < this.searchList.length) {
        this.isInde = true
      } else {
        this.isInde = false
      }
      if (this.checkList.length === this.searchList.length) {
        this.isAll = true
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.user-filter {
  width: 640px;
  padding: 20px;
  .search-box {
    display: flex;
    .el-input {
      width: calc(100% - 150px);
      margin-right: 12px;
      .el-icon-input {
        line-height: 28px;
      }
    }
  }
  .check-user {
    display: flex;
    flex-wrap: wrap;
    height: 256px;
    overflow: hidden;
    align-content: flex-start;
    li {
      width: 25%;
      height: 36px;
      display: flex;
      align-items: center;
    }
  }
  .check-user:hover {
    overflow-y: scroll;
    overflow-y: overlay;
  }
  ::-webkit-scrollbar {
    width: 6px;
  }
  ::-webkit-scrollbar-track {
    border-radius: 6px;
  }
  ::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.1);
  }
  .select-all {
    height: 36px;
    display: flex;
    align-items: center;
  }
  .user-filter-btn {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 10px;
    justify-content: flex-end;
  }
}
</style>
