<div class="apiDetail">
<div>
	<h2><span>Number</span><span class="path">setting.edit.drag.</span>autoOpenTime</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.exedit</span> 扩展 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>拖拽时父节点自动展开的延时间隔。 (单位：ms)<span class="highlight_red">[setting.edit.enable = true 时生效]</span></p>
			<p>默认值：500</p>
			<p class="highlight_red">请根据自己的需求适当调整此值</p>
		</div>
	</div>
	<h3>setting 举例</h3>
	<h4>1. 设置拖拽到父节点上立刻自动展开</h4>
	<pre xmlns=""><code>var setting = {
	edit: {
		enable: true,
		drag: {
			autoOpenTime: 0
		}
	}
};
......</code></pre>
</div>
</div>