<template>
  <div class="details activity-leave-info">
    <div class="details-item-box">
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">请假人</div>
          <div class="details-item-value">{{info.createName}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">请假类型</div>
          <div class="details-item-value">{{info.typeName}}</div>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">请假时间</div>
        <div class="details-item-value">{{info.typeName}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">审核状态</div>
        <div class="details-item-value">{{info.typeName}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">请假凭证</div>
        <div class="details-item-value">
          <div class="details-item-file" v-for="(item, index) in info.attachmentList" :key="index" @click="download(item)">{{item.fileName}}</div>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-content" v-html="info.content"></div>
      </div>
    </div>

  </div>
</template>

<script>
export default {
  props: {
    id: String
  },
  data() {
    return {
      info: {}
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      this.$api.activity.leave.info(this.id).then(res => {
        this.info = res.data
      })
    },
    download(val) {
      this.$api.proposal.downloadFile({ id: val.id }, val.fileName)
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-leave-info {
  width: 675px;
  padding: 10px 20px;
}
</style>
