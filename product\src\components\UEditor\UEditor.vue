<template>
  <div class="hello">
    <vue-ueditor-wrap v-model="model" @ready="ready" :config="myConfig"></vue-ueditor-wrap>
    <div class="helloText" v-if="maximumWords < length">当前已输入{{ length }}个字符，您最多可以输入{{ maximumWords
    }}个字符，当前已经超出{{ length - maximumWords }}个字符。
    </div>
    <div class="helloText" v-if="maximumWords > length">当前已输入{{ length }}个字符，您还可以输入{{ maximumWords - length
    }}个字符。</div>
    <div class="helloText" v-if="!maximumWords">当前已输入{{ length }}个字符</div>
  </div>
</template>

<script>
import VueUeditorWrap from './vue-ueditor-wrap.min.js'
export default {
  name: 'UEditor',
  components: {
    VueUeditorWrap // eslint-disable-line
  },
  data () {
    return {
      length: 0,
      myConfig: {
        // 是否跟随内容撑开
        autoHeightEnabled: false,
        elementPathEnabled: false,
        wordCount: false,
        // 高度
        initialFrameHeight: this.height,
        // 宽度
        initialFrameWidth: '100%',
        // 图片上传的路径
        serverUrl: `${this.$api.general.baseURL()}/ueditor/exec`,
        // serverUrl: `http://*************/lzt/ueditor/exec`,
        // 资源依赖的路径
        UEDITOR_HOME_URL: './UEditor/',
        // 鼠标右键的功能，但是一般不用编辑器带的功能，因为编辑器自带的不能右键复制，所以这个变量设置为空数组
        contextMenu: [],
        lineheight: [1, 1.25, 1.5, 1.75, 2, 2.25, 2.5, 2.75, 3, 3.25, 3.5, 3.75, 4],
        autotypeset: {
          mergeEmptyline: true, // 合并空行
          removeClass: true, // 去掉冗余的class
          removeEmptyline: false, // 去掉空行
          textAlign: 'left', // 段落的排版方式，可以是 left,right,center,justify 去掉这个属性表示不执行排版
          imageBlockLine: 'center', // 图片的浮动方式，独占一行剧中,左右浮动，默认: center,left,right,none 去掉这个属性表示不执行排版
          pasteFilter: false, // 根据规则过滤没事粘贴进来的内容
          clearFontSize: false, // 去掉所有的内嵌字号，使用编辑器默认的字号
          clearFontFamily: false, // 去掉所有的内嵌字体，使用编辑器默认的字体
          removeEmptyNode: false, // 去掉空节点
          // 可以去掉的标签
          removeTagNames: { 标签名字: 1 },
          indent: true, // 行首缩进
          indentValue: '2em', // 行首缩进的大小
          bdc2sb: false,
          tobdc: false
        },
        fontfamily: [
          { label: '', name: 'songti', val: '宋体, SimSun' },
          { label: '仿宋', name: 'fangsong', val: '仿宋, FangSong' },
          { label: '仿宋_GB2312', name: 'fangsong', val: '仿宋_GB2312, 仿宋, FangSong' },
          { label: '方正小标宋_GBK', name: '方正小标宋_GBK', val: '方正小标宋_GBK, 宋体, SimSun' },
          { label: '方正仿宋_GBK', name: '方正仿宋_GBK', val: '方正仿宋_GBK, 仿宋, FangSong' },
          { label: '方正楷体_GBK', name: '方正楷体_GBK', val: '方正楷体_GBK, 楷体, SimKai' },
          { label: '方正黑体_GBK', name: '方正黑体_GBK', val: '方正黑体_GBK, 黑体, SimHei' },
          { label: '', name: 'kaiti', val: '楷体, 楷体_GB2312, SimKai' },
          { label: '', name: 'yahei', val: '微软雅黑, Microsoft YaHei' },
          { label: '', name: 'heiti', val: '黑体, SimHei' },
          { label: '', name: 'lishu', val: '隶书, SimLi' },
          { label: '', name: 'andaleMono', val: 'andale mono' },
          { label: '', name: 'arial', val: 'arial, helvetica,sans-serif' },
          { label: '', name: 'arialBlack', val: 'arial black,avant garde' },
          { label: '', name: 'comicSansMs', val: 'comic sans ms' },
          { label: '', name: 'impact', val: 'impact,chicago' },
          { label: '', name: 'timesNewRoman', val: 'times new roman' }
        ],
        toolbars: this.toolbars,
        zIndex: 999
      }
    }
  },
  computed: {
    model: {
      get () {
        return this.value
      },
      set (value) {
        this.length = value ? value.replace(/<.*?>/g, '').replace(/&nbsp;/ig, ' ').length : 0
        this.$emit('input', value)
      }
    }
  },
  props: {
    type: [String],
    value: {
      type: String
    },
    maximumWords: {
      type: Number
    },
    height: {
      type: Number,
      default: 280
    },
    toolbars: {
      type: Array,
      default: () => [
        [
          'fullscreen',
          'source', '|', 'undo', 'redo', '|',
          'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
          'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
          'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
          'directionalityltr', 'directionalityrtl', 'indent', '|',
          'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 'touppercase', 'tolowercase', '|',
          'link', 'unlink', 'anchor', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
          'simpleupload', 'insertimage', 'emotion', 'scrawl',
          'insertvideo',
          'music', 'attachment', 'map', 'gmap', 'insertframe', 'insertcode', 'webapp', 'pagebreak', 'template', 'background', '|',
          'horizontal', 'date', 'time', 'spechars', 'snapscreen', 'wordimage', '|',
          'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', 'charts', '|',
          'print', 'preview', 'searchreplace', 'drafts', 'help'
        ]
      ]
    }
  },
  methods: {
    blur () {
      this.$emit('blur')
    },
    ready (editor) {
      editor.addListener('blur', this.blur)
      if (this.type === 'ta') {
        editor.ready(() => {
          editor.execCommand('fontfamily', 'CESI仿宋-GB2312') // 字体
          editor.execCommand('lineheight', 2) // 行间距
          editor.execCommand('fontsize', '21px') // 字号
        })
      } else {
        editor.ready(() => {
          editor.execCommand('fontfamily', '宋体') // 字体
          editor.execCommand('lineheight', 2) // 行间距
        })
      }
      this.editor = editor
    },
    down () {
      this.editor.ui.setFullScreen(false)
    }
  }
}
</script>

<style lang="scss">
.hello {
  overflow: hidden;

  .edui-editor {
    border-radius: 0 !important;
  }

  .edui-default {
    // z-index: 10000 !important; /* 你可以根据需要设置不同的层级值 */
  }

  .helloText {
    white-space: nowrap;
    border-top: 0;
    line-height: 24px;
    font-size: 14px;
    font-family: Arial, Helvetica, Tahoma, Verdana, Sans-Serif;
    text-align: right;
    color: #aaa;
    border: 1px solid #ccc;
    border-top: 0;
    padding-right: 6px;
  }

  h1,
  h2 {
    font-weight: normal;
  }

  ul {
    list-style-type: none;
    padding: 0;
  }

  li {
    display: inline-block;
    margin: 0 10px;
  }

  a {
    color: #42b983;
  }
}
</style>
