<template>
  <div class="totalLibraryType">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="一级类别"
                    prop="bookTypeFirstId"
                    class="form-input">
        <el-select v-model="form.bookTypeFirstId"
                   filterable
                   clearable
                   placeholder="请选择一级类别">
          <el-option v-for="item in bookType"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="二级类别"
                    class="form-input">
        <el-select v-model="form.bookTypeSecondId"
                   filterable
                   clearable
                   placeholder="请选择二级类别">
          <el-option v-for="item in typeSmall"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'totalLibraryType',
  data () {
    return {
      form: {
        bookTypeFirstId: '',
        bookTypeSecondId: ''
      },
      rules: {
        bookTypeFirstId: [
          { required: true, message: '请选择一级类别', trigger: 'blur' }
        ]
      },
      bookType: [],
      typeSmall: []
    }
  },
  mounted () {
    this.getSyTypeTree()
  },
  watch: {
    'form.bookTypeFirstId' (val) {
      if (val) {
        this.bookType.forEach(item => {
          if (item.id === val) {
            this.form.bookTypeSecondId = ''
            this.typeSmall = item.children
          }
        })
      } else {
        this.form.bookTypeSecondId = ''
        this.typeSmall = []
      }
    }
  },
  methods: {
    async getSyTypeTree () {
      const res = await this.$api.academy.getSyTypeTree({})
      var { data } = res
      this.bookType = data
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('callback', true, this.form.bookTypeFirstId, this.form.bookTypeSecondId)
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('callback', false)
    }
  }
}
</script>
<style lang="scss">
.totalLibraryType {
  width: 680px;
  padding: 24px;
}
</style>
