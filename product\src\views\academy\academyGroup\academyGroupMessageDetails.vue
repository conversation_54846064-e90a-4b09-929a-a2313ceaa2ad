<template>
  <div class="goodWordsDetails details">
    <div class="details-title">消息详情</div>
    <div class="details-item-box">
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">消息类型</div>
          <div class="details-item-value">{{details.messageType==1?'文字':'图片'}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">发布时间</div>
          <div class="details-item-value">{{details.createDate|datefmt('YYYY-MM-DD HH:mm:ss')}}</div>
        </div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">发布人</div>
          <div class="details-item-value">{{details.userName}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">来源</div>
          <div class="details-item-value">{{details.groupName}}</div>
        </div>
      </div>
      <div class="details-content"
           v-if="details.messageType==1"
           v-html="details.content"></div>
      <div class="details-item"
           v-else>
        <div class="details-item-label">图片</div>
        <div class="details-item-value">
          <div class="readingNotesDetailsImg"
               v-if="!details.content">
          </div>
          <div class="readingNotesDetailsImg"
               v-if="details.content">
            <el-image style="width: 100%; height: 100%"
                      :src="details.content"
                      :preview-src-list="[details.content]">
            </el-image>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'goodWordsDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id'],
  mounted () {
    this.groupmessageInfo()
  },
  methods: {
    async groupmessageInfo () {
      const res = await this.$api.academy.groupmessageInfo(this.id)
      var { data } = res
      this.details = data
    }
  }
}
</script>
<style lang="scss">
.goodWordsDetails {
  width: 820px;
  height: 100%;
  padding: 24px;

  .readingNotesDetailsImg {
    width: 95px;
    height: 128px;
    margin: 12px 0;
    .el-icon-circle-close {
      color: #fff;
    }
  }
  .details-content {
    width: 100%;
    padding: 24px;
    line-height: 26px;

    img {
      max-width: 700px;
      margin: auto;
    }
  }
}
</style>
