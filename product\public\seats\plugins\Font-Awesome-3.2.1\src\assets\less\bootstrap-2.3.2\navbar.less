//
// Navbars (Redux)
// --------------------------------------------------


// COMMON STYLES
// -------------

// Base class and wrapper
.navbar {
  overflow: visible;
  margin-bottom: @baseLineHeight;

  // Fix for IE7's bad z-indexing so dropdowns don't appear below content that follows the navbar
  *position: relative;
  *z-index: 2;
}

// Inner for background effects
// Gradient is applied to its own element because overflow visible is not honored by IE when filter is present
.navbar-inner {
  min-height: @navbarHeight;
  padding-left:  20px;
  padding-right: 20px;
  #gradient > .vertical(@navbarBackgroundHighlight, @navbarBackground);
  border: 1px solid @navbarBorder;
  .border-radius(@baseBorderRadius);
  .box-shadow(0 1px 4px rgba(0,0,0,.065));

  // Prevent floats from breaking the navbar
  .clearfix();
}

// Set width to auto for default container
// We then reset it for fixed navbars in the #gridSystem mixin
.navbar .container {
  width: auto;
}

// Override the default collapsed state
.nav-collapse.collapse {
  height: auto;
  overflow: visible;
}


// Brand: website or project name
// -------------------------
.navbar .brand {
  float: left;
  display: block;
  // Vertically center the text given @navbarHeight
  padding: ((@navbarHeight - @baseLineHeight) / 2) 20px ((@navbarHeight - @baseLineHeight) / 2);
  margin-left: -20px; // negative indent to left-align the text down the page
  font-size: 20px;
  font-weight: 200;
  color: @navbarBrandColor;
  text-shadow: 0 1px 0 @navbarBackgroundHighlight;
  &:hover,
  &:focus {
    text-decoration: none;
  }
}

// Plain text in topbar
// -------------------------
.navbar-text {
  margin-bottom: 0;
  line-height: @navbarHeight;
  color: @navbarText;
}

// Janky solution for now to account for links outside the .nav
// -------------------------
.navbar-link {
  color: @navbarLinkColor;
  &:hover,
  &:focus {
    color: @navbarLinkColorHover;
  }
}

// Dividers in navbar
// -------------------------
.navbar .divider-vertical {
  height: @navbarHeight;
  margin: 0 9px;
  border-left: 1px solid @navbarBackground;
  border-right: 1px solid @navbarBackgroundHighlight;
}

// Buttons in navbar
// -------------------------
.navbar .btn,
.navbar .btn-group {
  .navbarVerticalAlign(30px); // Vertically center in navbar
}
.navbar .btn-group .btn,
.navbar .input-prepend .btn,
.navbar .input-append .btn,
.navbar .input-prepend .btn-group,
.navbar .input-append .btn-group {
  margin-top: 0; // then undo the margin here so we don't accidentally double it
}

// Navbar forms
// -------------------------
.navbar-form {
  margin-bottom: 0; // remove default bottom margin
  .clearfix();
  input,
  select,
  .radio,
  .checkbox {
    .navbarVerticalAlign(30px); // Vertically center in navbar
  }
  input,
  select,
  .btn {
    display: inline-block;
    margin-bottom: 0;
  }
  input[type="image"],
  input[type="checkbox"],
  input[type="radio"] {
    margin-top: 3px;
  }
  .input-append,
  .input-prepend {
    margin-top: 5px;
    white-space: nowrap; // preven two  items from separating within a .navbar-form that has .pull-left
    input {
      margin-top: 0; // remove the margin on top since it's on the parent
    }
  }
}

// Navbar search
// -------------------------
.navbar-search {
  position: relative;
  float: left;
  .navbarVerticalAlign(30px); // Vertically center in navbar
  margin-bottom: 0;
  .search-query {
    margin-bottom: 0;
    padding: 4px 14px;
    #font > .sans-serif(13px, normal, 1);
    .border-radius(15px); // redeclare because of specificity of the type attribute
  }
}



// Static navbar
// -------------------------

.navbar-static-top {
  position: static;
  margin-bottom: 0; // remove 18px margin for default navbar
  .navbar-inner {
    .border-radius(0);
  }
}



// Fixed navbar
// -------------------------

// Shared (top/bottom) styles
.navbar-fixed-top,
.navbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: @zindexFixedNavbar;
  margin-bottom: 0; // remove 18px margin for default navbar
}
.navbar-fixed-top .navbar-inner,
.navbar-static-top .navbar-inner {
  border-width: 0 0 1px;
}
.navbar-fixed-bottom .navbar-inner {
  border-width: 1px 0 0;
}
.navbar-fixed-top .navbar-inner,
.navbar-fixed-bottom .navbar-inner {
  padding-left:  0;
  padding-right: 0;
  .border-radius(0);
}

// Reset container width
// Required here as we reset the width earlier on and the grid mixins don't override early enough
.navbar-static-top .container,
.navbar-fixed-top .container,
.navbar-fixed-bottom .container {
  #grid > .core > .span(@gridColumns);
}

// Fixed to top
.navbar-fixed-top {
  top: 0;
}
.navbar-fixed-top,
.navbar-static-top {
  .navbar-inner {
    .box-shadow(~"0 1px 10px rgba(0,0,0,.1)");
  }
}

// Fixed to bottom
.navbar-fixed-bottom {
  bottom: 0;
  .navbar-inner {
    .box-shadow(~"0 -1px 10px rgba(0,0,0,.1)");
  }
}



// NAVIGATION
// ----------

.navbar .nav {
  position: relative;
  left: 0;
  display: block;
  float: left;
  margin: 0 10px 0 0;
}
.navbar .nav.pull-right {
  float: right; // redeclare due to specificity
  margin-right: 0; // remove margin on float right nav
}
.navbar .nav > li {
  float: left;
}

// Links
.navbar .nav > li > a {
  float: none;
  // Vertically center the text given @navbarHeight
  padding: ((@navbarHeight - @baseLineHeight) / 2) 15px ((@navbarHeight - @baseLineHeight) / 2);
  color: @navbarLinkColor;
  text-decoration: none;
  text-shadow: 0 1px 0 @navbarBackgroundHighlight;
}
.navbar .nav .dropdown-toggle .caret {
  margin-top: 8px;
}

// Hover/focus
.navbar .nav > li > a:focus,
.navbar .nav > li > a:hover {
  background-color: @navbarLinkBackgroundHover; // "transparent" is default to differentiate :hover/:focus from .active
  color: @navbarLinkColorHover;
  text-decoration: none;
}

// Active nav items
.navbar .nav > .active > a,
.navbar .nav > .active > a:hover,
.navbar .nav > .active > a:focus {
  color: @navbarLinkColorActive;
  text-decoration: none;
  background-color: @navbarLinkBackgroundActive;
  .box-shadow(inset 0 3px 8px rgba(0,0,0,.125));
}

// Navbar button for toggling navbar items in responsive layouts
// These definitions need to come after '.navbar .btn'
.navbar .btn-navbar {
  display: none;
  float: right;
  padding: 7px 10px;
  margin-left: 5px;
  margin-right: 5px;
  .buttonBackground(darken(@navbarBackgroundHighlight, 5%), darken(@navbarBackground, 5%));
  .box-shadow(~"inset 0 1px 0 rgba(255,255,255,.1), 0 1px 0 rgba(255,255,255,.075)");
}
.navbar .btn-navbar .icon-bar {
  display: block;
  width: 18px;
  height: 2px;
  background-color: #f5f5f5;
  .border-radius(1px);
  .box-shadow(0 1px 0 rgba(0,0,0,.25));
}
.btn-navbar .icon-bar + .icon-bar {
  margin-top: 3px;
}



// Dropdown menus
// --------------

// Menu position and menu carets
.navbar .nav > li > .dropdown-menu {
  &:before {
    content: '';
    display: inline-block;
    border-left:   7px solid transparent;
    border-right:  7px solid transparent;
    border-bottom: 7px solid #ccc;
    border-bottom-color: @dropdownBorder;
    position: absolute;
    top: -7px;
    left: 9px;
  }
  &:after {
    content: '';
    display: inline-block;
    border-left:   6px solid transparent;
    border-right:  6px solid transparent;
    border-bottom: 6px solid @dropdownBackground;
    position: absolute;
    top: -6px;
    left: 10px;
  }
}
// Menu position and menu caret support for dropups via extra dropup class
.navbar-fixed-bottom .nav > li > .dropdown-menu {
  &:before {
    border-top: 7px solid #ccc;
    border-top-color: @dropdownBorder;
    border-bottom: 0;
    bottom: -7px;
    top: auto;
  }
  &:after {
    border-top: 6px solid @dropdownBackground;
    border-bottom: 0;
    bottom: -6px;
    top: auto;
  }
}

// Caret should match text color on hover/focus
.navbar .nav li.dropdown > a:hover .caret,
.navbar .nav li.dropdown > a:focus .caret {
  border-top-color: @navbarLinkColorHover;
  border-bottom-color: @navbarLinkColorHover;
}

// Remove background color from open dropdown
.navbar .nav li.dropdown.open > .dropdown-toggle,
.navbar .nav li.dropdown.active > .dropdown-toggle,
.navbar .nav li.dropdown.open.active > .dropdown-toggle {
  background-color: @navbarLinkBackgroundActive;
  color: @navbarLinkColorActive;
}
.navbar .nav li.dropdown > .dropdown-toggle .caret {
  border-top-color: @navbarLinkColor;
  border-bottom-color: @navbarLinkColor;
}
.navbar .nav li.dropdown.open > .dropdown-toggle .caret,
.navbar .nav li.dropdown.active > .dropdown-toggle .caret,
.navbar .nav li.dropdown.open.active > .dropdown-toggle .caret {
  border-top-color: @navbarLinkColorActive;
  border-bottom-color: @navbarLinkColorActive;
}

// Right aligned menus need alt position
.navbar .pull-right > li > .dropdown-menu,
.navbar .nav > li > .dropdown-menu.pull-right {
  left: auto;
  right: 0;
  &:before {
    left: auto;
    right: 12px;
  }
  &:after {
    left: auto;
    right: 13px;
  }
  .dropdown-menu {
    left: auto;
    right: 100%;
    margin-left: 0;
    margin-right: -1px;
    .border-radius(6px 0 6px 6px);
  }
}


// Inverted navbar
// -------------------------

.navbar-inverse {

  .navbar-inner {
    #gradient > .vertical(@navbarInverseBackgroundHighlight, @navbarInverseBackground);
    border-color: @navbarInverseBorder;
  }

  .brand,
  .nav > li > a {
    color: @navbarInverseLinkColor;
    text-shadow: 0 -1px 0 rgba(0,0,0,.25);
    &:hover,
    &:focus {
      color: @navbarInverseLinkColorHover;
    }
  }

  .brand {
    color: @navbarInverseBrandColor;
  }

  .navbar-text {
    color: @navbarInverseText;
  }

  .nav > li > a:focus,
  .nav > li > a:hover {
    background-color: @navbarInverseLinkBackgroundHover;
    color: @navbarInverseLinkColorHover;
  }

  .nav .active > a,
  .nav .active > a:hover,
  .nav .active > a:focus {
    color: @navbarInverseLinkColorActive;
    background-color: @navbarInverseLinkBackgroundActive;
  }

  // Inline text links
  .navbar-link {
    color: @navbarInverseLinkColor;
    &:hover,
    &:focus {
      color: @navbarInverseLinkColorHover;
    }
  }

  // Dividers in navbar
  .divider-vertical {
    border-left-color: @navbarInverseBackground;
    border-right-color: @navbarInverseBackgroundHighlight;
  }

  // Dropdowns
  .nav li.dropdown.open > .dropdown-toggle,
  .nav li.dropdown.active > .dropdown-toggle,
  .nav li.dropdown.open.active > .dropdown-toggle {
    background-color: @navbarInverseLinkBackgroundActive;
    color: @navbarInverseLinkColorActive;
  }
  .nav li.dropdown > a:hover .caret,
  .nav li.dropdown > a:focus .caret {
    border-top-color: @navbarInverseLinkColorActive;
    border-bottom-color: @navbarInverseLinkColorActive;
  }
  .nav li.dropdown > .dropdown-toggle .caret {
    border-top-color: @navbarInverseLinkColor;
    border-bottom-color: @navbarInverseLinkColor;
  }
  .nav li.dropdown.open > .dropdown-toggle .caret,
  .nav li.dropdown.active > .dropdown-toggle .caret,
  .nav li.dropdown.open.active > .dropdown-toggle .caret {
    border-top-color: @navbarInverseLinkColorActive;
    border-bottom-color: @navbarInverseLinkColorActive;
  }

  // Navbar search
  .navbar-search {
    .search-query {
      color: @white;
      background-color: @navbarInverseSearchBackground;
      border-color: @navbarInverseSearchBorder;
      .box-shadow(~"inset 0 1px 2px rgba(0,0,0,.1), 0 1px 0 rgba(255,255,255,.15)");
      .transition(none);
      .placeholder(@navbarInverseSearchPlaceholderColor);

      // Focus states (we use .focused since IE7-8 and down doesn't support :focus)
      &:focus,
      &.focused {
        padding: 5px 15px;
        color: @grayDark;
        text-shadow: 0 1px 0 @white;
        background-color: @navbarInverseSearchBackgroundFocus;
        border: 0;
        .box-shadow(0 0 3px rgba(0,0,0,.15));
        outline: 0;
      }
    }
  }

  // Navbar collapse button
  .btn-navbar {
    .buttonBackground(darken(@navbarInverseBackgroundHighlight, 5%), darken(@navbarInverseBackground, 5%));
  }

}
