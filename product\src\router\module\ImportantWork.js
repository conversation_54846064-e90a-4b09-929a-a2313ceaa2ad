const workColumn = () => import('../../views/ImportantWork/workColumn/workColumn')
const workNotice = () => import('../../views/ImportantWork/workNotice/workNotice')
const workTeam = () => import('../../views/ImportantWork/workTeam/workTeam')
const majorProjects = () => import('../../views/ImportantWork/majorProjects/majorProjects')
const contactCompany = () => import('../../views/ImportantWork/contactCompany/contactCompany')
const villageContactPoint = () => import('../../views/ImportantWork/villageContactPoint/villageContactPoint')
const contactExperts = () => import('../../views/ImportantWork/contactExperts/contactExperts')
const attractInvestment = () => import('../../views/ImportantWork/attractInvestment/attractInvestment')

const ImportantWork = [
  { // 栏目
    path: '/workColumn',
    name: 'workColumn',
    component: workColumn
  },
  { // 工作通知
    path: '/workNotice',
    name: 'workNotice',
    component: workNotice
  },
  { // 工作专班
    path: '/workTeam',
    name: 'workTeam',
    component: workTeam
  },
  { // 顶格推进重大项目
    path: '/majorProjects',
    name: 'majorProjects',
    component: majorProjects
  },
  { // 联系企业
    path: '/contactCompany',
    name: 'contactCompany',
    component: contactCompany
  },
  { // 乡村振兴联络点
    path: '/villageContactPoint',
    name: 'villageContactPoint',
    component: villageContactPoint
  },
  { // 联系服务专家
    path: '/contactExperts',
    name: 'contactExperts',
    component: contactExperts
  },
  { // 招商引资情况统计
    path: '/attractInvestment',
    name: 'attractInvestment',
    component: attractInvestment
  }
]
export default ImportantWork
