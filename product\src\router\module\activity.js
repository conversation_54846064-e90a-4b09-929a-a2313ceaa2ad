/**
 * 活动补录
 */

const activity = [
  {
    path: '/activity-add',
    name: 'activity-add',
    component: () => import(/* 政协活动 活动新增 */ '@activity/add/add.vue')
  },
  {
    path: '/activity-manage',
    name: 'activity-manage',
    component: () => import(/* 政协活动 活动管理 */ '@activity/manage/manage.vue')
  },
  {
    path: '/activity-mine',
    name: 'activity-mine',
    component: () => import(/* 政协活动 我的活动 */ '@activity/mine/mine.vue')
  },
  {
    path: '/activity-make-up',
    name: 'activity-make-up',
    component: () => import(/* 政协活动 活动补录 */ '@activity/make-up/make-up.vue')
  },
  {
    path: '/activity-make-up-manage',
    name: 'activity-make-up-manage',
    component: () => import(/* 政协活动 活动补录管理 */ '@activity/make-up/make-up-manage.vue')
  },
  {
    path: '/activity-leave',
    name: 'activity-leave',
    component: () => import(/* 政协活动 请假 */ '@activity/leave-verify/leave-verify.vue')
  },
  {
    path: '/activity-files',
    name: 'activity-files',
    component: () => import(/* 政协活动 资料 */ '@activity/files/files.vue')
  },
  {
    path: '/activity-attendance',
    name: 'activity-attendance',
    component: () => import(/* 政协活动 考勤 */ '@activity/attendance/attendance.vue')
  }

]

export default activity
