<template>
  <div class="activity-file-list">
    <search-box @search-click="search" @reset-click="reset" title="活动资料筛选">
      <zy-widget label="关键字">
        <el-input v-model="keyword" placeholder="请输入关键字" clearable @keyup.enter.native="search"></el-input>
      </zy-widget>
    </search-box>
    <div class="qd-btn-box">
      <el-button type="primary" size="small" icon="el-icon-plus" @click="isAdd=true">新增</el-button>
      <el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleBatchDelete">删除</el-button>
    </div>
    <el-table :data="list" stripe ref="table" @selection-change="handleSelectionChange">
      <el-table-column type="selection" fixed="left" width="60"></el-table-column>
      <el-table-column label="序号" prop="sort" min-width="80"></el-table-column>
      <el-table-column label="材料名称" prop="name" min-width="240" show-overflow-tooltip></el-table-column>
      <el-table-column label="材料名称" prop="name" min-width="240" show-overflow-tooltip></el-table-column>
      <el-table-column label="材料格式" prop="format" width="180"></el-table-column>
      <el-table-column label="创建人" prop="createName" min-width="180"></el-table-column>
      <el-table-column label="发布时间" prop="date" min-width="180"></el-table-column>
      <el-table-column label="是否公开" width="120">
        <template slot-scope="scope">
          {{scope.row.isAppShow === 1 ?'公开':'不公开'}}
        </template>
      </el-table-column>
    </el-table>
    <zy-pop-up v-model="isAdd" title="添加材料">
      <add-file @callback="handleCallback"></add-file>
    </zy-pop-up>
  </div>
</template>

<script>
import AddFile from './widget/add-file.vue'
export default {
  components: { AddFile },
  data() {
    return {
      keyword: '',
      list: [],
      selectionList: [],
      originList: [],
      isAdd: false
    }
  },
  methods: {
    search() {
      if (this.keyword === '') {
        return this.$message.warning('请输入想要搜索的')
      }
      this.list = this.originList.filter(v => v.name.indexOf(this.keyword) !== -1)
    },
    reset() {
      this.keyword = ''
      this.list = []
    },
    handleBatchDelete() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要删除的项')
      }
      this.selectionList.map(v => this.deleteList(v.fileId))
      this.selectionList.map(v => this.deleteOriginList(v.fileId))
    },
    deleteList(fileId) {
      this.list.splice(this.list.findIndex(v => v.fileId === fileId), 1)
    },
    deleteOriginList(fileId) {
      this.originList.splice(this.originList.findIndex(v => v.fileId === fileId), 1)
    },
    handleSelectionChange(val) {
      this.selectionList = val
    },
    handleCallback(val) {
      this.list.push.apply(this.list, val)
      this.originList = this.list
      this.isAdd = false
    }
  }
}
</script>
<style lang="scss">
.activity-file-list {
  .search-box {
    box-shadow: none;
    margin: 10px 0;
  }
  .qd-btn-box {
    height: 54px;
    display: flex;
    align-items: center;
  }
}
</style>
