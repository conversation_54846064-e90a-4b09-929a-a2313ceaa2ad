import {
  post
} from '../http'
const helpCenter = {
  tools: {
    list (params) {
      // const { keyword, pageNo, pageSize} = params
      return post('tools/list', params)
    },
    info (params) {
      return post(`/tools/info/${params}`)
    },
    add (params) {
      return post('/tools/add', params)
    },
    edit (params) {
      return post('/tools/edit', params)
    },
    dels (params) {
      return post('/tools/dels', params)
    }
  },
  operationmanual: {
    list (params) {
      return post('/operationmanual/list', params)
    },
    info (params) {
      return post(`/operationmanual/info/${params}`)
    },
    add (params) {
      return post('/operationmanual/add', params)
    },
    edit (params) {
      return post('/operationmanual/edit', params)
    },
    dels (params) {
      return post('/operationmanual/dels', params)
    }
  },
  tutorialvideo: {
    list (params) {
      return post('/tutorialvideo/list', params)
    },
    info (params) {
      return post(`/tutorialvideo/info/${params}`)
    },
    add (params) {
      return post('/tutorialvideo/add', params)
    },
    edit (params) {
      return post('/tutorialvideo/edit', params)
    },
    dels (params) {
      return post('/tutorialvideo/dels', params)
    }
  },
  helpcentermenu: {
    list (params) {
      return post('/helpcentermenu/list', params)
    },
    info (params) {
      return post(`/helpcentermenu/info/${params}`)
    },
    add (params) {
      return post('/helpcentermenu/add', params)
    },
    edit (params) {
      return post('/helpcentermenu/edit', params)
    },
    del (params) {
      return post(`/helpcentermenu/del/${params}`)
    },
    dels (params) {
      return post('/helpcentermenu/dels', params)
    }
  }
}
export default helpCenter
