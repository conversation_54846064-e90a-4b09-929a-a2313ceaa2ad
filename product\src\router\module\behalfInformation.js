const currentBehalf = () => import('@/views/behalfInformation/generalCurrentBehalf/currentBehalf')
const nationalCurrentBehalf = () => import('@/views/behalfInformation/generalCurrentBehalf/nationalCurrentBehalf')
const beforeBehalf = () => import('@/views/behalfInformation/generalBeforeBehalf/beforeBehalf')
const nationalBeforeBehalf = () => import('@/views/behalfInformation/generalBeforeBehalf/nationalBeforeBehalf')
const behalfStatistical = () => import('@/views/behalfInformation/generalBehalfStatistical/behalfStatistical')
const nationalBehalfStatistical = () => import('@/views/behalfInformation/generalBehalfStatistical/nationalBehalfStatistical')
const time = () => import('@/views/behalfInformation/generalTime/time')
const nationalTime = () => import('@/views/behalfInformation/generalTime/nationalTime')

const behalfInformation = [
  { // 本届代表
    path: '/currentBehalf',
    name: 'currentBehalf',
    component: currentBehalf
  },
  { // 本届全国代表
    path: '/nationalCurrentBehalf',
    name: 'nationalCurrentBehalf',
    component: nationalCurrentBehalf
  },
  { // 历届代表
    path: '/beforeBehalf',
    name: 'beforeBehalf',
    component: beforeBehalf
  },
  { // 历届全国代表
    path: '/nationalBeforeBehalf',
    name: 'nationalBeforeBehalf',
    component: nationalBeforeBehalf
  },
  { // 代表统计
    path: '/behalfStatistical',
    name: 'behalfStatistical',
    component: behalfStatistical
  },
  { // 全国代表统计
    path: '/nationalBehalfStatistical',
    name: 'nationalBehalfStatistical',
    component: nationalBehalfStatistical
  },
  { // 届次管理
    path: '/time',
    name: 'time',
    component: time
  },
  { // 全国届次管理
    path: '/nationalTime',
    name: 'nationalTime',
    component: nationalTime
  }
]
export default behalfInformation
