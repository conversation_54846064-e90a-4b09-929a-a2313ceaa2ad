<template>
  <div class="project-associated-new">
    <div class="button-box-list">
      <el-button type="primary"
                 @click="newData">关联</el-button>
      <el-input placeholder="请输入内容"
                clearable
                v-model="name"
                @keyup.enter.native="search">
        <div slot="prefix"
             class="input-search"></div>
      </el-input>
    </div>
    <div class="project-associated-box">
      <div class="project-associated-tree scrollBar">
        <el-tree :data="tree"
                 :props="defaultProps"
                 :expand-on-click-node="false"
                 @node-click="handleNodeClick"></el-tree>
      </div>
      <div class="project-associated-table">
        <div class="information-mosaic-list scrollBar">
          <el-table :data="tableData"
                    stripe
                    border
                    ref="table"
                    slot="zytable"
                    @select="selected"
                    @select-all="selectedAll">
            <el-table-column type="selection"
                             width="48">
            </el-table-column>
            <el-table-column label="标题"
                             prop="title"
                             show-overflow-tooltip>
            </el-table-column>
            <el-table-column label="发布时间"
                             width="160"
                             prop="createDate"
                             show-overflow-tooltip>
            </el-table-column>
            <el-table-column label="操作"
                             width="80">
              <template slot-scope="scope">
                <el-button @click="projectAssociatednew(scope.row.relateRecordId)"
                           type="text"
                           size="small">关联</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="paging_box">
          <el-pagination @size-change="howManyArticle"
                         @current-change="whatPage"
                         :current-page.sync="page"
                         :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                         :page-size.sync="pageSize"
                         background
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import tableData from '@mixins/tableData'
export default {
  name: 'projectAssociatedNew',
  data () {
    return {
      name: '',
      treeId: '',
      tree: [
        { id: '5', name: '资讯' },
        { id: '26', name: '会议' },
        { id: '4', name: '意见征集' },
        { id: '27', name: '通知公告' },
        { id: '53', name: '专题资讯' },
        { id: '49', name: '活动' }
      ],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10
    }
  },
  mixins: [tableData],
  props: ['id', 'columnId'],
  mounted () {
    this.value = 'relateRecordId'
  },
  methods: {
    handleNodeClick (data) {
      this.treeId = data.id
      this.choose = []
      this.selectObj = []
      this.projectAssociatedrecord()
    },
    async projectAssociatedrecord () {
      const res = await this.$api.appManagement.projectAssociatedrecord({
        subjectId: this.id,
        columnId: this.columnId,
        relateType: this.treeId,
        pageNo: this.page,
        pageSize: this.pageSize,
        title: this.name
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    search () {
      this.projectAssociatedrecord()
    },
    newData () {
      if (this.choose.length) {
        this.$confirm('此操作将关联当前选中的数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.projectAssociatednew(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消关联'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async projectAssociatednew (id) {
      const res = await this.$api.appManagement.projectAssociatednew({
        relateRecordIds: id,
        subjectId: this.id,
        columnId: this.columnId,
        relateType: this.treeId
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
        this.$emit('newCallback')
      }
    },
    howManyArticle (val) {
      this.projectAssociatedrecord()
    },
    whatPage (val) {
      this.projectAssociatedrecord()
    }
  }
}
</script>
<style lang="scss">
@import "./project-associated-new.scss";
</style>
