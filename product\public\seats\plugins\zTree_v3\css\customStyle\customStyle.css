/*-------------------------------------
zTree Style

version: 3.4
author: Hunter.z
website: http://code.google.com/p/jquerytree/
-------------------------------------*/
@font-face {font-family: "iconfont";
  src: url('./fonticons/iconfont.eot?t=1524794995988'); /* IE9*/
  src: url('./fonticons/iconfont.eot?t=1524794995988#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('./fonticons/iconfont.ttf?t=1524794995988') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('./fonticons/iconfont.svg?t=1524794995988#iconfont') format('svg'); /* iOS 4.1- */
}

.ztree * {
  padding: 0;
  margin:5px 0;
  color:#428bca;
  font-family: 'icomoon';
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.ztree {
  margin:0;
  padding:5px;
  padding-right: 0;
  color:#333
}
.ztree li {
  padding:0;
  margin:0;
  list-style:none;
  line-height:29px;
  text-align:left;
  white-space:nowrap;
  outline:0;
  /*border-bottom: 1px solid #ccc;*/
}
.ztree li:first-of-type {
  /*border-top:1px solid #ccc;*/
}
.ztree li:last-of-type {
  border:none;
}
.ztree li ul {
  margin:0;
  padding:0 0 0 18px
}
.ztree li ul.line {
  /* background:url(./img/line_conn.png) 0 0 repeat-y; */
}
.ztree li a {
  width: calc(100% - 49px);
  padding:5px 2px;
  margin:0 0 0 5px;
  cursor:pointer;
  color:#333;
  background-color: transparent;
  text-decoration:none;
  display: inline-block;
  text-decoration:none !important;
}

.ztree li a:hover {
  background-color: rgba(0, 0, 0, 0.05);
}
.ztree li a.curSelectedNode {
  background-color:#03a9f3;
  margin-left: 5px;
}
.ztree li a.curSelectedNode .node_name,.ztree li a.curSelectedNode .button{
  color:#fff;
}
.ztree li a.curSelectedNode_Edit {
  padding-top:0px;
  background-color:#e5e5e5;
  color:black;
  height:21px;
  border:1px #666 solid;
  opacity:0.8;
}
.ztree li a.tmpTargetNode_inner {
  padding-top:0px;
  background-color:#aaa;
  color:white;
  height:21px;
  border:1px #666 solid;
  opacity:0.8;
  filter:alpha(opacity=80)
}
.ztree li a.tmpTargetNode_prev {
}
.ztree li a.tmpTargetNode_next {
}
.ztree li a input.rename {
  height:14px;
  width:80px;
  padding:0;
  margin:0;
  font-size:12px;
  border:1px #585956 solid;
  *border:0px
}
.ztree li span {
  line-height:21px;
  margin-right:2px;
  margin-left:4px;
}
.ztree li span.button {
  line-height:0;
  margin:0;
  padding: 0;
  width:18px;
  height:18px;
  display: inline-block;
  vertical-align:middle;
  border:0 none;
  cursor: pointer;
  outline:none;
  /* background-color:transparent;
  background-repeat:no-repeat;
  background-attachment: scroll;
  background-image:url("./img/metro.png");
  *background-image:url("./img/metro.gif") */
}
.ztree li span.button.chk {
  width:18px;
  height:18px;
  margin:0 5px;
  cursor: auto;
}
.ztree li span.button.chk.checkbox_false_full::before {
  font-family:"iconfont" !important;
  font-size:20px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e686";
  line-height:16px;
  color:#03a9f3;
}
.ztree li span.button.chk.checkbox_false_full_focus::before {
  font-family:"iconfont" !important;
  font-size:20px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e686";
  line-height:16px;
  color:#03a9f3;
}
.ztree li span.button.chk.checkbox_false_part {
  background-position: -5px -48px;
}
.ztree li span.button.chk.checkbox_false_part_focus {
  background-position: -5px -68px;
}
.ztree li span.button.chk.checkbox_false_disable {
  background-position: -5px -89px;
}
.ztree li span.button.chk.checkbox_true_full::before {
  font-family:"iconfont" !important;
  font-size:20px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e685";
  line-height:16px;
  color:#03a9f3;
}
.ztree li span.button.chk.checkbox_true_full_focus::before {
  font-family:"iconfont" !important;
  font-size:20px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e685";
  line-height:16px;
  color:#03a9f3;
}
.ztree li span.button.chk.checkbox_true_part::before {
  font-family:"iconfont" !important;
  font-size:20px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e685";
  line-height:16px;
  color:#03a9f3;
}
.ztree li span.button.chk.checkbox_true_part_focus::before {
  font-family:"iconfont" !important;
  font-size:20px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e685";
  line-height:16px;
  color:#ccc;
}
.ztree li span.button.chk.checkbox_true_disable {
  background-position: -26px -89px;
}
.ztree li span.button.chk.radio_false_full {
  background-position: -47px -5px;
}
.ztree li span.button.chk.radio_false_full_focus {
  background-position: -47px -26px;
}
.ztree li span.button.chk.radio_false_part {
  background-position: -47px -47px;
}
.ztree li span.button.chk.radio_false_part_focus {
  background-position: -47px -68px;
}
.ztree li span.button.chk.radio_false_disable {
  background-position: -47px -89px;
}
.ztree li span.button.chk.radio_true_full {
  background-position: -68px -5px;
}
.ztree li span.button.chk.radio_true_full_focus {
  background-position: -68px -26px;
}
.ztree li span.button.chk.radio_true_part {
  background-position: -68px -47px;
}
.ztree li span.button.chk.radio_true_part_focus {
  background-position: -68px -68px;
}
.ztree li span.button.chk.radio_true_disable {
  background-position: -68px -89px;
}
.ztree li span.button.switch {
  width:21px;
  height:21px;
  margin: 0 -5px 0 0;
}
.ztree li span.button.root_open {
  background-position:-105px -63px
}
.ztree li span.button.root_close {
  background-position:-126px -63px
}
.ztree li span.button.roots_open {
  /*background-position: -105px 0;*/
  background:url("./img/icons/logo_13.png");
  background-size: 100% 100%;
}
.ztree li span.button.roots_close {
  /*background-position: -126px 0;*/
  background:url("./img/icons/logo_14.png");
  background-size: 100% 100%;
}
.ztree li span.button.center_open {
  background:url("./img/icons/logo_13.png");
  background-size: 100% 100%;
}
.ztree li span.button.center_close {
  /*background-position: -126px -21px;*/
  background:url("./img/icons/logo_14.png");
  background-size: 100% 100%;
}
.ztree li span.button.bottom_open {
  background:url("./img/icons/logo_14.png");
  background-size: 100% 100%;
}
.ztree li span.button.bottom_close {
 /* background-position: -126px -42px;*/
 background:url("./img/icons/logo_14.png");
 background-size: 100% 100%;
}
.ztree li span.button.noline_open {
  background-position: -105px -84px;
}
.ztree li span.button.noline_close {
  background-position: -126px -84px;
}
.ztree li span.button.root_docu {
  background:none;
}
.ztree li span.button.roots_docu {
  background-position: -84px 0;
}
.ztree li span.button.center_docu {
  background-position: -84px -21px;
}
.ztree li span.button.bottom_docu {
  background-position: -84px -42px;
}
.ztree li span.button.noline_docu {
  background:none;
}
.ztree li span.button.ico_open::before {
  font-family:"iconfont" !important;
  font-size:20px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e60f";
  line-height:16px;
  color:orange;
}
.ztree li span.button.ico_close::before{
  font-family:"iconfont" !important;
  font-size:20px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e71c";
  line-height:16px;
  color:orange;

}
.ztree li span.button.ico_docu::before {
  font-family:"iconfont" !important;
  font-size:20px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  content: "\e60c";
  line-height:16px;
}
.ztree li span.button.edit {
  margin-left:2px;
  margin-right: -1px;
  /*background-position: -189px -21px;*/
  background:url("./img/icons/logo_10.png");
  background-size: 100% 100%;
  vertical-align:middle;
  *vertical-align:middle
}
.ztree li span.button.edit:hover {
  /*background-position: -168px -21px;*/
  background:url("./img/icons/logo_18.png");
  background-size: 100% 100%;
}
.ztree li span.button.remove {
  margin-left:2px;
  margin-right: -1px;
  /*background-position: -189px -42px;*/
  background:url("./img/icons/logo_11.png");
  background-size: 100% 100%;
  vertical-align:middle;
  *vertical-align:middle
}
.ztree li span.button.remove:hover {
  /*background-position: -168px -42px;*/
  background:url("./img/icons/logo_19.png");
  background-size: 100% 100%;
}
.ztree li span.button.add {
  margin-left:2px;
  margin-right: -1px;
  /*background-position: -189px 0;*/
  background:url("./img/icons/logo_09.png");
  background-size: 100% 100%;
  vertical-align:middle;
  *vertical-align:middle
}
.ztree li span.button.add:hover {
  /*background-position: -168px 0;*/
  background:url("./img/icons/logo_16.png");
  background-size: 100% 100%;
}
.ztree li span.button.del {
  margin-left: 2px;
  margin-right: -1px;
  /*background-position: -189px 0;*/
  background: url("./img/icons/logo_11.png");
  background-size: 100% 100%;
  vertical-align: middle;
  *vertical-align: middle
}
.ztree li span.button.del:hover {
  /*background-position: -168px 0;*/
  background: url("./img/icons/logo_19.png");
  background-size: 100% 100%;
}
.ztree li span.button.ico_loading {
  margin-right:2px;
  background:url(./img/loading.gif) no-repeat scroll 0 0 transparent;
  vertical-align:bottom;
  *vertical-align:middle
}
ul.tmpTargetzTree {
  background-color:#FFE6B0;
  opacity:0.8;
  filter:alpha(opacity=80)
}
span.tmpzTreeMove_arrow {
  width:16px;
  height:21px;
  display: inline-block;
  padding:0;
  margin:2px 0 0 1px;
  border:0 none;
  position:absolute;
  background-color:transparent;
  background-repeat:no-repeat;
  background-attachment: scroll;
  background-position:-168px -84px;
  background-image:url("./img/metro.png");
  *background-image:url("./img/metro.gif")
}
ul.ztree.zTreeDragUL {
  margin:0;
  padding:0;
  position:absolute;
  width:auto;
  height:auto;
  overflow:hidden;
  background-color:#cfcfcf;
  border:1px #00B83F dotted;
  opacity:0.8;
  filter:alpha(opacity=80)
}
.ztreeMask {
  z-index:10000;
  background-color:#cfcfcf;
  opacity:0.0;
  filter:alpha(opacity=0);
  position:absolute
}