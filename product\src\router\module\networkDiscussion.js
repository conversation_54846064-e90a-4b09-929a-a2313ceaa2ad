export default [
  {
    path: '/networkDiscussion-manage',
    name: 'networkDiscussion-manage',
    component: () => import(/** 网络议政管理  列表 */ '@networkDiscussion/manage/manage')
  },
  {
    path: '/networkDiscussion-manage-add',
    name: 'networkDiscussion-manage-add',
    component: () => import(/** 网络议政管理 新增 */ '@networkDiscussion/manage/add')
  },
  {
    path: '/networkDiscussion-manage-info',
    name: 'networkDiscussion-manage-info',
    component: () => import(/** 网络议政管理 详情 */ '@networkDiscussion/manage/info')
  },
  {
    path: '/networkDiscussion-advice',
    name: 'networkDiscussion-advice',
    component: () => import(/** 议政建言管理 */ '@networkDiscussion/advice/advice')
  },
  {
    path: '/networkDiscussion-advice-info',
    name: 'networkDiscussion-advice-info',
    component: () => import(/** 议政建言管理 详情 */ '@networkDiscussion/advice/info')
  },
  {
    path: '/networkDiscussion-count',
    name: 'networkDiscussion-count',
    component: () => import(/** 统计分析 */ '@networkDiscussion/count/count')
  },
  {
    path: '/uploadDataList',
    name: 'uploadDataList',
    component: () => import(/** 议政建言管理 详情 */ '@networkDiscussion/upLoadDatas/uploadDataList')
  },
  {
    path: '/uploadDataAdd',
    name: 'uploadDataAdd',
    component: () => import(/** 议政建言管理 详情 */ '@networkDiscussion/upLoadDatas/uploadDataAdd')
  }
]
