<template>
  <div class="libraryDetails">
    <div class="libraryDetailsTypeBox">
      <div class="libraryDetailsImg">
        <img :src="details.coverImgUrl"
             alt="">
      </div>
      <div class="libraryDetailsType">{{details.bookTypeFirstName}}</div>
      <div class="libraryDetailsTypeChild">{{details.bookTypeSecondName}}</div>
    </div>
    <div class="libraryDetailsBox">
      <div class="libraryDetailsName">{{details.bookName}}</div>
      <div class="libraryDetailsAuthor">作者：{{details.authorName}}</div>
      <div class="libraryDetailsIntroduction">简介：{{details.bookDescription}}</div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'libraryDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id'],
  mounted () {
    if (this.id) {
      this.syBookInfo()
    }
  },
  methods: {
    async syBookInfo () {
      const res = await this.$api.academy.syBookInfo({ id: this.id })
      var { data } = res
      this.details = data
    }
  }
}
</script>
<style lang="scss">
.libraryDetails {
  width: 680px;
  display: flex;
  justify-content: space-between;
  width: 680px;
  padding: 24px;
  cursor: pointer;
  .libraryDetailsTypeBox {
    padding-top: 5px;
    .libraryDetailsImg {
      width: 128px;
      height: 170px;
      margin-bottom: 12px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .libraryDetailsType {
      color: #333;
      line-height: 26px;
      font-size: $textSize16;
    }
    .libraryDetailsTypeChild {
      color: #666;
      line-height: 26px;
      font-size: $textSize14;
    }
  }
  .libraryDetailsBox {
    width: 486px;
    height: 100%;
    position: relative;
    .libraryDetailsName {
      color: #333;
      line-height: 26px;
      font-size: 18px;
      margin-bottom: 7px;
    }
    .libraryDetailsIntroduction {
      line-height: 24px;
      color: #666;
      letter-spacing: 0.93px;
      font-size: $textSize14;
    }
    .libraryDetailsAuthor {
      color: #333;
      line-height: 28px;
      font-size: $textSize16;
      margin-bottom: 7px;
    }
  }
}
</style>
