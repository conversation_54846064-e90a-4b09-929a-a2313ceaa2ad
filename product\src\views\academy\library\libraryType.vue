<template>
  <div class="libraryType">
    <search-button-box @search-click="search"
                       @reset-click="reset">
      <template slot="button">
        <el-button type="primary"
                   icon="el-icon-plus"
                   @click="newData">新增</el-button>
      </template>
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
    </search-button-box>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  row-key="id"
                  :tree-props="{children: 'children'}">
          <el-table-column label="序号"
                           width="120"
                           prop="sort"></el-table-column>
          <el-table-column label="分类名称"
                           prop="name"></el-table-column>
          <el-table-column label="上级分类"
                           prop="parentName"></el-table-column>
          <el-table-column label="主题图"
                           width="220">
            <template slot-scope="scope">
              <div class="table-img">
                <el-image style="width: 100%; height: 100%"
                          :src="scope.row.coverImg"
                          :preview-src-list="[scope.row.coverImg]">
                  <div slot="error">暂未上传</div>
                </el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           width="190">
            <template slot-scope="scope">
              <el-button @click="editor(scope.row)"
                         type="primary"
                         plain
                         size="mini">修改</el-button>
              <el-button type="primary"
                         plain
                         size="mini"
                         @click="deleteClick(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <zy-pop-up v-model="show"
               :title="id?'修改分类':'新增分类'">
      <libraryTypeNew :id="id"
                      :parentId="parentId"
                      @callback="callback"></libraryTypeNew>
    </zy-pop-up>
  </div>
</template>
<script>
import libraryTypeNew from './libraryTypeNew'
export default {
  name: 'libraryType',
  data () {
    return {
      keyword: '',
      tableData: [],
      id: '',
      parentId: '',
      show: false
    }
  },
  components: {
    libraryTypeNew
  },
  mounted () {
    this.getSyTypeTree()
  },
  methods: {
    search () {
      this.getSyTypeTree()
    },
    reset () {
      this.keyword = ''
      this.getSyTypeTree()
    },
    newData () {
      this.id = ''
      this.parentId = this.typeId
      this.show = true
    },
    editor (row) {
      this.id = row.id
      this.parentId = ''
      this.show = true
    },
    callback () {
      this.show = false
      this.getSyTypeTree()
    },
    async getSyTypeTree () {
      const res = await this.$api.academy.getSyTypeTree({
        keyword: this.keyword
      })
      var { data } = res
      this.tableData = data
    },
    deleteClick (row) {
      this.$confirm('此操作将永久删除该分类, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.syTypeDels(row.id)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async syTypeDels (id) {
      const res = await this.$api.academy.syTypeDels({ ids: id })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.getSyTypeTree()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.libraryType {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 64px);
    .table-img {
      height: 38px;
      overflow: hidden;

      .el-image__inner {
        width: auto;
      }
      .el-icon-circle-close {
        color: #fff;
      }
    }
  }
}
</style>
