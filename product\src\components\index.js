import Vue from 'vue'
import zyTab from './zy-tab/zy-tab.vue'
import valid from './valid/valid.vue'
import zyTree from './zy-tree/zy-tree.vue'
import zyExport from './zy-export/zy-export.vue'
import zyTreeComponents from './zy-tree-components/zy-tree-components.vue'
import zyTabs from './zy-tabs/zy-tabs.vue'
// import zyMenu from './zy-menu/zy-menu.vue'
import UEditor from './UEditor/UEditor.vue'
import UEditors from './UEditors/UEditor.vue'
import zyTable from './zy-table/zy-table.vue'
import zyPopUp from './zy-pop-up/zy-pop-up.vue'
import zySelect from './zy-select/zy-select.vue'
import zySliding from './zy-sliding/zy-sliding.vue'
import zyCascader from './zy-cascader/zy-cascader.vue'
import zyMenuTree from './zy-menu-tree/zy-menu-tree.vue'
import screeningBox from './screening-box/screening-box.vue'
import searchBox from './search-box/search-box.vue'
import candidatesUser from './candidates-user/candidates-user.vue'
import zySelectCheckbox from './zy-select-checkbox/zy-select-checkbox.vue'
import zyCascaderCheckbox from './zy-cascader-checkbox/zy-cascader-checkbox.vue'
import zyCalendar from './zy-calendar/zy-calendar.vue'
import zyUpload from './zy-upload/zy-upload'
import signature from './signature/signature'
import zySlidingBox from './zy-sliding-box/zy-sliding-box.vue'
import searchButtonBox from './search-button-box/search-button-box.vue'
import CandidatesBox from './candidates-box/candidates-box.vue'
import qdUploadImg from './qd-upload-img/qd-upload-img.vue'
import wangEditor from './wang-editor/wang-editor.vue'
import previewCode from './preview-code/preview-code.vue'
import zyWidget from './zy-widget/zy-widget.vue'
import zyUploadFile from './zy-upload-file/zy-upload-file.vue'
import zyUploadFile2 from './zy-upload-file2/zy-upload-file2.vue'
import zyUploadFiles from './zy-upload-files/zy-upload-files.vue'
import zyUploadFilelist from './zy-upload-filelist/zy-upload-filelist.vue'
import zyFilterUser from './filter-user/filter-user.vue'
import XylLinkage from './linkage/linkage.vue'
import codePreview from './codePreview/codePreview.vue'
const zyComponents = [
  codePreview,
  zySlidingBox,
  XylLinkage,
  searchButtonBox,
  valid,
  zyCalendar,
  zyExport,
  zyTreeComponents,
  zyCascader,
  zySelect,
  zySelectCheckbox,
  zyCascaderCheckbox,
  UEditor,
  UEditors,
  zySliding,
  screeningBox,
  zyTab,
  zyTabs,
  zyTable,
  zyPopUp,
  zyMenuTree,
  zyTree,
  zyUpload,
  candidatesUser,
  signature,
  CandidatesBox,
  searchBox,
  qdUploadImg,
  wangEditor,
  previewCode,
  zyWidget,
  zyUploadFile,
  zyUploadFiles,
  zyUploadFile2,
  zyUploadFilelist,
  zyFilterUser
]
zyComponents.forEach(item => {
  Vue.component(item.name, item)
})
export default zyComponents
