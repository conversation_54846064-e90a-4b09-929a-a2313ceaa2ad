<template>
  <div class="project-information-new">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="标题"
                    prop="title"
                    class="form-title">
        <el-input placeholder="请输入标题"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="所属结构"
                    class="form-input">
        <zy-select width="296"
                   node-key="id"
                   :props="{children: 'children',label: 'name'}"
                   v-model="form.columnId"
                   :data="columnId"
                   placeholder="请选择所属结构"></zy-select>
      </el-form-item>
      <el-form-item label="发布时间"
                    prop="publishDate"
                    class="form-input">
        <el-date-picker v-model="form.publishDate"
                        type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="选择发布时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-input">
        <el-input placeholder="请输入排序"
                  v-model="form.sort"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="是否发布"
                    class="form-input">
        <el-radio-group v-model="form.isPublish">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="上传原图"
                    class="form-img">
        <el-upload class="form-img-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <img v-if="fileImg.filePath"
               :src="fileImg.filePath"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="上传附件"
                    class="form-upload">
        <el-upload class="form-upload-demo"
                   drag
                   action="/"
                   :before-remove="beforeRemove"
                   :before-upload="handleFile"
                   :http-request="fileUpload"
                   :file-list="file"
                   multiple>
          <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="外部链接"
                    class="form-title">
        <el-input placeholder="请输入外部链接"
                  v-model="form.externalLinks"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="内容"
                    class="form-ue">
        <!-- <UEditor v-model="form.content"></UEditor> -->
        <wang-editor v-model='form.content'></wang-editor>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'projectInformationNew',
  data () {
    return {
      form: {
        title: '',
        columnId: '',
        isPublish: '0',
        publishDate: '',
        externalLinks: '',
        content: '',
        sort: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        publishDate: [
          { required: true, message: '选择发布时间', trigger: 'blur' }
        ]
        // content: [
        //   { required: true, message: '请输入内容', trigger: 'blur' }
        // ]
      },
      columnId: [],
      file: [],
      fileImg: {}
    }
  },
  props: ['id', 'mosaicId'],
  mounted () {
    this.customTopicColumnTree()
    if (this.mosaicId) {
      this.customTopicinformationInfo()
    } else {
      this.form.publishDate = this.$format(new Date(), 'YYYY-MM-DD HH:mm:ss')
    }
  },
  methods: {
    async customTopicColumnTree () {
      const res = await this.$api.appManagement.customTopicColumnTree({ subjectId: this.id })
      var { data } = res
      this.columnId = data
    },
    async customTopicinformationInfo () {
      const res = await this.$api.appManagement.customTopicinformationInfo(this.mosaicId)
      var { data } = res
      this.form.title = data.title
      this.form.publishDate = data.publishDate
      this.form.isPublish = data.isPublish
      this.form.externalLinks = data.externalLinks
      this.form.sort = data.sort
      this.form.content = data.content
      this.form.columnId = data.columnId
      if (data.image) {
        data.image.filePath = data.image.fullUrl
        this.fileImg = data.image
      }
      if (data.attachmentList) {
        data.attachmentList.forEach((item, index) => {
          item.uid = item.id
          item.name = item.fileName
        })
        this.file = data.attachmentList
      }
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        this.fileImg = data[0]
      })
    },
    /**
   * 限制上传附件的文件类型
  */
    handleFile (file, fileList) {
    },
    /**
   * 上传附件请求方法
  */
    fileUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        data[0].name = data[0].fileName
        this.file.push(data[0])
      })
    },
    /**
   * 删除附件
  */
    beforeRemove (file, fileList) {
      var fileData = this.file
      this.file = fileData.filter(item => item.id !== file.id)
    },
    /**
   * 提交提案
  */
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var attach = []
          this.file.forEach(item => {
            attach.push(item.id)
          })
          var url = '/specialsubjectnews/add'
          if (this.mosaicId) {
            url = '/specialsubjectnews/edit'
          }
          this.$api.general.generalAdd(url, {
            id: this.mosaicId,
            subjectId: this.id,
            title: this.form.title,
            columnId: this.form.columnId,
            isPublish: this.form.isPublish,
            externalLinks: this.form.externalLinks,
            publishDate: this.form.publishDate,
            image: this.fileImg.id,
            attachmentIds: attach.join(','),
            content: this.form.content,
            sort: this.form.sort
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    /**
   * 取消按钮
  */
    cancel () {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
@import "./project-information-new.scss";
</style>
