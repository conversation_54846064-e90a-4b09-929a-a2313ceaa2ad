<template>
  <div class="qr-mark">
    <div class="icon-box" @click="handleclose">
      <i class="el-icon-close"></i>
    </div>
    <div class="qr-box">
      <vueQr :text="url" :size="200"></vueQr>
    </div>
    <p class="tips">打开APP扫描二维码签到{{tips}}</p>
  </div>
</template>
<script>
import vueQr from 'vue-qr'
export default {
  components: { vueQr },
  name: 'preview-code',
  props: {
    url: String,
    tips: String
  },
  methods: {
    handleclose () {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.qr-mark {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .icon-box {
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    border-radius: 24px;
    cursor: pointer;
    margin-left: 275px;
    margin-bottom: 25px;
    font-size: 18px;
    background-color: #fff;
  }
  .tips {
    font-size: 20px;
    line-height: 36px;
    color: #fff;
    margin-top: 25px;
  }
}
</style>
