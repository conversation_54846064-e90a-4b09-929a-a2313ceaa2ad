<template>
  <div class="candidates-box"
       @click="userClick">
    <el-scrollbar class="candidates--user-box">
      <div v-if="!userData.length"
           class="form-user-box-text">{{placeholder}}</div>
      <el-tag v-for="tag in userData"
              :key="tag.userId"
              size="medium"
              closable
              :disable-transitions="false"
              @close.stop="remove(tag)">
        {{tag.name}}
      </el-tag>
    </el-scrollbar>
    <zy-pop-up v-model="userShow"
               :title="placeholder">
      <candidates-user :point="point"
                       :disabled="disabled"
                       :data="userData"
                       @userCallback="userCallback"></candidates-user>
    </zy-pop-up>
  </div>
</template>
<script>
export default {
  name: 'CandidatesBox',
  data () {
    return {
      userShow: false,
      userData: this.data
    }
  },
  props: {
    point: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择用户'
    },
    max: {
      type: Number,
      default: 10000
    },
    beforeClose: Function,
    data: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    data () {
      this.userData = this.data
    }
  },
  methods: {
    userClick () {
      if (typeof this.beforeClose === 'function') {
        this.beforeClose(this.Shut)
      } else {
        this.Shut()
      }
    },
    Shut () {
      this.userShow = !this.userShow
    },
    // 移除tag
    remove (data) {
      var userData = this.userData
      this.userData = userData.filter(item => item.userId !== data.userId)
      this.$emit('update:data', this.userData)
    },
    /**
     * 选择用户的回调
    */
    userCallback (data, type) {
      if (type) {
        this.$emit('update:data', data)
      }
      this.userShow = !this.userShow
    }
  }
}
</script>
<style lang="scss">
@import "./candidates-box.scss";
</style>
