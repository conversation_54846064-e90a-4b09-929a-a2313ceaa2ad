/*
 * J<PERSON><PERSON>y zTree excheck v3.5.29
 * http://treejs.cn/
 *
 * Copyright (c) 2010 Hunter.z
 *
 * Licensed same as jquery - MIT License
 * http://www.opensource.org/licenses/mit-license.php
 *
 * Date: 2017-06-19
 */
(function ($) {
  //default consts of excheck
  var _consts = {
    event: {
      CHECK: "ztree_check"
    },
    id: {
      CHECK: "_check"
    },
    checkbox: {
      STYLE: "checkbox",
      DEFAULT: "chk",
      DISABLED: "disable",
      FALSE: "false",
      TRUE: "true",
      FULL: "full",
      PART: "part",
      FOCUS: "focus"
    },
    radio: {
      STYLE: "radio",
      TYPE_ALL: "all",
      TYPE_LEVEL: "level"
    }
  },
    //default setting of excheck
    _setting = {
      check: {
        enable: false,
        autoCheckTrigger: false,
        chkStyle: _consts.checkbox.STYLE,
        nocheckInherit: false,
        chkDisabledInherit: false,
        radioType: _consts.radio.TYPE_LEVEL,
        chkboxType: {
          "Y": "ps",
          "N": "ps"
        }
      },
      data: {
        key: {
          checked: "checked"
        }
      },
      callback: {
        beforeCheck: null,
        onCheck: null
      }
    },
    //default root of excheck
    _initRoot = function (setting) {
      var r = data.getRoot(setting);
      r.radioCheckedList = [];
    },
    //default cache of excheck
    _initCache = function (treeId) { },
    //default bind event of excheck
    _bindEvent = function (setting) {
      var o = setting.treeObj,
        c = consts.event;
      o.bind(c.CHECK, function (event, srcEvent, treeId, node) {
        event.srcEvent = srcEvent;
        tools.apply(setting.callback.onCheck, [event, treeId, node]);
      });
    },
    _unbindEvent = function (setting) {
      var o = setting.treeObj,
        c = consts.event;
      o.unbind(c.CHECK);
    },
    //default event proxy of excheck
    _eventProxy = function (e) {
      var target = e.target,
        setting = data.getSetting(e.data.treeId),
        tId = "", node = null,
        nodeEventType = "", treeEventType = "",
        nodeEventCallback = null, treeEventCallback = null;

      if (tools.eqs(e.type, "mouseover")) {
        if (setting.check.enable && tools.eqs(target.tagName, "span") && target.getAttribute("treeNode" + consts.id.CHECK) !== null) {
          tId = tools.getNodeMainDom(target).id;
          nodeEventType = "mouseoverCheck";
        }
      } else if (tools.eqs(e.type, "mouseout")) {
        if (setting.check.enable && tools.eqs(target.tagName, "span") && target.getAttribute("treeNode" + consts.id.CHECK) !== null) {
          tId = tools.getNodeMainDom(target).id;
          nodeEventType = "mouseoutCheck";
        }
      } else if (tools.eqs(e.type, "click")) {
        if (setting.check.enable && tools.eqs(target.tagName, "span") && target.getAttribute("treeNode" + consts.id.CHECK) !== null) {
          tId = tools.getNodeMainDom(target).id;
          nodeEventType = "checkNode";
        }
      }
      if (tId.length > 0) {
        node = data.getNodeCache(setting, tId);
        switch (nodeEventType) {
          case "checkNode":
            nodeEventCallback = _handler.onCheckNode;
            break;
          case "mouseoverCheck":
            nodeEventCallback = _handler.onMouseoverCheck;
            break;
          case "mouseoutCheck":
            nodeEventCallback = _handler.onMouseoutCheck;
            break;
        }
      }
      var proxyResult = {
        stop: nodeEventType === "checkNode",
        node: node,
        nodeEventType: nodeEventType,
        nodeEventCallback: nodeEventCallback,
        treeEventType: treeEventType,
        treeEventCallback: treeEventCallback
      };
      return proxyResult
    },
    //default init node of excheck
    _initNode = function (setting, level, n, parentNode, isFirstNode, isLastNode, openFlag) {
      if (!n) return;
      var checkedKey = setting.data.key.checked;
      if (typeof n[checkedKey] == "string") n[checkedKey] = tools.eqs(n[checkedKey], "true");
      n[checkedKey] = !!n[checkedKey];
      n.checkedOld = n[checkedKey];
      if (typeof n.nocheck == "string") n.nocheck = tools.eqs(n.nocheck, "true");
      n.nocheck = !!n.nocheck || (setting.check.nocheckInherit && parentNode && !!parentNode.nocheck);
      if (typeof n.chkDisabled == "string") n.chkDisabled = tools.eqs(n.chkDisabled, "true");
      n.chkDisabled = !!n.chkDisabled || (setting.check.chkDisabledInherit && parentNode && !!parentNode.chkDisabled);
      if (typeof n.halfCheck == "string") n.halfCheck = tools.eqs(n.halfCheck, "true");
      n.halfCheck = !!n.halfCheck;
      n.check_Child_State = -1;
      n.check_Focus = false;
      n.getCheckStatus = function () { return data.getCheckStatus(setting, n); };

      if (setting.check.chkStyle == consts.radio.STYLE && setting.check.radioType == consts.radio.TYPE_ALL && n[checkedKey]) {
        var r = data.getRoot(setting);
        r.radioCheckedList.push(n);
      }
    },
    //add dom for check
    _beforeA = function (setting, node, html) {
      var checkedKey = setting.data.key.checked;
      if (setting.check.enable) {
        data.makeChkFlag(setting, node);
        html.push("<span ID='", node.tId, consts.id.CHECK, "' class='", view.makeChkClass(setting, node), "' treeNode", consts.id.CHECK, (node.nocheck === true ? " style='display:none;'" : ""), "></span>");
      }
    },
    //update zTreeObj, add method of check
    _zTreeTools = function (setting, zTreeTools) {
      zTreeTools.checkNode = function (node, checked, checkTypeFlag, callbackFlag) {
        var checkedKey = this.setting.data.key.checked;
        if (node.chkDisabled === true) return;
        if (checked !== true && checked !== false) {
          checked = !node[checkedKey];
        }
        callbackFlag = !!callbackFlag;

        if (node[checkedKey] === checked && !checkTypeFlag) {
          return;
        } else if (callbackFlag && tools.apply(this.setting.callback.beforeCheck, [this.setting.treeId, node], true) == false) {
          return;
        }
        if (tools.uCanDo(this.setting) && this.setting.check.enable && node.nocheck !== true) {
          node[checkedKey] = checked;
          var checkObj = $$(node, consts.id.CHECK, this.setting);
          if (checkTypeFlag || this.setting.check.chkStyle === consts.radio.STYLE) view.checkNodeRelation(this.setting, node);
          view.setChkClass(this.setting, checkObj, node);
          view.repairParentChkClassWithSelf(this.setting, node);
          if (callbackFlag) {
            this.setting.treeObj.trigger(consts.event.CHECK, [null, this.setting.treeId, node]);
          }
        }
      }

      zTreeTools.checkAllNodes = function (checked) {
        view.repairAllChk(this.setting, !!checked);
      }

      zTreeTools.getCheckedNodes = function (checked) {
        var childKey = this.setting.data.key.children;
        checked = (checked !== false);
        return data.getTreeCheckedNodes(this.setting, data.getRoot(this.setting)[childKey], checked);
      }

      zTreeTools.getChangeCheckedNodes = function () {
        var childKey = this.setting.data.key.children;
        return data.getTreeChangeCheckedNodes(this.setting, data.getRoot(this.setting)[childKey]);
      }

      zTreeTools.setChkDisabled = function (node, disabled, inheritParent, inheritChildren) {
        disabled = !!disabled;
        inheritParent = !!inheritParent;
        inheritChildren = !!inheritChildren;
        view.repairSonChkDisabled(this.setting, node, disabled, inheritChildren);
        view.repairParentChkDisabled(this.setting, node.getParentNode(), disabled, inheritParent);
      }

      var _updateNode = zTreeTools.updateNode;
      zTreeTools.updateNode = function (node, checkTypeFlag) {
        if (_updateNode) _updateNode.apply(zTreeTools, arguments);
        if (!node || !this.setting.check.enable) return;
        var nObj = $$(node, this.setting);
        if (nObj.get(0) && tools.uCanDo(this.setting)) {
          var checkObj = $$(node, consts.id.CHECK, this.setting);
          if (checkTypeFlag == true || this.setting.check.chkStyle === consts.radio.STYLE) view.checkNodeRelation(this.setting, node);
          view.setChkClass(this.setting, checkObj, node);
          view.repairParentChkClassWithSelf(this.setting, node);
        }
      }
    },
    //method of operate data
    _data = {
      getRadioCheckedList: function (setting) {
        var checkedList = data.getRoot(setting).radioCheckedList;
        for (var i = 0, j = checkedList.length; i < j; i++) {
          if (!data.getNodeCache(setting, checkedList[i].tId)) {
            checkedList.splice(i, 1);
            i--; j--;
          }
        }
        return checkedList;
      },
      getCheckStatus: function (setting, node) {
        if (!setting.check.enable || node.nocheck || node.chkDisabled) return null;
        var checkedKey = setting.data.key.checked,
          r = {
            checked: node[checkedKey],
            half: node.halfCheck ? node.halfCheck : (setting.check.chkStyle == consts.radio.STYLE ? (node.check_Child_State === 2) : (node[checkedKey] ? (node.check_Child_State > -1 && node.check_Child_State < 2) : (node.check_Child_State > 0)))
          };
        return r;
      },
      getTreeCheckedNodes: function (setting, nodes, checked, results) {
        if (!nodes) return [];
        var childKey = setting.data.key.children,
          checkedKey = setting.data.key.checked,
          onlyOne = (checked && setting.check.chkStyle == consts.radio.STYLE && setting.check.radioType == consts.radio.TYPE_ALL);
        results = !results ? [] : results;
        for (var i = 0, l = nodes.length; i < l; i++) {
          if (nodes[i].nocheck !== true && nodes[i].chkDisabled !== true && nodes[i][checkedKey] == checked) {
            results.push(nodes[i]);
            if (onlyOne) {
              break;
            }
          }
          data.getTreeCheckedNodes(setting, nodes[i][childKey], checked, results);
          if (onlyOne && results.length > 0) {
            break;
          }
        }
        return results;
      },
      getTreeChangeCheckedNodes: function (setting, nodes, results) {
        if (!nodes) return [];
        var childKey = setting.data.key.children,
          checkedKey = setting.data.key.checked;
        results = !results ? [] : results;
        for (var i = 0, l = nodes.length; i < l; i++) {
          if (nodes[i].nocheck !== true && nodes[i].chkDisabled !== true && nodes[i][checkedKey] != nodes[i].checkedOld) {
            results.push(nodes[i]);
          }
          data.getTreeChangeCheckedNodes(setting, nodes[i][childKey], results);
        }
        return results;
      },
      makeChkFlag: function (setting, node) {
        if (!node) return;
        var childKey = setting.data.key.children,
          checkedKey = setting.data.key.checked,
          chkFlag = -1;
        if (node[childKey]) {
          for (var i = 0, l = node[childKey].length; i < l; i++) {
            var cNode = node[childKey][i];
            var tmp = -1;
            if (setting.check.chkStyle == consts.radio.STYLE) {
              if (cNode.nocheck === true || cNode.chkDisabled === true) {
                tmp = cNode.check_Child_State;
              } else if (cNode.halfCheck === true) {
                tmp = 2;
              } else if (cNode[checkedKey]) {
                tmp = 2;
              } else {
                tmp = cNode.check_Child_State > 0 ? 2 : 0;
              }
              if (tmp == 2) {
                chkFlag = 2; break;
              } else if (tmp == 0) {
                chkFlag = 0;
              }
            } else if (setting.check.chkStyle == consts.checkbox.STYLE) {
              if (cNode.nocheck === true || cNode.chkDisabled === true) {
                tmp = cNode.check_Child_State;
              } else if (cNode.halfCheck === true) {
                tmp = 1;
              } else if (cNode[checkedKey]) {
                tmp = (cNode.check_Child_State === -1 || cNode.check_Child_State === 2) ? 2 : 1;
              } else {
                tmp = (cNode.check_Child_State > 0) ? 1 : 0;
              }
              if (tmp === 1) {
                chkFlag = 1; break;
              } else if (tmp === 2 && chkFlag > -1 && i > 0 && tmp !== chkFlag) {
                chkFlag = 1; break;
              } else if (chkFlag === 2 && tmp > -1 && tmp < 2) {
                chkFlag = 1; break;
              } else if (tmp > -1) {
                chkFlag = tmp;
              }
            }
          }
        }
        node.check_Child_State = chkFlag;
      }
    },
    //method of event proxy
    _event = {

    },
    //method of event handler
    _handler = {
      onCheckNode: function (event, node) {
        if (node.chkDisabled === true) return false;
        var setting = data.getSetting(event.data.treeId),
          checkedKey = setting.data.key.checked;
        if (tools.apply(setting.callback.beforeCheck, [setting.treeId, node], true) == false) return true;
        node[checkedKey] = !node[checkedKey];
        view.checkNodeRelation(setting, node);
        var checkObj = $$(node, consts.id.CHECK, setting);
        view.setChkClass(setting, checkObj, node);
        view.repairParentChkClassWithSelf(setting, node);
        setting.treeObj.trigger(consts.event.CHECK, [event, setting.treeId, node]);
        return true;
      },
      onMouseoverCheck: function (event, node) {
        if (node.chkDisabled === true) return false;
        var setting = data.getSetting(event.data.treeId),
          checkObj = $$(node, consts.id.CHECK, setting);
        node.check_Focus = true;
        view.setChkClass(setting, checkObj, node);
        return true;
      },
      onMouseoutCheck: function (event, node) {
        if (node.chkDisabled === true) return false;
        var setting = data.getSetting(event.data.treeId),
          checkObj = $$(node, consts.id.CHECK, setting);
        node.check_Focus = false;
        view.setChkClass(setting, checkObj, node);
        return true;
      }
    },
    //method of tools for zTree
    _tools = {

    },
    //method of operate ztree dom
    _view = {
      checkNodeRelation: function (setting, node) {
        var pNode, i, l,
          childKey = setting.data.key.children,
          checkedKey = setting.data.key.checked,
          r = consts.radio;
        if (setting.check.chkStyle == r.STYLE) {
          var checkedList = data.getRadioCheckedList(setting);
          if (node[checkedKey]) {
            if (setting.check.radioType == r.TYPE_ALL) {
              for (i = checkedList.length - 1; i >= 0; i--) {
                pNode = checkedList[i];
                if (pNode[checkedKey] && pNode != node) {
                  pNode[checkedKey] = false;
                  checkedList.splice(i, 1);

                  view.setChkClass(setting, $$(pNode, consts.id.CHECK, setting), pNode);
                  if (pNode.parentTId != node.parentTId) {
                    view.repairParentChkClassWithSelf(setting, pNode);
                  }
                }
              }
              checkedList.push(node);
            } else {
              var parentNode = (node.parentTId) ? node.getParentNode() : data.getRoot(setting);
              for (i = 0, l = parentNode[childKey].length; i < l; i++) {
                pNode = parentNode[childKey][i];
                if (pNode[checkedKey] && pNode != node) {
                  pNode[checkedKey] = false;
                  view.setChkClass(setting, $$(pNode, consts.id.CHECK, setting), pNode);
                }
              }
            }
          } else if (setting.check.radioType == r.TYPE_ALL) {
            for (i = 0, l = checkedList.length; i < l; i++) {
              if (node == checkedList[i]) {
                checkedList.splice(i, 1);
                break;
              }
            }
          }

        } else {
          if (node[checkedKey] && (!node[childKey] || node[childKey].length == 0 || setting.check.chkboxType.Y.indexOf("s") > -1)) {
            view.setSonNodeCheckBox(setting, node, true);
          }
          if (!node[checkedKey] && (!node[childKey] || node[childKey].length == 0 || setting.check.chkboxType.N.indexOf("s") > -1)) {
            view.setSonNodeCheckBox(setting, node, false);
          }
          if (node[checkedKey] && setting.check.chkboxType.Y.indexOf("p") > -1) {
            view.setParentNodeCheckBox(setting, node, true);
          }
          if (!node[checkedKey] && setting.check.chkboxType.N.indexOf("p") > -1) {
            view.setParentNodeCheckBox(setting, node, false);
          }
        }
      },
      makeChkClass: function (setting, node) {
        var checkedKey = setting.data.key.checked,
          c = consts.checkbox, r = consts.radio,
          fullStyle = "";
        if (node.chkDisabled === true) {
          fullStyle = c.DISABLED;
        } else if (node.halfCheck) {
          fullStyle = c.PART;
        } else if (setting.check.chkStyle == r.STYLE) {
          fullStyle = (node.check_Child_State < 1) ? c.FULL : c.PART;
        } else {
          fullStyle = node[checkedKey] ? ((node.check_Child_State === 2 || node.check_Child_State === -1) ? c.FULL : c.PART) : ((node.check_Child_State < 1) ? c.FULL : c.PART);
        }
        var chkName = setting.check.chkStyle + "_" + (node[checkedKey] ? c.TRUE : c.FALSE) + "_" + fullStyle;
        chkName = (node.check_Focus && node.chkDisabled !== true) ? chkName + "_" + c.FOCUS : chkName;
        return consts.className.BUTTON + " " + c.DEFAULT + " " + chkName;
      },
      repairAllChk: function (setting, checked) {
        if (setting.check.enable && setting.check.chkStyle === consts.checkbox.STYLE) {
          var checkedKey = setting.data.key.checked,
            childKey = setting.data.key.children,
            root = data.getRoot(setting);
          for (var i = 0, l = root[childKey].length; i < l; i++) {
            var node = root[childKey][i];
            if (node.nocheck !== true && node.chkDisabled !== true) {
              node[checkedKey] = checked;
            }
            view.setSonNodeCheckBox(setting, node, checked);
          }
        }
      },
      repairChkClass: function (setting, node) {
        if (!node) return;
        data.makeChkFlag(setting, node);
        if (node.nocheck !== true) {
          var checkObj = $$(node, consts.id.CHECK, setting);
          view.setChkClass(setting, checkObj, node);
        }
      },
      repairParentChkClass: function (setting, node) {
        if (!node || !node.parentTId) return;
        var pNode = node.getParentNode();
        view.repairChkClass(setting, pNode);
        view.repairParentChkClass(setting, pNode);
      },
      repairParentChkClassWithSelf: function (setting, node) {
        if (!node) return;
        var childKey = setting.data.key.children;
        if (node[childKey] && node[childKey].length > 0) {
          view.repairParentChkClass(setting, node[childKey][0]);
        } else {
          view.repairParentChkClass(setting, node);
        }
      },
      repairSonChkDisabled: function (setting, node, chkDisabled, inherit) {
        if (!node) return;
        var childKey = setting.data.key.children;
        if (node.chkDisabled != chkDisabled) {
          node.chkDisabled = chkDisabled;
        }
        view.repairChkClass(setting, node);
        if (node[childKey] && inherit) {
          for (var i = 0, l = node[childKey].length; i < l; i++) {
            var sNode = node[childKey][i];
            view.repairSonChkDisabled(setting, sNode, chkDisabled, inherit);
          }
        }
      },
      repairParentChkDisabled: function (setting, node, chkDisabled, inherit) {
        if (!node) return;
        if (node.chkDisabled != chkDisabled && inherit) {
          node.chkDisabled = chkDisabled;
        }
        view.repairChkClass(setting, node);
        view.repairParentChkDisabled(setting, node.getParentNode(), chkDisabled, inherit);
      },
      setChkClass: function (setting, obj, node) {
        if (!obj) return;
        if (node.nocheck === true) {
          obj.hide();
        } else {
          obj.show();
        }
        obj.attr('class', view.makeChkClass(setting, node));
      },
      setParentNodeCheckBox: function (setting, node, value, srcNode) {
        var childKey = setting.data.key.children,
          checkedKey = setting.data.key.checked,
          checkObj = $$(node, consts.id.CHECK, setting);
        if (!srcNode) srcNode = node;
        data.makeChkFlag(setting, node);
        if (node.nocheck !== true && node.chkDisabled !== true) {
          node[checkedKey] = value;
          view.setChkClass(setting, checkObj, node);
          if (setting.check.autoCheckTrigger && node != srcNode) {
            setting.treeObj.trigger(consts.event.CHECK, [null, setting.treeId, node]);
          }
        }
        if (node.parentTId) {
          var pSign = true;
          if (!value) {
            var pNodes = node.getParentNode()[childKey];
            for (var i = 0, l = pNodes.length; i < l; i++) {
              if ((pNodes[i].nocheck !== true && pNodes[i].chkDisabled !== true && pNodes[i][checkedKey])
                || ((pNodes[i].nocheck === true || pNodes[i].chkDisabled === true) && pNodes[i].check_Child_State > 0)) {
                pSign = false;
                break;
              }
            }
          }
          if (pSign) {
            view.setParentNodeCheckBox(setting, node.getParentNode(), value, srcNode);
          }
        }
      },
      setSonNodeCheckBox: function (setting, node, value, srcNode) {
        if (!node) return;
        var childKey = setting.data.key.children,
          checkedKey = setting.data.key.checked,
          checkObj = $$(node, consts.id.CHECK, setting);
        if (!srcNode) srcNode = node;

        var hasDisable = false;
        if (node[childKey]) {
          for (var i = 0, l = node[childKey].length; i < l; i++) {
            var sNode = node[childKey][i];
            view.setSonNodeCheckBox(setting, sNode, value, srcNode);
            if (sNode.chkDisabled === true) hasDisable = true;
          }
        }

        if (node != data.getRoot(setting) && node.chkDisabled !== true) {
          if (hasDisable && node.nocheck !== true) {
            data.makeChkFlag(setting, node);
          }
          if (node.nocheck !== true && node.chkDisabled !== true) {
            node[checkedKey] = value;
            if (!hasDisable) node.check_Child_State = (node[childKey] && node[childKey].length > 0) ? (value ? 2 : 0) : -1;
          } else {
            node.check_Child_State = -1;
          }
          view.setChkClass(setting, checkObj, node);
          if (setting.check.autoCheckTrigger && node != srcNode && node.nocheck !== true && node.chkDisabled !== true) {
            setting.treeObj.trigger(consts.event.CHECK, [null, setting.treeId, node]);
          }
        }

      }
    },

    _z = {
      tools: _tools,
      view: _view,
      event: _event,
      data: _data
    };
  $.extend(true, $.fn.zTree.consts, _consts);
  $.extend(true, $.fn.zTree._z, _z);

  var zt = $.fn.zTree,
    tools = zt._z.tools,
    consts = zt.consts,
    view = zt._z.view,
    data = zt._z.data,
    event = zt._z.event,
    $$ = tools.$;

  data.exSetting(_setting);
  data.addInitBind(_bindEvent);
  data.addInitUnBind(_unbindEvent);
  data.addInitCache(_initCache);
  data.addInitNode(_initNode);
  data.addInitProxy(_eventProxy, true);
  data.addInitRoot(_initRoot);
  data.addBeforeA(_beforeA);
  data.addZTreeTools(_zTreeTools);

  var _createNodes = view.createNodes;
  view.createNodes = function (setting, level, nodes, parentNode, index) {
    if (_createNodes) _createNodes.apply(view, arguments);
    if (!nodes) return;
    view.repairParentChkClassWithSelf(setting, parentNode);
  }
  var _removeNode = view.removeNode;
  view.removeNode = function (setting, node) {
    var parentNode = node.getParentNode();
    if (_removeNode) _removeNode.apply(view, arguments);
    if (!node || !parentNode) return;
    view.repairChkClass(setting, parentNode);
    view.repairParentChkClass(setting, parentNode);
  }

  var _appendNodes = view.appendNodes;
  view.appendNodes = function (setting, level, nodes, parentNode, index, initFlag, openFlag) {
    var html = "";
    if (_appendNodes) {
      html = _appendNodes.apply(view, arguments);
    }
    if (parentNode) {
      data.makeChkFlag(setting, parentNode);
    }
    return html;
  }
})(jQuery);