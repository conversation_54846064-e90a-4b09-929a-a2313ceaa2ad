<template>
  <div class="signature">
    <p class="title">
      <i class="el-icon-lock"></i>
      签名密码
    </p>
    <div class="input-box">
      <el-input v-model="password"
                placeholder="请输入签名密码"
                clearable
                type="password"></el-input>
    </div>
    <div class="btn_box">
      <el-button type="primary"
                 size="mini"
                 @click="handleSure">确定</el-button>
      <el-button type="primary"
                 size="mini"
                 @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'signature',
  data () {
    return {
      password: ''
    }
  },
  methods: {
    // 去调用签名
    handleSure () {
      if (this.password === '') {
        return this.$message.warning('请输入签名密码')
      }
      this.$api.officeAutomation.userSignatureToken({ passWord: this.password }).then(res => {
        if (res.errcode === 200) {
          this.$emit('close', res.data)
        }
      })
    },
    // 取消
    handleCancel () {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.signature {
  width: 600px;
  padding: 15px 20px;
  .title {
    color: #3364a8;
    font-size: $textSize16;
    line-height: 30px;
  }
  .btn_box {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 20px;
    align-items: center;
  }
}
</style>
