.tabs-container {
  overflow: hidden;
}
.tabs-header {
  border-width: 1px;
  border-style: solid;
  border-bottom-width: 0;
  position: relative;
  padding: 0;
  padding-top: 2px;
  overflow: hidden;
}
.tabs-scroller-left,
.tabs-scroller-right {
  position: absolute;
  top: auto;
  bottom: 0;
  width: 18px;
  font-size: 1px;
  display: none;
  cursor: pointer;
  border-width: 1px;
  border-style: solid;
}
.tabs-scroller-left {
  left: 0;
}
.tabs-scroller-right {
  right: 0;
}
.tabs-tool {
  position: absolute;
  bottom: 0;
  padding: 1px;
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
}
.tabs-header-plain .tabs-tool {
  padding: 0 1px;
}
.tabs-wrap {
  position: relative;
  left: 0;
  overflow: hidden;
  width: 100%;
  margin: 0;
  padding: 0;
}
.tabs-scrolling {
  margin-left: 18px;
  margin-right: 18px;
}
.tabs-disabled {
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.tabs {
  list-style-type: none;
  height: 26px;
  margin: 0px;
  padding: 0px;
  padding-left: 4px;
  width: 50000px;
  border-style: solid;
  border-width: 0 0 1px 0;
}
.tabs li {
  float: left;
  display: inline-block;
  margin: 0 4px -1px 0;
  padding: 0;
  position: relative;
  border: 0;
}
.tabs li .tabs-inner {
  display: inline-block;
  text-decoration: none;
  cursor: hand;
  cursor: pointer;
  margin: 0;
  padding: 0 10px;
  height: 25px;
  line-height: 25px;
  text-align: center;
  white-space: nowrap;
  border-width: 1px;
  border-style: solid;
  -moz-border-radius: 4px 4px 0 0;
  -webkit-border-radius: 4px 4px 0 0;
  border-radius: 4px 4px 0 0;
}
.tabs li.tabs-selected .tabs-inner {
  font-weight: bold;
  outline: none;
}
.tabs li.tabs-selected .tabs-inner:hover {
  cursor: default;
  pointer: default;
}
.tabs li a.tabs-close,
.tabs-p-tool {
  position: absolute;
  font-size: 1px;
  display: block;
  height: 12px;
  padding: 0;
  top: 50%;
  margin-top: -6px;
  overflow: hidden;
}
.tabs li a.tabs-close {
  width: 12px;
  right: 5px;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.tabs-p-tool {
  right: 16px;
}
.tabs-p-tool a {
  display: inline-block;
  font-size: 1px;
  width: 12px;
  height: 12px;
  margin: 0;
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.tabs li a:hover.tabs-close,
.tabs-p-tool a:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  cursor: hand;
  cursor: pointer;
}
.tabs-with-icon {
  padding-left: 18px;
}
.tabs-icon {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 10px;
  top: 50%;
  margin-top: -8px;
}
.tabs-title {
  font-size: 14px;
}
.tabs-closable {
  padding-right: 8px;
}
.tabs-panels {
  margin: 0px;
  padding: 0px;
  border-width: 1px;
  border-style: solid;
  border-top-width: 0;
  overflow: hidden;
}
.tabs-header-bottom {
  border-width: 0 1px 1px 1px;
  padding: 0 0 2px 0;
}
.tabs-header-bottom .tabs {
  border-width: 1px 0 0 0;
}
.tabs-header-bottom .tabs li {
  margin: -1px 4px 0 0;
}
.tabs-header-bottom .tabs li .tabs-inner {
  -moz-border-radius: 0 0 4px 4px;
  -webkit-border-radius: 0 0 4px 4px;
  border-radius: 0 0 4px 4px;
}
.tabs-header-bottom .tabs-tool {
  top: 0;
}
.tabs-header-bottom .tabs-scroller-left,
.tabs-header-bottom .tabs-scroller-right {
  top: 0;
  bottom: auto;
}
.tabs-panels-top {
  border-width: 1px 1px 0 1px;
}
.tabs-header-left {
  float: left;
  border-width: 1px 0 1px 1px;
  padding: 0;
}
.tabs-header-right {
  float: right;
  border-width: 1px 1px 1px 0;
  padding: 0;
}
.tabs-header-left .tabs-wrap,
.tabs-header-right .tabs-wrap {
  height: 100%;
}
.tabs-header-left .tabs {
  height: 100%;
  padding: 4px 0 0 2px;
  border-width: 0 1px 0 0;
}
.tabs-header-right .tabs {
  height: 100%;
  padding: 4px 2px 0 0;
  border-width: 0 0 0 1px;
}
.tabs-header-left .tabs li,
.tabs-header-right .tabs li {
  display: block;
  width: 100%;
  position: relative;
}
.tabs-header-left .tabs li {
  left: auto;
  right: 0;
  margin: 0 -1px 4px 0;
  float: right;
}
.tabs-header-right .tabs li {
  left: 0;
  right: auto;
  margin: 0 0 4px -1px;
  float: left;
}
.tabs-justified li .tabs-inner {
  padding-left: 0;
  padding-right: 0;
}
.tabs-header-left .tabs li .tabs-inner {
  display: block;
  text-align: left;
  padding-left: 10px;
  padding-right: 10px;
  -moz-border-radius: 4px 0 0 4px;
  -webkit-border-radius: 4px 0 0 4px;
  border-radius: 4px 0 0 4px;
}
.tabs-header-right .tabs li .tabs-inner {
  display: block;
  text-align: left;
  padding-left: 10px;
  padding-right: 10px;
  -moz-border-radius: 0 4px 4px 0;
  -webkit-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0;
}
.tabs-panels-right {
  float: right;
  border-width: 1px 1px 1px 0;
}
.tabs-panels-left {
  float: left;
  border-width: 1px 0 1px 1px;
}
.tabs-header-noborder,
.tabs-panels-noborder {
  border: 0px;
}
.tabs-header-plain {
  border: 0px;
  background: transparent;
}
.tabs-pill {
  padding-bottom: 3px;
}
.tabs-header-bottom .tabs-pill {
  padding-top: 3px;
  padding-bottom: 0;
}
.tabs-header-left .tabs-pill {
  padding-right: 3px;
}
.tabs-header-right .tabs-pill {
  padding-left: 3px;
}
.tabs-header .tabs-pill li .tabs-inner {
  -moz-border-radius: 4px 4px 4px 4px;
  -webkit-border-radius: 4px 4px 4px 4px;
  border-radius: 4px 4px 4px 4px;
}
.tabs-header-narrow,
.tabs-header-narrow .tabs-narrow {
  padding: 0;
}
.tabs-narrow li,
.tabs-header-bottom .tabs-narrow li {
  margin-left: 0;
  margin-right: -1px;
}
.tabs-narrow li.tabs-last,
.tabs-header-bottom .tabs-narrow li.tabs-last {
  margin-right: 0;
}
.tabs-header-left .tabs-narrow,
.tabs-header-right .tabs-narrow {
  padding-top: 0;
}
.tabs-header-left .tabs-narrow li {
  margin-bottom: -1px;
  margin-right: -1px;
}
.tabs-header-left .tabs-narrow li.tabs-last,
.tabs-header-right .tabs-narrow li.tabs-last {
  margin-bottom: 0;
}
.tabs-header-right .tabs-narrow li {
  margin-bottom: -1px;
  margin-left: -1px;
}
.tabs-scroller-left {
  background: #f5f5f5 url('images/tabs_icons.png') no-repeat 1px center;
}
.tabs-scroller-right {
  background: #f5f5f5 url('images/tabs_icons.png') no-repeat -15px center;
}
.tabs li a.tabs-close {
  background: url('images/tabs_icons.png') no-repeat -34px center;
}
.tabs li .tabs-inner:hover {
  background: #eee;
  color: #404040;
  filter: none;
}
.tabs li.tabs-selected .tabs-inner {
  background-color: #ffffff;
  color: #000000;
}
.tabs li .tabs-inner {
  color: #000000;
  background-color: #f5f5f5;
}
.tabs-header,
.tabs-tool {
  background-color: #f5f5f5;
}
.tabs-header-plain {
  background: transparent;
}
.tabs-header,
.tabs-scroller-left,
.tabs-scroller-right,
.tabs-tool,
.tabs,
.tabs-panels,
.tabs li .tabs-inner,
.tabs li.tabs-selected .tabs-inner,
.tabs-header-bottom .tabs li.tabs-selected .tabs-inner,
.tabs-header-left .tabs li.tabs-selected .tabs-inner,
.tabs-header-right .tabs li.tabs-selected .tabs-inner {
  border-color: #ddd;
}
.tabs-p-tool a:hover,
.tabs li a:hover.tabs-close,
.tabs-scroller-over {
  background-color: #eee;
}
.tabs li.tabs-selected .tabs-inner {
  border-bottom: 1px solid #ffffff;
}
.tabs-header-bottom .tabs li.tabs-selected .tabs-inner {
  border-top: 1px solid #ffffff;
}
.tabs-header-left .tabs li.tabs-selected .tabs-inner {
  border-right: 1px solid #ffffff;
}
.tabs-header-right .tabs li.tabs-selected .tabs-inner {
  border-left: 1px solid #ffffff;
}
.tabs-header .tabs-pill li.tabs-selected .tabs-inner {
  background: #00bbee;
  color: #fff;
  filter: none;
  border-color: #ddd;
}
