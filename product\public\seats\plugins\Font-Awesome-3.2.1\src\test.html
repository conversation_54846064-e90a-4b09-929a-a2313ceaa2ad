---
layout: base
title: Font Awesome Visual Test Cases
relative_path: ../
---

<section class="container">
  <h1 class="page-header">Visual Test Cases</h1>

  <h3>Icons <small>Icons should have proper alignment and not be clipped</small></h3>
  <div class="row">
    <div class="span3">
      <div class="well">
        <i class="icon-building"></i> Building
      </div>
    </div>
    <div class="span3">
      <div class="well">
        <i class="icon-building icon-large"></i> Building Large
      </div>
    </div>
    <div class="span3">
      <i class="icon-building"></i> Building
    </div>
    <div class="span3">
      <i class="icon-building icon-large"></i> Building Large
    </div>
  </div>
  <div class="row" style="font-size: 24px; line-height: 1.5em;">
    <div class="span4">
      <div class="well">
        <i class="icon-building"></i> Building
      </div>
    </div>
    <div class="span4">
      <div class="well">
        <i class="icon-building icon-large"></i> Building Large
      </div>
    </div>
    <div class="span4">
      <i class="icon-building icon-large"></i> Building Large
    </div>
  </div>
  <div class="row">
    <div class="span3">
      <div class="well">
        <i class="icon-circle"></i> Circle
      </div>
    </div>
    <div class="span3">
      <div class="well">
        <i class="icon-circle icon-large"></i> Circle Large
      </div>
    </div>
    <div class="span3">
      <i class="icon-circle"></i> Circle
    </div>
    <div class="span3">
      <i class="icon-circle icon-large"></i> Circle Large
    </div>
  </div>
  <div class="row" style="font-size: 24px; line-height: 1.5em;">
    <div class="span4">
      <div class="well">
        <i class="icon-circle"></i> Circle
      </div>
    </div>
    <div class="span4">
      <div class="well">
        <i class="icon-circle icon-large"></i> Circle Large
      </div>
    </div>
    <div class="span4">
      <i class="icon-circle icon-large"></i> Circle Large
    </div>
  </div>


  <h3>Links with Icons <small>icon should activate link & underline</small></h3>
  <div class="row lead">
    <div class="span2">
      <a href="#"><i class="icon-building"></i> Link Here</a>
    </div>
    <div class="span2">
      <a href="#"><i class="icon-building icon-large"></i> Link Here</a>
    </div>
    <div class="span2">
      <a href="#"><i class="icon-caret-down"></i> Link Here</a>
    </div>
    <div class="span2">
      <a href="#">Link Here <i class="icon-double-angle-right"></i></a>
    </div>
    <div class="span2">
      <a href="#">Link Here <i class="icon-double-angle-right icon-large"></i></a>
    </div>
    <div class="span2">
      <a href="#">Link Here <i class="icon-caret-down"></i></a>
    </div>
  </div>
  <div class="row">
    <div class="span2">
      <a href="#"><i class="icon-building"></i> Link Here</a>
    </div>
    <div class="span2">
      <a href="#"><i class="icon-building icon-large"></i> Link Here</a>
    </div>
    <div class="span2">
      <a href="#"><i class="icon-caret-down"></i> Link Here</a>
    </div>
    <div class="span2">
      <a href="#">Link Here <i class="icon-double-angle-right"></i></a>
    </div>
    <div class="span2">
      <a href="#">Link Here <i class="icon-double-angle-right icon-large"></i></a>
    </div>
    <div class="span2">
      <a href="#">Link Here <i class="icon-caret-down"></i></a>
    </div>
  </div>


  <h3>Navbar  <small>should stay same height </small></h3>
  <div class="navbar navbar-inverse navbar-static-top">
    <div class="navbar-inner">
      <div class="container">
        <a class="brand" href="#"><i class="icon-flag"></i> Font Awesome</a>
        <ul class="nav">
          <li style="border: solid 1px white;"><a href="#">Nav Item 1</a></li>
          <li class="dropdown" style="border: solid 1px white;">
            <a href="#" role="button" class="dropdown-toggle" data-toggle="dropdown">
              Nav Item 2
              <i class="icon-caret-down"></i>
            </a>
            <ul class="dropdown-menu" role="menu">
              <li><a href="#"><i class="icon-building"></i> Menu Item 1</a></li>
              <li><a href="#"><i class="icon-building icon-large"></i> Menu Item 2</a></li>
            </ul>
          </li>
          <li style="border: solid 1px white;"><a href="#examples"><i class="icon-building icon-large"></i> Nav Item 3</a></li>
          <li style="border: solid 1px white;"><a href="#examples"><i class="icon-envelope"></i> Nav Item 4</a></li>
          <li style="border: solid 1px white;"><a href="#examples"><i class="icon-circle icon-large"></i> Nav Item 5</a></li>
          <li style="border: solid 1px white;"><a href="#examples"><i class="icon-circle"></i> Nav Item 6</a></li>
        </ul>
      </div>
    </div>
  </div>


  <h3>Plain buttons next to buttons w/icons <small>should be same height, icons not clipped</small></h3>
  <h4>Buttons</h4>
  <div class="row">
    <div class="span6">
      <div style="border: solid 1px #d3d3d3; text-align: center;">
        <div>
          <button class="btn btn-mini">Button</button>
          <button class="btn btn-mini"><i class="icon-building"></i> Button</button>
          <button class="btn btn-mini"><i class="icon-building"></i></button>
        </div>
        <div>
          <button class="btn btn-small">Button</button>
          <button class="btn btn-small"><i class="icon-building"></i> Button</button>
          <button class="btn btn-small"><i class="icon-building"></i></button>
        </div>
        <div>
          <button class="btn">Button</button>
          <button class="btn"><i class="icon-building"></i> Button</button>
          <button class="btn"><i class="icon-building"></i></button>
        </div>
        <div>
          <button class="btn btn-large">Button</button>
          <button class="btn btn-large"><i class="icon-building"></i> Button</button>
          <button class="btn btn-large"><i class="icon-building"></i></button>
        </div>
      </div>
    </div>
    <div class="span6">
      <div style="border: solid 1px #d3d3d3; text-align: center;">
        <div>
          <button class="btn btn-mini">Button</button>
          <button class="btn btn-mini"><i class="icon-building icon-large"></i> Button</button>
          <button class="btn btn-mini"><i class="icon-building icon-large"></i></button>
        </div>
        <div>
          <button class="btn btn-small">Button</button>
          <button class="btn btn-small"><i class="icon-building icon-large"></i> Button</button>
          <button class="btn btn-small"><i class="icon-building icon-large"></i></button>
        </div>
        <div>
          <button class="btn">Button</button>
          <button class="btn"><i class="icon-building icon-large"></i> Button</button>
          <button class="btn"><i class="icon-building icon-large"></i></button>
        </div>
        <div>
          <button class="btn btn-large">Button</button>
          <button class="btn btn-large"><i class="icon-building icon-large"></i> Button</button>
          <button class="btn btn-large"><i class="icon-building icon-large"></i></button>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="span6">
      <div style="border: solid 1px #d3d3d3; text-align: center;">
        <div>
          <button class="btn btn-mini">Button</button>
          <button class="btn btn-mini"><i class="icon-circle"></i> Button</button>
          <button class="btn btn-mini"><i class="icon-circle"></i></button>
        </div>
        <div>
          <button class="btn btn-small">Button</button>
          <button class="btn btn-small"><i class="icon-circle"></i> Button</button>
          <button class="btn btn-small"><i class="icon-circle"></i></button>
        </div>
        <div>
          <button class="btn">Button</button>
          <button class="btn"><i class="icon-circle"></i> Button</button>
          <button class="btn"><i class="icon-circle"></i></button>
        </div>
        <div>
          <button class="btn btn-large">Button</button>
          <button class="btn btn-large"><i class="icon-circle"></i> Button</button>
          <button class="btn btn-large"><i class="icon-circle"></i></button>
        </div>
      </div>
    </div>
    <div class="span6">
      <div style="border: solid 1px #d3d3d3; text-align: center;">
        <div>
          <button class="btn btn-mini">Button</button>
          <button class="btn btn-mini"><i class="icon-circle icon-large"></i> Button</button>
          <button class="btn btn-mini"><i class="icon-circle icon-large"></i></button>
        </div>
        <div>
          <button class="btn btn-small">Button</button>
          <button class="btn btn-small"><i class="icon-circle icon-large"></i> Button</button>
          <button class="btn btn-small"><i class="icon-circle icon-large"></i></button>
        </div>
        <div>
          <button class="btn">Button</button>
          <button class="btn"><i class="icon-circle icon-large"></i> Button</button>
          <button class="btn"><i class="icon-circle icon-large"></i></button>
        </div>
        <div>
          <button class="btn btn-large">Button</button>
          <button class="btn btn-large"><i class="icon-circle icon-large"></i> Button</button>
          <button class="btn btn-large"><i class="icon-circle icon-large"></i></button>
        </div>
      </div>
    </div>
  </div>


  <h4>Anchors</h4>
  <div class="row">
    <div class="span6">
      <div style="border: solid 1px #d3d3d3; text-align: center;">
        <div>
          <a class="btn btn-mini">Button</a>
          <a class="btn btn-mini"><i class="icon-building"></i> Button</a>
          <a class="btn btn-mini"><i class="icon-building"></i></a>
        </div>
        <div>
          <a class="btn btn-small">Button</a>
          <a class="btn btn-small"><i class="icon-building"></i> Button</a>
          <a class="btn btn-small"><i class="icon-building"></i></a>
        </div>
        <div>
          <a class="btn">Button</a>
          <a class="btn"><i class="icon-building"></i> Button</a>
          <a class="btn"><i class="icon-building"></i></a>
        </div>
        <div>
          <a class="btn btn-large">Button</a>
          <a class="btn btn-large"><i class="icon-building"></i> Button</a>
          <a class="btn btn-large"><i class="icon-building"></i></a>
        </div>
      </div>
    </div>
    <div class="span6">
      <div style="border: solid 1px #d3d3d3; text-align: center;">
        <div>
          <a class="btn btn-mini">Button</a>
          <a class="btn btn-mini"><i class="icon-building icon-large"></i> Button</a>
          <a class="btn btn-mini"><i class="icon-building icon-large"></i></a>
        </div>
        <div>
          <a class="btn btn-small">Button</a>
          <a class="btn btn-small"><i class="icon-building icon-large"></i> Button</a>
          <a class="btn btn-small"><i class="icon-building icon-large"></i></a>
        </div>
        <div>
          <a class="btn">Button</a>
          <a class="btn"><i class="icon-building icon-large"></i> Button</a>
          <a class="btn"><i class="icon-building icon-large"></i></a>
        </div>
        <div>
          <a class="btn btn-large">Button</a>
          <a class="btn btn-large"><i class="icon-building icon-large"></i> Button</a>
          <a class="btn btn-large"><i class="icon-building icon-large"></i></a>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="span6">
      <div style="border: solid 1px #d3d3d3; text-align: center;">
        <div>
          <a class="btn btn-mini">Button</a>
          <a class="btn btn-mini"><i class="icon-circle"></i> Button</a>
          <a class="btn btn-mini"><i class="icon-circle"></i></a>
        </div>
        <div>
          <a class="btn btn-small">Button</a>
          <a class="btn btn-small"><i class="icon-circle"></i> Button</a>
          <a class="btn btn-small"><i class="icon-circle"></i></a>
        </div>
        <div>
          <a class="btn">Button</a>
          <a class="btn"><i class="icon-circle"></i> Button</a>
          <a class="btn"><i class="icon-circle"></i></a>
        </div>
        <div>
          <a class="btn btn-large">Button</a>
          <a class="btn btn-large"><i class="icon-circle"></i> Button</a>
          <a class="btn btn-large"><i class="icon-circle"></i></a>
        </div>
      </div>
    </div>
    <div class="span6">
      <div style="border: solid 1px #d3d3d3; text-align: center;">
        <div>
          <a class="btn btn-mini">Button</a>
          <a class="btn btn-mini"><i class="icon-circle icon-large"></i> Button</a>
          <a class="btn btn-mini"><i class="icon-circle icon-large"></i></a>
        </div>
        <div>
          <a class="btn btn-small">Button</a>
          <a class="btn btn-small"><i class="icon-circle icon-large"></i> Button</a>
          <a class="btn btn-small"><i class="icon-circle icon-large"></i></a>
        </div>
        <div>
          <a class="btn">Button</a>
          <a class="btn"><i class="icon-circle icon-large"></i> Button</a>
          <a class="btn"><i class="icon-circle icon-large"></i></a>
        </div>
        <div>
          <a class="btn btn-large">Button</a>
          <a class="btn btn-large"><i class="icon-circle icon-large"></i> Button</a>
          <a class="btn btn-large"><i class="icon-circle icon-large"></i></a>
        </div>
      </div>
    </div>
  </div>


  <h3>Buttons with pulled 2x, 3x, 4x <small>should be same height, have correct line height</small></h3>
  <div class="row">
    <div class="span4">
      <div style="border: solid 1px #d3d3d3;">
        <a class="btn btn-large" href="#">
          <i class="icon-github icon-2x pull-left"></i>
          Longer<br>Button</a>
        <a class="btn btn-large" href="#">
          Longer<br>Button</a>
      </div>
    </div>
    <div class="span4">
      <div style="border: solid 1px #d3d3d3;">
        <a class="btn" href="#">
          <i class="icon-github icon-2x pull-left"></i>
          Longer<br>Button</a>
        <a class="btn" href="#">
          Longer<br>Button</a>
      </div>
    </div>
    <div class="span4">
      <div style="border: solid 1px #d3d3d3;">
        <a class="btn btn-small" href="#">
          <i class="icon-github icon-2x pull-left"></i>
          Longer<br>Button</a>
        <a class="btn btn-small" href="#">
          Longer<br>Button</a>
      </div>
    </div>
  </div>


  <h3>Nav pills & nav tabs <small>should be same height and properly vertically aligned</small></h3>
  <div class="row">
    <div class="span9">
      <ul class="nav nav-pills" style="border: solid 1px #d3d3d3;">
        <li class="active"><a href="#">Nav Item 1</a></li>
        <li class="dropdown active">
          <a href="#" role="button" class="dropdown-toggle" data-toggle="dropdown">
            Nav Item 2
            <span class="caret"></span>
          </a>
          <ul class="dropdown-menu" role="menu">
            <li><a href="#"><i class="icon-building"></i> Menu Item 1</a></li>
            <li><a href="#"><i class="icon-building icon-large"></i> Menu Item 2</a></li>
          </ul>
        </li>
        <li class="dropdown active">
          <a href="#" role="button" class="dropdown-toggle" data-toggle="dropdown">
            Nav Item 3
            <i class="icon-caret-down"></i>
          </a>
          <ul class="dropdown-menu" role="menu">
            <li><a href="#"><i class="icon-building"></i> Menu Item 1</a></li>
            <li><a href="#"><i class="icon-building icon-large"></i> Menu Item 2</a></li>
          </ul>
        </li>
        <li class="active"><a href="#examples"><i class="icon-building icon-large"></i> Nav Item 4</a></li>
        <li class="active"><a href="#examples"><i class="icon-spinner icon-spin"></i> Nav Item 5</a></li>
        <li class="active"><a href="#examples"><i class="icon-circle icon-large"></i> Nav Item 6</a></li>
        <li class="active"><a href="#examples"><i class="icon-circle"></i> Nav Item 7</a></li>
      </ul>
      <ul class="nav nav-tabs">
        <li class="active"><a href="#">Nav Item 1</a></li>
        <li class="dropdown active">
          <a href="#" role="button" class="dropdown-toggle" data-toggle="dropdown">
            Nav Item 2
            <span class="caret"></span>
          </a>
          <ul class="dropdown-menu" role="menu">
            <li><a href="#"><i class="icon-building"></i> Menu Item 1</a></li>
            <li><a href="#"><i class="icon-building icon-large"></i> Menu Item 2</a></li>
          </ul>
        </li>
        <li class="dropdown active">
          <a href="#" role="button" class="dropdown-toggle" data-toggle="dropdown">
            Nav Item 3
            <i class="icon-caret-down"></i>
          </a>
          <ul class="dropdown-menu" role="menu">
            <li><a href="#"><i class="icon-building"></i> Menu Item 1</a></li>
            <li><a href="#"><i class="icon-building icon-large"></i> Menu Item 2</a></li>
          </ul>
        </li>
        <li class="active"><a href="#examples"><i class="icon-building icon-large"></i> Nav Item 4</a></li>
        <li class="active"><a href="#examples"><i class="icon-spinner icon-spin"></i> Nav Item 5</a></li>
        <li class="active"><a href="#examples"><i class="icon-circle icon-large"></i> Nav Item 6</a></li>
        <li class="active"><a href="#examples"><i class="icon-circle"></i> Nav Item 7</a></li>
      </ul>
    </div>
    <div class="span3">
      <ul class="nav nav-list">
        <li>
          <a href="#c1">
            <i class="icon-chevron-right icon-fixed-width pull-right"></i> Overview
          </a>
        </li>
        <li>
          <a href="#c1">
            Overview
            <i class="icon-chevron-right icon-fixed-width pull-right"></i>
          </a>
        </li>
      </ul>
    </div>
  </div>



  <h3>2x, 3x, 4x icons in text <small>should have margins next to and below text</small></h3>
  <div class="row">
    <div class="span6">
      <p class="lead">
        <i class="icon-stethoscope icon-3x pull-left icon-border"></i>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.
      </p>
    </div>
    <div class="span6">
      <p class="lead">
        <i class="icon-ambulance icon-4x pull-right icon-border"></i>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.
      </p>
    </div>
  </div>
  <div class="row">
    <div class="span4">
      <i class="icon-building icon-2x pull-left icon-border"></i>
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.
    </div>
    <div class="span4">
      <i class="icon-stethoscope icon-3x pull-right icon-border"></i>
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.
    </div>
    <div class="span4">
      <i class="icon-ambulance icon-4x pull-left"></i>
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.
    </div>
  </div>


  <h3>Bullets <small>should wrap appropriately, have proper spacing</small></h3>
  <div class="row">
    <div class="span6">
      <div style="border: solid 1px #d3d3d3;">
        <ul class="icons-ul">
          <li><i class="icon-li icon-double-angle-right"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
          <li><i class="icon-li icon-arrow-right"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.</li>
          <li><i class="icon-li icon-building"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            <i class="icon-building"></i>
            <span class="label">foo</span>
            <a class="btn">foo</a>
          </li>
        </ul>
      </div>
    </div>
    <div class="span6">
      <div style="border: solid 1px #d3d3d3;">
        <ul class="icons-ul">
          <li><i class="icon-li icon-double-angle-right icon-large"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
          <li><i class="icon-li icon-arrow-right icon-large"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.</li>
          <li><i class="icon-li icon-building icon-large"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
        </ul>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="span6">
      <div style="border: solid 1px #d3d3d3;">
        <ul class="icons-ul lead">
          <li><i class="icon-li icon-double-angle-right"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
          <li><i class="icon-li icon-arrow-right"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.</li>
          <li><i class="icon-li icon-building"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
        </ul>
      </div>
    </div>
    <div class="span6">
      <div style="border: solid 1px #d3d3d3;">
        <ul class="icons-ul lead">
          <li><i class="icon-li icon-double-angle-right icon-large"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
          <li><i class="icon-li icon-arrow-right icon-large"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.</li>
          <li><i class="icon-li icon-building icon-large"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
        </ul>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="span6">
      <div style="border: solid 1px #d3d3d3;">
        <ul class="icons-ul">
          <li><a href="#"><i class="icon-li icon-double-angle-right"></i></a>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
          <li><a href="#"><i class="icon-li icon-arrow-right"></i></a>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.</li>
          <li><a href="#"><i class="icon-li icon-building"></i></a>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
        </ul>
      </div>
    </div>
    <div class="span6">
      <div style="border: solid 1px #d3d3d3;">
        <ul class="icons-ul">
          <li><a href="#"><i class="icon-li icon-double-angle-right icon-large"></i></a>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
          <li><a href="#"><i class="icon-li icon-arrow-right icon-large"></i></a>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.</li>
          <li><a href="#"><i class="icon-li icon-building icon-large"></i></a>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
        </ul>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="span6">
      <div style="border: solid 1px #d3d3d3;">
        <ul class="icons-ul">
          <li><i class="icon-li icon-refresh icon-spin"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
          <li><i class="icon-li icon-spinner icon-spin"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.</li>
          <li><i class="icon-li icon-spinner icon-spin"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            <i class="icon-building"></i>
            <span class="label">foo</span>
            <a class="btn">foo</a>
          </li>
        </ul>
      </div>
    </div>
    <div class="span6">
      <div style="border: solid 1px #d3d3d3;">
        <ul class="icons-ul">
          <li><i class="icon-li icon-refresh icon-spin icon-large"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
          <li><i class="icon-li icon-spinner icon-spin icon-large"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.</li>
          <li><i class="icon-li icon-spinner icon-spin icon-large"></i>Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            <i class="icon-building"></i>
            <span class="label">foo</span>
            <a class="btn">foo</a>
          </li>
        </ul>
      </div>
    </div>
  </div>


  <div class="row">
    <div class="span6">
      <div style="border: solid 1px #d3d3d3;">
        <ul>
          <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
          <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.</li>
          <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
        </ul>
      </div>
    </div>
    <div class="span6">
      <div style="border: solid 1px #d3d3d3;">
        <ul>
          <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
          <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam et lectus id nisl hendrerit varius. Aliquam erat volutpat. Suspendisse potenti. Aliquam erat volutpat. Aliquam ut dolor lectus.</li>
          <li>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</li>
        </ul>
      </div>
    </div>
  </div>


  <h3>Spinning icons <small>icons should be aligned well and on center, buttons should be same height, won't work in ie7</small></h3>
  <div style="border: solid 1px #d3d3d3;">
    <i class="icon-refresh icon-spin"></i> Loading...
    <button class="btn btn-small"><i class="icon-refresh icon-spin"></i> Loading...</button>
    <button class="btn btn-small">Loading...</button>
    <i class="icon-circle-blank icon-spin"></i> Loading...
    <button class="btn btn-small"><i class="icon-circle-blank icon-spin"></i> Loading...</button>
    <a href="#"><i class="icon-refresh icon-spin"></i> Loading...</a>
  </div>
  <div style="border: solid 1px #d3d3d3;">
    <i class="icon-refresh icon-spin"></i> Loading...
    <button class="btn"><i class="icon-refresh icon-spin"></i> Loading...</button>
    <button class="btn">Loading...</button>
    <i class="icon-circle-blank icon-spin"></i> Loading...
    <button class="btn"><i class="icon-circle-blank icon-spin"></i> Loading...</button>
  </div>
  <div style="border: solid 1px #d3d3d3;">
    <i class="icon-spinner icon-spin"></i> Loading...
    <button class="btn btn-large"><i class="icon-spinner icon-spin"></i> Loading...</button>
    <button class="btn btn-large">Loading...</button>
    <i class="icon-circle-blank icon-spin"></i> Loading...
    <button class="btn btn-large"><i class="icon-circle-blank icon-spin"></i> Loading...</button>
  </div>
  <h4>icon-large</h4>
  <div style="border: solid 1px #d3d3d3;">
    <i class="icon-refresh icon-large icon-spin"></i> Loading...
    <button class="btn btn-small"><i class="icon-refresh icon-large icon-spin"></i> Loading...</button>
    <button class="btn btn-small">Loading...</button>
    <i class="icon-circle-blank icon-large icon-spin"></i> Loading...
    <button class="btn btn-small"><i class="icon-circle-blank icon-large icon-spin"></i> Loading...</button>
  </div>
  <div style="border: solid 1px #d3d3d3;">
    <i class="icon-refresh icon-large icon-spin"></i> Loading...
    <button class="btn"><i class="icon-refresh icon-large icon-spin"></i> Loading...</button>
    <button class="btn">Loading...</button>
    <i class="icon-circle-blank icon-large icon-spin"></i> Loading...
    <button class="btn"><i class="icon-circle-blank icon-large icon-spin"></i> Loading...</button>
  </div>
  <div style="border: solid 1px #d3d3d3;">
    <i class="icon-spinner icon-large icon-spin"></i> Loading...
    <button class="btn btn-large"><i class="icon-spinner icon-large icon-spin"></i> Loading...</button>
    <button class="btn btn-large">Loading...</button>
    <i class="icon-circle-blank icon-large icon-spin"></i> Loading...
    <button class="btn btn-large"><i class="icon-circle-blank icon-large icon-spin"></i> Loading...</button>
  </div>
  <h4>Bootstrap Prepend and Append</h4>
  <div class="row">
    <div class="span6">
      <div class="input-prepend">
        <span class="add-on"><i class="icon-spinner icon-spin"></i></span>
        <input class="span2" id="prependedInput" type="text" placeholder="Username">
      </div>
    </div>
    <div class="span6">
      <div class="input-append">
        <input class="span2" id="appendedInput" type="text">
        <span class="add-on"><i class="icon-spinner icon-spin"></i></span>
      </div>
    </div>
  </div>

  <h3>Stacked icons</h3>
  <div class="well">
    {% include tests/stacked.html %}
  </div>
  <div class="well lead">
    {% include tests/stacked.html %}
  </div>
  <h4>Stacked icons inside anchor</h4>
  <div class="well">
    {% include tests/stacked-inside-anchor.html %}
  </div>


  <h3>Mirrored and rotated icons</h3>
  <div class="row">
    <div class="span6">
      <div class="well">
        {% include tests/rotated-flipped.html %}
      </div>
    </div>
    <div class="span6">
      <div class="well lead">
        {% include tests/rotated-flipped.html %}
      </div>
    </div>
  </div>
  <h4>Mirrored and rotated icons inside anchors and buttons</h4>
  <div class="row">
    <div class="span6">
      <div class="well">
        {% include tests/rotated-flipped-inside-anchor.html %}
      </div>
    </div>
    <div class="span6">
      <div class="well">
        {% include tests/rotated-flipped-inside-btn.html %}
      </div>
    </div>
  </div>

</section>
