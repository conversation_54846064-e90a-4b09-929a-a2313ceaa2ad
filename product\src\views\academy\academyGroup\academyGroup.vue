<template>
  <div class="academyGroup">
    <search-button-box @search-click="search"
                       :resetButton="false">
      <template slot="button">
        <el-button type="primary"
                   icon="el-icon-plus"
                   @click="newData">新增</el-button>
      </template>
      <template slot="search">
        <el-input placeholder="请输入内容"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
    </search-button-box>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  stripe
                  border
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="群名"
                           min-width="230"
                           prop="groupName"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="群主"
                           min-width="200"
                           prop="groupOwner">
          </el-table-column>
          <el-table-column label="创建时间"
                           min-width="180"
                           prop="createDate">
            <template slot-scope="scope">
              {{scope.row.createDate|datefmt}}
            </template>
          </el-table-column>
          <el-table-column label="关联书籍"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="booksClick(scope.row)"
                         type="text">关联书籍</el-button>
            </template>
          </el-table-column>
          <el-table-column label="群成员列表"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="viewUserList(scope.row)"
                         type="primary"
                         plain
                         size="mini">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           width="320">
            <template slot-scope="scope">
              <el-button @click="modify(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
              <el-button @click="user(scope.row)"
                         type="primary"
                         plain
                         size="mini">分配群成员</el-button>
              <el-button @click="deleteClick(scope.row)"
                         type="danger"
                         plain
                         size="mini">解散群</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="show"
               title="新增群组">
      <academyGroupNew :id="id"
                       :name="name"
                       @newCallback="newCallback"></academyGroupNew>
    </zy-pop-up>
    <zy-pop-up v-model="userShow"
               title="新增用户">
      <candidates-user point="point_11"
                       :data="data"
                       @userCallback="userCallback"></candidates-user>
    </zy-pop-up>
    <zy-pop-up v-model="booksShow"
               title="关联书籍">
      <academyGroupAssociated :id="id"
                              @callback="newCallback"></academyGroupAssociated>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@mixins/tableData'
import academyGroupNew from './academyGroupNew'
import academyGroupAssociated from './academyGroupAssociated'
export default {
  name: 'addressBookCollection',
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      id: '',
      name: '',
      show: false,
      data: [],
      userShow: false,
      booksShow: false
    }
  },
  inject: ['newTab'],
  mixins: [tableData],
  components: {
    academyGroupNew,
    academyGroupAssociated
  },
  mounted () {
    this.groupList()
  },
  methods: {
    search () {
      this.groupList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    modify (row) {
      this.id = row.id
      this.name = row.groupName
      this.show = true
    },
    booksClick (row) {
      this.id = row.id
      this.booksShow = true
    },
    newCallback () {
      this.groupList()
      this.show = false
      this.booksShow = false
    },
    /**
     * 分配的群成员
     */
    user (row) {
      this.$api.systemSettings.poinexistsids('point_11', { businessType: 7, businessId: row.id }).then(res => {
        var { data, errcode } = res
        if (errcode === 200) {
          this.data = data
          this.id = row.id
          this.name = row.groupName
          this.userShow = true
        }
      })
    },
    /**
     * 选择用户的回调
     */
    userCallback (data, type) {
      if (data.length === 0) {
        this.$message({
          message: '请至少选择一个群成员！',
          type: 'warning'
        })
        return
      }
      this.userShow = false
      if (type) {
        var arr = []
        data.forEach(item => {
          arr.push(item.userId)
        })
        this.groupUser(arr.join(','))
      }
    },
    /**
     * 获取当前分组下面的用户
     */
    async groupUser (ids) {
      const res = await this.$api.appManagement.groupUser({ groupId: this.id, groupName: this.name, userIds: ids })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.groupList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    viewUserList (row) {
      this.newTab({ name: '用户列表', menuId: '1', to: '/groupMembers', params: { id: row.id, name: row.groupName } })
    },
    async groupList () {
      const res = await this.$api.appManagement.groupList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        groupType: 2
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.groupList()
    },
    whatPage (val) {
      this.groupList()
    },
    deleteClick (row) {
      this.$confirm('此操作将解散当前选中的群, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.disband(row.id)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消解散'
        })
      })
    },
    async disband (id) {
      const res = await this.$api.appManagement.disband(id)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.groupList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.academyGroup {
  width: 100%;
  height: 100%;

  .tableData {
    height: calc(100% - 116px);
    width: 100%;
  }
}
</style>
