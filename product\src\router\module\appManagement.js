const informationList = () => import('@appManagement/information/information-list/information-list')
const informationColumn = () => import('@appManagement/information/information-column/information-column')
const customTopicList = () => import('@appManagement/custom-topic/custom-topic-list')
const moduleManagement = () => import('@appManagement/module-management/module-management')
const membersSaid = () => import('@appManagement/members-said/members-said')
const showYourself = () => import('@appManagement/show-yourself/show-yourself')
const addressBook = () => import('@appManagement/interactive/address-book/address-book')
const addressBookCollection = () => import('@appManagement/interactive/address-book-collection/address-book-collection')
const addressBookGroup = () => import('@appManagement/interactive/address-book-group/address-book-group')
const interactiveFile = () => import('@appManagement/interactive/interactive-file/interactive-file')
const groupMembers = () => import('@appManagement/interactive/address-book-collection/group-members')
const contentData = () => import('@appManagement/content-data/content-data')
const contentDataColumn = () => import('@appManagement/content-data/content-data-column/content-data-column')
const plenumData = () => import('@appManagement/plenum-data/plenum-data')
const plenumDataColumn = () => import('@appManagement/plenum-data/plenum-data-column/plenum-data-column')
const committeeData = () => import('@appManagement/committee-data/committee-data')
const committeeDataColumn = () => import('@appManagement/committee-data/committee-data-column/committee-data-column')
const committeeDataCustomTopic = () => import('@appManagement/committee-data-custom-topic/committee-data-custom-topic')
const InstallationRates = () => import('@appManagement/InstallationRates/InstallationRates')
const groupingManagement = () => import('@appManagement/Performance/groupingManagement/groupingManagement')
const modulManagement = () => import('@appManagement/Performance/modulManagement/modulManagement')

const appManagement = [
  { // 模块管理
    path: '/modulManagement',
    name: 'modulManagement',
    component: modulManagement
  },
  { // 分组管理
    path: '/groupingManagement',
    name: 'groupingManagement',
    component: groupingManagement
  },
  { // app安装率统计
    path: '/InstallationRates',
    name: 'InstallationRates',
    component: InstallationRates
  },
  { // 资讯列表
    path: '/informationList',
    name: 'informationList',
    component: informationList
  },
  { // 资讯栏目
    path: '/informationColumn',
    name: 'informationColumn',
    component: informationColumn
  },
  { // 自定义专题列表
    path: '/customTopicList',
    name: 'customTopicList',
    component: customTopicList
  },
  { // 模块管理
    path: '/moduleManagement',
    name: 'moduleManagement',
    component: moduleManagement
  },
  { // 代表说
    path: '/membersSaid',
    name: 'membersSaid',
    component: membersSaid
  },
  { // 晒一晒
    path: '/showYourself',
    name: 'showYourself',
    component: showYourself

  },
  { // 通讯录
    path: '/addressBook',
    name: 'addressBook',
    component: addressBook

  },
  { // 通讯录分组
    path: '/addressBookGroup',
    name: 'addressBookGroup',
    component: addressBookGroup

  },
  { // 通讯录群组
    path: '/addressBookCollection',
    name: 'addressBookCollection',
    component: addressBookCollection

  },
  { // 互动交流文件
    path: '/interactiveFile',
    name: 'interactiveFile',
    component: interactiveFile
  },
  { // 群成员列表
    path: '/groupMembers',
    name: 'groupMembers',
    component: groupMembers
  },
  { // 内容资料
    path: '/contentData',
    name: 'contentData',
    component: contentData
  },
  { // 内容资料栏目
    path: '/contentDataColumn',
    name: 'contentDataColumn',
    component: contentDataColumn
  },
  { // 全会资料
    path: '/plenumData',
    name: 'plenumData',
    component: plenumData
  },
  { // 全会资料栏目
    path: '/plenumDataColumn',
    name: 'plenumDataColumn',
    component: plenumDataColumn
  },
  { // 履职参阅
    path: '/committeeData',
    name: 'committeeData',
    component: committeeData
  },
  { // 履职参阅栏目
    path: '/committeeDataColumn',
    name: 'committeeDataColumn',
    component: committeeDataColumn
  },
  { // 履职参阅专题
    path: '/committeeDataCustomTopic',
    name: 'committeeDataCustomTopic',
    component: committeeDataCustomTopic
  },
  {
    path: '/manageOpeningWord',
    name: 'manageOpeningWord',
    component: () => import(/* 智慧小助手 开头语管理 */ '@appManagement/smart-assistant/manage-opening-word.vue')
  },
  {
    path: '/manageSpecialKeyword',
    name: 'manageSpecialKeyword',
    component: () => import(/* 智慧小助手 特定关键词管理 */ '@appManagement/smart-assistant/manage-special-keyword.vue')
  },
  {
    path: '/splashImgManage',
    name: 'splashImgManage',
    component: () => import(/* 启动图片管理 */ '@appManagement/theme-manage/splash-img-manage/splash-img-manage.vue')
  },
  {
    path: '/backgroundImgManage',
    name: 'backgroundImgManage',
    component: () => import(/* 背景图管理 */ '@appManagement/theme-manage/background-img-manage/background-img-manage.vue')
  },
  {
    path: '/vote-add',
    name: 'vote-add',
    component: () => import(/* 投票新增 */ '@appManagement/vote/add/add')
  },
  {
    path: '/vote-manage',
    name: 'vote-manage',
    component: () => import(/* 投票管理 */ '@appManagement/vote/manage/manage')
  },
  {
    path: '/vote-count',
    name: 'vote-count',
    component: () => import(/* 投票管理 */ '@appManagement/vote/count/')
  },
  {
    path: '/vote-item',
    name: 'vote-item',
    component: () => import(/* 投票选项 */ '@appManagement/vote/vote-item/vote-item')
  },
  {
    path: '/questionnaire-management',
    name: 'questionnaire-management',
    component: () => import('@appManagement/questionnaire/management/')
  },
  {
    path: '/create-questionnaire',
    name: 'create-questionnaire',
    component: () => import('@appManagement/questionnaire/create/')
  },
  {
    path: '/questionnaire-question-bank',
    name: 'questionnaire-question-bank',
    component: () => import('@appManagement/questionnaire/question-bank/')
  },
  {
    path: '/questionnaire-add',
    name: 'questionnaire-add',
    component: () => import(/* 问卷新增 */ '@appManagement/questionnaire/management/add/add')
  },
  {
    path: '/questionnaire-Statistical',
    name: 'questionnaire-Statistical',
    component: () => import(/* 问卷新增 */ '@appManagement/questionnaire/management/Statistical')
  },
  {
    path: '/question-detail',
    name: 'question-detail',
    component: () => import(/* 问卷新增 */ '@appManagement/questionnaire/management/detail')
  }
]
export default appManagement
