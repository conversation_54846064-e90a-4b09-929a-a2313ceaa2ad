<section id="stacked">
  <h2 class="page-header">Stacked Icons</h2>
  <div class="row">
    <div class="span3">
      A method for easily stacking multiple icons. Use the <code>icon-stack</code> class on the parent and
      <code>icon-stack-base</code> for the bottom icon.
    </div>
    <div class="span9">
      <div class="well well-large well-transparent lead">
        <span class="icon-stack">
          <i class="icon-check-empty icon-stack-base"></i>
          <i class="icon-twitter"></i>
        </span>
        icon-twitter on icon-check-empty<br>
        <span class="icon-stack">
          <i class="icon-circle icon-stack-base"></i>
          <i class="icon-flag icon-light"></i>
        </span>
        icon-flag on icon-circle<br>
        <span class="icon-stack">
          <i class="icon-sign-blank icon-stack-base"></i>
          <i class="icon-terminal icon-light"></i>
        </span>
        icon-terminal on icon-sign-blank<br>
        <span class="icon-stack">
          <i class="icon-camera"></i>
          <i class="icon-ban-circle icon-stack-base text-error"></i>
        </span>
        icon-camera on icon-ban-circle
      </div>
{% highlight html %}
<span class="icon-stack">
  <i class="icon-check-empty icon-stack-base"></i>
  <i class="icon-twitter"></i>
</span>
icon-twitter on icon-check-empty<br>
<span class="icon-stack">
  <i class="icon-circle icon-stack-base"></i>
  <i class="icon-flag icon-light"></i>
</span>
icon-flag on icon-circle<br>
<span class="icon-stack">
  <i class="icon-sign-blank icon-stack-base"></i>
  <i class="icon-terminal icon-light"></i>
</span>
icon-terminal on icon-sign-blank<br>
<span class="icon-stack">
  <i class="icon-camera"></i>
  <i class="icon-ban-circle icon-stack-base text-error"></i>
</span>
icon-camera on icon-ban-circle
{% endhighlight %}
    </div>
  </div>
</section>
