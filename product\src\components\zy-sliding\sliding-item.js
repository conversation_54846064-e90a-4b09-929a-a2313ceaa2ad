export default {
  name: 'XylSlidingItem',
  props: ['value'],
  render (createElement) {
    var _this = this
    var parent = this.$parent
    return createElement('div', {
      class: ['xyl-sliding-item', { 'is-active': parent.value === _this.value }],
      on: {
        click (event) {
          parent.$emit('input', _this.value)
          // parent.slidingUpdate(_this, _this.value)
        }
      }
    }, this.$slots.default)
  }
}
