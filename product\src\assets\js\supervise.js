import store from '@/store'
import router from '@/router'
import { Message } from 'element-ui'
import { Base64 } from 'js-base64'

const utils = {
  emptyMenuParam (to) {
    return to.replace(/(\?|&)t=\d+/g, '')
  },
  openMenu (menu) {
    const menuList = JSON.parse(sessionStorage.menuLists)[sessionStorage.curMenuIndex].children
    menu = this.findMenuByTo(menuList, menu.to) || menu
    store.commit('addTabsMenuItem', menu)
    router.replace(menu.to)
  },
  findMenuByTo (menuList, to) {
    for (var i = 0; i < menuList.length; i++) {
      const menu = menuList[i]
      if (menu.to === to) {
        return menu
      } else {
        const r = this.findMenuByTo(menu.children, to)
        if (r) {
          return r
        }
      }
    }
  },
  updateMenu (to) {
    to = this.emptyMenuParam(to)
    const tabsMenuList = store.state.tabsMenuList
    tabsMenuList.forEach((item, index) => {
      if (this.emptyMenuParam(item.to) === to) {
        store.commit('updateTabsMenuItem', { index, to })
      }
    })
  },
  updateMenuTable (to) {
    to = to.replace(/(\/operate)|(type=\w*&)/g, '')
    this.updateMenu(to)
  },
  updateMenuOperate (to, type) {
    to = to.replace(type, 'view')
    this.updateMenu(to)
  },
  closeMenu (to) {
    to = this.emptyMenuParam(to)
    const tabsMenuList = store.state.tabsMenuList
    if (tabsMenuList.length > 1) {
      tabsMenuList.forEach((item, index) => {
        if (this.emptyMenuParam(item.to) === to) {
          store.commit('deleteTabsMenuItem', index)
          to = tabsMenuList[Math.max(0, index - 1)].to
          router.replace(to)
        }
      })
    }
  },
  disabledType () {
    return ['view']
  },
  checkDisabledType (type) {
    return this.disabledType().includes(type.split('_')[0])
  },
  uploadFormat: {
    file: ['pdf', 'txt', 'doc', 'docx', 'xls', 'xlsx', 'zip', 'rar'],
    data: ['xls']
  },
  getUploadFormat (type = 'file') {
    return `仅支持${this.uploadFormat[type].join('、')}格式文件`
  },
  checkUploadFormat (file, type = 'file') {
    const suffix = file.name.split('.').reverse()[0]
    if (this.uploadFormat[type].includes(suffix)) {
      return true
    } else {
      Message.warning('不支持的文件类型')
      return false
    }
  },
  downloadFile (file) {
    const docId = (file.uid || file.id) + ''
    const data = {
      doc: {
        docId: docId,
        title: file.name,
        fetchUrl: file.url
      },
      user: {
        uid: 'admin',
        nickName: '管理员',
        avatar: 'https://bisheng-upload.nodoc.cn/system/defaultAvatar.png'
      }
    }
    const url = `http://************/apps/editor/openPreview?data=${Base64.encode(JSON.stringify(data))}`
    window.open(url)
  },
  getObjByKeys (obj, arr) {
    const newObj = {}
    arr.forEach(item => {
      newObj[item] = obj[item]
    })
    return newObj
  },
  fillZero (i) {
    return i < 10 ? '0' + i : i
  },
  getCurrentDateTime () {
    const date = new Date()
    const Y = date.getFullYear() + '-'
    const M = this.fillZero(date.getMonth() + 1) + '-'
    const D = this.fillZero(date.getDate()) + ' '
    const h = this.fillZero(date.getHours()) + ':'
    const m = this.fillZero(date.getMinutes()) + ':'
    const s = this.fillZero(date.getSeconds())
    return Y + M + D + h + m + s
  },
  getCurrentDate () {
    return this.getCurrentDateTime.split(' ')[0]
  },
  getCurrentTime () {
    return this.getCurrentDateTime.split(' ')[1]
  },
  timestampToDateTime (timestamp) {
    const date = timestamp ? new Date(timestamp) : new Date()
    const Y = date.getFullYear() + '-'
    const M = this.fillZero(date.getMonth() + 1) + '-'
    const D = this.fillZero(date.getDate()) + ' '
    const h = this.fillZero(date.getHours()) + ':'
    const m = this.fillZero(date.getMinutes()) + ':'
    const s = this.fillZero(date.getSeconds())
    return Y + M + D + h + m + s
  },
  timestampToDate (timestamp) {
    return this.timestampToDateTime(timestamp).split(' ')[0]
  },
  timestampToTime (timestamp) {
    return this.timestampToDateTime(timestamp).split(' ')[1]
  },
  setDataGroup (oldData, count) {
    const group = Math.ceil(oldData.length / count)
    const newData = []
    for (let i = 0; i < group; i++) {
      newData.push(oldData.slice(i * count, (i + 1) * count))
    }
    return newData
  },
  getDateTimeDiff (start, end) {
    start = new Date(start).getTime()
    end = new Date(end).getTime()
    const diff = Math.abs(end - start) / 1000
    const day = Math.floor(diff / (24 * 60 * 60))
    const hour = Math.floor(diff % (24 * 60 * 60) / (60 * 60))
    const minute = Math.floor(diff % (60 * 60) / 60)
    const second = Math.floor(diff % 60)
    console.log(day, hour, minute, second)
  },
  sortArrayByName (array, name, sort = 1) {
    array.sort((m, n) => {
      return sort * (m[name] - n[name])
    })
    return array
  }
}
export default utils
