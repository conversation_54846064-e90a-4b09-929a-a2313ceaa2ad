<div class="apiDetail">
<div>
	<h2><span>?</span><span class="path">treeNode.</span>* DIY *</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Used to save other custom data of node, do not use the same attribute name with ztree used, the user can freely set.</p>
		</div>
	</div>
	<h3>Examples of treeNode</h3>
	<h4>1. Use 'ename' attribute to save more info</h4>
	<pre xmlns=""><code>var node = { "id":1, "name":"test1", "ename":"test eName"};</code></pre>
</div>
</div>