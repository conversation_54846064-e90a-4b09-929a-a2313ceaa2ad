var selTreeId = ""; //选中的机构节点id
var selPerList = new Array(); //选中的人员数组
var perNums = 0; //机构节点下人员
var searchType = 1; //查询类型
var selInput = ""; //鼠标点击的选人文本框
var pickPerType = ""; //选被授权人 选查看人范围类型

//被授权人选人框
$(".itemBody").on("click", ".gPer", function () {
  selInput = $(this);
  pickPerType = "grantPer";
  $("#zb_btn").hide();
  $(":radio[name='treeType'][value='1']").prop("checked", "checked");
  searchType = 1;
  $("#perModal").modal("show");
  $(".modal_main_r_box, .checkUsrBox").empty();
  $("#perModalSearch").val("");
  //切换人员全选,半选，未选样式
  changeTheRadioClass();
  //获取已选择的人数
  loadPerNums();
  //加载选人框的部门数据
  loadCheckPerDeptInfo();
});

//查看人员范围选人框
$(".itemBody").on("click", ".gRPer", function () {
  selInput = $(this);
  pickPerType = "grantRPer";
  $("#zb_btn").show();
  $(":radio[name='treeType'][value='1']").prop("checked", "checked");
  searchType = 1;
  $("#perModal").modal("show");
  $(".modal_main_r_box, .checkUsrBox").empty();
  var grantUsers = JSON.parse($(this).attr("data-grantList"));
  if (grantUsers.length > 0) {
    for (let index = 0; index < grantUsers.length; index++) {
      var pId = grantUsers[index].id;
      var pAllName = grantUsers[index].userName;
      var pName = grantUsers[index].userName;
      var pMobile = grantUsers[index].mobile;
      var pPosition = grantUsers[index].position == "null" ? "" : grantUsers[index].position;
      $(".checkUsrBox").append(`<div class="checkedPerBox-custom"><img class="userImg" src="img/user_img.png"></i><label class="checkedPerLab">${pAllName}</label><label class="positionLab">${pPosition}</label><img class="closeImg" src="img/icon_close.png" id="${pId}" data-allName="${pAllName}" data-name="${pName}" data-mobile="${pMobile}" data-position="${pPosition}"></img></div>`);
    }
  }
  $("#perModalSearch").val("");
  //切换人员全选,半选，未选样式
  changeTheRadioClass();
  //获取已选择的人数
  loadPerNums();
  //加载选人框的部门数据
  loadCheckPerDeptInfo();
});

$(":radio[name = treeType]").click(function () {
  $(".modal_main_r_box, .checkUsrBox").empty();
  searchType = $(this).val();
  if ($(this).val() == 1) { // 按组织机构显示
    //加载选人框的部门数据
    loadCheckPerDeptInfo();
  }
  else {
    //加载选人框的组别数据
    loadCheckPerGroupInfo();
  }
})

//加载选人框的组别数据
function loadCheckPerGroupInfo () {
  //调用查询接口
  axios({
    method: "get",
    url: server.local_path + "meetOffice/meetTree?type=2&meetId=" + mId,
    headers: JSON.parse(header)
  }).then(function (response) {
    var dataArr = response.data.data;
    perTreeInfoArr = new Array();
    newPerTreeNodeGroupList(dataArr);

  }).catch(function (error) {
    layer.msg(error.response.data.message);
  });
}

//整理选人框组别树形结构层级
function newPerTreeNodeGroupList (nodeData) {
  for (var i = 0; i < nodeData.length; i++) {
    var parentNode = nodeData[i];
    var parentObj = new Object();
    parentObj.id = parentNode.id;
    parentObj.name = parentNode.name;
    parentObj.pId = parentNode.parentId;
    perTreeInfoArr.push(parentObj);
  }
  //创建树
  createCheckPerDeptTree(perTreeInfoArr);
}

var perTreeInfoArr = new Array();
//加载选人框的部门数据
function loadCheckPerDeptInfo () {
  //调用查询接口
  axios({
    method: "post",
    url: server.local_path + "pointrees/point_7",
    headers: JSON.parse(header)
  }).then(function (response) {
    var dataArr = response.data.data;
    perTreeInfoArr = new Array();
    newPerTreeNodeList(dataArr);

  }).catch(function (error) {
    layer.msg(error.response.data.message);
  });
}

//整理选人框部门树形结构层级
function newPerTreeNodeList (nodeData) {
  for (var i = 0; i < nodeData.length; i++) {
    var parentNode = nodeData[i];
    var parentObj = new Object();
    parentObj.id = parentNode.id;
    parentObj.name = parentNode.name;
    parentObj.pId = parentNode.parentId;
    perTreeInfoArr.push(parentObj);
    if (nodeData[i].children.length > 0) {
      newPerTreeNodeList(nodeData[i].children);
    }
  }
  //创建树
  createCheckPerDeptTree(perTreeInfoArr);
}

var perTimeFn = null;

//创建组织部门树
function createCheckPerDeptTree (treeData) {
  var setting = {
    view: {
      showLine: false,
      showIcon: false
    },
    data: {
      simpleData: {
        enable: true
      }
    },
    check: {
      enable: false
    },
    callback: {
      onClick: onclick,
      onDbClick: onDbClick
    },
  };

  function onclick (event, treeId, treeNode) {
    clearTimeout(perTimeFn);
    //执行延时
    perTimeFn = setTimeout(function () {
      selTreeId = treeNode.id;
      if (searchType == 1) {
        //根据机构id查询机构下面的人员
        loadPerListByOrganId(treeNode.id);
      }
      else {
        //根据组别id查询组别下面的人员
        loadPerListByGroupId();
      }
    }, 300);
  }

  function onDbClick (event, treeId, treeNode) {
    clearTimeout(perTimeFn);
  }

  var zNodes = treeData;

  $(document).ready(function () {
    $.fn.zTree.init($("#treeLeft"), setting, zNodes);
    var treeObj = $.fn.zTree.getZTreeObj("treeLeft");
  });
}

//人员搜索
$('#perModalSearch').bind('keypress', function (event) {
  if (event.keyCode == "13") {
    if (searchType == 1) {
      //根据机构id查询机构下面的人员
      loadPerListByOrganId();
    }
    else {
      //根据组别id查询组别下面的人员
      loadPerListByGroupId();
    }
  }
})

//根据机构id查询机构下面的人员
function loadPerListByOrganId () {
  //选人框是否选择了人员
  var selArr = new Array();
  var sels = $(".checkUsrBox").children(".checkedPerBox-custom").length;
  if (sels > 0) {
    var selList = $(".checkUsrBox").children();
    for (var s = 0; s < selList.length; s++) {
      var thisPid = $(selList[s].children[3]).attr("id");
      selArr.push(thisPid);
    }
  }

  var kw = $.trim($("#perModalSearch").val());
  axios.post(server.local_path + "pointree/users", Qs.stringify({ pointCode: "point_7", treeId: selTreeId, keyword: kw }), {
    headers: JSON.parse(header)
  })
    .then(function (response) {
      $(".modal_main_r_box").empty();
      var resArr = response.data.data;
      if (resArr != null && resArr.length > 0) {
        perNums = resArr.length;
        for (var i = 0; i < resArr.length; i++) {
          var uId = resArr[i].userId;
          var uMobile = resArr[i].mobile;
          var uPosition = resArr[i].position;
          var uName = resArr[i].name;
          var uAllName = resArr[i].userName;
          if (sels > 0) {
            if ($.inArray(uId, selArr) == -1) {
              $(".modal_main_r_box").append(`<div class="checkPerBox-custom"><img src="img/user_img.png"></i><label class="perLab" for="${uId}">${uAllName}</label><input type="checkbox" name="PCK" id="${uId}" value="${uId}" data-allName="${uAllName}" data-name="${uName}" data-mobile="${uMobile}" data-position="${uPosition}"></div>`);
            }
            else {
              $(".modal_main_r_box").append(`<div class="checkPerBox-custom"><img src="img/user_img.png"></i><label class="perLab" for="${uId}">${uAllName}</label><input type="checkbox" name="PCK" checked="checked" id="${uId}" value="${uId}" data-allName="${uAllName}" data-name="${uName}" data-mobile="${uMobile}" data-position="${uPosition}"></div>`);
            }
          }
          else {
            $(".modal_main_r_box").append(`<div class="checkPerBox-custom"><img src="img/user_img.png"></i><label class="perLab" for="${uId}">${uAllName}</label><input type="checkbox" name="PCK" id="${uId}" value="${uId}" data-allName="${uAllName}" data-name="${uName}" data-mobile="${uMobile}" data-position="${uPosition}"></div>`);
          }
        }
      }
      //切换人员全选,半选，未选样式
      changeTheRadioClass();
      //获取已选择的人数
      loadPerNums();
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
}

//根据组别id查询组别下面的人员
function loadPerListByGroupId () {
  //选人框是否选择了人员
  var selArr = new Array();
  var sels = $(".checkUsrBox").children(".checkedPerBox-custom").length;
  if (sels > 0) {
    var selList = $(".checkUsrBox").children();
    for (var s = 0; s < selList.length; s++) {
      var thisPid = $(selList[s].children[3]).attr("id");
      selArr.push(thisPid);
    }
  }

  var kw = $.trim($("#perModalSearch").val());
  axios({
    method: "get",
    url: server.local_path + "meetOffice/listNode?id=" + selTreeId + "&keyword=" + kw + "&meetId=" + mId + "&parentId=0&type=2",
    headers: JSON.parse(header),
  })
    .then(function (response) {
      $(".modal_main_r_box").empty();
      var resArr = response.data.data;
      if (resArr != null && resArr.length > 0) {
        perNums = resArr.length;
        for (var i = 0; i < resArr.length; i++) {
          var uId = resArr[i].userId;
          var uMobile = resArr[i].mobile;
          var uPosition = resArr[i].position;
          var uName = resArr[i].name;
          var uAllName = resArr[i].userName;
          if (sels > 0) {
            if ($.inArray(uId, selArr) == -1) {
              $(".modal_main_r_box").append(`<div class="checkPerBox-custom"><img src="img/user_img.png"></i><label class="perLab" for="${uId}">${uAllName}</label><input type="checkbox" name="PCK" id="${uId}" value="${uId}" data-allName="${uAllName}" data-name="${uName}" data-mobile="${uMobile}" data-position="${uPosition}"></div>`);
            }
            else {
              $(".modal_main_r_box").append(`<div class="checkPerBox-custom"><img src="img/user_img.png"></i><label class="perLab" for="${uId}">${uAllName}</label><input type="checkbox" name="PCK" checked="checked" id="${uId}" value="${uId}" data-allName="${uAllName}" data-name="${uName}" data-mobile="${uMobile}" data-position="${uPosition}"></div>`);
            }
          }
          else {
            $(".modal_main_r_box").append(`<div class="checkPerBox-custom"><img src="img/user_img.png"></i><label class="perLab" for="${uId}">${uAllName}</label><input type="checkbox" name="PCK" id="${uId}" value="${uId}" data-allName="${uAllName}" data-name="${uName}" data-mobile="${uMobile}" data-position="${uPosition}"></div>`);
          }
        }
      }
      //切换人员全选,半选，未选样式
      changeTheRadioClass();
      //获取已选择的人数
      loadPerNums();
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
}

//人员全选
$("#allcheck").on("click", function () {
  if ($("#allcheck").is(".unCheck") || $("#allCheckChild").is(".halfCheck")) {
    $(":checkbox[name='PCK']").prop("checked", "checked");
    $(".checkUsrBox").empty();
    $('.modal_main_r_box input:checkbox[name="PCK"]').each(function () {
      var pId = $(this).val();
      var pAllName = $(this).attr("data-allName");
      var pName = $(this).attr("data-name");
      var pMobile = $(this).attr("data-mobile");
      var pPosition = $(this).attr("data-position") == "null" ? "" : $(this).attr("data-position");
      $(".checkUsrBox").append(`<div class="checkedPerBox-custom"><img class="userImg" src="img/user_img.png"></i><label class="checkedPerLab">${pAllName}</label><label class="positionLab">${pPosition}</label><img class="closeImg" src="img/icon_close.png" id="${pId}" data-allName="${pAllName}" data-name="${pName}" data-mobile="${pMobile}" data-position="${pPosition}"></img></div>`);
    });
  }
  else {
    $(":checkbox[name='PCK']").removeProp("checked", "checked");
    $(".checkUsrBox").empty();
  }
  //切换人员全选,半选，未选样式
  changeTheRadioClass();
  //获取已选择的人数
  loadPerNums();
});

//人员标签点击事件
$('.modal_main_r_box').on("change", 'input:checkbox[name="PCK"]', function () {
  var isChecked = $(this).is(":checked");
  var pId = $(this).val();
  var pAllName = $(this).attr("data-allName");
  var pName = $(this).attr("data-name");
  var pMobile = $(this).attr("data-mobile");
  var pPosition = $(this).attr("data-position") == "null" ? "" : $(this).attr("data-position");

  if (isChecked == true) {
    $(".checkUsrBox").append(`<div class="checkedPerBox-custom"><img class="userImg" src="img/user_img.png"></i><label class="checkedPerLab">${pAllName}</label><label class="positionLab">${pPosition}</label><img class="closeImg" src="img/icon_close.png" id="${pId}" data-allName="${pAllName}" data-name="${pName}" data-mobile="${pMobile}" data-position="${pPosition}"></img></div>`);
    if (selInput.hasClass('gRPer') === true) {
      var grantUsers = JSON.parse(selInput.attr("data-grantList"));
      var pItem = { "id": pId, "userName": pName, "position": pPosition }
      grantUsers.push(pItem)
      selInput.attr("data-grantList", JSON.stringify(grantUsers));
    }
  }
  else {
    $('.checkUsrBox .closeImg').each(function () {
      var thisPid = $(this).attr("id");
      if (thisPid == pId) {
        $(this).parent().remove();
      }
    });
  }
  //切换人员全选,半选，未选样式
  changeTheRadioClass();
  //获取已选择的人数
  loadPerNums();
});

//人员标签全部删除事件
$('.allDel').on("click", function () {
  $(":checkbox[name='PCK']").removeProp("checked", "checked");
  $(".checkUsrBox").empty();
  //切换人员全选,半选，未选样式
  changeTheRadioClass();
  //获取已选择的人数
  loadPerNums();
});

//人员标签删除事件
$('.checkUsrBox').on("click", '.closeImg', function () {
  var pId = $(this).attr("id");
  $(this).parent().remove();
  $(":checkbox[name='PCK'][value='" + pId + "']").removeProp("checked", "checked");
  var grantUsers = [];
  var sels = $(".checkUsrBox").children(".checkedPerBox-custom").length;
  if (sels > 0) {
    var selList = $(".checkUsrBox").children();
    for (var s = 0; s < selList.length; s++) {
      var thisPid = $(selList[s].children[3]).attr("id");
      var thisPname = $(selList[s].children[3]).attr("data-name");
      var thisPposi = $(selList[s].children[3]).attr("data-position");
      var pItem = { "id": thisPid, "userName": thisPname, "position": thisPposi }
      grantUsers.push(pItem);
    }
  }
  selInput.attr("data-grantList", JSON.stringify(grantUsers));
  //切换人员全选,半选，未选样式
  changeTheRadioClass();
  //获取已选择的人数
  loadPerNums();
});

//获取已选择的人数
function loadPerNums () {
  var perNums = $(".checkUsrBox").children(".checkedPerBox-custom").length;
  $("#userNum").text(perNums);
}

//切换人员全选,半选，未选样式
function changeTheRadioClass () {
  var checkedNums = 0;
  $('.modal_main_r_box input:checkbox[name="PCK"]').each(function () {
    var isChecked = $(this).is(":checked");
    if (isChecked == true) {
      checkedNums++;
    }
  });
  if (checkedNums > 0 && checkedNums == perNums) {
    $("#allcheck").removeClass("unCheck");
    $("#allCheckChild").removeClass("halfCheck").addClass("check");
  }
  else if (checkedNums > 0 && checkedNums < perNums) {
    $("#allcheck").removeClass("unCheck");
    $("#allCheckChild").removeClass("check").addClass("halfCheck");
  }
  else {
    $("#allcheck").addClass("unCheck");
    $("#allCheckChild").removeClass("check");
  }
}

//选人框确定
$("#saveUser").on("click", function () {
  var userIds = "";
  var userNames = "";
  $('.checkUsrBox .closeImg').each(function () {
    var thisPid = $(this).attr("id");
    var thisPname = $(this).attr("data-name");
    userIds += thisPid + ",";
    userNames += thisPname + ",";
  });
  var userNum = userIds.split(",").length - 1;
  userIds = userIds.substring(0, userIds.length - 1);
  userNames = userNames.substring(0, userNames.length - 1);
  if (pickPerType == "grantPer") {
    if (userNum > 1) {
      layer.msg("不能选择多人");
    }
    else {
      selInput.attr("name", userIds);
      selInput.val(userNames);
      $("#perModal").modal("hide");
    }
  }
  else {
    if (userNum > 1) {
      selInput.attr("name", userIds);
      selInput.val(userNum + "人");
    }
    else {
      selInput.attr("name", userIds);
      selInput.val(userNames);
    }
    $("#perModal").modal("hide");
  }
});

//确定 保存授权人员
$("#saveGrantBtn").on("click", function () {
  var gIds = new Array(); //所有被授权人的id
  var grIds = new Array(); //所有查看人范围的id
  var paramsArr = new Array(); //参数集合
  $('.itemBody input').each(function () {
    if ($(this).is('.gPer')) {
      var userId = $(this).attr("name");
      gIds.push(userId);
    }
    if ($(this).is('.gRPer')) {
      var userId = $(this).attr("name");
      grIds.push(userId === undefined ? "" : userId);
    }
  });
  for (var i = 0; i < gIds.length; i++) {
    var idsArr = grIds[i].split(',');
    if (idsArr.length > 0) {
      for (var j = 0; j < idsArr.length; j++) {
        var params = {
          meetId: mId,
          type: 1,
          userId: gIds[i],
          authorizationId: idsArr[j]
        }
        paramsArr.push(params);
      }
    }
  }
  var params = JSON.stringify(paramsArr)
  axios({
    method: "post",
    url: server.local_path + "meetroomauthorization/saveControl",
    headers: JSON.parse(grantHeader),
    data: paramsArr
  })
    .then(function (response) {
      var resultInfo = response.data;
      if (resultInfo.errcode == 200) {
        layer.msg("操作成功");
        $("#grantModal").modal("hide");
      }
    }).catch(function (error) {
      layer.msg(error.response.data.message);
    });
});