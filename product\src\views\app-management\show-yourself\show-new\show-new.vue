<template>
  <div class="said-new scrollBar">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <!-- <el-form-item label="标题"
                    class="form-title"
                    prop="title">
        <el-input placeholder="请输入标题"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item> -->
      <el-form-item label="创建人"
                    class="form-title"
                    prop="publishName">
        <div class="form-user-box"
             @click="userClick">
          <el-input v-if="!dutyform.userData.length"
                    placeholder="请选择人员"
                    v-model="form.publishName"
                    clearable>
          </el-input>
          <el-tag v-for="tag in dutyform.userData"
                  :key="tag.userId"
                  size="medium"
                  closable
                  :disable-transitions="false"
                  @close.stop="remove(tag)">
            {{tag.name}}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="时间"
                    class="form-title"
                    prop="publishDate">
        <el-date-picker v-model="form.publishDate"
                        type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="选择日期时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="内容"
                    class="form-title"
                    prop="content">
        <el-input placeholder="请输入内容"
                  v-model="form.content"
                  type="textarea"
                  :rows="4"
                  clearable>
        </el-input>
      </el-form-item>

      <el-form-item label="附件"
                    class="form-icon">
        <el-upload class="upload-demo"
                   action="/"
                   :on-preview="handlePreview"
                   :on-remove="handleRemove"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :file-list="fileList"
                   list-type="picture">
          <el-button size="small"
                     type="primary">点击上传</el-button>
          <div slot="tip"
               class="el-upload__tip">只能上传jpg/png文件</div>
        </el-upload>
        <!-- <el-upload class="form-icon-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <img v-if="file.fullUrl"
               :src="file.fullUrl"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload> -->
      </el-form-item>

      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
    <zy-pop-up v-model="userShow"
               title="选择创建人">
      <candidates-user point="point_5"
                       :data="dutyform.userData"
                       @userCallback="userCallback"></candidates-user>
    </zy-pop-up>
  </div>
</template>
<script>
export default {
  name: 'showNew',
  data () {
    return {
      menu: [],
      form: {
        publishBy: '',
        publishName: '',
        publishDate: '',
        title: '',
        content: ''
      },
      rules: {
        // title: [
        //   { required: true, message: '请输入标题', trigger: 'blur' }
        // ],
        publishDate: [
          { required: true, message: '请选择时间', trigger: 'blur' }
        ],
        // publishName: [
        //   { required: true, message: '请选择创建人', trigger: 'blur' }
        // ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ]
      },
      props: {
        children: 'children',
        label: 'value'
      },
      file: {},
      fileList: [],
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick (picker) {
            picker.$emit('pick', new Date())
          }
        }, {
          text: '昨天',
          onClick (picker) {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24)
            picker.$emit('pick', date)
          }
        }, {
          text: '一周前',
          onClick (picker) {
            const date = new Date()
            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', date)
          }
        }]
      },
      dutyform: {
        userData: [],
        date: ''
      },
      dutyRules: {
        userData: [{ required: true, message: '请选择人员', trigger: 'change' }],
        date: [{ required: true, message: '请选中时间', trigger: 'blur' }]
      },
      userShow: false
    }
  },
  props: ['id'],
  mounted () {
    if (this.id) {
      this.showyourselfInfo()
    }
  },
  watch: {

  },
  methods: {
    async showyourselfInfo () {
      const res = await this.$api.appManagement.showyourselfInfo(this.id)
      var { data: { publishBy, publishName, publishDate, title, content, attachmentList } } = res
      this.form.publishBy = publishBy
      this.form.publishName = publishName
      this.form.publishDate = publishDate
      this.form.title = title
      this.form.content = content
      var publishUser = {}
      publishUser.userId = publishBy
      publishUser.name = publishName
      publishUser.userName = publishName
      this.dutyform.userData.push(publishUser)
      for (var i in attachmentList) {
        var imgList = {}
        imgList.id = attachmentList[i].shortName
        imgList.name = attachmentList[i].shortName
        imgList.url = attachmentList[i].fullUrl
        this.fileList.push(imgList)
      }
    },
    /**
     * 移除图片附件
     */
    handleRemove (file, fileList) {
      // console.log(file, fileList)
      var imgList = this.fileList
      for (var i in imgList) {
        if (imgList[i].id === file.id) {
          imgList.splice(i, 1)// 删除掉
          this.fileList = imgList
        }
      }
    },
    handlePreview (file) {
      // console.log(file)
    },
    /**
     * 激活选择创建人组件
     */
    userClick () {
      this.userShow = !this.userShow
    },
    /**
     * 选人回调
     */
    userCallback (data, type) {
      console.log(data)
      if (type) {
        this.dutyform.userData = data
        this.form.publishBy = data[0].userId
        this.form.publishName = data[0].name
      }
      this.userShow = !this.userShow
    },
    // 删除人员选择
    remove (data) {
      var userData = this.dutyform.userData
      this.dutyform.userData = userData.filter(item => item.userId !== data.userId)
    },
    /**
     * 判断图片格式
     */
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    /**
     * 上传图片
     */
    imgUpload (files) {
      const param = new FormData()
      param.append('file', files.file)
      this.$api.general.fileImg(param).then(res => {
        var { data } = res
        this.file = data
        var imgs = {}
        imgs.id = data.shortName
        imgs.name = data.shortName
        imgs.url = data.fullUrl
        this.fileList.push(imgs)
      })
    },
    /**
     * 提交数据
     */
    submitForm (formName) {
      var attachmentIds = ''
      var imgList = this.fileList
      for (var i in imgList) {
        attachmentIds += (Number(i) === 0 ? '' : ',') + imgList[i].id
      }
      console.log(attachmentIds)
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/showyourself/add'
          if (this.id) {
            url = '/showyourself/edit'
          }
          this.$api.systemSettings.generalAdd(url, {
            id: this.id,
            publishBy: this.form.publishBy,
            publishDate: this.form.publishDate,
            title: this.form.title,
            content: this.form.content,
            attachmentIds: attachmentIds
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
@import "./show-new.scss";
</style>
