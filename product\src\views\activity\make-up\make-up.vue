<template>
  <div class="activity-make-up">
    <step :active="active"></step>
    <base-form v-show="active === 1" type="make-up" ref="baseForm"></base-form>
    <join-user v-show="active === 2" ref="joinUser"></join-user>
    <file-list v-show="active === 3" ref="material"></file-list>
    <footerBtn :active.sync="active" @submit="handleSubmit"></footerBtn>
  </div>
</template>

<script>
import _ from 'lodash'
import step from '../add/widget/step'
import baseForm from '../add/widget/base-form'
import joinUser from '../add/widget/join-user'
import fileList from '../add/widget/file-list'
import footerBtn from '../add/widget/footer-btn'
export default {
  components: { step, baseForm, joinUser, fileList, footerBtn },
  data () {
    return {
      active: 1
    }
  },
  mounted () {
    const id = this.$route.query.id
    if (id) {
      this.getInfo(id)
    } else {
      this.$refs.baseForm.form.organizer = JSON.parse(sessionStorage.getItem('user' + this.$logo())).officeId
    }
  },
  inject: ['tabDelJump'],

  methods: {
    // 获取详情
    getInfo (id) {
      this.$api.activity.info(id).then(res => {
        const {
          meetName, meetType,
          organizer, address,
          meetStartTime,
          meetEndTime, isAppShow,
          isRelease, isNotice, content,
          inviterList, materiainfos
        } = res.data
        this.$refs.baseForm.form = {
          meetName,
          meetType,
          organizer,
          address,
          time2: [meetStartTime, meetEndTime],
          isAppShow,
          isNotice,
          isRelease,
          content
        }
        this.$refs.joinUser.userData = inviterList
        this.$refs.material.list = materiainfos
      })
    },
    handleSubmit () {
      const isForm = this.$refs.baseForm.validForm()
      if (isForm) {
        const data = _.cloneDeep(this.$refs.baseForm.form)
        delete data.signEndTime
        delete data.time1
        data.meetStartTime = data.time2[0]
        data.meetEndTime = data.time2[1]
        delete data.time2
        const userArr = this.$refs.joinUser.userData
        if (userArr.length > 0) {
          data.inviters = userArr.map(v => v.userId).join(',')
        }
        const files = this.$refs.material.list
        if (files.length > 0) {
          data.materiainfos = files.map(v => {
            return {
              fileId: v.fileId,
              isAppShow: v.isAppShow,
              date: v.dete,
              sort: v.sort
            }
          })
        }
        const id = this.$route.query.id
        if (id) {
          data.id = id
        }
        this.$api.activity.makeUpAdd(data).then(res => {
          this.handleClose()
        })
      } else {
        this.$message.warning('有必填项未输入或者未选择')
        return false
      }
    },
    // 关闭页面
    handleClose () {
      const { id, toId, mid } = this.$route.query
      // 没有从列表页面进来的逻辑
      if (!mid) {
        this.$refs.baseForm.reset()
        this.$refs.joinUser.userData = []
        this.$refs.material.list = []
        this.$refs.material.reset()
      }
      if (id) {
        this.$message.success('编辑活动补录成功')
        this.tabDelJump(mid, toId)
      } else {
        this.$message.success('新增活动补录成功')
        this.$refs.baseForm.reset()
        this.$refs.joinUser.userData = []
        this.$refs.material.reset()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-make-up {
  width: 100%;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);
  padding: 20px 30px;
}
</style>
