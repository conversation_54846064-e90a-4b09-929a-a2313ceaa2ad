<template>
  <div class="interactive-file">
    <zy-sliding v-model="slidingId"
                :sliding="sliding"
                @sliding-click="slidingClick"></zy-sliding>
    <search-button-box @search-click="search"
                       :resetButton="false">
      <template slot="button">
        <el-button type="primary"
                   icon="el-icon-plus"
                   v-permissions="'auth:fileinfo:add'"
                   @click="newData">新增</el-button>
      </template>
      <template slot="search">
        <el-input placeholder="请输入文件名查询"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
    </search-button-box>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  stripe
                  border
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="所属者"
                           width="160"
                           prop="owner">
          </el-table-column>
          <el-table-column label="文件名称"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button @click="download(scope.row)"
                         type="text">{{scope.row.name}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="文件归类"
                           width="160"
                           prop="fileClass">
          </el-table-column>
          <el-table-column label="文件类型"
                           width="160"
                           prop="fileType">
          </el-table-column>
          <el-table-column label="创建时间"
                           width="190"
                           prop="publishTime">
            <template slot-scope="scope">{{scope.row.createTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</template>
          </el-table-column>
          <el-table-column label="是否公开"
                           width="100">
            <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.isPublic=='1'"></i>
                <i class="el-icon-close"
                   v-else></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           v-if="slidingId == '2' && $hasPermission(['auth:fileinfo:edit','auth:fileinfo:del:id'])"
                           width="180">
            <template slot-scope="scope">
              <el-button @click="modify(scope.row)"
                         type="primary"
                         v-permissions="'auth:fileinfo:edit'"
                         plain
                         size="mini">编辑</el-button>
              <el-button @click="deleteClick(scope.row)"
                         type="danger"
                         v-permissions="'auth:fileinfo:del:id'"
                         v-if="slidingId == '2'"
                         plain
                         size="mini">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="show"
               title="新增文件">
      <interactive-file-new @callback="newCallback"></interactive-file-new>
    </zy-pop-up>
    <zy-pop-up v-model="addShow"
               title="编辑">
      <interactive-file-add :id="id"
                            @callback="addCallback"></interactive-file-add>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@mixins/tableData'
import interactiveFileNew from './interactive-file-new'
import interactiveFileAdd from './interactive-file-add'
export default {
  name: 'interactiveFile',
  data () {
    return {
      slidingId: '1',
      sliding: [
        { id: '1', name: '公开文件' },
        { id: '2', name: '我的文件' }
      ],
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      id: '',
      show: false,
      addShow: false
    }
  },
  mixins: [tableData],
  components: {
    interactiveFileNew,
    interactiveFileAdd
  },
  mounted () {
  },
  methods: {
    slidingClick (item) {
      this.choose = []
      this.selectObj = []
      this.fileinfoList()
    },
    search () {
      this.fileinfoList()
    },
    newData () {
      this.show = true
    },
    modify (row) {
      this.id = row.id
      this.addShow = true
    },
    newCallback () {
      this.fileinfoList()
      this.show = false
    },
    addCallback () {
      this.fileinfoList()
      this.addShow = false
    },
    async fileinfoList () {
      var params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        isMine: false
      }
      if (this.slidingId === '2') {
        params.isMine = true
      }
      const res = await this.$api.appManagement.fileinfoList(params)
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    download (row) {
      this.$confirm('此操作将下载附件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.appManagement.downloadFile(row.id, row.name + row.fileType)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消下载'
        })
      })
    },
    howManyArticle (val) {
      this.fileinfoList()
    },
    whatPage (val) {
      this.fileinfoList()
    },
    deleteClick (row) {
      this.$confirm('此操作将删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.fileinfoDel(row.id)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async fileinfoDel (id) {
      const res = await this.$api.appManagement.fileinfoDel(id)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.fileinfoList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.interactive-file {
  width: 100%;
  height: 100%;

  .tableData {
    height: calc(100% - 184px);
    width: 100%;

    .table-icon {
      .el-icon-check {
        font-size: $textSize16;
        color: #35be38;
      }

      .el-icon-close {
        font-size: $textSize16;
        color: #e24c4b;
      }
    }
  }
}
</style>
