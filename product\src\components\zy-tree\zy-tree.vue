<template>
  <div class="zy-tree">
    <el-scrollbar class="zy-tree-box">
      <zy-tree-components
        :tree="tree"
        v-model="id"
        :anykey="anykey"
        :props="props"
        :node-key="nodeKey"
        @on-tree-click="selectedClick"
      ></zy-tree-components>
    </el-scrollbar>
  </div>
</template>
<script>
export default {
  name: 'zyTree',
  data () {
    return {
      id: this.value
    }
  },
  props: {
    value: [String, Number, Array, Object],
    // 数据
    tree: {
      type: Array,
      default: () => []
    },
    // 树结构配置
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label'
        }
      }
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 宽度
    width: {
      type: String,
      default: '296'
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    anykey: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  model: {
    prop: 'value',
    event: 'id'
  },
  watch: {
    value (val) {
      if (val) {
        this.id = val
      }
    },
    id (val) {
      this.$emit('id', val)
    }
  },
  methods: {
    selectedClick (item) {
      this.$emit('on-tree-click', item)
    }
  }
}
</script>
<style lang="scss">
@import './zy-tree.scss';
</style>
