// 导入封装的方法
import {
  post,
  postform
} from '../http'

const notificationAnnouncement = {
  noticeList (params) { // 通知列表
    return post('/notice/list?', params)
  },
  noticeaddedit (url, params) { // 新增详情
    return post(url, params)
  },
  noticeinfo (params) { //  详情
    return post(`/notice/info/${params}`)
  },
  noticedels (params) { //  删除
    return post('/notice/dels', params)
  },
  readingDetail (params) { //  阅读详情
    return post(`/notice/readingDetail?id=${params}`)
  },
  sendRemindSms (params) { //  短信提醒
    return post('/notice/sendRemindSms', params)
  },
  noticereturnoptionlist (params) { //  选项列表
    return post('/noticereturnoption/list', params)
  },
  noticereturnoptioninfo (params) { //  选项获取详情
    return post(`/noticereturnoption/info/${params}`)
  },

  noticereturnlist (params) { //  回执列表
    return post('/noticereturn/list', params)
  },
  noticereturnoptionadd (url, params) { //  选项新增
    return post(url, params)
  },
  noticereturnoptiondels (params) { //  选项新增
    return post('/noticereturnoption/dels', params)
  },
  noticereturndels (params) { //  回执删除
    return post('/noticereturn/dels', params)
  },

  uploadFile (params) {
    return postform('/attachment/uploadFile', params, { timeout: 80000 })
  }

}
export default notificationAnnouncement
