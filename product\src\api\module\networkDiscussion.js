// 导入封装的方法
import { post, get } from '../http'
export default {
  // 网络议政管理
  manage: {
    arealist (params) {
      return post('/area/tree', params)
    },
    list (params) {
      return post('/survey/list', params)
    },
    add (params) {
      return post('/survey/addLegislationSurvey', params)
    },
    edit (params) {
      return post('/survey/edit', params)
    },
    info (id) {
      return get(`/survey/info/${id}`)
    },
    del (id) {
      return get(`/survey/del/${id}`)
    },
    dels (ids) {
      return post('/survey/dels', { ids: ids })
    }
  },
  // 议政建言
  advice: {
    list (params) {
      return post('/surveytownhalladvice/list', params)
    },
    info (id) {
      return get(`/surveytownhalladvice/info/${id}`)
    },
    verify (params) {
      return get('/surveytownhalladvice/edit', params)
    },
    verifys (params) {
      return post('/surveytownhalladvice/updStatus', params)
    },
    del (id) {
      return get(`/surveytownhalladvice/del/${id}`)
    },
    dels (ids) {
      return post('/surveytownhalladvice/dels', { ids: ids })
    }
  },
  // 议政建言
  uploadData: {
    list (params) {
      return post('/surveydata/list', params)
    },
    info (id) {
      return get(`/surveydata/info/${id}`)
    },
    edit (params) {
      return get('/surveydata/edit', params)
    },
    del (id) {
      return get(`/surveydata/del/${id}`)
    },
    dels (ids) {
      return post('/surveydata/dels', { ids: ids })
    },
    add (params) {
      return post('/surveydata/add', params)
    }
  }
}
