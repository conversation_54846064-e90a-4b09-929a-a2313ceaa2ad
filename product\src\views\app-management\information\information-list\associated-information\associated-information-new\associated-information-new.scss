.associated-information-new {
  width: 1100px;
  height: 400px;
  padding: 16px 24px;

  .button-box-list {
    height: 30px;
    margin-bottom: 16px;

    .el-button {
      width: 64px;
      max-width: 64px;
      min-width: 64px;
      padding: 0;
      height: 30px;
      font-size: $textSize12;
    }
  }

  .information-mosaic-list {
    width: 100%;
    height: calc(100% - 98px);
    border: 1px solid #d9d9d9;
    overflow: auto;

    .el-table {
      background-color: #f5f5f5;
    }

    .el-table th {
      background-color: #e6e5e8;
      height: 30px;
      padding: 0;
      font-size: $textSize12;

      & > .cell {
        font-weight: 500;
      }
    }

    .el-table__body td {
      height: 30px;
      padding: 0;
      font-size: $textSize12;
      background-color: #f5f5f5;
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: #e0eaf2;
    }
  }
}
