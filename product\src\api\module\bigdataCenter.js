// 导入封装的方法
import Vue from 'vue'
import {
  post,
  get
} from '../http'
const bigdataCenter = {
  visualizationURL () { // 大数据可视化路径
    return JSON.parse(sessionStorage.getItem('BigDataLiveShowUrl' + Vue.prototype.$logo()))
  },
  bigdataCenterURL () { // 大数据产品接口地址
    return JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
  },
  intelligentReportSRC () { // 大数据智能报表路径
    return JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo())) + '/attachment/uploadFile'
  },
  fileVisitUrl () { // 云盘访问地址
    return JSON.parse(sessionStorage.getItem('fileVisitUrl' + Vue.prototype.$logo()))
  },
  fullSearch: { // 全文检索
    fullTextSearch (params) { // 列表
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return get(`${dsjUrl}/common/searchData`, params)
    },
    fullTextCount (params) { // 关注和来源统计
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return get(`${dsjUrl}/common/aggs`, params)
    },
    hotSearch (params) { // 热门搜索
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return get(`${dsjUrl}/common/hotSearch`, params)
    },
    hotReadList (params) { // 热门阅读
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return post(`${dsjUrl}/common/hotRead`, params)
    },
    articleInfo (id) { // 详情
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return get(`${dsjUrl}/common/searchDataDetail`, { id: id })
    },
    fullTextRecommend (id) { // 推荐
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return post(`${dsjUrl}/common/dataRecomment`, { id: id })
    }
  },
  dataVisualization: { // 数据可视化
    liveshowList (params) { // 列表
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return get(`${dsjUrl}/liveshow/list`, params)
    },
    liveshowRemoveView (params) { // 删除
      // return post(bigdataCenterURL + `/liveshow/removeView/${viewId}`)
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return post(`${dsjUrl}/liveshow/removeView`, params)
    }
  },
  intelligentReport: { // 智能报表
    reportList (params) { // 列表
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return get(`${dsjUrl}/report/list`, params)
    },
    reportInfo (params) { // 详情
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return get(`${dsjUrl}/report/info`, params)
    },
    reportRdsuggest (params) { // 审查
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return get(`${dsjUrl}/report/rdsuggest`, params)
    },
    reportUpdate (params) { // 编辑
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return post(`${dsjUrl}/report/update`, params)
    }
  },
  dataPlatform: { // 数据平台
    newsList(params) { // 公开资讯列表
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return get(`${dsjUrl}/data/news/list`, params)
    },
    dataList(params) { // 通用列表
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return get(`${dsjUrl}/data/list`, params)
    },
    dataDetail(params) { // 详情
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return get(`${dsjUrl}/data/detail`, params)
    },
    dataDataRecomment(params) { // 相关推荐
      var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
      return post(`${dsjUrl}/data/dataRecomment`, params)
    }
  }
}
export default bigdataCenter
