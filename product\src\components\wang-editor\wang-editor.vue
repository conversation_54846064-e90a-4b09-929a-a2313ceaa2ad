<template>
  <div>
    <div :id="editorId"
         class="wang-editor"></div>
    <p v-if="contentLength > $props.max"
       style="color: red;">
      已超出最大{{ $props.max }}字数限制！
    </p>
  </div>
</template>

<script>
// 引入富文本编辑器
import WangEditor from 'wangeditor'
import axios from 'axios'
const { BtnMenu } = WangEditor
export default {
  name: 'wang-editor',
  props: {
    value: {
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: () => '请输入正文'
    },
    max: {
      type: Number,
      default: () => 9999999
    }
  },
  data () {
    return {
      editor: '',
      editorId: '',
      contentLength: 0
    }
  },
  watch: {
    value (newval) {
      if (this.editor) {
        if (newval !== this.editor.txt.html()) {
          this.editor.txt.html(newval)
        }
      }
    }
  },
  model: {
    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg
    event: 'input'// 这个字段，是指父组件监听 parent-event 事件
  },
  methods: {
    // // 生成一个随机不重复id,可以通过时间和随机数生成
    randomId () {
      const baseId = 'wang_editor'
      const now = new Date().getTime()
      return `${baseId}_${now}`
    },
    // 初始化编辑器
    initEditor () {
      const _this = this
      _this.editorId = _this.randomId()// 生成一个id
      this.$nextTick(() => {
        // 获取实例,wangEditor是被注册在window的
        const editor = new WangEditor('#' + _this.editorId)
        _this.editor = editor// 将实例保存待调用其他api
        _this.setConfig()
        editor.create()// 开始创建编辑器；
        _this.editor.txt.html(this.value)
        // 设置是否可编辑
        if (this.disabled !== 'undefined') {
          this.editor.$textElem.attr('contenteditable', !this.disabled)
        }
      })
    },
    // 创建富文本编辑器
    setConfig () {
      var _this = this
      // 开始创建
      const setting = {
        uploadImgShowBase64: false, // 是否允许上传base64位图片
        pasteFilterStyle: true, // 是否过滤粘贴的样式
        zIndex: 100, // 设置层叠位置
        // 菜单列表
        menus: [
          'head', // 标题
          'bold', // 粗体
          'fontSize', // 字号
          'fontName', // 字体
          'italic', // 斜体
          'indent', // 缩进
          'lineHeight', // 行高
          'underline', // 下划线
          'strikeThrough', // 删除线
          'foreColor', // 文字颜色
          'backColor', // 背景颜色
          'link', // 插入链接
          'list', // 列表
          'justify', // 对齐方式
          'quote', // 引用
          'emoticon', // 表情
          'image', // 插入图片
          'table', // 表格
          'video', // 插入视频
          // 'code', // 插入代码
          'undo', // 撤销
          'redo', // 恢复
          'qgs' // 恢复
        ],
        showLinkImg: true, // 是否显示“网络图片”tab
        onchange: function (html) {
          // console.log('html===>', html)
          html = html.replace(/<strong>(.*?)<\/strong>/g, '$1')
          html = html.replace(/<b>(.*?)<\/b>/g, '$1')
          _this.$emit('input', html)
          const text = html
          _this.contentLength = text.length
          if (_this.contentLength > _this.$props.max) {
            _this.$emit('restrictions', `已超出最大${_this.$props.max}字数限制！`)
          } else {
            _this.$emit('restrictions', '')
          }
        },
        onlineVideoCallback: v => {
          if (v.endsWith('.mp4')) {
            _this.editor.cmd.do(
              'insertHTML', `<video src="${v}" controls="controls" style="max-width:100%"></video>`
            )
          }
        },
        customUploadImg: (resultFiles, insertImgFn) => {
          const formData = new FormData()
          formData.append('upfile', resultFiles[0])
          axios.post(`${_this.$api.general.baseURL()}/ueditor/exec?action=uploadimage`, formData).then(res => {
            insertImgFn(res.data.url)
          })
        }
      }
      // 配置给编辑器
      _this.editor.config = Object.assign(_this.editor.config, setting)
      _this.editor.config.placeholder = _this.$props.placeholder
    }
  },
  created () {
    // 创建editor实例
    class AlertMenu extends BtnMenu {
      constructor(editor) {
        // data-title属性表示当鼠标悬停在该按钮上时提示该按钮的功能简述
        const $elem = WangEditor.$(
          `<div class="w-e-menu" data-title="清除格式">
               <svg style="width:14px;heigth:14px;" viewBox="0 0 1024 1024"><path d="M969.382408 288.738615l-319.401123-270.852152a67.074236 67.074236 0 0 0-96.459139 5.74922l-505.931379 574.922021a68.35184 68.35184 0 0 0-17.886463 47.910169 74.101061 74.101061 0 0 0 24.274486 47.910168l156.50655 132.232065h373.060512L975.131628 383.281347a67.074236 67.074236 0 0 0-5.74922-96.459139z m-440.134747 433.746725H264.144729l-90.071117-78.572676c-5.74922-5.74922-12.137243-12.137243-12.137243-17.886463a36.411728 36.411728 0 0 1 5.749221-24.274485l210.804741-240.828447 265.102932 228.691204z m-439.495945 180.781036h843.218964a60.047411 60.047411 0 1 1 0 120.733624H89.751716a60.047411 60.047411 0 1 1 0-120.733624z m0 0"></path></svg>
            </div>`
        )
        super($elem, editor)
      }

      clickHandler () {
        var editor = this.editor
        console.log('editor===>>', editor)
        var str = editor.txt.html()
        str = str.replace(/<xml>[\s\S]*?<\/xml>/ig, '')
        str = str.replace(/<style>[\s\S]*?<\/style>/ig, '')
        str = str.replace(/<\/?[^>]*>/g, '')
        str = str.replace(/[ | ]*\n/g, '\n')
        str = str.replace(/&nbsp;/ig, '')
        editor.txt.html(str)
      }

      tryChangeActive () { }
    }
    WangEditor.registerMenu('qgs', AlertMenu)
    this.initEditor()
  }
}

</script>
<style lang="scss">
.wang-editor {
  width: 100%;

  .w-e-text-container {
    .w-e-text {
      img {
        width: 80%;
        height: auto;
        text-align: center;
      }
    }
  }
}
</style>
