<template>
  <div class="add-question">
    <el-tabs v-model="activeName">
      <el-tab-pane label="单选"
                   name="1"></el-tab-pane>
      <el-tab-pane label="多选"
                   name="2"></el-tab-pane>
      <el-tab-pane label="文本"
                   name="3"></el-tab-pane>
    </el-tabs>
    <el-container>
      <el-aside width="600px">
        <el-input prefix-icon="el-icon-search"
                  class="search-input"
                  placeholder="请输入内容"
                  v-model="keyword"
                  clearable />
        <div class="nav-qs-box">
          <div class="qs-box flex-between"
               v-for="(item, index) in qsList"
               :key="item.id">
            <div class="qs-left">
              <div class="qs-title">
                {{ index + 1 + '. ' + item.question + '：' }}
              </div>
              <div v-if="activeName === '1' || activeName === '2'">
                <div class="qs-item"
                     v-for="items in item.choiceText"
                     :key="items.id">
                  <div v-if="activeName === '1'"
                       class="o-box" />
                  <div v-if="activeName === '2'"
                       class="square-box" />
                  <div class="qs-item-det">{{ items }}</div>
                </div>
              </div>
            </div>
            <div class="qs-right">
              <el-checkbox :disabled="item.disabled"
                           v-model="item.checked"
                           @change="checkedVal(item)"></el-checkbox>
            </div>
          </div>
        </div>
        <el-pagination @current-change="getList"
                       :current-page.sync="pageNo"
                       :page-size="pageSize"
                       layout="total, prev, pager, next"
                       :total="total" />
      </el-aside>
      <el-main>
        <div class="choes-title">已选单选题目（{{ radioList.length }}）</div>
        <div v-for="(item, index) in radioList"
             :key="item.id"
             class="edit-box flex-between">
          <div class="">{{ index + 1 + '. ' + item.question }}</div>
          <i class="el-icon-close"
             @click="delItem(item, 'radioList')"></i>
        </div>
        <div class="choes-title">已选多选题目（{{ checkList.length }}）</div>
        <div v-for="(item, index) in checkList"
             :key="index"
             class="edit-box flex-between">
          <div>{{ index + 1 + item.question }}</div>
          <i class="el-icon-close"
             @click="delItem(item, 'checkList')"></i>
        </div>
        <div class="choes-title">已选文本题目（{{ textList.length }}）</div>
        <div v-for="(item, index) in textList"
             :key="index"
             class="edit-box flex-between">
          <div class="">{{ index + 1 + item.question }}</div>
          <i class="el-icon-close"
             @click="delItem(item, 'textList')"></i>
        </div>
      </el-main>
    </el-container>
    <div class="btn">
      <el-button type="primary"
                 @click="submit">确 定</el-button>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      activeName: '1',
      qsList: [],
      radioList: [],
      checkList: [],
      textList: [],
      keyword: '',
      pageNo: 1,
      pageSize: 20,
      total: 0
    }
  },
  props: {
    formList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  watch: {
    activeName (val) {
      this.pageNo = 1
      this.getList()
    },
    keyword () {
      this.getList()
    }
  },
  created () {
    this.getList()
  },
  methods: {
    submit () {
      this.$emit('callBack', this.radioList.concat(this.checkList).concat(this.textList))
    },
    async getList () {
      const params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        keyword: this.keyword
      }
      params.answerType = Number(this.activeName)
      const res = await this.$api.appManagement.questionnairequestionbank.list(params)
      const { data, total } = res
      data.forEach(item => {
        if (item.choiceText) {
          item.choiceText = item.choiceText.split('|')
        }
      })
      this.qsList = data
      this[this.activeName === '1' ? 'radioList' : this.activeName === '2' ? 'checkList' : 'textList'].forEach(item => {
        this.qsList.forEach(el => {
          if (el.id === item.id) {
            el.checked = true
            console.log(el)
          }
        })
      })
      this.formList.forEach(item => {
        this.qsList.forEach(el => {
          if ((this.activeName === '1' || this.activeName === '2') && item.choiceText.toString() === el.choiceText.toString() && item.answerType === Number(this.activeName) && item.question === el.question) {
            el.disabled = true
          } else if (item.answerType === Number(this.activeName) && item.question === el.question) {
            el.disabled = true
          }
        })
      })
      this.total = total
    },
    checkedVal (item) {
      var arrName
      if (this.activeName === '1') {
        arrName = 'radioList'
      } else if (this.activeName === '2') {
        arrName = 'checkList'
      } else {
        arrName = 'textList'
      }
      if (item.checked) {
        item.answerType = Number(this.activeName)
        this[arrName].push(item)
      } else {
        this[arrName] = this[arrName].filter(items => {
          return item.id !== items.id
        })
      }
    },
    delItem (item, choesName) {
      this[choesName] = this[choesName].filter(items => {
        return item.id !== items.id
      })
      this.qsList.forEach(element => {
        if (element.id === item.id) {
          element.checked = false
        }
      })
    }
  }
}
</script>
<style lang="scss">
.add-question {
  width: 900px;
  padding: 20px;
  .nav-qs-box {
    max-height: 500px;
    overflow: auto;
  }
  .search-input {
    margin-bottom: 27px;
    margin-top: 10px;
    .el-input__inner {
      height: 40px;
      background: #ffffff;
      border: 2px solid #ebebeb;
      border-radius: 20px;
    }
  }
  .qs-box {
    width: 100%;
    box-sizing: border-box;
    padding: 30px;
    background: #f7f9fc;
    border-radius: 10px;
    margin-bottom: 10px;
    .qs-left {
      width: 90%;
      .qs-title {
        font-size: 18px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #333333;
        margin-bottom: 20px;
      }
      .qs-item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        .o-box,
        .square-box {
          width: 18px;
          height: 18px;
          background: #ffffff;
          border: 1px solid #cccccc;
          margin-right: 9px;
          flex-shrink: 0;
        }
        .o-box {
          border-radius: 50%;
        }
        .square-box {
          border-radius: 4px;
        }
        .qs-item-det {
          font-size: $textSize16;
          font-family: PingFang SC;
          font-weight: 400;
          color: #666666;
        }
      }
    }
  }
  .flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .choes-title {
    font-size: $textSize16;
    font-family: PingFang SC;
    font-weight: 400;
    color: #999999;
    margin-bottom: 20px;
  }
  .edit-box {
    margin-bottom: 20px;
    i {
      cursor: pointer;
    }
  }
  .el-aside {
    border-right: 1px solid #ebebeb;
    box-sizing: border-box;
    padding-right: 20px;
  }
  .btn {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
  .el-checkbox__inner {
    border: 1px solid #7f8c8d;
  }
}
</style>
