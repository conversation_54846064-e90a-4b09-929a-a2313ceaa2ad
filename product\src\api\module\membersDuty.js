// 导入封装的方法
import {
  get,
  post,
  postform,
  exportFile
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
export const exe = params => exportFile('', params)

const membersDuty = {
  // 值班列表
  memberGuestList (data) {
    return post('/officeonlineschedule/list', data)
  },
  // 新增代表值班
  memberGuestAdd (data) {
    return post('/officeonlineschedule/add', data)
  },
  // 代表值班编辑
  memberGuestEdit (data) {
    return post('/officeonlineschedule/edit', data)
  },
  // 删除代表值班
  memberGuestDelete (data) {
    return post('/officeonlineschedule/dels', data)
  },
  // 主题详情
  memberGuestThemeInfo (data) {
    return post('/officeonlinetopic/list', data)
  },
  // 主题修改
  memberGuestThemeEdit (data) {
    // return post(`/officeonlinetopic/edit`, data, { 'Content-Type': 'application/json;charset=UTF-8' })
    return post('/officeonlinetopic/edit', data)
  },
  // 代表值班的导出
  memberGuestExport (data) {
    return exportFile('/officeonlinetopic/export', data)
  },
  // 评论列表
  replyList (data) {
    return post('/comment/list', data)
  },
  replyLists (data) {
    return post('/oaservicereply/list', data)
  },
  // 评论删除
  replyDelete (data) {
    return post('/comment/dels', data)
  },
  // 评论审核
  replyVerify (data) {
    return post('/comment/audit', data)
  },
  replyVerifys (data) {
    return post('/oaservicereply/editState', data)
  },
  // 评论编辑
  replyEdit (data) {
    return post('/comment/save', data)
  },
  // 来信列表
  letterList (data) {
    return post('/officeonlineletter/list', data)
  },
  // 来信详情
  letterInfo (data) {
    return get(`/officeonlineletter/info/${data.id}`)
  },
  // 来信导出
  letterExport (data) {
    return exportFile('/officeonlineletter/export', data)
  },
  // 来信删除
  letterDelete (data) {
    return post('/officeonlineletter/dels', data)
  },
  // 来信编辑
  letterEdit (data) {
    return post('/officeonlineletter/edit', data)
  },
  // 来信默认公开状态
  letterDefaultStatus () {
    return post('/officeonlineletter/showSwitch')
  },
  // 设置来信默认公开状态
  letterChangeDefaultStatus (data) {
    return post('/officeonlineletter/editSwitch', data)
  },
  // 批量修改公开状态
  letterChangeOpenStatus (data) {
    return post('/officeonlineletter/publish', data)
  },
  // 获取值班代表
  memberList (data) {
    return post(`/officeonlineschedule/info/${data}`)
  },
  // 导入值班excel
  memberListExport (data) {
    return postform('/officeonlinetopic/import', data)
  },
  // 导出评论回复
  replyListExport (params) {
    return exportFile('/comment/export', params)
  }
}

export default membersDuty
