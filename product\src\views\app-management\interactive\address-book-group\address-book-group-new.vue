<template>
  <div class="address-book-group-new">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="分组名称"
                    class="form-input"
                    prop="name">
        <el-input placeholder="请输入分组名称"
                  v-model="form.name"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="上级分组">
        <zy-select width="296"
                   :data="tree"
                   node-key="id"
                   v-model="form.superior"
                   placeholder="请选择上级分组"
                   :props="{label:'name',children:'children'}"></zy-select>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-input"
                    prop="sort">
        <el-input placeholder="请输入排序"
                  v-model="form.sort"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="是否启用"
                    class="form-input">
        <el-radio-group v-model="form.isUsing">
          <el-radio label="1">启用</el-radio>
          <el-radio label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'addressBookGroupNew',
  data () {
    return {
      tree: [],
      form: {
        name: '',
        superior: '',
        sort: '',
        isUsing: '1'
      },
      rules: {
        name: [
          { required: true, message: '请输入分组名称', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ]
      }
    }
  },
  props: ['id', 'treeType', 'sort'],
  mounted () {
    if (this.treeType) {
      this.treeTist()
    }
    if (this.id) {
      this.treeTistInfo()
    } else {
      this.form.sort = this.sort
    }
  },
  methods: {
    async treeTist () {
      const res = await this.$api.appManagement.treeTist({
        treeType: this.treeType
      })
      var { data } = res
      this.tree = data
    },
    async treeTistInfo () {
      const res = await this.$api.appManagement.treeTistInfo(this.id)
      var { data: { parentId, sort, name } } = res
      this.form.superior = parentId
      this.form.name = name
      this.form.sort = sort
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.form.superior === '') {
            this.form.superior = 0
          }
          var url = '/tree/add?'
          if (this.id) {
            url = '/tree/edit?'
          }
          this.$api.systemSettings.generalAdd(url, {
            id: this.id,
            parentId: this.form.superior,
            name: this.form.name,
            sort: this.form.sort,
            treeType: this.treeType
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.address-book-group-new {
  width: 692px;
  height: 100%;
  padding: 24px 40px;

  .form-img {
    width: 296px;

    .form-img-uploader {
      width: 128px;
      height: 128px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: #199bc5;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 128px;
        height: 128px;
        line-height: 128px;
        text-align: center;
      }

      .user-img {
        width: 128px;
        height: 128px;
        display: block;
      }
    }
  }
}
</style>
