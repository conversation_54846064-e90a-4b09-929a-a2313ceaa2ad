<template>
  <div class="project-column">
    <div class="button-box-list">
      <el-button type="primary"
                 v-permissions="'auth:zySpecialsubjectColumn:save'"
                 @click="newData">新增</el-button>
      <el-button type="primary"
                 v-permissions="'auth:zySpecialsubjectColumn:delete'"
                 @click="deleteClick">删除</el-button>
    </div>
    <div class="information-mosaic-list scrollBar">
      <el-table ref="multipleTable"
                :data="tableData"
                stripe
                border
                style="width: 100%"
                row-key="id"
                :tree-props="{children: 'children'}"
                @selection-change="handleSelectionChange">
        <el-table-column type="selection"
                         width="48">
        </el-table-column>
        <el-table-column label="序号"
                         width="80"
                         prop="sort">
        </el-table-column>
        <el-table-column label="栏目名称"
                         prop="name"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="上级栏目"
                         prop="parentName"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="是否启用"
                         prop="createDate"
                         show-overflow-tooltip>
          <template slot-scope="scope">
            {{scope.row.isOpen=='1'?'启用':'禁用'}}
          </template>
        </el-table-column>
        <el-table-column label="是否图标展示"
                         prop="createDate"
                         show-overflow-tooltip>
          <template slot-scope="scope">
            {{scope.row.iconopen=='1'?'是':'否'}}
          </template>
        </el-table-column>
        <el-table-column label="操作"
                         v-if="$hasPermission(['auth:zySpecialsubjectColumn:save'])"
                         width="80">
          <template slot-scope="scope">
            <el-button @click="handleClick(scope.row)"
                       type="text"
                       size="small">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <zy-pop-up v-model="show"
               :title="mosaicId==''?'新增专题栏目':'编辑专题栏目'">
      <project-column-new :id="id"
                          :mosaicId="mosaicId"
                          @newCallback="newCallback"></project-column-new>
    </zy-pop-up>

  </div>
</template>
<script>
import projectColumnNew from './project-column-new/project-column-new'
export default {
  name: 'projectColumn',
  data () {
    return {
      tableData: [],
      multipleSelection: [],
      mosaicId: '',
      show: false
    }
  },
  props: ['id'],
  components: {
    projectColumnNew
  },
  mounted () {
    if (this.id) {
      this.customTopicColumnList()
    }
  },
  methods: {
    async customTopicColumnList () {
      const res = await this.$api.appManagement.customTopicColumnList({ subjectId: this.id, pageNo: 1, pageSize: 1000 })
      var { data } = res
      this.tableData = data
    },
    handleClick (row) {
      this.mosaicId = row.id
      this.show = true
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    newData () {
      this.mosaicId = ''
      this.show = true
    },
    newCallback () {
      this.customTopicColumnList()
      this.show = false
    },
    deleteClick () {
      if (this.multipleSelection.length) {
        this.$confirm('此操作将删除当前选中的专题栏目, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var arr = []
          this.multipleSelection.forEach(item => {
            arr.push(item.id)
          })
          var idSets = arr.join(',')
          this.customTopicColumnDel(idSets)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async customTopicColumnDel (id) {
      const res = await this.$api.appManagement.customTopicColumnDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.customTopicColumnList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
@import "./project-column.scss";
</style>
