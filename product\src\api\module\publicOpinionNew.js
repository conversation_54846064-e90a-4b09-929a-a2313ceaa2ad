// 导入封装的方法
import {
  get,
  post,
  postformProgress,
  postform,
  exportFile
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const szxurl = 'http://103.239.154.90:81/WebService/Social/'
const publicOpinion = {
  GetSocialListMy (params) { // 山东省政协社情民意
    return get(szxurl + 'SocialGet.asmx/GetSocialListMy', params)
  },
  GetSocialListGroup (params) { // 山东省政协社情民意
    return get(szxurl + 'SocialGet.asmx/GetSocialListGroup', params)
  },
  GetSocialReportmode (params) { // 1.1.1.6.获取社情民意报送方式
    return get(szxurl + 'SocialGet.asmx/GetSocialReportmode?', params)
  },
  GetPerformanceList (params) { // 1.1.1.6.获取履职参阅
    return get(szxurl + 'SocialGet.asmx/GetPerformanceList?', params)
  },
  GetNoticeList (params) { //  通知公告
    return get(szxurl + 'SocialGet.asmx/GetNoticeList?', params)
  },
  GetSocialTotalOrgSheng (params) { //  通知公告
    return get(szxurl + 'SocialGet.asmx/GetSocialTotalOrgSheng?', params)
  },

  GetSocialInfo (params) { //  通知公告
    return get(szxurl + 'SocialGet.asmx/GetSocialInfo?', params)
  },
  SocialPost (url, params) { //  tijiao
    if (url === '15.72.181.123') {
      var urla = 'http://192.168.153.32:81/WebService/Social/'
      return post(urla + 'SocialPost.ashx', params)
    } else {
      return post(szxurl + 'SocialPost.ashx', params)
    }
  },

  socialinfoadd (url, params) { // 提交社情民意
    return post('/socialinfo/' + url, params)
  },
  socialinfoList (params) { // 我的社情民意列表
    return post('/socialinfo/list', params)
  },
  socialinfohistory (params) { // 我的社情民意列表
    return post('/socialinfohistory/list', params)
  },
  socialinfohistoryAll (params) { // 我的社情民意列表
    return post('/socialinfohistory/info/' + params)
  },
  socialinfodels (params) { // 我的社情民意列表
    return post('socialinfo/dels', params)
  },
  socialinfoinfoable (params) { // 社情民意详情
    return post(`/socialinfo/infoable/${params}`)
  },
  complatetask (params) { // 办理当前流程
    return post('/socialinfo/complatetask', params)
  },
  socialcount (params) { // 来稿统计
    return post('/socialinfo/count', params)
  },

  socialinfoinfo (params) { // 社情民意详情【仅显示】
    return post('/socialinfo/info/' + params)
  },
  socialinfolook (params) { // 社情民意详情【仅显示】
    return post('/socialinfo/look/' + params)
  },
  socialapproveinfo (params) { // 原稿详情
    return post(`/socialapprove/info/${params}`)
  },
  opinionoriginalinfo (params) { // 原稿详情
    return post(`/opinionoriginal/info/${params}`)
  },
  treelist (params) { // 查询树列表
    return get('/tree/list', params)
  },
  dictionarylist (params) { //
    return get('/dictionary/list', params)
  },
  socialcollectorlist (params) { // 配置
    return get('/socialscore/list', params)
  },
  socialcollectoredit (params) { // 配置修改
    return get('/socialscore/edit', params)
  },

  dictionarypubkvs (params) {
    return post('/dictionary/pubkvs', params)
  },
  editqdstatus (params) { //  处理信息
    return post('/socialinfo/editqdstatus', params)
  },

  editqdstatuses (params) { //  批量处理信息
    return post('/socialinfo/editqdstatuses', params)
  },

  statusinfo (params) { //  处理信息详情
    return post('/socialinfo/statusinfo/' + params)
  },
  socialinfocount (params) { //  统计
    return post('/socialinfo/count', params)
  },
  signbest (params) { //  统计
    return post('/socialinfo/signbest', params)
  },
  exportsfields (params) { //  统计
    return post('/socialinfo/exportsfields', params)
  },
  socialinfoexports (params) { //  统计
    return exportFile('/socialinfo/exports', params)
  },
  exportcount (params) { //  统计导出
    return exportFile('/socialinfo/exportcount', params)
  },

  socialconfiglist (params) { //  配置列表
    return post('/socialconfig/list', params)
  },
  socialconfigdel (params) { //  配置列表删除
    return post('/socialconfig/del', params)
  },
  socialconfig (url, params) { //  配置列表
    return post(url, params)
  },
  socialinfoexportlist (params) { //  配置列表
    return post('socialinfo/exportlist', params)
  },
  commonApiTreeList (params) { //  树列表
    return post('/commonApi/tree/list', params)
  },
  opinionApiAdd (params) { //  提交
    return post('/opinionApi/add', params, {
      'Content-Type': 'application/json;charset=UTF-8'
    })
  },
  opinionApiList (params) { //  列表
    return post('/opinionApi/list', params)
  },
  opinionApiLook (params) { //  列表
    return post('/opinionApi/look/' + params)
  },
  opinionApiReport (params) { //  上报
    return post('/opinionApi/report', params, {
      'Content-Type': 'application/json;charset=UTF-8'
    })
  },
  commonApiDictionaryPubkvs (params) { //  字典
    return post('/commonApi/dictionary/pubkvs', params)
  },
  productApiuploadFile (params, callback, id) {
    return postformProgress('/productApi/attachment/uploadFile', params, 880000, callback, id)
  },
  commonApiPointreeUsers (params) { //  选人
    return post('/commonApi/pointree/users', params)
  },
  getSocialUnitStatistics (params) { // 社情民意首页柱状图
    return post('/socialinfo/unitStatistics', params)
  },
  smartCheck (params) { //  公文校正
    const theme = sessionStorage.getItem('theme')
    const BigDataUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + theme))
    return post(BigDataUrl + '/smart/check', params)
  },
  // 大数据接口
  similarity (params) { //  相似度
    const theme = sessionStorage.getItem('theme')
    const BigDataUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + theme))
    return post(BigDataUrl + '/smart/similarity', params)
  },
  // 大数据接口
  corrector (params) { //  相似度
    const theme = sessionStorage.getItem('theme')
    const BigDataUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + theme))
    return post(BigDataUrl + '/smart/corrector', params)
  },
  // 大数据接口
  searchDataDetail (params) { //  相似度
    const theme = sessionStorage.getItem('theme')
    const BigDataUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + theme))
    return get(BigDataUrl + '/common/searchDataDetail', params)
  }

}
export default publicOpinion
