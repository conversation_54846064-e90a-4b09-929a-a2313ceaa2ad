<template>
  <div class="zy-select-checkbox"
       :style="{ width: width + 'px' }">
    <el-popover popper-class="zy-select-checkbox-popover"
                :trigger="trigger"
                :disabled="disabled"
                :width="width"
                v-model="options_show">
      <div slot="reference"
           :class="['zy-select-checkbox-input',disabled?'zy-select-checkbox-input-disabled':'']">
        <div :class="['zy-select-checkbox-input-icon',options_show?'zy-select-checkbox-input-icon-a':'']">
          <i class="el-icon-arrow-down"></i>
        </div>
        <div v-if="!selectData.length"
             class="zy-select-checkbox-input-text">{{placeholder}}</div>
        <el-tag v-for="tag in selectData"
                :key="tag[nodeKey]"
                size="medium"
                closable
                :disable-transitions="false"
                @close="remove(tag)">
          {{tag[props.label]}}
        </el-tag>
      </div>

      <el-scrollbar class="zy-select-checkbox-box-box">
        <div>
          <el-input placeholder="请输入关键字查询"
                    v-model="keyword"
                    clearable>
          </el-input>
        </div>
        <el-scrollbar class="zy-select-checkbox-box">
          <el-tree :class="['zy-select-checkbox-tree',child? 'zy-select-checkbox-tree-a':'']"
                   ref="tree"
                   :data="data"
                   show-checkbox
                   :props="props"
                   check-strictly
                   highlight-current
                   :node-key="nodeKey"
                   :filter-node-method="filterNode"
                   @check-change="selectedClick">
          </el-tree>
        </el-scrollbar>
      </el-scrollbar>
    </el-popover>
  </div>
</template>
<script>
export default {
  name: 'zySelectCheckbox',
  data () {
    return {
      keyword: '',
      ids: [],
      selectData: [],
      options_show: false
    }
  },
  props: {
    value: [Array],
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 树结构配置
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label'
        }
      }
    },
    // 触发方式 click/focus/hover/manual
    trigger: {
      type: String,
      default: 'click'
    },
    placeholder: {
      type: String,
      default: '请选择内容'
    },
    message: {
      type: String,
      default: '选择的内容'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    child: {
      type: Boolean,
      default: false
    },
    // 宽度
    width: {
      type: String,
      default: '296'
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    max: {
      type: Number
    }
  },
  model: {
    prop: 'value',
    event: 'id'
  },
  mounted () {
    this.dataMethods()
  },
  watch: {
    value (val) {
      this.dataMethods()
    },
    data (val) {
      this.dataMethods()
    },
    keyword (val) {
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    filterNode (value, data) {
      if (!value) return true
      return data[this.props.label].indexOf(value) !== -1
    },
    dataMethods () {
      this.ids = this.getArray(this.value)
      this.selected(this.ids)
      this.$nextTick(function () {
        this.$refs.tree.setCheckedKeys(this.ids)
      })
    },
    // 数组去重
    getArray (a) {
      var hash = {}
      var len = a.length
      var result = []
      for (var i = 0; i < len; i++) {
        if (!hash[a[i]]) {
          hash[a[i]] = true
          result.push(a[i])
        }
      }
      return result
    },
    // 首次进来传入的数据
    selected (data) {
      var arr = []
      data.forEach(item => {
        arr = arr.concat(this.selectedMethods(this.data, item))
      })
      this.selectData = arr
      this.$emit('choose-click', this.selectData)
    },
    // 首次进来默认选中
    selectedMethods (data, id) {
      var obj = []
      data.forEach(item => {
        if (item[this.nodeKey] === id) {
          obj.push(item)
        }
        if (item.children && item.children.length > 0) {
          obj = obj.concat(this.selectedMethods(item.children, id))
        }
      })
      return obj
    },
    // 下拉框选中事件
    selectedClick (data, type) {
      if (type) {
        if (this.max) {
          if (this.ids.length === this.max) {
            this.$nextTick(function () {
              this.$refs.tree.setCheckedKeys(this.ids)
            })
            this.$emit('id', this.ids)
            this.$emit('choose-click', this.selectData)
            this.$message({
              message: `${this.message}不能超过${this.max}个`,
              type: 'warning'
            })
            return
          }
        }
        this.ids.push(data[this.nodeKey])
        this.selectData.push(data)
      } else {
        var ids = this.ids
        var selectData = this.selectData
        this.ids = ids.filter(item => item !== data[this.nodeKey])
        this.selectData = selectData.filter(item => item[this.nodeKey] !== data[this.nodeKey])
      }
      this.$emit('id', this.ids)
      this.$emit('choose-click', this.selectData)
    },
    // 移除tag
    remove (data) {
      if (this.disabled) {
        return
      }
      var ids = this.ids
      var selectData = this.selectData
      this.ids = ids.filter(item => item !== data[this.nodeKey])
      this.selectData = selectData.filter(item => item[this.nodeKey] !== data[this.nodeKey])
      this.$nextTick(function () {
        this.$refs.tree.setCheckedKeys(this.ids)
      })
      this.$emit('id', this.ids)
      this.$emit('choose-click', this.selectData)
    }
  }
}
</script>
<style lang="scss">
@import "./zy-select-checkbox.scss";
</style>
