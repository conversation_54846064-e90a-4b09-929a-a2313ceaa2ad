//
// Buttons
// --------------------------------------------------


// Base styles
// --------------------------------------------------

// Core
.btn {
  display: inline-block;
  .ie7-inline-block();
  padding: 4px 12px;
  margin-bottom: 0; // For input.btn
  font-size: @baseFontSize;
  line-height: @baseLineHeight;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  .buttonBackground(@btnBackground, @btnBackgroundHighlight, @grayDark, 0 1px 1px rgba(255,255,255,.75));
  border: 1px solid @btnBorder;
  *border: 0; // Remove the border to prevent IE7's black border on input:focus
  border-bottom-color: darken(@btnBorder, 10%);
  .border-radius(@baseBorderRadius);
  .ie7-restore-left-whitespace(); // Give IE7 some love
  .box-shadow(~"inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05)");

  // Hover/focus state
  &:hover,
  &:focus {
    color: @grayDark;
    text-decoration: none;
    background-position: 0 -15px;

    // transition is only when going to hover/focus, otherwise the background
    // behind the gradient (there for IE<=9 fallback) gets mismatched
    .transition(background-position .1s linear);
  }

  // Focus state for keyboard and accessibility
  &:focus {
    .tab-focus();
  }

  // Active state
  &.active,
  &:active {
    background-image: none;
    outline: 0;
    .box-shadow(~"inset 0 2px 4px rgba(0,0,0,.15), 0 1px 2px rgba(0,0,0,.05)");
  }

  // Disabled state
  &.disabled,
  &[disabled] {
    cursor: default;
    background-image: none;
    .opacity(65);
    .box-shadow(none);
  }

}



// Button Sizes
// --------------------------------------------------

// Large
.btn-large {
  padding: @paddingLarge;
  font-size: @fontSizeLarge;
  .border-radius(@borderRadiusLarge);
}
.btn-large [class^="icon-"],
.btn-large [class*=" icon-"] {
  margin-top: 4px;
}

// Small
.btn-small {
  padding: @paddingSmall;
  font-size: @fontSizeSmall;
  .border-radius(@borderRadiusSmall);
}
.btn-small [class^="icon-"],
.btn-small [class*=" icon-"] {
  margin-top: 0;
}
.btn-mini [class^="icon-"],
.btn-mini [class*=" icon-"] {
  margin-top: -1px;
}

// Mini
.btn-mini {
  padding: @paddingMini;
  font-size: @fontSizeMini;
  .border-radius(@borderRadiusSmall);
}


// Block button
// -------------------------

.btn-block {
  display: block;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
  .box-sizing(border-box);
}

// Vertically space out multiple block buttons
.btn-block + .btn-block {
  margin-top: 5px;
}

// Specificity overrides
input[type="submit"],
input[type="reset"],
input[type="button"] {
  &.btn-block {
    width: 100%;
  }
}



// Alternate buttons
// --------------------------------------------------

// Provide *some* extra contrast for those who can get it
.btn-primary.active,
.btn-warning.active,
.btn-danger.active,
.btn-success.active,
.btn-info.active,
.btn-inverse.active {
  color: rgba(255,255,255,.75);
}

// Set the backgrounds
// -------------------------
.btn-primary {
  .buttonBackground(@btnPrimaryBackground, @btnPrimaryBackgroundHighlight);
}
// Warning appears are orange
.btn-warning {
  .buttonBackground(@btnWarningBackground, @btnWarningBackgroundHighlight);
}
// Danger and error appear as red
.btn-danger {
  .buttonBackground(@btnDangerBackground, @btnDangerBackgroundHighlight);
}
// Success appears as green
.btn-success {
  .buttonBackground(@btnSuccessBackground, @btnSuccessBackgroundHighlight);
}
// Info appears as a neutral blue
.btn-info {
  .buttonBackground(@btnInfoBackground, @btnInfoBackgroundHighlight);
}
// Inverse appears as dark gray
.btn-inverse {
  .buttonBackground(@btnInverseBackground, @btnInverseBackgroundHighlight);
}


// Cross-browser Jank
// --------------------------------------------------

button.btn,
input[type="submit"].btn {

  // Firefox 3.6 only I believe
  &::-moz-focus-inner {
    padding: 0;
    border: 0;
  }

  // IE7 has some default padding on button controls
  *padding-top: 3px;
  *padding-bottom: 3px;

  &.btn-large {
    *padding-top: 7px;
    *padding-bottom: 7px;
  }
  &.btn-small {
    *padding-top: 3px;
    *padding-bottom: 3px;
  }
  &.btn-mini {
    *padding-top: 1px;
    *padding-bottom: 1px;
  }
}


// Link buttons
// --------------------------------------------------

// Make a button look and behave like a link
.btn-link,
.btn-link:active,
.btn-link[disabled] {
  background-color: transparent;
  background-image: none;
  .box-shadow(none);
}
.btn-link {
  border-color: transparent;
  cursor: pointer;
  color: @linkColor;
  .border-radius(0);
}
.btn-link:hover,
.btn-link:focus {
  color: @linkColorHover;
  text-decoration: underline;
  background-color: transparent;
}
.btn-link[disabled]:hover,
.btn-link[disabled]:focus {
  color: @grayDark;
  text-decoration: none;
}
