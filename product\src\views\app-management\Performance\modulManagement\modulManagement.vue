<template>
  <div class="groupingManagement">
    <screening-box
      @search-click="search"
      :resetButton="false"
    >
      <el-input
        placeholder="请输入内容"
        v-model="keyword"
        clearable
        @keyup.enter.native="search"
      >
      </el-input>
    </screening-box>
    <div class="button-box">
      <el-button
        type="primary"
        icon="el-icon-plus"
        @click="newData"
      >新增</el-button>
      <el-button
        type="primary"
        icon="el-icon-delete"
        @click="deleteClick"
      >删除</el-button>
      <el-button
        type="primary"
        @click="generateShow =true "
      >生成年度报告</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table
          :data="tableData"
          stripe
          border
          ref="table"
          slot="zytable"
          @select="selected"
          @select-all="selectedAll"
        >
          <el-table-column
            type="selection"
            fixed="left"
            width="60"
          ></el-table-column>

          <el-table-column
            label="排序"
            width="80"
            prop="sort"
          >
          </el-table-column>
          <el-table-column
            label="背景图片"
            width="120"
          >
            <template slot-scope="scope">
              <div class="table-img">
                <img
                  @click="preview(scope.row)"
                  :src="scope.row.backgroundImg"
                  alt=""
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="文本左边距"
            min-width="120"
            prop="locationLeft"
          >
          </el-table-column>

          <!-- <el-table-column
            label="文本右边距"
            min-width="120"
            prop="localtionRight"
          >
          </el-table-column> -->
          <el-table-column
            label="文本顶边距"
            min-width="120"
            prop="localtionTop"
          >
          </el-table-column>
          <!-- <el-table-column
            label="文本底边距"
            min-width="120"
            prop="localtionBottom"
          >
          </el-table-column> -->
          <el-table-column
            label="边距单位"
            min-width="120"
            prop="localtionUnit"
          >
          </el-table-column>
          <el-table-column
            label="分组"
            min-width="120"
            prop="groupName"
          >
          </el-table-column>

          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                @click="modify(scope.row)"
                type="primary"
                plain
                size="mini"
              >编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination
        @size-change="howManyArticle"
        @current-change="whatPage"
        :current-page.sync="page"
        :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
        :page-size.sync="pageSize"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>

    <zy-pop-up
      v-model="show"
      title="新增分组"
    >
      <addGrouping
        :id="id"
        @newCallback="newCallback"
      ></addGrouping>
    </zy-pop-up>

    <zy-pop-up
      v-model="imgshow"
      title="新增分组"
    >

      <div class="PicPreview">
        <img
          :src="imgItem.backgroundImg"
          alt=""
        >
      </div>
    </zy-pop-up>

    <zy-pop-up
      v-model="generateShow"
      title="年度报告"
    >
      <div class="PicPreview">
        <el-date-picker
          v-model="value5"
          align="right"
          type="year"
          placeholder="选择年"
        >
        </el-date-picker>

        <div class="btn">
          <el-button
            type="primary"
            plain
            size="mini"
            @click="generate"
          >确定</el-button>
          <el-button
            plain
            size="mini"
            @click="generateShow =false "
          >取消</el-button>
        </div>
      </div>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '../../../../mixins/tableData'
import addGrouping from './addGrouping/addGrouping'
export default {
  name: 'moduleManagement',
  data () {
    return {
      id: '',
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      props: {
        children: 'children',
        label: 'value'
      },
      data: [],
      show: false,
      imgshow: false,
      imgItem: {},
      generateShow: false,
      value5: ''
    }
  },
  inject: ['newTab'],
  mixins: [tableData],
  components: {
    addGrouping
  },
  mounted () {
    this.moduleList()
  },
  methods: {
    search () {
      this.moduleList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    newCallback () {
      this.moduleList()
      this.show = false
    },
    modify (row) {
      this.id = row.id
      this.show = true
    },
    preview (path) {
      // this.imgshow = true
      // console.log(path)
      this.imgItem = path
    },
    async moduleList () {
      const res = await this.$api.appManagement.yearsummarylist(
        { pageNo: this.page, pageSize: this.pageSize, keyword: this.keyword }
      )
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.moduleList()
    },
    whatPage (val) {
      this.moduleList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将永久删除该菜单, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.moduleDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async moduleDel (id) {
      const res = await this.$api.appManagement.yearsummarydels({ ids: id })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.moduleList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async generate () {
      if (!this.value5) {
        this.$message({
          message: '请选择年份',
          type: 'warning'
        })
        return
      }
      const res = await this.$api.appManagement.yearsummarygenerate({
        countYear: this.$format(this.value5, 'YYYY')
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }

}
</script>
<style lang="scss">
@import './modulManagement.scss';
</style>
