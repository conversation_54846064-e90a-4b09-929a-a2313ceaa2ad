<template>
  <div class="information-mosaic">
    <div class="button-box-list">
      <el-button type="primary"
                 v-permissions="'auth:zyinforeportpic:pic:add'"
                 @click="newData">新增</el-button>
      <el-button type="primary"
                 v-permissions="'auth:zyinforeportpic:dels'"
                 @click="deleteClick">删除</el-button>
    </div>
    <div class="information-mosaic-list scrollBar">
      <el-table ref="multipleTable"
                :data="tableData"
                stripe
                border
                style="width: 100%"
                @selection-change="handleSelectionChange">
        <el-table-column type="selection"
                         width="48">
        </el-table-column>
        <el-table-column label="序号"
                         width="80"
                         prop="sort">
        </el-table-column>
        <el-table-column label="图片名称"
                         show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button @click="details(scope.row)"
                       type="text"
                       size="small">{{scope.row.pic.shortName}}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作"
                         v-if="$hasPermission(['auth:zyinforeportpic:pic:edit'])"
                         width="80">
          <template slot-scope="scope">
            <el-button @click="handleClick(scope.row)"
                       v-permissions="'auth:zyinforeportpic:pic:edit'"
                       type="text"
                       size="small">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <zy-pop-up v-model="show"
               title="组图">
      <mosaic-new :id="id"
                  :mosaicId="mosaicId"
                  @newCallback="newCallback"></mosaic-new>
    </zy-pop-up>

    <zy-pop-up v-model="imgUrlShow"
               title="图片详情">
      <img height="580px"
           :src="imgUrl"
           alt="">
    </zy-pop-up>
  </div>
</template>
<script>
import mosaicNew from './mosaic-new/mosaic-new'
export default {
  name: 'informationMosaic',
  data () {
    return {
      tableData: [],
      multipleSelection: [],
      mosaicId: '',
      show: false,
      imgUrl: '',
      imgUrlShow: false
    }
  },
  props: ['id'],
  components: {
    mosaicNew
  },
  mounted () {
    if (this.id) {
      this.picList()
    }
  },
  methods: {
    async picList () {
      const res = await this.$api.appManagement.picList({ detailId: this.id, pageNo: 1, pageSize: 1000 })
      var { data } = res
      this.tableData = data
    },
    handleClick (row) {
      this.mosaicId = row.id
      this.show = true
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    newData () {
      this.mosaicId = ''
      this.show = true
    },
    newCallback () {
      this.picList()
      this.show = false
    },
    details (row) {
      this.imgUrl = row.pic.fullUrl
      this.imgUrlShow = true
    },
    deleteClick () {
      if (this.multipleSelection.length) {
        this.$confirm('此操作将删除当前选中的组图, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var arr = []
          this.multipleSelection.forEach(item => {
            arr.push(item.id)
          })
          var idSets = arr.join(',')
          this.picDel(idSets)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async picDel (id) {
      const res = await this.$api.appManagement.picDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.picList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
@import "./information-mosaic.scss";
</style>
