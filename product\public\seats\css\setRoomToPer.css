:root {
	--color: #72B9D0;
}

body {
	overflow: auto;
}

html {
	background: #fff;
}

li {
	list-style: none outside none;
}

.box {
	width: 100%;
	height: 100%;
}

p {
	font-size: 20px;
	color: #333;
	text-align: center;
	margin: 0 0 20px;
}

i.hander {
	display: block;
	width: 100%;
	height: 25px;
	background: #ccc;
	text-align: center;
	font-size: 12px;
	color: #333;
	line-height: 25px;
	font-style: normal;
}

.inputBorder {
	border-color: #94ACFF;
}

.row {
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	margin-right: 0;
	margin-left: 0;
	height: 40px;
	line-height: 35px;
}

#saveSeats,
#bw,
#dd,
#exportSeatInfo,
#release {
	margin-right: 20px;
	background: #199BC5;
	border-radius: 4px;
	color: #fff;
	border: none;
	height: 44px;
	align-items: center;
}

#addAccBtn,
#operationBtn,
#sendBtn {
	margin-right: 20px;
	background: #199BC5;
	border-radius: 2px;
	color: #fff;
	border: none;
	height: 36px;
	line-height: 36px;
	align-items: center;
}

.btnImg {
	width: 20px;
	height: 20px;
	position: relative;
	top: -2px;
}

#uploadPic {
	margin-left: 20px;
	background: #199BC5;
	border-radius: 2px;
	color: #fff;
	border: none;
	height: 36px;
	align-items: center;
}

#importPer,
#autoSort {
	height: 30px;
	line-height: 15px;
	margin: 2px auto;
}

.layui-upload-file {
	position: absolute;
	z-index: -1;
}

#meetName {
	position: absolute;
	top: -8px;
	right: 0;
	z-index: 100;
	font-size: 24px !important;
	margin-bottom: 0;
	width: 100%;
}

.bjBox {
	position: absolute;
	top: 30px;
	right: 0;
	z-index: 100;
	font-size: 20px !important;
	margin: 0 !important;
	color: #000000;
	text-align: left;
	padding-left: 30px;
	width: 100%;
}

#deleteSeats {
	background-color: #FF5064;
	color: #FFFFFF;
	cursor: pointer;
}

#deleteSeats:hover {
	background-color: #ff8291;
}

#clearAll {
	border-color: #FF5064;
	color: #FF5064;
}

/* .btn-info:hover{
	background: #47afd1 !important;
	border-color: #47afd1 !important;
	color: #FFF !important;
} */

.cancel {
	background: transparent;
	border: 1px solid var(--color);
	color: var(--color);
}

/* .cancel:hover{
	border-color: #199BC5 !important;
	color: #199BC5 !important;
} */

.cr {
	margin-right: 30px;
	padding: 5px 15px;
	border-radius: 4px;
	text-align: center;
	cursor: pointer;
	color: white;
}

.qr {
	padding: 5px 15px;
	border-radius: 4px;
	text-align: center;
	cursor: pointer;
	color: white;
}

.toButton {
	margin-bottom: 16px;
}

.toRight {
	margin-right: 20px;
}

.importType {
	padding-top: 7px;
}

.importType .radio {
	padding-left: 25px;
}

.container {
	position: absolute;
	left: 32px;
	width: 210px;
	height: 45px;
	margin: 5px 5px 0px 5px;
}

.impContainer {
	position: absolute;
	left: 33px;
	width: 200px;
	height: 45px;
	margin: 5px 5px 0px 5px;
}

.sortContainer {
	position: relative;
	left: 0;
	top: 0;
	width: 95.5%;
	height: 45px;
	margin: 5px 5px 0px 5px;
}

.txt {
	width: 100%;
	border: 0px;
	font-size: 16px;
	outline: medium;
}

.group {
	width: 200px;
	height: 35px;
	margin: 2px 0;
	padding: 5px 5px 5px 12px;
	background: rgba(255, 255, 255, 1);
	border: 1px solid rgba(229, 229, 229, 1);
	border-radius: 4px;
	color: #666666;
}

.dept_content {
	display: none;
	position: absolute;
	width: 14%;
	height: 240px;
	border: 0 solid #9e9e9e;
	background-color: #fff;
	opacity: 1;
	z-index: 9999;
	overflow-x: hidden;
	overflow-y: auto;
	box-shadow: 0px 2px 8px 0px rgba(216, 216, 216, 0.5);
}

.drag {
	position: absolute;
	border-radius: 5px;
	background: #fff;
	cursor: move;
	display: inline-grid;
	font-size: 12px;
	/*background-image: url(../img/seat.svg);*/
	background-repeat: no-repeat;
	background-size: 100% 100%;
}

.signDrag {
	position: absolute;
	border: 0px solid #dce0e4;
	border-radius: 0px;
	cursor: move;
	display: flex;
	justify-content: center;
	align-items: center;
	background-repeat: no-repeat;
	background-size: 100% 100%;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size: 14px !important;
	color: #262626;
}

.selected {
	background-color: #e46424 !important;
	border-color: #e46424;
}

.combo-select {
	width: 200px;
	height: 35px;
	margin: 2px 0;
	font: 100% Helvetica, Arial, Sans-serif;
	background: url(../../../../../assets/images/table-select.png) right 0.75rem center no-repeat;
	border: 1px solid rgba(229, 229, 229, 1);
	border-radius: 4px;
	appearance: none;
	color: #aeaeae;
	padding: 5px 5px 5px 8px;
	-moz-appearance: none;
	-webkit-appearance: none;
	-ms-appearance: none;
}

.textbox {
	border: 1px solid rgb(50, 121, 244);
}

#perList .radio {
	padding-left: 5px;
}

.layui-btn {
	display: inline-block;
	margin-left: 5px;
	height: 35px;
	line-height: 35px;
	padding: 0 18px;
	background-color: #009688;
	color: #fff;
	white-space: nowrap;
	text-align: center;
	font-size: 14px;
	border: none;
	border-radius: 5px;
	cursor: pointer;
}

.releaseTip {
	display: flex;
	position: absolute;
	top: 0;
	left: 30px;
	z-index: 100;
	text-align: right;
}

.areaTip {
	display: flex;
	position: absolute;
	top: 0;
	right: 30px;
	z-index: 100;
	text-align: right;
}

#noticeInfo {
	font-size: 22px !important;
	font-family: PingFang SC;
	line-height: 33px;
	color: #3279F4;
	opacity: 1;
}

#noMeetList, #nameRepeat, #noSeatNo, #noNameList {
	font-size: 20px !important;
	font-family: PingFang SC;
	line-height: 33px;
	color: #333333;
	opacity: 1;
	font-weight: bold;
	margin-top: 0 !important;
}

.list {
	margin: 5px;
	padding: 5px;
}

.list li {
	display: inline;
}

.dropdown-menu a {
	cursor: pointer;
}

.radio-info input[type="radio"]:checked+label::before {
	border-color: var(--color);
}

.radio-info input[type="radio"]:checked+label::after {
	background-color: var(--color);
}

.textbox-focused {
	border-color: var(--color);
}

.layui-upload-file {
	opacity: 0;
}

.btn-info.active,
.btn-info:active,
.show>.btn-info.dropdown-toggle {
	background: #fff;
}

.btn.focus,
.btn:focus {
	box-shadow: none;
}

.btn {
	height: 44px;
	padding: 0 16px;
	align-items: center;
}

/* .btn-info:hover {
	border-color: rgba(217, 217, 217, 1);
} */

.radio label {
	padding-left: 0;
}

.searchResult {
	line-height: 48px;
	margin-left: 24px;
	color: #8c8c8c;
}

#winModal .modal-content {
	width: 500px;
	min-height: 531px;
	margin: 0 auto;
	border-radius: 0;
	border: none;
	margin-top: 120px;
}

#rsdModal .modal-content {
	width: 600px;
	height: 500px;
	margin: 0 auto;
	border-radius: 0;
	border: none;
	margin-top: 120px;
}

#infoModal .modal-content {
	width: 600px;
	height: 390px;
	margin: 0 auto;
	border-radius: 10px;
	border: none;
	margin-top: 120px;
}

.modal-title {
	font-size: 20px !important;
	font-family: PingFang SC;
	font-weight: bold;
	line-height: 30px;
	color: #333333;
	opacity: 1;
}

.modal-title img {
	width: 24px;
	position: relative;
	top: -2px;
	margin-right: 5px;
}

.modal-body img {
	width: 24px;
	position: relative;
	top: -5px;
	margin-right: 5px;
}

.close {
	opacity: 1;
}

.modal-header {
	padding: 0 20px;
	height: 42px;
	border-bottom: 1px solid #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 15px;
	border-radius: 10px;
	border-bottom: 0px solid #e9ecef;
	background: #FFFFFF;
}

.modal-footer {
	padding-top: 0;
	height: 56px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-top: 0px solid #e9ecef;
}

.modal-footer label {
	width: 68px;
	height: 34px;
	line-height: 34px;
	text-align: center;
	border: 1px solid rgba(217, 217, 217, 1);
	background: linear-gradient(0deg, rgba(249, 250, 251, 1) 0%, rgba(255, 255, 255, 1) 100%);
	border-radius: 2px;
	font-size: 14px;
	margin-bottom: 0;
	margin-right: 0;
	margin-left: 16px !important;
}

.modal-footer>label:nth-child(1) {
	background: #199BC5;
	color: #fff;
	border: none;
}

.modal-body {
	height: 300px;
	overflow: auto;
	padding: 20px;
	display: flex;
	flex-direction: column;
	background: #FFFFFF;
}

#grantModal .modal-body {
	padding: 20px;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;
	max-height: 300px;
	overflow: auto;
}

.modal-body>div {
	display: flex;
	margin-top: 10px !important;
}

.modal-body span {
	line-height: 44px;
	margin-right: 14px;
	font-size: 14px !important;
}

.modal-body input {
	height: 44px;
	background: rgba(255, 255, 255, 1);
	border: 1px solid rgba(217, 217, 217, 1);
	border-radius: 2px;
	padding-left: 16px;
	font-size: 14px;
}

.modal-body #_easyui_textbox_input2,
.modal-body .textbox-addon-right {
	height: 44px !important;
	line-height: 44px !important;
}

.modal-body .textbox-addon-right a,
.modal-body .textbox-addon-right {
	width: 26px;
	height: 44px !important;
	margin: 0;
}

.selectIco {
	width: 24px;
	height: 24px;
	position: absolute;
	bottom: 8px;
	top: 10px;
	right: 0;
	z-index: 5;
	color: #B3B3B3;
}

#organName::-webkit-input-placeholder {
	color: #8c8c8c;
}

.form-control:disabled,
.form-control[readonly] {
	opacity: 1;
}

.form-control:focus {
	color: #8C8C8C;
	border: 1px solid var(--color);
	;
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.15);
	outline: 0;
}

.radioRow {
	background: rgba(255, 255, 255, 1);
	box-shadow: 0px -1px 0px 0px rgba(230, 230, 230, 1);
	border-bottom: 1px solid rgb(230, 230, 230);
	line-height: 36px;
	height: 40px;
}

.layui-table-cell .layui-form-checkbox[lay-skin="primary"] {
	top: 6px;
	padding: 0;
}

#rsdList {
	height: 150px;
	overflow: auto;
}

.rsd_content {
	display: none;
	position: absolute;
	width: 14%;
	height: 200px;
	border: 1px solid #9e9e9e;
	background-color: #fff;
	opacity: 1;
	z-index: 1002;
}

.btn-footer {
	height: 45px;
	margin: 5px;
	padding: 0;
}

.infoTitle {
	font-size: 18px !important;
	font-family: PingFang SC;
	font-weight: 400;
	line-height: 30px;
	color: #333333;
	opacity: 1;
}

.checkbox-custom input[type=checkbox],
.radio-custom input[type=radio] {
	position: relative;
	left: 5px;
	margin: auto;
	-webkit-appearance: none;
	background: transparent;
	width: 20px;
	height: 20px;
	border: 1px solid #ccc;
	vertical-align: middle;
}

.checkbox-custom .radius-50,
.radio-custom .radius-50 {
	border-radius: 50%;
}

.checkbox-custom input[type=checkbox]:checked,
.radio-custom input[type=radio]:checked {
	border-color: var(--color);
	background: var(--color);
}

.checkbox-custom input[type=checkbox]:checked:after,
.radio-custom input[type=radio]:checked:after {
	content: '';
	position: absolute;
	left: 4px;
	top: 5px;
	width: 10px;
	height: 5px;
	border-left: 2px solid #fff;
	border-bottom: 2px solid #fff;
	-webkit-transform: rotateZ(-45deg);
	transform: rotateZ(-45deg);
}

/****************选择人员*******************/
#perModal {
	align-items: center;
	overflow-y: auto;
}

#perModal .modal-body {
	flex: inherit;
	display: flex;
	padding: 0;
	overflow-y: hidden;
	overflow-x: hidden;
	height: 406px;
	border-radius: 0;
}

.modal-content {
	width: 660px;
	margin: 0 auto;
	border-radius: 0;
	border: none;
}

#textUserBox .modal-content {
	width: 100% !important;
	margin: 0 auto;
	border-radius: 0;
	border: none;
}

#perModal .modal-body>div {
	margin-top: 0;
}

.modal_search {
	padding-left: 16px;
	position: relative;
}

.modal_search input {
	width: 340px;
	height: 30px;
	background: rgba(255, 255, 255, 1);
	border: 1px solid rgba(217, 217, 217, 1);
	border-radius: 2px;
	font-size: 12px !important;
	padding-left: 31px;
	box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.15);
}

#perModal .modal-body>div {
	display: block;
}

.modal_type {
	padding-left: 20px;
}

.modal_main_r p {
	padding-left: 12px;
}

.modal_search img {
	position: absolute;
	width: 14px;
	height: 14px;
	left: 25px;
	top: 8px;
}

.modal_main {
	display: flex;
}

.modal_main>div {
	width: 200px;
	display: flex;
	flex-direction: column;
}

.modal_title {
	font-size: 14px;
	color: #8c8c8c;
	margin-bottom: 9px;
	display: flex;
	justify-content: left;
	padding-right: 16px;
}

.modal_main>div p {
	line-height: 100%;
}

.modal_main {
	height: 326px;
}

#perModal .modal-dialog {
	max-width: 660px;
	height: 504px;
	margin: 0 auto;
	background: #fff;
	margin-top: 8%;
}

.modal_right {
	width: 426px;
	border-left: 1px solid #d9d9d9;
}

.modal_main_r {
	margin-top: 16px;
}

.is_check_modal {
	background: #e8e8e8;
}

.modal_main_r_item {
	display: flex;
	justify-content: space-between;
	padding: 0 16px;
	align-items: center;
	padding-right: 10px;
}

.modal_right>.modal_title {
	padding-right: 0;
	align-items: center;
	padding-left: 12px;
	height: 15px;
	margin-bottom: 0;
}

.modal_right>div {
	display: flex;
	flex-direction: column;
}

.modal_right_item_content {
	font-size: 14px;
	display: flex;
	flex-direction: column;
	width: 330px;
	margin-right: 15px;
}

.modal_right_item_content>p:nth-child(2) {
	color: #8c8c8c;
}

.modal_right_item {
	display: flex;
	align-items: center;
	margin-bottom: 12px;
}

.modal_right_item>img:nth-child(1) {
	margin-right: 10px;
}

.modal_main_l {
	border-right: 1px solid #d9d9d9;
	margin-top: 16px;
}

.modal_main_r_item>div {
	font-size: 14px;
	color: #262626;
}

.modal_main_r_item>div img {
	width: 12px;
	height: 12px;
	margin-right: 10px;
}

.modal_btn {
	height: 56px;
	display: flex;
	align-items: center;
	justify-content: center;
	padding-right: 20px;
	background: #f5f5f5;
}

.modal_btn label {
	width: 68px;
	height: 34px;
	line-height: 34px;
	text-align: center;
	border: 1px solid rgba(217, 217, 217, 1);
	background: linear-gradient(0deg, rgba(249, 250, 251, 1) 0%, rgba(255, 255, 255, 1) 100%);
	border-radius: 2px;
	font-size: 14px;
	margin-left: 16px;
}

.modal_btn>label:nth-child(1) {
	background: rgba(33, 112, 217, 1);
	color: #fff;
	border: none;
}

.layui-tree-main {
	display: flex;
	height: 30px;
	font-size: 14px;
	align-items: center;
}

.layui-tree-iconClick,
.layui-tree-iconArrow {
	width: 14px;
	height: 14px;
	display: flex;
}

.layui-tree-iconArrow {
	width: 14px;
	height: 14px;
	background-image: url(../../../../assets/images/gzhy/minus.png);
	background-repeat: no-repeat;
}

.layui-hide {
	background: none !important;
}

.layui-tree>div>.layui-tree-lineExtend .layui-tree-main {
	padding-left: 30px;
}

.layui-tree>div>.layui-tree-lineExtend>div>.layui-tree-lineExtend .layui-tree-main {
	padding-left: 60px;
}

.layui-tree-main>span:nth-child(2) {
	cursor: pointer;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.isUserClick {
	background-color: #E8E8E8;
}

.layui-tree>div>.layui-tree-pack .layui-tree-main {
	padding-left: 30px;
}

.layui-tree>div>.layui-tree-pack>div>.layui-tree-pack .layui-tree-main {
	padding-left: 60px;
}

#perModal .modal-body {
	flex-direction: initial;
}

.ztree li a.curSelectedNode {
	color: var(--color);
}

/* 选人框复选框样式 */
.perLab {
	width: 130px;
	position: relative;
	top: 2px;
	left: 15px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	font-size: 12px !important;
}

.checkPerBox-custom img {
	position: relative;
	top: -12px;
	left: 12px;
}

.checkMark {
	width: 8px;
	height: 16px;
	border-color: rgba(117, 200, 43, 1);
	border-style: solid;
	border-width: 0 2px 2px 0;
	transform: rotate(45deg);
}

#allCheck {
	background: var(--color);
	width: 20px;
	height: 20px;
	border-radius: 12px;
	position: absolute;
	top: 90px;
	left: 358px;
}

.check {
	content: "";
	position: absolute;
	left: 5px;
	top: 5px;
	width: 10px;
	height: 6px;
	border: 2px solid #fff;
	border-radius: 1px;
	border-top: none;
	border-right: none;
	background: transparent;
	transform: rotate(-45deg);
}

.halfCheck {
	content: "";
	position: absolute;
	left: 5px;
	top: 9px;
	width: 10px;
	height: 0px;
	border: 2px solid #fff;
	border-radius: 1px;
	border-top: none;
	border-right: none;
	background: transparent;
	transform: rotate(0deg);
}

.unCheck {
	background: #FFF !important;
	border: 1px solid #ccc !important;
}

.checkPerBox-custom input[type=checkbox],
.radio-custom input[type=radio] {
	position: relative;
	top: -12px;
	left: 15px;
	margin: auto;
	-webkit-appearance: none;
	background: #FFF;
	width: 20px;
	height: 20px;
	border-radius: 10px;
	border: 1px solid #ccc;
	vertical-align: middle;
	cursor: pointer;
}

.checkPerBox-custom .radius-50,
.radio-custom .radius-50 {
	border-radius: 50%;
}

.checkPerBox-custom input[type=checkbox]:checked,
.radio-custom input[type=radio]:checked {
	border-color: var(--color);
	background: var(--color);
}

.checkPerBox-custom input[type=checkbox]:checked:after,
.radio-custom input[type=radio]:checked:after {
	content: '';
	position: absolute;
	left: 4px;
	top: 5px;
	width: 10px;
	height: 5px;
	border-left: 2px solid #fff;
	border-bottom: 2px solid #fff;
	-webkit-transform: rotateZ(-45deg);
	transform: rotateZ(-45deg);
}

/* end */

/* 已选择人员框部分样式 */
.allDel {
	position: relative;
	top: -10px;
	left: 225px;
	cursor: pointer;
}

.checkedPerBox-custom {
	height: 50px;
}

.userImg {
	position: relative;
	top: -12px;
	left: 12px;
}

.closeImg {
	position: relative;
	top: -35px;
	left: 30px;
	cursor: pointer;
}

.checkedPerLab {
	width: 195px;
	position: relative;
	top: 2px;
	left: 15px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	font-size: 12px !important;
}

.positionLab {
	width: 195px;
	position: relative;
	top: -7px;
	left: 12px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	height: 20px;
	line-height: 15px;
	padding-left: 15px;
	font-size: 12px !important;
	color: #aaa;
}

/* end */

#grantModal .modal-dialog {
	margin-top: calc((100vh - 450px)/2);
}

.tempBody input[type="checkbox"] {
	width: 18px;
	height: 18px;
	display: inline-block;
	text-align: center;
	vertical-align: middle;
	line-height: 18px;
	position: relative;
}

.tempBody input[type="checkbox"]::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	background: #fff;
	width: 100%;
	height: 100%;
	border: 1px solid #d9d9d9
}

.tempBody input[type="checkbox"]:checked::before {
	content: "\2713";
	background-color: var(--color);
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	border: 1px solid var(--color);
	color: #fff;
	font-size: 16px;
	font-weight: bold;
}

.bjRadio {
	display: inline-flex;
}

.bjNameRadio {
	width: 22px;
	height: 22px;
	border-radius: 20px;
	margin-top: 2px;
	cursor: pointer;
}

.bjNameRadioCheck {
	border: 1px solid #3279F4;
}

.bjNameRadioUnCheck {
	border: 1px solid #c0c0c0;
}

.bjNameRadioChild {
	width: 8px;
	height: 8px;
	background-color: #3279F4;
	position: relative;
	top: 6px;
	left: 6px;
	border-radius: 8px;
}

.bjRadioInput {
	width: 100px;
	height: 25px;
	margin-left: 2px;
	font-size: 16px;
	border: none;
	outline: none;
}
