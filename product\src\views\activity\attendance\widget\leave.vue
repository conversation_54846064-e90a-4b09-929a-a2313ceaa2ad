<template>
  <el-form :model="form" class="sign-up-form qd-form" ref="form" :rules="rules" label-width="120px" inline>
    <el-form-item label="请假人" class="form-item-wd50">
      <el-input v-model="form.userNames" disabled></el-input>
    </el-form-item>
    <el-form-item label="请假类型" class="form-item-wd50" prop="reasonChoose">
      <el-select v-model="form.reasonChoose" placeholder="请选择请假理由">
        <el-option v-for="item in reasonList" :key="item.id" :label="item.value" :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="请假凭证" class="form-item-wd100">
      <upload v-model="form.files"></upload>
    </el-form-item>
    <el-form-item label="内容" class="form-item-wd100">
      <el-input v-model="form.reason" type="textarea" rows="3"></el-input>
    </el-form-item>
    <div class="form-footer-btn">
      <el-button type="primary" size="small" @click="onSubmit('form')">确定</el-button>
    </div>
  </el-form>
</template>

<script>
import upload from './upload'
import dayjs from 'dayjs'
export default {
  components: { upload },
  props: { info: Object, aid: String, status: Number },
  data() {
    return {
      form: {
        userNames: '',
        userId: '',
        reasonChoose: '',
        reason: '',
        files: []
      },
      reasonList: [],
      rules: {
        reasonChoose: [{ required: true, message: '请选择情况类型', trigger: 'change' }]
      }
    }
  },
  created() {
    if (this.info) {
      this.form.userNames = this.info.userName
      this.form.userId = this.info.userId
    }
    this.getDictionary()
  },
  methods: {
    getDictionary() {
      this.$api.microAdvice.dictionaryCommittee({ types: 'act_leave_type' }).then(res => {
        this.reasonList = res.data.act_leave_type
      })
    },
    onSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const data = this.form
          data.attachmentIds = this.form.files.map(v => v.id).join(',')
          delete data.files
          data.status = this.status
          data.meetId = this.aid
          data.leaveDatetime = dayjs().format('YYYY-MM-DD HH:mm:ss')
          this.$api.activity.leave.add(data).then(res => {
            this.$message.success(res.errmsg)
            if (res.errcode === 200) {
              this.$emit('callback')
            }
          })
        } else {
          return false
        }
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.sign-up-form {
  width: 725px;
  padding: 10px 15px;
}
</style>
