/* 文件上传预览 */

.upload-wraper {
    border: 1px dashed #ccc;
    min-height: 100px;
    margin: 0 10px;
    padding: 0 10px 10px 10px;
    display: flex;
    display: -webkit-flex;
    justify-content: flex-start;
    flex-wrap: wrap;
}

.upload-wraper>div {
    margin: 10px 10px 0 0;
}

.upload-preview {
    border-radius: .5rem;
    width: 95px;
    height: 80px;
    border: 1px solid #E5E5E5;
    position: relative;
    overflow: hidden;
}

.upload-preview .file-link {
    display: block;
    width: 100%;
    height: 100%;
    text-align: center;
    padding-top: 10px;
    background-color: rgba(0, 0, 0, .1);
}

.upload-preview .file-link>.fa {
    font-size: 50px;
}

.upload-preview img {
    border-radius: .5rem;
    width: 100%;
    height: 100%;
}

img[data-zoom] {
    cursor: -webkit-zoom-in;
}

.upload-preview .preview-name {
    opacity: 0;
    position: absolute;
    color: #fff;
    text-align: center;
    font-size: 14px;
    font-family: <PERSON><PERSON>, "Helvetica Neue", Helvetica, Arial;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 20px;
    border-bottom-left-radius: .5rem;
    border-bottom-right-radius: .5rem;
    background-color: rgba(0, 0, 0, .5);
    transition: all .5s ease;
}

.upload-preview:hover .preview-name {
    opacity: 1;
}

.upload-preview .preview-del,
.upload-preview .image-download {
    position: absolute;
    width: 25px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
    color: #fff;
    font-size: 18px;
    top: 3px;
    left: 50%;
    cursor: pointer;
    transition: all .3s ease-in;
    transform: translate(-50%, -30px) rotate(180deg);
}

.upload-preview:hover .preview-del {
    transform: translate(-50%, 0px) rotate(0deg);
}

.upload-preview:hover .preview-del.image-del {
    transform: translate(-100%, 0px) rotate(0deg);
}

.upload-preview:hover .image-download {
    transform: translate(30%, 0px) rotate(0deg);
}

.upload-preview:hover .image-download.isread {
    transform: translate(-50%, 0px) rotate(0deg);
}

.upload-file {
    border-radius: .5rem;
    height: 80px;
    width: 95px;
    border: 2px solid #E5E5E5;
    position: relative;
    background-size: 30px 30px;
}

.upload-file:hover {
    background-image: linear-gradient(-45deg, #F6F6F6 25%, transparent 25%, transparent 50%, #F6F6F6 50%, #F6F6F6 75%, transparent 75%, transparent);
    animation: stripes 2s linear infinite;
}

@keyframes stripes {
    from {
        background-position: 0 0
    }
    to {
        background-position: 60px 30px
    }
}

.upload-file .file-icon {
    font-size: 25px;
    color: skyblue;
    position: absolute;
    top: 30%;
    left: 50%;
    transform: translate(-50%);
}

.upload-file .file-msg {
    font-size: 10px;
    color: #ccc;
    position: absolute;
    width: 100%;
    text-align: center;
    top: 60%;
    left: 50%;
    transform: translate(-50%);
}

.upload-file input {
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 5;
}

.is-loading {
    background-color: black;
    background-position: center center;
    background-repeat: no-repeat;
    background-image: url("./img/loading.gif");
}

.is-loading img {
    opacity: 0;
}

.preview-zoom {
    border: 5px solid #fff;
    border-radius: .2rem;
    box-shadow: 0 0 10px #bbb;
}