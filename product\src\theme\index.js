import client from 'webpack-theme-color-replacer/client'
import forElementUI from 'webpack-theme-color-replacer/forElementUI'
import themeConst from './config'

export let curColor = themeConst.themeColor

export const changeThemeColor = (newColor = '#94070A') => {
  const options = {
    newColors: [...forElementUI.getElementUISeries(newColor), '#3657C0', '#3279F4']
  }
  return client.changer.changeColor(options, Promise)
    .then(t => {
      curColor = newColor
      localStorage.setItem('theme_color', newColor)
    })
}

export const initThemeColor = () => {
  const savedColor = localStorage.getItem('theme_color')
  if (savedColor) {
    curColor = savedColor
    changeThemeColor(savedColor)
  }
}
