.gradient-text (@color: #808080, @color1: #999, @color2: #B3B3B3, @color3: #B3B3B3, @color4: #666) {
    // fallback for browsers that don't support this
//    color: @color;

    // makes simple gradient text in webkit browsers
//    background: -webkit-gradient(linear, left top, left bottom, from(@colorTop), to(@colorBottom));

    // makes a more complex background, allowing iPhone-like text gradients
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%, @color1), color-stop(55%, @color2), color-stop(55%, @color3), color-stop(100%, @color4)); // Safari 4+, Chrome 2+
    background-image: -webkit-linear-gradient(top, @color1 0%, @color2 55%, @color3 55%, @color4 100%); // Safari 5.1+, Chrome 10+

    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

// Mixin for generating button backgrounds
// ---------------------------------------
.buttonBackgroundThreeColors(@startColor, @midColor, @colorStop, @endColor) {
  // gradientBar will set the background to a pleasing blend of these, to support IE<=9
  #gradient > .vertical-three-colors(@startColor, @midColor, @colorStop, @endColor);
  border-color: @startColor @startColor darken(@endColor, 15%);
  border-color: rgba(0,0,0,.1) rgba(0,0,0,.1) fadein(rgba(0,0,0,.1), 15%);

  .reset-filter();

  // in these cases the gradient won't cover the background, so we override
  &:hover, &:active, &.active, &.disabled, &[disabled] {
    background-color: @endColor;
  }

  // IE 7 + 8 can't handle box-shadow to show active, so we darken a bit ourselves
  &:active,
  &.active {
    background-color: darken(@endColor, 10%) e("\9");
  }
}

.icon-size (@size: 14px, @width-multiplier: .9, @height-multiplier: 1) {
  i { font-size: @size; }
  line-height: @size * 1.1;
  height: @size * @height-multiplier * 1.05;
  text-align: center;
}

.sticky-footer (@footer-height: @baseLineHeight * 4, @footer-padding-top: 70px, @footer-padding-bottom: 70px, @footer-margin-top: 70px) {
  .wrapper {
    margin: 0 auto -(@footer-height + @footer-padding-bottom + @footer-padding-top + @footer-margin-top + 1);
  }
  .push {
    height: @footer-height + @footer-padding-bottom + @footer-padding-top + @footer-margin-top + 1;
  }

  .footer {
    margin-top: @footer-margin-top;
    height: @footer-height;
    padding: @footer-padding-top 0 @footer-padding-bottom;
  }
}
