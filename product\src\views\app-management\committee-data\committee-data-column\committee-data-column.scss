.committee-data-column {
  height: 100%;
  width: 100%;

  .information-box {
    height: calc(100% - 128px);
    width: 100%;
    display: flex;

    .information-tree-box {
      width: 222px;
      height: 100%;

      .zy-tree {
        width: 222px;
        min-width: 222px;
      }

      .information-tree-text {
        height: 52px;
        line-height: 52px;
        padding-left: 24px;
        font-size: $textSize16;
        background-color: #e6e5e8;
      }

      .information-tree {
        height: calc(100% - 52px);
      }
    }

    .information-data-box {
      width: calc(100% - 222px);
      height: 100%;

      .plenum-data {
        height: calc(100% - 52px);
        width: 100%;
      }
    }
  }
}
