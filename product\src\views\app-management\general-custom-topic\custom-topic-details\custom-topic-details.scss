.custom-project-details {
  width: 1000px;
  height: 100%;

  div {
    line-height: 22px;
  }

  .custom-project-details-title {
    width: 684px;
    margin: auto;
    line-height: 28px;
    font-size: 20px;
    padding: 20px 0;
    text-align: center;
  }

  .custom-project-details-xx {
    height: 46px;
    background: #f5f7f8;
    box-shadow: 0px -1px 0px 0px rgba(230, 230, 230, 1);
    display: flex;
    align-items: center;
    justify-content: center;

    .custom-project-details-source {
      font-size: $textSize14;
      line-height: 20px;
    }

    .custom-project-details-tiem {
      margin-left: 72px;
      font-size: $textSize14;
      line-height: 20px;
    }
  }

  .custom-project-details-content {
    width: 984px;
    padding: 22px 88px;
    margin: auto;
  }

  .cover {
    width: 984px;
    padding: 22px 88px;
    margin: auto;
    display: flex;
    margin-bottom: 24px;

    .cover-text {
      padding-right: 30px;
      font-size: $textSize14;
    }

    .cover-img {
      width: 500px;
      height: 300px;

      img {
        // width: 100%;
        height: 100%;
      }
    }
  }

  .theme {
    width: 984px;
    padding: 22px 88px;
    margin: auto;
    display: flex;

    .theme-text {
      padding-right: 30px;
      font-size: $textSize14;
    }

    .theme-img {
      width: 500px;
      height: 300px;

      img {
        // width: 100%;
        height: 100%;
      }
    }
  }
}
