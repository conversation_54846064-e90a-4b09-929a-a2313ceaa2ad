
const partyIndex = () => import('@PartyBuilding-qd/partyIndex/partyIndex')
const office = () => import('@PartyBuilding-qd/office/office')
const officeNews = () => import('@PartyBuilding-qd/office/officeNews/officeNews') // 党建资讯新增
const officeDetial = () => import('@PartyBuilding-qd/office/officeDetial/officeDetial') // 党建资讯报送详情
const examineofficeDetail = () => import('@PartyBuilding-qd/Partyexamine/examineofficeDetail/examineofficeDetail') // 党建资讯管理详情
const partyAssessment = () => import('@PartyBuilding-qd/office/partyAssessment') // 党建考核
const otherPartyInfo = () => import('@PartyBuilding-qd/office/otherPartyInfo') // 其他党建信息
const fumian = () => import('@PartyBuilding-qd/office/fumian') // 其他党建信息
const partyType = () => import('@PartyBuilding-qd/partyType/partyType') // 党建配置
const othtrpartyType = () => import('@PartyBuilding-qd/partyType/othtrpartyType/othtrpartyType') // 其他
const yearConfig = () => import('@PartyBuilding-qd/partyType/yearConfig/yearConfig') // 其他
// const assessment = () => import('@PartyBuilding-qd/partyType/assessment') // 考核配置
const NegativeListconfig = () => import('@PartyBuilding-qd/partyType/NegativeListconfig/NegativeListconfig') // 负面清单配置
const department = () => import('@PartyBuilding-qd/partyType/departmentMenege/departmentMenege') // 部门及人员管理
const transfer = () => import('@PartyBuilding-qd/partyType/transfer/transfer') // 部门及人员管理
const examineoffice = () => import('@PartyBuilding-qd/Partyexamine/examineoffice/examineoffice') // 党建审核
const negativelist = () => import('@PartyBuilding-qd/Partyexamine/negativelist/negativelist') // 党建负面审核
const otherexmine = () => import('@PartyBuilding-qd/Partyexamine/otherexmine/otherexmine') // 党建其他审核
const partyAssessmentManage = () => import('@PartyBuilding-qd/Partyexamine/partyAssessmentManage/partyAssessmentManage') // 党建其他审核

const customTheme = () => import('@/views/customTheme/customTheme') // 自定义专题
const themeContent = () => import('@/views/customTheme/themeContent/themeContent') // 专题内容
const customThemeAdd = () => import('@/views/customTheme/general-custom-topic/custom-topic-add/custom-topic-add') // 自定义专题新增

const PartyBuilding = [
  {
    path: '/themeContent',
    name: 'themeContent',
    component: themeContent
  },
  {
    path: '/customThemeAdd',
    name: 'customThemeAdd',
    component: customThemeAdd
  },
  {
    path: '/customTheme',
    name: 'customTheme',
    component: customTheme
  },
  {
    path: '/partyIndex',
    name: 'partyIndex',
    component: partyIndex
  },
  { // 部门及人员管理
    path: '/partyAssessmentManage',
    name: 'partyAssessmentManage',
    component: partyAssessmentManage
  },
  { // 部门及人员管理
    path: '/negativelist',
    name: 'negativelist',
    component: negativelist
  },
  { // 部门及人员管理
    path: '/otherexmine',
    name: 'otherexmine',
    component: otherexmine
  },
  { // 部门及人员管理
    path: '/examineoffice',
    name: 'examineoffice',
    component: examineoffice
  },

  { // 部门及人员管理
    path: '/transfer',
    name: 'transfer',
    component: transfer
  },
  { // 部门及人员管理
    path: '/department',
    name: 'department',
    component: department
  },
  { // 负面清单
    path: '/NegativeListconfig',
    name: 'NegativeListconfig',
    component: NegativeListconfig
  },

  { // 年份配置
    path: '/yearConfig',
    name: 'yearConfig',
    component: yearConfig
  },
  { // 党建考核
    path: '/partyAssessment',
    name: 'partyAssessment',
    component: partyAssessment
  },
  { // 其他党建
    path: '/otherPartyInfo',
    name: 'otherPartyInfo',
    component: otherPartyInfo
  },
  { // 其他党建
    path: '/fumian',
    name: 'fumian',
    component: fumian
  },
  { // 机关党建
    path: '/office',
    name: 'office',
    component: office
  },
  { // 新增机关党建
    path: '/officeNews',
    name: 'officeNews',
    component: officeNews
  },
  { // 新增机关党建
    path: '/officeDetial',
    name: 'officeDetial',
    component: officeDetial
  },
  { // 党建资讯管理详情
    path: '/examineofficeDetail',
    name: 'examineofficeDetail',
    component: examineofficeDetail
  },
  { // 党建类型
    path: '/partyType',
    name: 'partyType',
    component: partyType
  },

  { // 党建类型
    path: '/othtrpartyType',
    name: 'othtrpartyType',
    component: othtrpartyType
  }

]

export default PartyBuilding
