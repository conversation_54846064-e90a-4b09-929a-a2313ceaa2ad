// TODO:*******此文件为机关考核相关路由组件的注册**********

// 党建精神文明建设
const ConstructionSpiritualCivilizationPage = () => import('@/views/AssessmentOrgan/ConstructionSpiritualCivilization/ConstructionSpiritualCivilizationPage')

// 月工作纪实
const MonthlyWorkRecord = () => import('@/views/AssessmentOrgan/MonthlyWorkRecord/MonthlyWorkRecord')
// 新建月工作纪实
const MonthlyWorkRecordAdd = () => import('@/views/AssessmentOrgan/MonthlyWorkRecordAdd/MonthlyWorkRecordAdd')
// 月工作纪实列表 内容详情
const detailsContents = () => import('@/views/AssessmentOrgan/MonthlyWorkRecord/detailsContents')

// 季度评议
const QuarterlyReview = () => import('@/views/AssessmentOrgan/QuarterlyReview/QuarterlyReview')

// 业务工作目标
const BusinessObjectives = () => import('@/views/AssessmentOrgan/BusinessObjectives/BusinessObjectives')
// 新建 完成情况
// const newFinishDetail = () => import('@/views/AssessmentOrgan/BusinessObjectives/newFinishDetail')

// 创新创优目标 InnovationExcellence

const InnovationExcellence = () => import('@/views/AssessmentOrgan/InnovationExcellence/InnovationExcellence')
// 双招双引 DoubleQuote
const DoubleQuote = () => import('@/views/AssessmentOrgan/DoubleQuote/DoubleQuote')
// 新建 双招双引
const DoubleQuoteAddOrEdit = () => import('@/views/AssessmentOrgan/DoubleQuote/DoubleQuoteAddOrEdit')
// 双招双引 详情
const DoubleDetails = () => import('@/views/AssessmentOrgan/DoubleQuote/DoubleDetails')

// 三双活动 ThreeActivities
const ThreeActivities = () => import('@/views/AssessmentOrgan/ThreeActivities/ThreeActivities')
// 三双 详情 ThreeDetails
const ThreeDetails = () => import('@/views/AssessmentOrgan/ThreeActivities/ThreeDetails')
// 新增\编辑 三双 newOrEditThreeActivities
const newOrEditThreeActivities = () => import('@/views/AssessmentOrgan/ThreeActivities/newOrEditThreeActivities')

// 五进五送 IntoFive
const IntoFive = () => import('@/views/AssessmentOrgan/IntoFive/IntoFive')
// 五进五送 详情 FiveDetails
const FiveDetails = () => import('@/views/AssessmentOrgan/IntoFive/FiveDetails')
// 新增\编辑 五送 newOrEditFive
const newOrEditFive = () => import('@/views/AssessmentOrgan/IntoFive/newOrEditFive')

// 加分项 addMarks
const addMarks = () => import('@/views/AssessmentOrgan/addMarks/addMarks')
// 新建 加分项
const addMarksAddOrEdit = () => import('@/views/AssessmentOrgan/addMarks/addMarksAddOrEdit')
// 加分项 详情
const addMarksDetail = () => import('@/views/AssessmentOrgan/addMarks/addMarksDetail')

// 机关民主评议 OrganDemocraticReview
const OrganDemocraticReview = () => import('@/views/AssessmentOrgan/OrganDemocraticReview/OrganDemocraticReview')

// 用户民主评议 UserDemocraticReview
const UserDemocraticReview = () => import('@/views/AssessmentOrgan/UserDemocraticReview/UserDemocraticReview')

// 机关考核审核
const organCheck = () => import('@/views/AssessmentOrgan/organCheck/organCheck')

// 个人考核结果 PersonalAssessmentResults
const PersonalAssessmentResults = () => import('@/views/AssessmentOrgan/PersonalAssessmentResults/PersonalAssessmentResults')

// 考核时间配置 PersonalAssessmentResults
const AssessmentTime = () => import('@/views/AssessmentOrgan/AssessmentTime/AssessmentTime')

// 考核项管理 AssessmentManagement
const AssessmentManagement = () => import('@/views/AssessmentOrgan/AssessmentManagement/AssessmentManagement')

// 人员调动 StaffMovement
const StaffMovement = () => import('@/views/AssessmentOrgan/StaffMovement/StaffMovement')

// 部门考核及人员管理 DepartmentAndPeople
const DepartmentAndPeople = () => import('@/views/AssessmentOrgan/DepartmentAndPeople/DepartmentAndPeople')

// 部门考核结果 DepartmentAndPeople
const DepartmentResults = () => import('@/views/AssessmentOrgan/DepartmentResults/DepartmentResults')

// 考核分值配置 scoreConfiguration
const scoreConfiguration = () => import('@/views/AssessmentOrgan/scoreConfiguration/scoreConfiguration')

// 记分规则管理 ScoringRules
const ScoringRules = () => import('@/views/AssessmentOrgan/ScoringRules/ScoringRules')

// 额外负分
// 部分  departmentscore
const departmentscore = () => import('@/views/AssessmentOrgan/departmentscore/departmentscore')
// 用户  userscore
const userscore = () => import('@/views/AssessmentOrgan/userscore/userscore')
// 机关考核的相关路由组件注册
const AssessmentOrgan = [// 机关考核总
  { // 党建精神文明建设
    path: '/ConstructionSpiritualCivilization',
    name: 'ConstructionSpiritualCivilizationPage',
    component: ConstructionSpiritualCivilizationPage
  },
  { // 月工作纪实 列表首页
    path: '/MonthlyWorkRecord',
    name: 'MonthlyWorkRecord',
    component: MonthlyWorkRecord
  },
  { // 月工作纪实 列表内容详情
    path: '/detailsContents',
    name: 'detailsContents',
    component: detailsContents
  },
  { // 新建月工作纪实
    path: '/MonthlyWorkRecordAdd',
    name: 'MonthlyWorkRecordAdd',
    component: MonthlyWorkRecordAdd
  },
  { // 季度评议
    path: '/QuarterlyReview',
    name: 'QuarterlyReview',
    component: QuarterlyReview
  },

  { // 业务工作目标
    path: '/BusinessObjectives',
    name: 'BusinessObjectives',
    component: BusinessObjectives
  },
  // { // 新建完成情况
  //   path: '/newFinishDetail',
  //   name: 'newFinishDetail',
  //   component: newFinishDetail
  // },

  { // 创新创优目标
    path: '/InnovationExcellence',
    name: 'InnovationExcellence',
    component: InnovationExcellence
  },
  { // 双招双引 DoubleQuote
    path: '/DoubleQuote',
    name: 'DoubleQuote',
    component: DoubleQuote
  },
  { // 新建 双招双引
    path: '/DoubleQuoteAddOrEdit',
    name: 'DoubleQuoteAddOrEdit',
    component: DoubleQuoteAddOrEdit
  },
  { // 双招双引 详情
    path: '/DoubleDetails',
    name: 'DoubleDetails',
    component: DoubleDetails
  },
  { // 三双活动 ThreeActivities
    path: '/ThreeActivities',
    name: 'ThreeActivities',
    component: ThreeActivities
  },
  { // 三双 详情 ThreeDetails
    path: '/ThreeDetails',
    name: 'ThreeDetails',
    component: ThreeDetails
  },
  { // 新建 三双
    path: '/newOrEditThreeActivities',
    name: 'newOrEditThreeActivities',
    component: newOrEditThreeActivities
  },

  { // 五进五送 IntoFive
    path: '/IntoFive',
    name: 'IntoFive',
    component: IntoFive
  },
  { // 五进五送 详情 FiveDetails
    path: '/FiveDetails',
    name: 'FiveDetails',
    component: FiveDetails
  },
  { // 新建 五送 newOrEditFive
    path: '/newOrEditFive',
    name: 'newOrEditFive',
    component: newOrEditFive
  },

  { // 加分项 addMarks
    path: '/addMarks',
    name: 'addMarks',
    component: addMarks
  },
  { // 新建 加分项
    path: '/addMarksAddOrEdit',
    name: 'addMarksAddOrEdit',
    component: addMarksAddOrEdit
  },
  { // 加分项 详情
    path: '/addMarksDetail',
    name: 'addMarksDetail',
    component: addMarksDetail
  },

  { // 机关民主评议 OrganDemocraticReview
    path: '/OrganDemocraticReview',
    name: 'OrganDemocraticReview',
    component: OrganDemocraticReview
  },

  { // 用户民主评议 UserDemocraticReview
    path: '/UserDemocraticReview',
    name: 'UserDemocraticReview',
    component: UserDemocraticReview
  },

  { // 机关考核审核 organCheck
    path: '/organCheck',
    name: 'organCheck',
    component: organCheck
  },

  { // 个人考核结果 PersonalAssessmentResults
    path: '/PersonalAssessmentResults',
    name: 'PersonalAssessmentResults',
    component: PersonalAssessmentResults
  },

  { // 考核时间配置 AssessmentTime
    path: '/AssessmentTime',
    name: 'AssessmentTime',
    component: AssessmentTime
  },

  { // 考核项管理 AssessmentManagement
    path: '/AssessmentManagement',
    name: 'AssessmentManagement',
    component: AssessmentManagement
  },

  { // 人员调动 StaffMovement
    path: '/StaffMovement',
    name: 'StaffMovement',
    component: StaffMovement
  },

  { // 部门考核及人员管理 DepartmentAndPeople
    path: '/DepartmentAndPeople',
    name: 'DepartmentAndPeople',
    component: DepartmentAndPeople
  },

  { // 部门考核结果 DepartmentResults
    path: '/DepartmentResults',
    name: 'DepartmentResults',
    component: DepartmentResults
  },

  { // 考核分值配置
    path: '/scoreConfiguration',
    name: 'scoreConfiguration',
    component: scoreConfiguration
  },

  { // 记分规则管理 ScoringRules
    path: '/ScoringRules',
    name: 'ScoringRules',
    component: ScoringRules
  },

  // 额外负分
  { // 部门 departmentscore
    path: '/departmentscore',
    name: 'departmentscore',
    component: departmentscore
  },

  { // 用户 userscore
    path: '/userscore',
    name: 'userscore',
    component: userscore
  }
]
export default AssessmentOrgan
