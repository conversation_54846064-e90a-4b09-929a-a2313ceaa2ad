//文本框值改变事件
$("#perTree").on("change", "input[type=text]", function() {
  var sNo = $(this).val(); //获取设置的座位编号
  var pName = selTreeNode.name; //获取人员姓名
  var pMobile = selTreeNode.umobile; //获取人员手机号
  var rpName = ""; //与之重复设置座位号的人员姓名
  var isPass = false; //是否通过，判断输入的座位号是否能对应到会场指定的座位
  var isRepeat = false; //是否重复，判断输入的座位号与设置的人员座位号有没有重复

  //遍历人员树所有节点的座位号,判断是否有重复
  var treeObj = $.fn.zTree.getZTreeObj("perTree");
  var treeNode = treeObj.getNodes();
  var treeNodes = treeObj.transformToArray(treeNode); //获取树所有节点

  for (var i = 0; i < treeNodes.length; i++) {

    if (treeNodes[i].isPer == true) {

      //过滤掉座位号为空的人员
      if (sNo != "") {

        if (sNo == treeNodes[i].seatesNo) //座位号重复
        {
          rpName = treeNodes[i].name;
          isRepeat = true;
          break;
        } else { //座位号没有重复
          isRepeat = false;
          continue;
        }
      }
    }
  }

  //设置的座位号与所有的座位相匹配之后，找到了相同的座位号, 提示设置的座位号在所有的座位里面有重复设置
  if (isRepeat == true) {
    //		layer.msg("给 " + pName + " 设置的座位号 " + sNo + " 与 " + rpName + " 的座位号重复");
    selTreeNode.seatesId = "";
    selTreeNode.seatesNo = "";
    $(this).val("");
    return;
  }

  //当设置的座位号不为空时，进入匹配座位号的代码
  if (sNo != "") {
    if (seatesArr != undefined) {
      //遍历所有座位，给参会人员设置座位号
      for (var i = 0; i < seatesArr.length; i++) {
        //当设置的座位号与加载出来的座位号匹配，设置此人员的座位id 然后跳出循环
        if (sNo == seatesArr[i].seatNumber) {
          selTreeNode.seatesId = seatesArr[i].id; //将座位id绑定到树节点的座位id
          selTreeNode.seatesNo = seatesArr[i].seatNumber; //将座位号绑定绑定到树节点的座位号
          //遍历所有座位
          $('.box div').each(function() {
            if ($(this).is('.drag')) {
              var snum = $(this).children().get(0).textContent; //座位顺序号
              var smobile = $(this).children().get(5).textContent; //座位上的人员手机号
              var sname = $(this).children().get(6).textContent; //座位上的人员姓名
              if (smobile == pMobile) {
                $(this).children().get(5).textContent = ""; //置空座位上的人员id
                $(this).children().get(6).textContent = ""; //置空座位数的人员姓名
              }
              if (sNo == snum) {
                $(this).children().get(5).textContent = selTreeNode.umobile; //将树节点的人员id绑定到座位 用手机号代替人员id
                $(this).children().get(6).textContent = selTreeNode.name; //将树节点的人员姓名绑定到座位
              }
            }
          });
          isPass = true;
          break;
        } else {
          isPass = false;
          continue;
        }
      }
    } else {
      layer.msg("找不到相应的座位");
      return;
    }
    //设置的座位号与所有的座位相匹配之后，没有找到座位id, 提示设置的座位号在所有的座位里面没有找到此座位
    if (isPass == false) {
      //			layer.msg("给 " + pName + " 设置的座位号 " + sNo + " 对应不到指定的座位");
      selTreeNode.seatesId = "";
      selTreeNode.seatesNo = "";
      $(this).val("");
      return;
    }
  }
  //当设置的座位号为空时，清空存放座位id的标签值
  else {
    selTreeNode.seatesId = "";
    selTreeNode.seatesNo = "";

    //遍历所有座位
    $('.box div').each(function() {
      if ($(this).is('.drag')) {
        var sname = $(this).children().get(6).textContent; //座位上的人员姓名
        if (selTreeNode.name == sname) {
          //					$(this).children().get(5).textContent = ""; //置空座位上的人员id
          $(this).children().get(6).textContent = ""; //置空座位数的人员姓名
        }
      }
    });
  }
});

//获得焦点 弹出部门选择框
$("#deptName").on('click', function(e) {
  foucsInput = $(this);
  var selfHei = $("#deptWin").innerHeight();
  var topHei = foucsInput.offset().top - selfHei; //当前点击的元素距离父容器panel顶部的距离
  $("#deptWin").css("width", $(this).innerWidth());
  //如果弹出框距父容器顶部的距离小于弹出框的高度,就把弹出框显示在标签下方，反之显示在上方
  if (topHei < selfHei) {
    $("#deptWin").css("top", $(this).offset().top + 50);
  } else {
    $("#deptWin").css("top", $(this).offset().top - selfHei - 5);
  }
  $("#deptWin").css("left", $(this).offset().left - $("#deptWin").innerWidth() + $(this).innerWidth() + 1);
  $('#deptWin').css("display", "block");
  loadDeptInfo();
  //阻止事件穿透
  e.stopPropagation();
});

var treeInfoArr = new Array();
//加载组织部门数据
function loadDeptInfo() {

  //调用查询接口
  axios({
    method: "post",
    url: server.local_path + "meetMeeting/officeTree",
    headers: JSON.parse(header)
  }).then(function(response) {
    var dataArr = response.data.data;
    treeInfoArr = new Array();
    newTreeNodeList(dataArr);

  }).catch(function(error) {
    layer.msg(error.response.data.message);
  });
}

//整理树形结构层级
function newTreeNodeList(nodeData) {
  for (var i = 0; i < nodeData.length; i++) {
    var parentNode = nodeData[i];
    var parentObj = new Object();
    parentObj.id = parentNode.id;
    parentObj.name = parentNode.name;
    parentObj.pId = parentNode.parentId;
    treeInfoArr.push(parentObj);
    if (nodeData[i].children.length > 0) {
      newTreeNodeList(nodeData[i].children);
    }
  }
  //创建树
  createDeptTree(treeInfoArr);
}

var TimeFn = null;

//创建组织部门树
function createDeptTree(treeData) {
  var setting = {
    view: {
      showLine: false,
      showIcon: false
    },
    data: {
      simpleData: {
        enable: true
      }
    },
    check: {
      enable: false
    },
    callback: {
      onClick: onclick,
      onDbClick: onDbClick
    },
  };

  function onclick(event, treeId, treeNode) {
    clearTimeout(TimeFn);
    //执行延时
    TimeFn = setTimeout(function() {

      selDeptId = treeNode.id;
      selDeptName = treeNode.name;

      $('#deptId').val(selDeptId);
      $('#deptName').val(selDeptName);

      $('#deptWin').css("display", "none");
    }, 300);
  }

  function onDbClick(event, treeId, treeNode) {
    clearTimeout(TimeFn);
  }

  var zNodes = treeData;

  $(document).ready(function() {
    $.fn.zTree.init($("#treeDemo"), setting, zNodes);
    var treeObj = $.fn.zTree.getZTreeObj("treeDemo");

    var node = treeObj.getNodeByParam("id", selDeptId);
    treeObj.cancelSelectedNode(); //先取消所有的选中状态
    treeObj.selectNode(node, true); //将指定ID的节点选中
    treeObj.expandNode(node, true, false); //将指定ID节点展开
  });
}

//点击弹出框内，不作任何操作
$("#deptWin").click(function(e) {
  e.stopPropagation();
});

//失去焦点
$("body").on("click", function() {
  $('#deptWin').css("display", "none");
});

//监听滚动事件
var scroH = 0; //滚动高度
var scroW = 0; //滚动宽度
$(".box").on("scroll", function() {
  scroH = $(".box").scrollTop(); //滚动高度
  scroW = $(".box").scrollLeft(); //滚动宽度
});

//重置点击事件
$("#reset").on("click", function() {
  $("#personName").val("");
  $("#phone").val("");
  $("#deptName").val("");
  $("#deptId").val("");
  $("#circles").val("");
  $("#pTypeSelect option:first").prop("selected", 'selected');
  selDeptId = "";
  selDeptName = "";
});

//在排座容器内用鼠标划出一块区域，并选中区域内的座位
function boxOnMouse() {
  var selList = [];
  var fileNodes = document.getElementsByTagName("div");
  for (var i = 0; i < fileNodes.length; i++) {
    if (fileNodes[i].className.indexOf("drag") != -1) {
      // fileNodes[i].className = "drag selected";
      selList.push(fileNodes[i]);
    }
  }
  var isSelect = true;
  var evt = window.event || arguments[0];
  var startX = (evt.x || evt.clientX);
  var startY = (evt.y || evt.clientY);
  var selDiv = document.createElement("div");
  selDiv.style.cssText =
    "position:absolute;width:0px;height:0px;font-size:0px;margin:0px;padding:0px;border:1px dashed #0099FF;background-color:#C3D5ED;z-index:1000;filter:alpha(opacity:60);opacity:0.6;display:none;";
  selDiv.id = "selectDiv";
  document.body.appendChild(selDiv);
  selDiv.style.left = startX + scroW + "px";
  selDiv.style.top = startY + scroH + "px";
  var _x = null;
  var _y = null;
  clearEventBubble(evt);
  document.onmousemove = function() {
    evt = window.event || arguments[0];
    if (isSelect) {
      if (selDiv.style.display == "none") {
        selDiv.style.display = "";
      }
      _x = (evt.x || evt.clientX);
      _y = (evt.y || evt.clientY);
      selDiv.style.left = Math.min(_x, startX) + "px";
      selDiv.style.top = Math.min(_y, startY) + "px";
      selDiv.style.width = Math.abs(_x - startX) + "px";
      selDiv.style.height = Math.abs(_y - startY) + "px";
      // ---------------- 关键算法 ---------------------
      var _l = selDiv.offsetLeft + scroW,
        _t = selDiv.offsetTop + scroH - 85;
      var _w = selDiv.offsetWidth,
        _h = selDiv.offsetHeight;
      for (var i = 0; i < selList.length; i++) {
        var sl = selList[i].offsetWidth + selList[i].offsetLeft;
        var st = selList[i].offsetHeight + selList[i].offsetTop;
        if (sl > _l && st > _t && selList[i].offsetLeft < _l + _w && selList[i].offsetTop < _t + _h) {
          if (selList[i].className.indexOf("selected") == -1) {
            selList[i].className = selList[i].className + " selected";
          }
        } else {
          if (selList[i].className.indexOf("selected") != -1) {
            selList[i].className = "drag";
          }
        }
      }
    }
    clearEventBubble(evt);
  }
  document.onmouseup = function() {
    isSelect = false;
    if (selDiv) {
      document.body.removeChild(selDiv);
      showSelDiv(selList);
    }
    selList = null, _x = null, _y = null, selDiv = null, startX = null, startY = null, evt = null;
  }
}

function clearEventBubble(evt) {
  if (evt.stopPropagation)
    evt.stopPropagation();
  else
    evt.cancelBubble = true;
  if (evt.preventDefault)
    evt.preventDefault();
  else
    evt.returnValue = false;
}

function showSelDiv(arr) {
  selSeatId = new Array();
  for (var i = 0; i < arr.length; i++) {
    if (arr[i].className.indexOf("selected") != -1) {
      if ($(arr[i]).is(".drag")) {
        var selItem = $(arr[i]).children().get(0).innerHTML;
        selSeatId.push(selItem);
      }
    }
  }
}

//以下保持按Ctrl时候能多选
var keepCtrl = false;
$(document).bind("keydown", function(e) {
  if (e.keyCode = 17) {
    keepCtrl = true;
  }
});

$(document).bind("keyup", function(e) {
  if (e.keyCode = 17) {
    keepCtrl = false;
  }
});

//座位点击事件
function seatesClick(seate) {
  //如果没有按下ctrl键
  if (!keepCtrl) {
    selSets = seate;
    $('.box div').each(function() {
      $(this).removeClass("selected ctrlSel");
    });
    $(seate).addClass("selected");
    selSeatId = new Array();
    var selItem = $(seate).children().get(0).innerHTML;
    selSeatId.push(selItem);
    // selSeatesInfo();
  } else {
    selSets = undefined;
    $(seate).addClass("selected ctrlSel");
    selSeatId = new Array();
    $('.box div').each(function() {
      if ($(this).is('.selected')) {
        $(this).addClass("ctrlSel");
        var selItem = $(this).children().get(0).innerHTML;
        selSeatId.push(selItem);
      } else
        $(this).removeClass("selected ctrlSel");
    });
  }
}

function unique(arr) {
  const res = new Map()
  return arr.filter((arr) => !res.has(arr) && res.set(arr, 1))
}

//下载模版  1 报名人员导入   0 邀请人员导入
function downTemplateByType(sign) {
  axios({
    method: 'post',
    url: server.local_path + "meetSeatPlan/importemplate?meetId="+ mId +"personType=" + sign,
    headers: JSON.parse(header),
    responseType: 'blob'
  }).then(res => {
    const content = res.data
    const blob = new Blob([content])
    const fileName = sign == 1 ? "报名人员导入模版.xlsx" : "邀请人员导入模版.xlsx";
    if ('download' in document.createElement('a')) { // 非IE下载
      const elink = document.createElement('a')
      elink.download = fileName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    } else { // IE10+下载
      navigator.msSaveBlob(blob, fileName)
    }
  })
  // let userId = JSON.parse(sessionStorage.getItem('user')).id
  // axios({
  //   method: "get",
  //   url: server.local_path + "meetMeeting/listGroupPeople?meetId=" + mId + "&&pageNo=1&pageSize=1000",
  //   headers: JSON.parse(header),
  // })
  // .then(function(response) {
  //   var res = response.data;
  //   if (res.data.length > 0) {
  //     $(".tempBody").empty();
  //     var groupList = [] // 人员分组
  //     var checkGroupList = [] // 选中的人员分组
  //     var userIdList = [] // 小组秘书
  //     var ckboxDisabled = false //人员分组是否可选
  //     for (let i = 0; i < res.data.length; i++) {
  //       groupList.push({
  //         name: res.data[i].groupName,
  //         value: res.data[i].groupId
  //       })
  //       $(".tempBody").append('<span>' +
  //         '<input type="checkbox" name="temp" value="' + res.data[i].groupId + '" id="' + res.data[i].groupId +
  //         '"/>' +
  //         '<label for="' + res.data[i].groupId + '" style="position: relative; top: 3px; margin-left: 10px;">' +
  //         res.data[i].groupName + '</label>' +
  //         '</span>');
  //       if (res.data[i].po.length > 0) {
  //         for (let j = 0; j < res.data[i].po.length; j++) {
  //           userIdList.push(res.data[i].po[j].id)
  //           // 登录人的id如果在小组秘书范围内，设置复选框选中
  //           if (userId === res.data[i].po[j].id) {
  //             checkGroupList.push(res.data[i].groupId)
  //             setCheckboxChecked(res.data[i].groupId)
  //             break
  //           }
  //         }
  //       }
  //     }
  //     $(".tempBody").append('<span>' +
  //       '<input type="checkbox" name="temp" value="无分组" id="无分组"/>' +
  //       '<label for="无分组" style="position: relative; top: 3px; margin-left: 10px;">无分组</label>' +
  //       '</span>');
  //     // 数组去重 登录人的id如果在小组秘书范围内，设置复选框不可选，如果不在小组秘书范围内，设置复选框可选
  //     userIdList = unique(userIdList)
  //     if (userIdList.length > 0) {
  //       for (let u = 0; u < userIdList.length; u++) {
  //         if (userIdList[u] === userId) {
  //           setCheckboxDisabled(userIdList[u])
  //           ckboxDisabled = true
  //           break
  //         }
  //       }
  //       // 登录人的id如果不在小组秘书范围内，复选框全部选中
  //       if (ckboxDisabled === false) {
  //         for (let g = 0; g < groupList.length; g++) {
  //           checkGroupList.push(groupList[g].value)
  //           setCheckboxChecked('')
  //         }
  //       }
  //     }
  //   }
  //   $("#tempModal").modal("show")
  // }).catch(function(error) {

  // });
}

//根据登录人的id是否在小组秘书范围内，设置复选框选中
function setCheckboxChecked(groupId) {
  $('.tempBody span').each(function() {
    var gId = $(this).children().get(0).value;
    if (groupId !== '') {
      if (gId === groupId) {
        $($(this).children().get(0)).prop("checked", true)
      }
    } else {
      $($(this).children().get(0)).prop("checked", true)
    }
  });
}

//根据登录人的id是否在小组秘书范围内，设置复选框可选
function setCheckboxDisabled(groupId) {
  $('.tempBody span').each(function() {
    var gId = $(this).children().get(0).value;
    if (gId === groupId) {
      $($(this).children().get(0)).prop("disabled", true)
    }
  });
}

$("#downBtn").on("click", function() {
  var groupIds = ''
  $.each($('.tempBody input:checkbox:checked'), function() {
    groupIds += $(this).val() + ','
  });
  groupIds = groupIds.substring(0, groupIds.length - 1)
  axios({
    method: 'post',
    url: server.local_path + "meetroomarrange/importemplate?meetId=" + mId + '&groupId=' + groupIds,
    headers: JSON.parse(header),
    responseType: 'blob'
  }).then(res => {
    const content = res.data
    const blob = new Blob([content])
    const fileName = "按房间号导入模版.xlsx";
    if ('download' in document.createElement('a')) { // 非IE下载
      const elink = document.createElement('a')
      elink.download = fileName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    } else { // IE10+下载
      navigator.msSaveBlob(blob, fileName)
    }
  })
});

//监听滚动事件
var scroH = 0; //滚动高度
var scroW = 0; //滚动宽度
$(".box").on("scroll", function() {
  scroH = $(".box").scrollTop(); //滚动高度
  scroW = $(".box").scrollLeft(); //滚动宽度
});

//根据座位所属区域，设置座位颜色
function setColorByAreaType() {
  $('.box div').each(function() {
    if ($(this).is('.drag')) {
      var areaType = $(this).children().get(1).textContent; //座位类型
      for (var i = 0; i < areaArr.length; i++) {
        if (areaType == areaArr[i].id) {
          $(this).css("background", areaArr[i].colour);
          break;
        }
      }
    }
  });
}

//显示座位号/顺序号
function showSeatNoOrSort(item) {
  var value = $.trim($(item).text());
  //显示顺序号
  if (value == "顺序号") {
    $("#showBtn").text("显示顺序号").append("&nbsp;<img src='img/minus.png'>");
  }
  //显示座位号
  else {
    $("#showBtn").text("显示座位号").append("&nbsp;<img src='img/minus.png'>");
  }
}

//生成图片
$("#makePic").on("click", function() {
  //判断排座容器有没有出现滚动条，根据情况调整生成图片的宽高
  var bx = $(".box");
  if (bx[0].scrollHeight > bx[0].clientHeight || bx[0].offsetHeight > bx[0].clientHeight) { //出现滚动条
    //获取节点高度，后面为克隆节点设置高度。
    var h = Number(maxY) + 50;
    var w = Number(maxX) + 300;
  } else { //未出现滚动条
    //获取节点高度，后面为克隆节点设置高度。
    var h = $(".box").height();
    var w = $(".box").width();
  }

  //克隆节点，默认为false，不复制方法属性，为true是全部复制。
  var cloneDom = $(".box").clone(true);
  cloneDom.width(w + "px");
  cloneDom.height(h + "px");

  //设置克隆节点的css属性，因为之前的层级为0，我们只需要比被克隆的节点层级低即可。
  cloneDom.css({
    "background-color": "white",
    "position": "absolute",
    "top": "0px",
    "left": "120px",
    "z-index": "-2"
    //		"height": h,
    //		"width": w
  });
  //将克隆节点动态追加到body后面。
  $("#cloneDiv").append(cloneDom);
  //插件生成base64img图片。
  html2canvas(cloneDom, {
    //Whether to allow cross-origin images to taint the canvas
    allowTaint: true,
    //Whether to test each image if it taints the canvas before drawing them
    taintTest: false,
    onrendered: function(canvas) {
      //添加属性
      canvas.setAttribute('id', 'thecanvas');
      //读取属性值
      // var value= canvas.getAttribute('id');
      document.getElementById('images').innerHTML = '';
      document.getElementById('images').appendChild(canvas);

      /*自动保存为png*/
      // 获取图片资源
      var oCanvas = document.getElementById("thecanvas");
      var img_data1 = Canvas2Image.saveAsPNG(oCanvas, true).getAttribute('src');
      var timeStamp = moment(new Date()).format("YYYYMMDDHHmmss");
      var selMname = $("#meetName").text();
      var picName = selMname + "-" + timeStamp + '.jpg'
      saveFile(img_data1, picName);
    }
  });
});

// 保存文件函数
var saveFile = function(data, filename) {
  var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
  save_link.href = data;
  save_link.download = filename;

  var event = document.createEvent('MouseEvents');
  event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
  save_link.dispatchEvent(event);
  //生成并下载图片后，将克隆的元素清空
  document.getElementById('cloneDiv').innerHTML = '';
};

//设置座位选中与非选中的操作 非选中设置为选中 选中的改变选中状态
function setSeatSelected() {
  $('.drag')
    .drag("init", function() {
      if ($(this).is('.selected'))
        return $('.selected');
    });
}
