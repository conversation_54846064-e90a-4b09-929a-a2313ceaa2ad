<template>
  <div class="committee-data-column-new">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="栏目名称"
                    class="form-input"
                    prop="name">
        <el-input placeholder="请输入栏目名称"
                  v-model.trim="form.name"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="上级栏目">
        <zy-select width="296"
                   node-key="id"
                   :props="{ label: 'name', children: 'children' }"
                   v-model="form.superior"
                   :data="menu"
                   placeholder="请选择上级栏目"></zy-select>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-input"
                    prop="sort">
        <el-input-number placeholder="请输入排序"
                         v-model="form.sort"
                         clearable
                         @input.native="onInput0_999"
                         @change="toInteger"
                         :min='1'>
        </el-input-number>
      </el-form-item>
      <el-form-item label="是否置顶"
                    class="form-input">
        <el-radio-group v-model="form.isTop">
          <el-radio label="1">置顶</el-radio>
          <el-radio label="0">不置顶</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否APP显示"
                    class="form-input">
        <el-radio-group v-model="form.isPushApp">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'committeeDataColumnNew',
  data () {
    return {
      menu: [],
      form: {
        name: '',
        superior: '',
        sort: '',
        isTop: '1',
        isPushApp: '1'
      },
      rules: {
        name: [
          { required: true, message: '请输入栏目名称', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入小于999的整数序号', trigger: 'blur' }
        ]
      }
    }
  },
  props: ['id'],
  mounted () {
    this.informationColumnTree()
    if (this.id) {
      this.informationColumnInfo()
    }
  },
  methods: {
    onInput0_999 (e) {
      this.$message.closeAll()
      if (e.target.value < 0 || e.target.value > 999) {
        this.$message.warning('只能输入[0-999]区间的整数,请您重新输入')
      }
      e.target.value = (e.target.value >= 0 && e.target.value <= 999 && e.target.value.match(/^\d{1,3}(\.\d*)?$/)[0]) || null
    },
    toInteger () {
      const reg = /^[0-9]+$/
      if (!reg.test(this.form.sort)) {
        this.$message.warning('只能输入整数排序,请您重新输入')
        this.$nextTick(() => {
          this.form.sort = parseInt(this.form.sort)
        })
      }
    },
    async informationColumnTree () {
      const res = await this.$api.appManagement.informationColumnTree({ module: 6 })
      var { data } = res
      this.menu = data
    },
    async informationColumnInfo () {
      const res = await this.$api.appManagement.informationColumnInfo(this.id)
      var { data: { parentId, sort, name, isTop, isPushApp } } = res
      this.form.superior = parentId
      this.form.name = name
      this.form.sort = sort
      this.form.isTop = isTop
      this.form.isPushApp = isPushApp
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.form.superior === '') {
            this.form.superior = 1
          }
          var url = '/zyinfostructure/add'
          if (this.id) {
            url = '/zyinfostructure/edit'
          }
          this.$api.systemSettings.generalAdd(url, {
            id: this.id,
            parentId: this.form.superior,
            name: this.form.name,
            sort: this.form.sort,
            isTop: this.form.isTop,
            isPushApp: this.form.isPushApp,
            module: 6
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
@import "./committee-data-column-new.scss";
</style>
