<template>
  <div class="associated-information">
    <div class="button-box-list">
      <el-button type="primary"
                 v-permissions="'auth:inforelation:add'"
                 @click="newData">新增</el-button>
      <el-button type="primary"
                 v-permissions="'auth:inforelation:dels'"
                 @click="deleteClick">删除</el-button>
    </div>
    <div class="information-mosaic-list scrollBar">
      <el-table :data="tableData"
                stripe
                border
                ref="table"
                style="width: 100%"
                @select="selected"
                @select-all="selectedAll">
        <el-table-column type="selection"
                         width="48">
        </el-table-column>
        <el-table-column label="标题"
                         prop="title"
                         show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button @click="details(scope.row)"
                       type="text">{{scope.row.title}}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="所属栏目"
                         width="120"
                         show-overflow-tooltip
                         prop="structureName">
        </el-table-column>
        <el-table-column label="资讯类型"
                         width="90"
                         prop="infoClass">
        </el-table-column>
        <el-table-column label="显示类型"
                         width="90"
                         prop="infoType">
        </el-table-column>
        <el-table-column label="来源"
                         width="120"
                         prop="source"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="发布人"
                         width="120"
                         prop="createBy">
        </el-table-column>
        <el-table-column label="发布时间"
                         width="160"
                         prop="publishDate">
        </el-table-column>
      </el-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>

    <zy-pop-up v-model="detailsShow"
               title="详情">
      <information-details :id="uid"></information-details>
    </zy-pop-up>

    <zy-pop-up v-model="show"
               title="新增关联">
      <associated-information-new :id="id"
                                  @newCallback="newCallback"></associated-information-new>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@mixins/tableData'
import associatedInformationNew from './associated-information-new/associated-information-new'
import informationDetails from '../../information-details/information-details'
export default {
  name: 'associatedInformation',
  data () {
    return {
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      mosaicId: '',
      show: false,
      uid: '',
      detailsShow: false
    }
  },
  mixins: [tableData],
  props: ['id'],
  components: {
    associatedInformationNew,
    informationDetails
  },
  mounted () {
    if (this.id) {
      this.associatedList()
    }
  },
  methods: {
    details (row) {
      this.uid = row.relateId
      this.detailsShow = true
    },
    async associatedList () {
      const res = await this.$api.appManagement.associatedList({
        detailId: this.id,
        pageNo: this.page,
        pageSize: this.pageSize
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    newData () {
      this.mosaicId = ''
      this.show = true
    },
    newCallback () {
      this.associatedList()
      this.show = false
    },
    howManyArticle (val) {
      this.associatedList()
    },
    whatPage (val) {
      this.associatedList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的组图, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.associatedDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async associatedDel (id) {
      const res = await this.$api.appManagement.associatedDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.associatedList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
@import "./associated-information.scss";
</style>
