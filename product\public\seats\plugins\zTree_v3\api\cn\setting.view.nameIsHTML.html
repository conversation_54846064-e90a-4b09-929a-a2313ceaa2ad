<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">setting.view.</span>nameIsHTML</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.core</span> 核心 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>设置 name 属性是否支持 HTML 脚本</p>
			<p class="highlight_red">如果允许 HTML 脚本，请根据自己的需求做校验，避免出现 js 注入等安全问题。</p>
			<p>默认值: false</p>
		</div>
	</div>
	<h3>Boolean 格式说明</h3>
	<div class="desc">
	<p> true / false 分别表示 支持 / 不支持 HTML 脚本</p>
	</div>
	<h3>setting 举例</h3>
	<h4>1. 设置 name 属性支持 HTML 脚本</h4>
	<pre xmlns=""><code>var setting = {
	view: {
		nameIsHTML: true
	}
};
var node = {"name":"&lt;font color='red'&gt;test&lt;/font&gt;"};
......</code></pre>
</div>
</div>