const meetingHome = () => import('@meeting/meetingHome/meetingHome')
const meetingType = () => import('@meeting/meetingType/meetingType') // 不同会议类型
const meetingAssistant = () => import('@meeting/meetingAssistant/meetingAssistant') // 会议小助手
const meetingTemplate = () => import('@meeting/meetingTemplate/meetingTemplate') // 会议模板
const noticeTemplate = () => import('@meeting/noticeTemplate/noticeTemplate') // 通知模板
const pcTemplate = () => import('@meeting/pcTemplate/pcTemplate') // pc模板
const phoneTemplate = () => import('@meeting/phoneTemplate/phoneTemplate') // phone模板
const phoneList = () => import('@meeting/phoneTemplate/phoneList') // phone模板
const signUpMng = () => import('@meeting/meetingRelated/signUpMng') // 报名管理
const meetingAll = () => import('@meeting/meetingAll/meetingAll')
const meetingAddIndex = () => import('@meeting/meetingAdd/meetingAddIndex')
const meetingAdd = () => import('@meeting/meetingAdd/meetingAdd')
const meetingAddUser = () => import('@meeting/meetingAdd/meetingAddUser')
const meetingAddMaterials = () => import('@meeting/meetingAdd/meetingAddMaterials')
const peopleGroup = () => import('@meeting/meetingRelated/peopleGroup') // 人员分组
const meetingRoom = () => import('@meeting/meetingRoomMng/meetingRoomMng') // 会议室管理
const meetingRoomSeat = () => import('@meeting/meetingRoomMng/seat/seat') // 会议室管理-排座
const placeMng = () => import('@meeting/placeMng/placeMng') // 住地管理
const placeMngRoom = () => import('@meeting/placeMng/room/room') // 住地管理-房间
const meetMaterial = () => import('@meeting/meetingRelated/meetMaterial') // 会议材料
const meetUsers = () => import('@meeting/meetingRelated/meetUsers') // 会议参会人员
const meetInfoAdd = () => import('@meeting/meetingRelated/meetInfoAdd') // 会议基本信息
const readRecords = () => import('@meeting/meetingRelated/readRecords') // 阅读记录
const sendRecords = () => import('@meeting/meetingRelated/sendRecords') // 发送记录
const sendInform = () => import('@meeting/meetingRelated/sendInform') // 发送通知
const signInMng = () => import('@meeting/meetingRelated/signInMng') // 签到管理
const meeetingReceipt = () => import('@meeting/meetingRelated/receipt') // 会议回执
const personnelMng = () => import('@meeting/personnelMng/personnelMng') // 系统外人员管理
const transactionMng = () => import('@meeting/transactionMng') // 事务安排字段管理
const transactionSchedule = () => import('@meeting/meetingRelated/transactionSchedule') // 事务安排
const seatArrangements = () => import('@meeting/meetingRelated/meetingSeat') // 座位安排
const roomArrangements = () => import('@meeting/meetingRelated/meetingRoom') // 住宿安排
const meetingRoomDetail = () => import('@meeting/meetingRelated/meetingRoomDetail') // 住宿安排信息
const childMeeting = () => import('@meeting/meetingRelated/childMeeting') // 子级会议
const message = () => import('@meeting/meetingHome/detail/message') // 首页的消息详情
const todo = () => import('@meeting/meetingHome/detail/todo') // 首页的待办事项详情
const meeting = [
  {
    path: '/meetingAll',
    name: 'meetingAll',
    component: meetingAll
  },
  {
    path: '/meetingAddIndex',
    name: 'meetingAddIndex',
    component: meetingAddIndex
  },
  {
    path: '/meetingAdd',
    name: 'meetingAdd',
    component: meetingAdd
  },
  {
    path: '/meetingAddUser',
    name: 'meetingAddUser',
    component: meetingAddUser
  },
  {
    path: '/meetingAddMaterials',
    name: 'meetingAddMaterials',
    component: meetingAddMaterials
  },
  {
    path: '/meetingHome',
    name: 'meetingHome',
    component: meetingHome
  },
  {
    path: '/meetingType',
    name: 'meetingType',
    component: meetingType
  },
  {
    path: '/meetingAssistant',
    name: 'meetingAssistant',
    component: meetingAssistant
  },
  {
    path: '/meetingTemplate',
    name: 'meetingTemplate',
    component: meetingTemplate
  },
  {
    path: '/noticeTemplate',
    name: 'noticeTemplate',
    component: noticeTemplate
  },
  {
    path: '/pcTemplate',
    name: 'pcTemplate',
    component: pcTemplate
  },
  {
    path: '/phoneTemplate',
    name: 'phoneTemplate',
    component: phoneTemplate
  },
  {
    path: '/phoneList',
    name: 'phoneList',
    component: phoneList
  },
  {
    path: '/signUpMng',
    name: 'signUpMng',
    component: signUpMng
  },
  {
    path: '/peopleGroup',
    name: 'peopleGroup',
    component: peopleGroup
  },
  {
    path: '/meetInfoAdd',
    name: 'meetInfoAdd',
    component: meetInfoAdd
  },
  {
    path: '/meetUsers',
    name: 'meetUsers',
    component: meetUsers
  },
  {
    path: '/meetMaterial',
    name: 'meetMaterial',
    component: meetMaterial
  },
  {
    path: '/readRecords',
    name: 'readRecords',
    component: readRecords
  },
  {
    path: '/sendRecords',
    name: 'sendRecords',
    component: sendRecords
  },
  {
    path: '/sendInform',
    name: 'sendInform',
    component: sendInform
  },
  {
    path: '/meetingRoom',
    name: 'meetingRoom',
    component: meetingRoom
  }, {
    path: '/meetingRoomSeat',
    name: 'meetingRoomSeat',
    component: meetingRoomSeat
  },
  {
    path: '/placeMng',
    name: 'placeMng',
    component: placeMng
  },
  {
    path: '/placeMngRoom',
    name: 'placeMngRoom',
    component: placeMngRoom
  },
  {
    path: '/signInMng',
    name: 'signInMng',
    component: signInMng
  },
  {
    path: '/meeetingReceipt',
    name: 'meeetingReceipt',
    component: meeetingReceipt
  },
  {
    path: '/personnelMng',
    name: 'personnelMng',
    component: personnelMng
  },
  {
    path: '/transactionMng',
    name: 'transactionMng',
    component: transactionMng
  },
  {
    path: '/transactionSchedule',
    name: 'transactionSchedule',
    component: transactionSchedule
  },
  {
    path: '/seatArrangements',
    name: 'seatArrangements',
    component: seatArrangements
  },
  {
    path: '/roomArrangements',
    name: 'roomArrangements',
    component: roomArrangements
  },
  {
    path: '/meetingRoomDetail',
    name: 'meetingRoomDetail',
    component: meetingRoomDetail
  },
  {
    path: '/childMeeting',
    name: 'childMeeting',
    component: childMeeting
  },
  {
    path: '/message',
    name: 'message',
    component: message
  },
  {
    path: '/todo',
    name: 'todo',
    component: todo
  },
  {
    path: '/newMeetingAdd',
    name: 'newMeetingAdd',
    component: () => import('@newMeeting/newMeetingAdd')
  },
  {
    path: '/newMeetingYearAll',
    name: 'newMeetingYearAll',
    component: () => import('@newMeeting/newMeetingYearAll')
  },
  {
    path: '/newMeetingAll',
    name: 'newMeetingAll',
    component: () => import('@newMeeting/newMeetingAll')
  },
  {
    path: '/meetingRoomManagement',
    name: 'meetingRoomManagement',
    component: () => import('@newMeeting/meetingRoomManagement')
  },
  {
    path: '/businessArrangementManagement',
    name: 'businessArrangementManagement',
    component: () => import('@newMeeting/businessArrangementManagement')
  },
  {
    path: '/newMeetingAddForm',
    name: 'newMeetingAdd',
    component: () => import('@newMeeting/newMeetingAdd/add/add')
  },
  {
    path: '/newMeetingYearAllForm',
    name: 'newMeetingYearAllForm',
    component: () => import('@newMeeting/newMeetingYearAll/add/add')
  },
  {
    path: '/newMeetingYearAllFile',
    name: 'newMeetingYearAllFile',
    component: () => import('@newMeeting/newMeetingYearAll/widget/file-management')
  },
  {
    path: '/newMeetingYearAllAttendance',
    name: 'newMeetingYearAllAttendance',
    component: () => import('@newMeeting/newMeetingYearAll/widget/attendance')
  },
  {
    path: '/newMeetingLeave',
    name: 'newMeetingLeave',
    component: () => import('@newMeeting/newMeetingAll/leave')
  },
  {
    path: '/faceSignInMeetingAssociation',
    name: 'faceSignInMeetingAssociation',
    component: () => import('@faceSignIn/faceSignInMeetingAssociation/')
  },
  {
    path: '/faceSignInBillboardManagement',
    name: 'faceSignInBillboardManagement',
    component: () => import('@faceSignIn/faceSignInBillboardManagement/')
  },
  {
    path: '/faceSignInEquipmentManagement',
    name: 'faceSignInEquipmentManagement',
    component: () => import('@faceSignIn/faceSignInEquipmentManagement/')
  },
  {
    path: '/faceSignInRecordQuery',
    name: 'faceSignInRecordQuery',
    component: () => import('@faceSignIn/faceSignInRecordQuery/')
  },
  {
    path: '/portraitManagement',
    name: 'portraitManagement',
    component: () => import('@faceSignIn/faceSignInEquipmentManagement/portraitManagement')
  },
  {
    path: '/cameraManagement',
    name: 'cameraManagement',
    component: () => import('@faceSignIn/faceSignInEquipmentManagement/cameraManagement')
  },
  {
    path: '/portraitManagementList',
    name: 'portraitManagementList',
    component: () => import('@faceSignIn/faceSignInEquipmentManagement/portraitManagementList')
  },
  {
    path: '/signInWithoutFeeling',
    name: 'signInWithoutFeeling',
    component: () => import('@faceSignIn/board/signInWithoutFeeling')
  },
  {
    path: '/Openingceremony',
    name: 'Openingceremony',
    component: () => import('@faceSignIn/board/Openingceremony')
  },
  {
    path: '/Conferencedispatch',
    name: 'Conferencedispatch',
    component: () => import('@faceSignIn/board/Conferencedispatch')
  }
]

export default meeting
