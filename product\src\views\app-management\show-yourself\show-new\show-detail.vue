<template>
  <div class="said-new">
    <el-form :model="form"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="创建人"
                    class="form-title"
                    prop="publishName">
        <div class="form-user-box">
          <div>
            {{form.publishName}}
          </div>
        </div>
      </el-form-item>
      <el-form-item label="时间"
                    class="form-title"
                    prop="publishDate">
        <div>
          {{form.publishDate}}
        </div>
      </el-form-item>
      <el-form-item label="内容"
                    class="form-title"
                    prop="content">
        <div>
          {{form.content}}
        </div>
      </el-form-item>

      <el-form-item v-if="form.attachmentList.length!==0"
                    style="width:100%"
                    label="附件"
                    class="form-icon">
        <div v-for="(item, index) in form.attachmentList"
             :key="index"
             class="form-icon-uploader">
          <el-image :src="item.fullUrl"
                    class="user-img"
                    :preview-src-list="form.srcList">
          </el-image>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'showDetail',
  data () {
    return {
      form: {
        publishBy: '',
        publishName: '',
        publishDate: '',
        title: '',
        content: '',
        attachmentList: [],
        srcList: []
      }
    }
  },
  props: ['id'],
  mounted () {
    if (this.id) {
      this.showyourselfInfo()
    }
  },
  watch: {

  },
  methods: {
    async showyourselfInfo () {
      const res = await this.$api.appManagement.showyourselfInfo(this.id)
      var { data: { publishBy, publishName, publishDate, title, content, attachmentList } } = res
      this.form.publishBy = publishBy
      this.form.publishName = publishName
      this.form.publishDate = publishDate
      this.form.title = title
      this.form.content = content
      this.form.attachmentList = attachmentList || []
      if (attachmentList.length !== 0) {
        attachmentList.forEach(element => {
          console.log(element.fullUrl)
          this.form.srcList.push(element.fullUrl)
          console.log(this.form.srcList)
        })
      }
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
@import "./show-new.scss";
</style>
