import Vue from 'vue'
import {
  Autocomplete,
  Button,
  Form,
  FormItem,
  Input,
  Popover,
  Checkbox,
  Carousel,
  Progress,
  CarouselItem,
  Breadcrumb,
  BreadcrumbItem,
  Menu,
  Submenu,
  MenuItem,
  InfiniteScroll,
  Loading,
  MessageBox,
  Message,
  Container,
  Header,
  Aside,
  Main,
  Table,
  TableColumn,
  Select,
  Option,
  OptionGroup,
  RadioGroup,
  RadioButton,
  Image,
  Tree,
  Upload,
  Pagination,
  Timeline,
  TimelineItem,
  Badge,
  CheckboxGroup,
  Radio,
  ColorPicker,
  DatePicker,
  Collapse,
  CollapseItem,
  Col,
  Row,
  TabPane,
  Dialog,
  Tabs,
  Tag,
  Switch,
  InputNumber,
  Link,
  Calendar,
  Cascader,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  Tooltip,
  Alert,
  Drawer,
  empty
} from 'element-ui'
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'
import Scrollbar from 'element-ui/lib/scrollbar'
Vue.component(CollapseTransition.name, CollapseTransition)

const components = [empty, Autocomplete, Button, Form, FormItem, Input, Popover, Checkbox, InfiniteScroll, Carousel, Progress, Breadcrumb, BreadcrumbItem, CarouselItem, Menu, Submenu, MenuItem, CheckboxGroup, Container, Header, Aside, Main, Scrollbar, Table, TableColumn, Select, Option, OptionGroup, RadioGroup, RadioButton, Image, Tree, Upload, Pagination, Timeline, TimelineItem, Badge, Radio, ColorPicker, DatePicker, Collapse, CollapseItem, Col, Row, TabPane, Dialog, Tabs, Tag, Switch, InputNumber, Link, Calendar, Cascader, DropdownMenu, Dropdown, DropdownItem, Tooltip, Alert, Drawer]
components.forEach(v => Vue.use(v))
Vue.use(Loading.directive)
Vue.prototype.$loading = Loading.service
Vue.prototype.$msgbox = MessageBox
Vue.prototype.$alert = MessageBox.alert
Vue.prototype.$confirm = MessageBox.confirm
// Vue.prototype.$prompt = MessageBox.prompt
// Vue.prototype.$notify = Notification
Vue.prototype.$message = Message
