const membersDuty = [
  {
    path: '/memberGuestList',
    name: 'memberGuestList',
    component: () => import(/** 代表会客厅管理 */ '@membersDuty/memberGuestList/memberGuestList')
  },
  {
    path: '/memberGuestLetter',
    name: 'memberGuestLetter',
    component: () => import(/** 代表会客厅来信 */ '@membersDuty/memberGuestLetter/memberGuestLetter')
  },
  {
    path: '/memberGuestReply',
    name: 'memberGuestReply',
    component: () => import(/** 代表会客厅评论回复 */ '@membersDuty/memberGuestList/memberGuestReply')
  },
  {
    path: '/memberGuestLetterReply',
    name: 'memberGuestLetterReply',
    component: () => import(/** 代表会客厅评论回复 */ '@membersDuty/memberGuestLetter/memberGuestReply')
  }
]

export default membersDuty
