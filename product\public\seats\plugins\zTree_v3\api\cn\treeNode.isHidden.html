<div class="apiDetail">
<div>
	<h2><span>Bo<PERSON>an</span><span class="path">treeNode.</span>isHidden</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.exhide</span> 扩展 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>判断 treeNode 节点是否被隐藏。</p>
			<p class="highlight_red">1、初始化 zTree 时，如果节点设置 isHidden = true，会被自动隐藏</p>
			<p class="highlight_red">2、请勿对已加载的节点修改此属性，隐藏 / 显示 请使用 hideNode() / hideNodes() / showNode() / showNodes() 方法</p>
		</div>
	</div>
	<h3>Boolean 格式说明</h3>
	<div class="desc">
	<p> true 表示被隐藏</p>
	<p> false 表示被显示</p>
	</div>
	<h3>treeNode 举例</h3>
	<h4>1. 查看第一个根节点是否被隐藏</h4>
	<pre xmlns=""><code>var treeObj = $.fn.zTree.getZTreeObj("tree");
var sNodes = treeObj.getNodes();
if (sNodes.length > 0) {
	var isHidden = sNodes[0].isHidden;
}
</code></pre>
</div>
</div>