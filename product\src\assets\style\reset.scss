html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
main,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  // font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

// @font-face {
//   font-family: FZBIAOYSFW;
//   src: url("../font/FZBIAOYSFW.TTF");
// }

// @font-face {
//   font-family: FZBIAOYSJW;
//   src: url("../font/FZBIAOYSJW.TTF");
// }

// div,
// span,
// p,
// button,
// input {
//   // font-family: FZBIAOYSJW;
//   font-family: Microsoft YaHei;
// }
div,
p,
button,
input,
ol,
ul,
li {
  font-size: $textSize16;
}
.el-breadcrumb,
.el-button,
li {
  span {
    font-size: $textSize14;
  }
}
.el-input {
  font-size: $textSize14;
}
tr,
th,
td {
  font-size: $textSize14;
  span,
  div {
    font-size: $textSize14;
  }
}
div,
p,
button,
input,
ol,
ul,
li {
  font-size: $textSize16;
}
.el-breadcrumb,
.el-button,
li {
  span {
    font-size: $textSize14;
  }
}
.el-input {
  font-size: $textSize14;
}
tr,
th,
td {
  font-size: $textSize14;
  span,
  div {
    font-size: $textSize14;
  }
}

html,
body {
  // font-family: FZBIAOYSJW;
  font-family: Microsoft YaHei;
  // -moz-user-select: none;
  // -khtml-user-select: none;
  // user-select: none;
}

.button-box {
  min-height: 64px;
  display: flex;
  flex-wrap: wrap;
  padding-left: 24px;
  padding-top: 12px;

  .el-button {
    height: 40px;
    line-height: 40px;
    padding: 0 16px;
    margin-right: 12px;
    margin-bottom: 12px;

    .el-button [class*='el-icon-'] + span {
      margin-left: 9px;
    }
  }

  .el-button + .el-button {
    margin-left: 0;
  }
}

.paging_box {
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.newForm {
  .form-candidates {
    width: 100%;

    .el-form-item__content {
      height: auto;
      line-height: auto;

      .candidates-button {
        height: 100%;
        display: flex;
        justify-content: space-between;

        .el-button {
          height: 44px;
          width: 104px;
        }

        .candidates-number {
          height: 44px;
          line-height: 44px;
          display: flex;
          align-items: center;

          .number {
            font-size: $textSize14;
          }

          .all-candidates {
            margin-left: 6px;
            font-size: $textSize12;
            color: #8c8c8c;

            .el-icon-arrow-down {
              margin-left: 4px;
              transform: rotate(0deg);
              transition-duration: 0.2s;
            }
          }

          .all-candidates-active {
            color: #0473de;

            .el-icon-arrow-down {
              transform: rotate(-180deg);
              transition-duration: 0.2s;
            }
          }
        }
      }
    }
  }

  .candidates {
    width: calc(100% - 24px);
    // margin-left: 24px;
    padding: 8px;
    border: 1px solid #d9d9d9;
    box-shadow: 0px -1px 2px 0px rgba(0, 0, 0, 0.15);
    border-radius: 2px;
    margin-bottom: 16px;
    display: flex;
    flex-wrap: wrap;
    align-content: flex-start;
    height: 116px;
    max-height: 116px;
    overflow: hidden;

    .candidates-box {
      width: 94px;
      min-width: 94px;
      max-width: 94px;
      height: 28px;
      line-height: 28px;
      padding: 0 9px;
      background: rgba(4, 115, 222, 0.1);
      border-radius: 2px;
      display: flex;
      align-items: center;
      margin-right: 8px;
      margin-bottom: 8px;

      .candidates-text {
        font-size: $textSize14;
        width: 100%;
        text-align: center;
      }

      .candidates-close {
        margin-left: 6px;
        width: 14px;
        min-width: 14px;
        height: 14px;
        background: url('../socialImg/close.png');
        background-size: 100% 100%;
      }
    }
  }

  .candidates-active {
    box-sizing: border-box;
    overflow: hidden;
    height: auto;
    max-height: 100%;
    transition: max-height 1s;
  }

  .el-form-item {
    .el-form-item__label {
      padding: 12px 0;
      padding-top: 6px;
      line-height: 24px;
      color: #222222;
    }
  }

  .form-title {
    width: 100%;

    .el-input {
      width: 100%;
    }
  }

  .form-input {
    width: 296px;

    .el-input {
      width: 296px;
    }

    .el-input-number {
      width: 100%;
    }
  }

  .zy-tree-select {
    width: 296px;
  }

  .form-multiple-input {
    width: 100%;

    .el-input {
      width: 296px;
    }

    .el-input + .el-input {
      margin-left: 24px;
    }
  }

  .form-multiple {
    width: 100%;

    .el-input {
      width: 296px;
      margin-left: 30px;
    }
  }

  .form-ue {
    width: 100%;

    .el-form-item__content {
      line-height: normal;
    }
  }

  .form-upload {
    width: 100%;

    .form-upload-demo {
      .el-upload {
        width: 100%;

        .el-upload-dragger {
          height: 79px;
          width: 100%;
          max-width: 952px;
          background-color: #e6e5e8;

          .el-upload__text {
            padding-top: 22px;
            font-size: $textSize14;
            line-height: 22px;
          }

          .el-upload__tip {
            font-size: $textSize12;
            line-height: 20px;
            color: #6e6e6e;
            margin-top: 0;
          }
        }
      }
    }
  }

  .is-disabled .el-input__inner {
    background-color: #e6e5e8;
    color: #242424;
  }

  .is-disabled .el-textarea__inner {
    background-color: #e6e5e8;
    color: #242424;
  }

  .form-button {
    display: flex;
    justify-content: center;
    padding: 6px 0;

    .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 16px;
    }

    .el-button + .el-button {
      margin-left: 24px;
    }
  }
}

.details {
  width: 900px;
  padding: 0 !important;
  padding-bottom: 24px !important;

  .details-title {
    text-align: center;
    font-size: 28px;
    line-height: 38px;
    padding: 32px 0;
    font-weight: bold;
    color: #000;

    .details-title-c {
      color: #ff0000;
    }
  }

  .details-item-title {
    width: 100%;
    min-height: 68px;
    border-bottom: 1px solid #e6e6e6;
    display: flex;
    box-sizing: border-box;

    .details-item-label {
      width: 148px;
      display: flex;
      font-weight: 500;
      align-items: center;
      border-right: 1px solid #e6e6e6;
      padding-right: 9px;
      justify-content: center;
      color: #262626;
      background-color: #f8faff;
    }

    .details-item-value {
      display: flex;
      align-items: center;
      padding: 9px;
      width: calc(100% - 148px);
      line-height: 24px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .details-item-box {
    width: 100%;
    border-top: 1px solid #e6e6e6;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;

    .details-item-column {
      width: 50%;
      flex-direction: column;
    }

    .details-item-column + .details-item-column {
      border-left: 1px solid #e6e6e6;
    }

    .details-item-img {
      height: 215px;
      width: 100%;
      border-bottom: 1px solid #e6e6e6;
      min-height: 43px;
      display: flex;
      box-sizing: border-box;

      .details-item-label {
        width: 148px;
        height: 100%;
        display: flex;
        font-weight: 500;
        align-items: center;
        border-right: 1px solid #e6e6e6;
        padding-right: 9px;
        justify-content: center;
        color: #262626;
        background-color: #f8faff;
      }

      .details-item-value {
        width: calc(100% - 148px);
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        text-overflow: ellipsis;

        img {
          height: 200px;
        }
      }
    }

    .details-item {
      width: 100%;
      min-height: 43px;
      border-bottom: 1px solid #e6e6e6;
      display: flex;
      box-sizing: border-box;

      .details-item-label {
        width: 180px;
        display: flex;
        font-weight: 500;
        align-items: center;
        border-right: 1px solid #e6e6e6;
        padding-right: 9px;
        justify-content: center;
        color: #262626;
        background-color: #f8faff;
      }

      .details-item-value {
        padding: 9px;
        width: calc(100% - 180px);
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .details-item-file {
          color: $zy-color;
          cursor: pointer;
        }
      }

      .replybutton {
        .el-button--text {
          padding: 6px 0;
          display: block;
        }

        .el-button + .el-button {
          margin: 0;
        }
      }
    }

    .details-item-content-label {
      width: 100%;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e6e6e6;
      padding-right: 9px;
      justify-content: center;
      color: #262626;
      padding: 9px;
      background-color: #f8faff;
      line-height: 24px;
      text-align: center;
    }

    .details-item-content {
      width: 100%;
      padding: 24px;
      border-bottom: 1px solid #e6e6e6;
      overflow: hidden;

      img {
        max-width: 100%;
      }
    }

    .details-contact {
      width: 100%;

      .details-item-contact-label {
        width: 100%;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e6e6e6;
        padding-right: 9px;
        justify-content: center;
        color: #262626;
        padding: 9px;
        background-color: #e9effe;
        line-height: 24px;
        text-align: center;
      }

      .details-contact-item {
        display: flex;
        width: 100%;
        min-height: 43px;
        border-bottom: 1px solid #e6e6e6;
        display: flex;
        box-sizing: border-box;

        .label {
          background-color: #f8faff;

          .span {
            color: #d90005;
          }
        }

        .details-contact-item-value {
          width: 148px;
          display: flex;
          font-weight: 500;
          align-items: center;
          border-right: 1px solid #e6e6e6;
          padding-right: 9px;
          justify-content: center;
          color: #262626;
        }

        .details-contact-item-value-box {
          width: calc(100% - 148px);
          display: flex;
        }

        .row1 {
          flex: 1;
        }

        .row2 {
          flex: 2;
        }

        .row3 {
          flex: 3;
        }

        .row4 {
          flex: 4;
        }

        .row5 {
          flex: 5;
        }
      }
    }

    .details-button {
      padding-top: 24px;
      padding-left: 24px;
    }
  }
}
