// 导入封装的方法
import {
  get,
  post,
  postformTime,
  exportFile
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
// export const home = params => postform('', params)
export const exe = params => exportFile('', params)

const microAdvice = {
  // 我的微建议列表
  myMicroAdviceList (data) {
    return get('/minisuggestion/my/list', data)
  },
  // 我领办的微建议列表
  myLeadMicroAdviceList (data) {
    return get('/minisuggestion/collar/list', data)
  },
  // 获取草稿箱
  microAdviceDraft (data) {
    return get('lzt/minisuggestion/drafts/list', data)
  },
  allMicroAdviceList (data) {
    return get('/minisuggestion/list', data)
  },
  // 提交微建议
  microAdviceAdd (data) {
    return post('/minisuggestion/add', data)
  },
  // 修改微建议
  microAdviceEdit (data) {
    return post('/minisuggestion/edit', data)
  },
  // 删除微建议
  microAdviceDelete (data) {
    return post('/minisuggestion/dels', data)
  },
  // 微建议详情
  microAdviceDetail (data) {
    return post('/minisuggestion/info', data)
  },
  // 新增满意度测评
  satisfactionAdd (data) {
    return post('/minievaluate/add', data)
  },
  // 修改满意度测评
  satisfactionEdit (data) {
    return post('/minievaluate/edit', data)
  },
  // 满意度测评详情
  satisfactionInfo (data) {
    return get(`/minievaluate/info/${data}`)
  },
  // 上传附件 超时时间 10分钟
  uploadFile (data) {
    return postformTime('/attachment/uploadFile', data, { timeout: 10 * 60 * 1000 })
  },
  // 办理部门机构树
  microAdviceManageTree (data) {
    return post('/tree/list', data)
  },
  // 公开或者隐藏微建议
  microAdviceOpenStatus (data) {
    return post('/minisuggestion/batchUpdate', data)
  },
  // 一键还原待审核
  microAdviceRetureVerify (data) {
    return post('/minisuggestion/restoreToAuditingWait', data)
  },
  // 一键还原未回复
  microAdviceReturnReply (data) {
    return post('/minisuggestion/restoreToReplyWait', data)
  },
  // 审核微建议列表
  verifymicroAdviceList (data) {
    return post('/minisuggestion/assign/list', data)
  },
  // 办理微建议列表
  conductMicroAdviceList (data) {
    return post('/minisuggestion/transact/list', data)
  },
  // 微建议审核
  microAdviceVerify (data) {
    return post('/minisuggestion/assign', data)
  },
  // 微建议退回
  microAdviceReturn (data) {
    return post('/minisuggestion/returned', data)
  },
  // 超级审核权限
  microAdviceSuperVerify (data) {
    return post('/minisuggestion/superAssign', data)
  },
  // 添加回复
  microAdviceReplyAdd (data) {
    return post('/minireply/add', data)
  },
  // 删除回复
  microAdviceReplyDelete (data) {
    return post('/minireply/dels', data)
  },
  // 编辑回复
  microAdviceReplyEdit (data) {
    return post('/minireply/edit', data)
  },
  // 获取回复详情
  microAdviceReplyInfo (data) {
    return get(`/minireply/info/${data}`)
  },
  // 获取回复列表
  microAdviceReplyList (data) {
    return get('/minireply/list', data)
  },
  // 微建议统计按提交人
  microAdviceSubmitter (data) {
    return get('/minianalyse/countByAuditingType', data)
  },
  // 微建议统计按状态
  microAdviceCountByStatus (data) {
    return get('/minianalyse/countByStatus', data)
  },
  // 微建议统计按单位
  microAdviceCountByUnit (data) {
    return get('/minianalyse/countByTransactOrg', data)
  },
  // 微建议统计按满意度
  microAdviceCountBySatisfaction (data) {
    return get('/minianalyse/countByEvaluate', data)
  },
  // 微建议统计按领办人
  microAdviceCountByColler (data) {
    return get('/minianalyse/countByCollar', data)
  },
  dictionaryCommittee (data) {
    return get('/dictionary/pubkvs', data)
  },
  // 微建议导出word
  microAdviceExportWord (data) {
    return exportFile('/minisuggestion/exportwords', data)
  },
  assignenable (data) {
    return get('/minisuggestion/assign/enable', data)
  },
  evaluatenenable (data) {
    return get('/minisuggestion/evaluate/enable', data)
  },
  minigrouplist (data) { // 办理单位列表
    return get('/minigroup/list', data)
  },
  minigroup (url, data) { // 新增办理单位列表
    return post(url, data)
  },
  minigroupinfo (id) { // 办理单位详情
    return get(`/minigroup/info/${id}`)
  },
  minigroupdels (data) { // 办理单位详情
    return get('/minigroup/dels', data)
  }

}

export default microAdvice
