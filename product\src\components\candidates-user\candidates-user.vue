<template>
  <div class="candidates-user"
       v-loading="loading"
       element-loading-text="拼命加载中">
    <div class="candidates-user-box">
      <div class="candidates-user-content">
        <div class="search-box">
          <el-input placeholder="搜索人员名字"
                    v-model="name"
                    clearable
                    @keyup.enter.native="search">
            <div slot="prefix"
                 class="input-search"></div>
          </el-input>
        </div>
        <div class="user-box">
          <div class="user-tree-box">
            <div class="institutions-text">选择机构</div>
            <div class="user-tree">
              <!-- <zy-tree :tree="tree"
                       :choiceId.sync="choiceval"
                       @on-choice-click="choiceClick"></zy-tree> -->
              <zy-tree :tree="tree"
                       v-model="choiceval"
                       :props="{ children: 'children', label: 'name' }"
                       @on-tree-click="choiceClick"
                       :anykey="defaultUnitShowIds"></zy-tree>
            </div>
          </div>
          <div class="user-personnel-box">
            <div class="personnel-checkbox">
              <div class="personnel-checkbox-text">人员列表</div>
              <el-checkbox :indeterminate="isIndeterminate"
                           v-model="checkAll"
                           @change="handleCheckAllChange"></el-checkbox>
            </div>
            <div class="user-content-box scrollBar">
              <el-checkbox-group v-model="checkedCities"
                                 @change="handleCheckedCitiesChange">
                <div class="user-content"
                     v-for="city in cities"
                     :key="city.userId">
                  <div class="user-content-icon-name">
                    <div class="user-content-icon"></div>
                    <div class="user-content-name el-checkbox__label ellipsis">
                      {{ city.userName }}
                    </div>
                  </div>
                  <el-checkbox :value="city.userId"
                               :label="city.userId"
                               :disabled="maxUser(city.userId)"></el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>
      <div class="selected-user-box">
        <div class="selected-user-number">
          <div class="selected-user-number-text">
            {{ point }}已选择({{ storageData.length }}人)
          </div>
          <div class="selected-user-icon-delete"
               @click="deleteAll"></div>
        </div>
        <div class="selected-user scrollBar">
          <div class="selected-user-content"
               v-for="(item, index) in storageData"
               :key="index">
            <div class="selected-user-icon">
              <div class="selected-user-icon-name"></div>
            </div>
            <div class="selected-user-information">
              <div class="selected-user-name">
                {{ item.userName || item.name }}
              </div>
              <div class="selected-user-text ellipsis">{{ item.position }}</div>
            </div>
            <div class="selected-user-delete">
              <div class="selected-user-icon-delete"
                   @click="deleteclick(item)"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="candidates-user-button">
      <el-button type="primary"
                 @click="submitForm">确定</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
</template>
<script>
import _ from 'lodash'
export default {
  name: 'candidatesUser',
  data () {
    return {
      choiceval: '',
      tree: [],
      name: '',
      checkAll: false,
      checkedCities: [],
      cities: [],
      isIndeterminate: true,
      storage: {},
      storageData: [],
      selectObj: [],
      loading: false,
      defaultUnitIds: [],
      defaultUnitShowIds: []
    }
  },
  props: {
    point: {
      type: String,
      default: ''
    },
    max: {
      type: Number,
      default: 10000
    },
    data: {
      type: Array,
      default: () => []
    },
    flag: {
      type: String,
      default: ''
    },
    groupId: {
      type: String,
      default: ''
    },
    disabled: {
      type: Array,
      default: () => []
    },
    defualtUser: {
      type: Array,
      default: () => []
    },
    defaultUnit: {
      type: Array,
      default: () => []
    }
  },
  created () {
    if (this.data.length) {
      this.default()
    }
    this.pointrees()
  },
  watch: {
    defualtUser (val) {
      if (val.length) {
        this.cities = val
        val.forEach(item => {
          this.checkedCities = []
          this.storageData = []
          this.checkedCities.push(item.userId)
          this.storage[this.choiceval] = item
          this.storageData.push(item)
        })
      }
    }
  },
  methods: {
    maxUser (id) {
      var show = false
      if (this.storageData.length >= this.max) {
        show = true
      }
      this.storageData.forEach(item => {
        if (item.userId === id) {
          show = false
        }
      })
      this.storageData.forEach(item => {
        if (item.disabled && item.userId === id) {
          show = true
        }
      })
      // this.disabled.forEach(item => {
      //   if (item.userId === id) {
      //     show = true
      //   }
      // })
      return show
    },
    search () {
      this.loading = true
      this.roleChooseusers()
    },
    default () {
      // this.storageData = this.data
      this.data.forEach(item => {
        this.storageData.push(item)
        this.selectObj[item.userId] = item.userId
      })
    },
    async pointrees () {
      const res = await this.$api.general.pointrees(this.point)
      var { data } = res
      this.tree = data
      var that = this
      const treeIdArr = []
      function getArr (arr) {
        that.defaultUnit.forEach(v => {
          arr.forEach(v2 => {
            if (v2.id.indexOf(v) !== -1) {
              treeIdArr.push(v2.id)
            } else {
              getArr(v2.children)
            }
          })
        })
      }
      if (this.defaultUnit.length) {
        this.loading = true
        getArr(this.tree)
        this.defaultUnitIds = _.uniq(treeIdArr)
        this.defaultUnitIds.forEach((v, index) => { that.forRoleChooseusers(v, index) })
        this.getPidList()
      }
    },
    getPidList () {
      var arr = []
      this.defaultUnitIds.forEach(v => {
        arr = arr.concat(this.filterTree(this.tree, v))
      })
      this.getPid(arr)
    },
    getPid (item) {
      item.forEach(v => {
        this.defaultUnitShowIds.push(v.id)
        if (v.children) {
          this.getPid(v.children)
        }
      })
    },
    filterTree (nodes, id) {
      if (!nodes || !nodes.length) return void 0 // eslint-disable-line
      const children = []
      for (let node of nodes) {
        node = Object.assign({}, node)
        const sub = this.filterTree(node.children, id)
        if ((sub && sub.length) || node.id === id) {
          sub && (node.children = sub)
          children.push(node)
        }
      }
      return children.length ? children : void 0 // eslint-disable-line
    },
    choiceClick (item) {
      this.loading = true
      this.roleChooseusers()
    },
    async forRoleChooseusers (val, index) {
      var datas = {
        pointCode: this.point,
        treeId: val,
        keyword: this.name
      }
      const res = await this.$api.general.pointreeUsers(datas)
      var { data } = res
      this.cities = this.cities.concat(data)
      const labeluserUsersData = await this.$api.systemSettings.labeluserUsers({ labelCode: ' 202104', pageNo: 1, pageSize: 99999 })
      data.forEach((item, index) => { // 匹配标签用户与 所展示的列表数据
        labeluserUsersData.data.forEach((v) => {
          if (v.userId === item.userId) {
            this.checkedCities.push(item.userId)
            this.storageData.push(item)
          }
        })
      })
      this.loading = false
      if (index + 1 === this.defaultUnitIds.length) {
        this.choiceval = this.defaultUnitIds[index]
      }
    },
    async roleChooseusers () {
      var datas = {
        pointCode: this.point,
        treeId: this.choiceval,
        keyword: this.name
      }
      const res = await this.$api.general.pointreeUsers(datas)
      var { data } = res
      this.disabled.forEach(item => {
        data = data.filter(tab => tab.userId !== item.userId)
      })
      this.cities = data
      this.loading = false
      if (!this.defaultUnit.length && !this.defualtUser.length) {
        this.memoryChecked()
      }
    },
    handleCheckAllChange (val) {
      var arr = []
      this.cities.forEach(item => {
        arr.push(item.userId)
        if (Object.prototype.hasOwnProperty.call(this.selectObj, item.userId)) {
          if (!val) {
            this.deleteData(item)
          }
          val ? null : delete this.selectObj[item.userId] // eslint-disable-line
        } else {
          this.selectObj[item.userId] = item.userId
          this.pushData(item.userId)
        }
      })
      this.checkedCities = val ? arr : []
      this.storage[this.choiceval] = val ? arr : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange (value) {
      if (!this.defaultUnit.length || !this.defualtUser.length) {
        this.storage[this.choiceval] = value
      }
      const checkedCount = value.length
      this.checkAll = checkedCount > this.cities.length || checkedCount === this.cities.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length
      var values = []
      value.forEach(item => {
        values[item] = item
      })
      this.cities.forEach((item) => {
        if (Object.prototype.hasOwnProperty.call(values, item.userId)) {
        } else {
          delete this.selectObj[item.userId]
          this.deleteData(item)
        }
      })
      value.forEach(item => {
        if (Object.prototype.hasOwnProperty.call(this.selectObj, item)) {
        } else {
          this.selectObj[item] = item
          if (!this.defaultUnit.length || !this.defualtUser.length) {
            this.pushData(item)
          }
        }
      })
    },
    deleteAll () {
      this.$confirm('此操作将删除当前已选的所有用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var arr = []
        this.storageData.forEach(item => {
          if (item.disabled) {
            arr.push(item)
          }
        })
        this.storageData = arr
        this.selectObj = []
        arr.forEach(item => {
          this.selectObj[item.userId] = item.userId
        })
        if (arr.length) {
          this.$message({
            type: 'info',
            message: '当前选中用户有部分不能移除'
          })
        }
        this.memoryChecked()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    deleteclick (data) {
      if (data.disabled) {
        this.$message({
          type: 'info',
          message: '当前选中用户不能移除'
        })
        return
      }
      this.deleteData(data)
      delete this.selectObj[data.userId]
      this.memoryChecked()
    },
    deleteData (data) {
      var found = this.data.some(function (item) {
        return item.userId.includes(data.userId)
      })
      if (found) {
        this.deleteApi(data)
      }
      const arr = this.storageData
      arr.forEach((item, index) => {
        if (item.userId === data.userId) {
          arr.splice(index, 1)
        }
      })
      this.storageData = arr
    },
    async deleteApi (a) {
      var params = {
        userId: a.userId,
        groupId: this.groupId
      }
      const res = await this.$api.general.deleteApi(params)
      this.$message({
        message: res.errmsg,
        type: 'success'
      })
    },
    pushData (id) {
      this.cities.forEach((item, index) => {
        if (item.userId === id) {
          this.storageData.push(item)
        }
      })
    },
    memoryChecked () {
      var add = []
      this.cities.forEach((row, index) => {
        if (Object.prototype.hasOwnProperty.call(this.selectObj, row.userId)) {
          add.push(row.userId)
        }
      })
      this.checkedCities = add
      const checkedCount = add.length
      this.checkAll = checkedCount > this.cities.length || checkedCount === this.cities.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length
    },
    submitForm () {
      this.$emit('userCallback', this.storageData, true)
    },
    resetForm () {
      this.$emit('userCallback', this.data, false)
    }
  }
}
</script>
<style lang="scss">
@import "./candidates-user.scss";
</style>
