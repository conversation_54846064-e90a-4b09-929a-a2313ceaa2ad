<!DOCTYPE html>
<HTML>
<HEAD>
	<TITLE> ZTREE DEMO - url</TITLE>
	<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<link rel="stylesheet" href="../../../css/demo.css" type="text/css">
	<link rel="stylesheet" href="../../../css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="../../../js/jquery-1.4.4.min.js"></script>
	<script type="text/javascript" src="../../../js/jquery.ztree.core.js"></script>
	<!--  <script type="text/javascript" src="../../../js/jquery.ztree.excheck.js"></script>
	  <script type="text/javascript" src="../../../js/jquery.ztree.exedit.js"></script>-->
	<SCRIPT type="text/javascript">
		<!--
		var setting = {
			data: {
				simpleData: {
					enable: true
				}
			}
		};

		var zNodes =[
			{ id:1, pId:0, name:"zTree Home", url:"http://www.treejs.cn/", target:"_blank"},
			{ id:2, pId:0, name:"zTree in Google", url:"http://code.google.com/p/jquerytree/", target:"_blank"},
			{ id:3, pId:0, name:"zTree in Iteye", url:"http://ztreeapi.iteye.com/", target:"_blank"},
			{ id:4, pId:0, name:"Nothing...", url:"", target:"_blank", click:"alert('I can not jump...');"}
		];

		$(document).ready(function(){
			$.fn.zTree.init($("#treeDemo"), setting, zNodes);
		});
		//-->
	</SCRIPT>
 </HEAD>

<BODY>
<h1>Hyperlinks Demo</h1>
<h6>[ File Path: core/url.html ]</h6>
<div class="content_wrap">
	<div class="zTreeDemoBackground left">
		<ul id="treeDemo" class="ztree"></ul>
	</div>
	<div class="right">
		<ul class="info">
			<li class="title"><h2>1, Explanation of setting</h2>
				<ul class="list">
				<li>No extrally setting needed for basic functions.</li>
				<li class="highlight_red">If you need flexible control of hyperlinks to jump, please use the 'onClick' callback. See the API documentation for more related contents.</li>
				</ul>
			</li>
			<li class="title"><h2>2, Explanation of treeNode</h2>
				<ul class="list">
				<li class="highlight_red">1. the 'url' attribute is used to save the hyperlink's path.</li>
				<li class="highlight_red">2. the 'target' attribute is used to save the hyperlink's target.</li>
				<li class="highlight_red">3. the 'click' attribute is used to save the hyperlink's 'onclick' attribute.</li>
				<li>Please refer to the API documentation "treeNode data details" to view other attributes description.</li>
				</ul>
			</li>
		</ul>
	</div>
</div>
</BODY>
</HTML>