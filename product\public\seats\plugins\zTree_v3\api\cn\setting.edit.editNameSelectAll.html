<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">setting.edit.</span>editNameSelectAll</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.exedit</span> 扩展 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>节点编辑名称 input 初次显示时,设置 txt 内容是否为全选状态。 <span class="highlight_red">[setting.edit.enable = true 时生效]</span></p>
			<p>默认值: false</p>
		</div>
	</div>
	<h3>Boolean 格式说明</h3>
	<div class="desc">
	<p> true 表示 全选状态</p>
	<p> false 表示 不是全选状态，光标默认在最后</p>
	</div>
	<h3>setting 举例</h3>
	<h4>1. 设置节点编辑名称 input 初次显示时，txt内容为全选状态</h4>
	<pre xmlns=""><code>var setting = {
	edit: {
		enable: true,
		editNameSelectAll: true
	}
};
......</code></pre>
</div>
</div>