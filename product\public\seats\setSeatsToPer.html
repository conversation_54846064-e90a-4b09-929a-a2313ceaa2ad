<html>

<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <title>人员排座与导入</title>
  <!-- 引用css -->
  <link href="plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="css/style.css" rel="stylesheet">
  <link rel="stylesheet" href="css/base.css">
  <link rel="stylesheet" href="plugins/layui/css/layui.css">
  <link rel="stylesheet" href="js/icheck/skins/all.css">
  <link rel="stylesheet" href="js/ztree/zTreeStyle/zTreeStyle.css">
  <link rel="stylesheet" href="plugins/easyui-1.7.0/themes/default/easyui.css">
  <link rel="stylesheet" href="plugins/easyui-1.7.0/themes/icon.css">
  <link rel="stylesheet" href="css/setSeatsToPer.css">
  <style type="text/css">
    * {
      font-size: 14px !important;
    }
  </style>
</head>

<body>



  <div id="perList"
    style="position:relative; float: left;width: 250px;height: 100%;padding: 24px 10px 10px 10px; overflow: hidden; background: #FFFFFF">
    <div class="row toButton">
      <select id="sortType" class="easyui-combobox custom-select" data-options="editable:false" panelHeight="90px"
        name="state" style="width:216px; height: 44px;">
        <option value="0">按部门排序</option>
        <option value="1">按姓氏笔画排序</option>
      </select>
    </div>
    <div class="row toButton">
      <div class="radio radio-info">
        <input type="radio" name="perRadio" id="signed" value="1" checked="checked">
        <label style="width: 78px;font-family: '微软雅黑'; font-weight: normal; color: #aeaeae;" for="signed">已报名人员</label>
      </div>
      <div class="radio radio-info">
        <input type="radio" name="perRadio" id="unsign" value="0">
        <label style="font-family: '微软雅黑'; font-weight: normal; color: #aeaeae;" for="unsign">出席列席人员</label>
      </div>
    </div>
    <div
      style="width: 230px; height: 52px; margin-top: 16px; padding: 2px; white-space:nowrap;overflow: hidden;text-overflow:ellipsis;background: #E6E6E6;display: flex;justify-content: space-between;align-items: center;">
      <!-- <label id="reset" class="btn cr" style="border: 1px solid #0850BC;background: #0850BC;color: #fff;font-size: 14px;"><i class="fa fa-refresh" style="margin-right: 5px;"></i>重置</label> -->
      <span class="searchResult">查看搜索结果</span>
      <button id="query" type="button" class="btn btn-info"
        style="background: #199BC5; border: none; color: #FFF; margin-right: 5px;">搜索</button>
    </div>

    <div id="perDiv" style="position: relative;top: 0;left: 0;overflow-y: auto;">
      <ul id="perTree" class="ztree" style="overflow: hidden;"></ul>
    </div>
  </div>
  <div id="setsDiv"
    style="position: absolute;left: 274px; padding: 24px 0 0 0; height: 100%;background: rgb(255, 255, 255);min-width: 734px;">
    <div style="width: 100%;height: 36px;display: flex;flex-wrap: nowrap;">
      <div class="btn-group show toRight">
        <button id="downloadBtn" type="button" class="btn cancel dropdown-toggle" data-toggle="dropdown"
          aria-haspopup="true" aria-expanded="true">
          导入排座
          <img src="img/minus.png">
        </button>
        <div class="dropdown-menu" x-placement="bottom-start"
          style="position: absolute; transform: translate3d(0px, 33px, 0px); top: 0px; left: 0px; will-change: transform; text-align: center;min-width: 110px;">
          <a class="dropdown-item showSortNo" id="importPer_a">按顺序号导入</a>
          <a class="dropdown-item showSeatNo" id="importPer_b">按座位号导入</a>
          <a class="dropdown-item showSeatNo" id="importPer_c">常委会议室模板</a>
          <a class="dropdown-item showSeatNo" id="importPer_d">人民会堂模板</a>
        </div>
      </div>
      <button type="button" class="btn cancel toRight" id="makePic">生成图片</button>
      <div class="btn-group show toRight">
        <button id="downloadBtn" type="button" class="btn cancel dropdown-toggle" data-toggle="dropdown"
          aria-haspopup="true" aria-expanded="true">
          下载导入模版
          <img src="img/minus.png">
        </button>
        <div class="dropdown-menu" x-placement="bottom-start"
          style="position: absolute; transform: translate3d(0px, 33px, 0px); top: 0px; left: 0px; will-change: transform; text-align: center;min-width: 110px;">
          <a class="dropdown-item showSortNo" onclick="downTemplateByType(1);">按顺序号导入</a>
          <a class="dropdown-item showSeatNo" onclick="downTemplateByType(0);">按座位号导入</a>
          <a class="dropdown-item showSeatNo" onclick="downTemplateBycw()">常委会议室模板</a>
          <a class="dropdown-item showSeatNo" onclick="downTemplateByrm()">人民会堂模板</a>
        </div>
      </div>
      <div class="btn-group show toRight">
        <button id="showBtn" type="button" class="btn cancel dropdown-toggle" data-toggle="dropdown"
          aria-haspopup="true" aria-expanded="true">
          显示顺序号
          <img src="img/minus.png">
        </button>
        <div class="dropdown-menu" x-placement="bottom-start"
          style="position: absolute; transform: translate3d(0px, 33px, 0px); top: 0px; left: 0px; will-change: transform; text-align: center;min-width: 110px;">
          <a class="dropdown-item showSortNo" onclick="showSeatNoOrSort(this);">顺序号</a>
          <a class="dropdown-item showSeatNo" onclick="showSeatNoOrSort(this);">座位号</a>
        </div>
      </div>
      <button type="button" class="btn cancel toRight" id="swapSeats">对调座位</button>
      <button type="button" class="btn cancel toRight" id="autoPatch">自动补位</button>
      <!--<button type="button" class="btn btn-info" id=""></button>-->
      <div class="btn-group show toRight">
        <button id="insertSeats" type="button" class="btn cancel dropdown-toggle" data-toggle="dropdown"
          aria-haspopup="true" aria-expanded="true">
          插入座位
          <img src="img/minus.png">
        </button>
        <div class="dropdown-menu" x-placement="bottom-start"
          style="position: absolute; transform: translate3d(0px, 33px, 0px); top: 0px; left: 0px; will-change: transform; text-align: center;min-width: 110px;">
          <a class="dropdown-item showSortNo" onclick="insertSeatsByType(1);">自动补齐</a>
          <a class="dropdown-item showSeatNo" onclick="insertSeatsByType(2);">座位类推</a>
        </div>
      </div>
      <button type="button" class="btn cancel toRight" id="deleteSeats">删除排座</button>
      <div class="row" style="width: 260px; height: 100%; border: 1px solid rgba(217,217,217,1); border-radius: .25em;">
        <div class="radio radio-info">
          <input type="radio" id="strokes" name="autoSortType" type="radio" value="1" checked="checked">
          <label style="width: 120px;font-family: '微软雅黑'; font-weight: normal;" for="strokes">姓氏笔画由少到多</label>
        </div>
        <button type="button" class="btn cancel toRight" id="autoSort">自动排座</button>
      </div>
    </div>
    <div class="row" style="width: 100%;height: 36px; margin-top: 15px;">
      <div>
        <button type="button" class="btn btn-info" id="saveSeats">保存排座</button>
        <!-- <button type="button" class="btn btn-info" id="operationDesc"><i class="fa fa-info-circle"></i> 操作说明</button> -->
      </div>
    </div>
    <div class="box" onmousedown="boxOnMouse();"
      style="position: relative; overflow: hidden;text-align: center;width: 100%;min-height: 500px;">
      <label id="meetName"></label>
      <label id="roomName"></label>
      <span class="areaTip"></span>
    </div>
  </div>

  <!--begain 部门选择弹出框-->
  <div id="deptWin" class="dept_content">
    <ul id="treeDemo" class="ztree" style="overflow: hidden;"></ul>
  </div>
  <!--end-->

  <div id="cloneDiv"></div>
  <div id="images" style="display: none;"></div>

  <!----------搜索--------->
  <div class="modal fade" id="winModal" tabindex="-1" role="dialog" aria-labelledby="winModalLabel" aria-hidden="true"
    data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <span class="modal-title" id="winModalLabel" style="font-size: 14px;">搜索</span>
          <div class="close" data-dismiss="modal" aria-label="Close"><img src="img/icon_close.png" alt=""></div>
        </div>
        <div class="modal-body">
          <div>
            <span>姓名</span>
            <input type="text" id="personName" placeholder="请输入姓名" style="width: 504px;" autocomplete="off">
          </div>
          <div>
            <span>联系电话</span>
            <input type="text" id="phone" placeholder="请输入手机号码" style="width: 504px;" autocomplete="off" />
          </div>
          <div>
            <span>部门</span>
            <div style="position: relative; right: 28px;">
              <input id="deptId" type="hidden" />
              <input id="deptName" class="form-control meetInfo" style="width: 296px;" type="text" readonly="readonly"
                placeholder="请选择选项">
              <img src="img/select.png" id="organDel" class="selectIco"></img>
            </div>
          </div>
          <div>
            <span>界别</span>
            <div style="position: relative; right: 28px;">
              <select id="circlesSelect" class="easyui-combobox custom-select" data-options="editable:false"
                panelHeight="240px" name="state" style="width:296px; height: 44px;">

              </select>
            </div>
          </div>
          <div>
            <span>组别</span>
            <div style="position: relative; right: 28px;">
              <select id="groupSelect" class="easyui-combobox custom-select" data-options="editable:false"
                panelHeight="240px" name="state" style="width:296px; height: 44px;">

              </select>
            </div>
          </div>
          <div>
            <span>人员类型</span>
            <div style="position: relative; right: 28px;">
              <select id="pTypeSelect" class="easyui-combobox custom-select" data-options="editable:false"
                panelHeight="140px" name="state" style="width:296px; height: 44px;">
                <option value="">全部人员</option>
                <option value="1">列席人员</option>
                <option value="0">非列席人员</option>
              </select>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <label id="confirmBtn" class="btn-info">确定</label>
          <label id="resetParam" class="cancel">重置</label>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="picModal" tabindex="-1" role="dialog" aria-labelledby="winModalLabel" aria-hidden="true"
    data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <span class="modal-title" id="winModalLabel" style="font-size: 14px;">缩略图</span>
          <div class="close" data-dismiss="modal" aria-label="Close"><img src="img/icon_close.png" alt=""></div>
        </div>
        <div class="modal-body">
          <div id="picDiv"></div>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" id="infoModal" tabindex="-1" role="dialog" aria-labelledby="winModalLabel" aria-hidden="true"
    data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <span class="modal-title" id="winModalLabel" style="font-size: 14px;">匹配信息</span>
          <div class="close" data-dismiss="modal" aria-label="Close"><img src="img/icon_close.png" alt=""></div>
        </div>
        <div class="modal-body">
          <span style="width: 200px;"><label style="color: red;">注意：</label><label>以下信息无法匹配</label></span>
          <div>
            <label style="width: 80px;">匹配信息</label>
            <textarea class="form-textarea" rows="8"
              style="width: 100%; border: 1px solid rgba(217, 217, 217, 1); padding: 5px; color: #2170d9; border-radius: 5px;"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <label id="confirmBtn" class="btn-info" data-dismiss="modal">确定</label>
          <label id="resetParam" class="cancel" data-dismiss="modal">取消</label>
        </div>
      </div>
    </div>
  </div>
  <!-- 引用js -->
  <script src="plugins/axios/axios.min.js"></script>
  <script src="plugins/axios/qs.min.js"></script>
  <script src="plugins/jquery/jquery.min.js "></script>
  <script src="plugins/bootstrap/js/popper.min.js "></script>
  <!-- <script src="plugins/bootstrap/js/bootstrap.min.js "></script> -->
  <script type="text/javascript" src="js/drag/jquery.event.drag-2.2.js"></script>
  <script type="text/javascript" src="js/drag/jquery.event.drag.live-2.2.js"></script>
  <script type="text/javascript" src="js/drag/jquery.event.drop-2.2.js"></script>
  <script type="text/javascript" src="js/drag/jquery.event.drop.live-2.2.js"></script>
  <script type="text/javascript" src="js/drag/excanvas.min.js"></script>
  <script type="text/javascript" src="js/ztree/js/jquery.ztree.core.js"></script>
  <script type="text/javascript" src="js/ztree/js/jquery.ztree.excheck.js"></script>
  <script type="text/javascript" src="js/icheck/icheck.js"></script>
  <script type="text/javascript" src="plugins/layui/layer/layer.js"></script>
  <script type="text/javascript" src="js/layui/layui.js"></script>
  <script src="plugins/easyui-1.7.0/jquery.easyui.min.js"></script>
  <script type="text/javascript" src="js/base.js"></script>
  <script type="text/javascript" src="js/setSeatsToPer.js"></script>
  <script type="text/javascript" src="js/setSeatsToPerMin.js"></script>
  <!-- html2canvas将Dom节点在Canvas里边画出来 -->
  <script src="js/createPic/html2canvas.js"></script>
  <!-- 将canvas图片保存成图片 -->
  <!-- <script src="js/createPic/moment.js"></script> -->
  <script src="js/createPic/canvas2image.js"></script>
  <script src="js/createPic/base64.js"></script>
</body>

</html>