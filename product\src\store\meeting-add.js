/*
 * @Author: wuxxing
 * @Date: 2021-04-09 11:23:58
 * @LastEditTime: 2021-06-13 01:00:12
 * @Description: 模块需要TODO:非全局
 */
const meetingAdd = {
  namespaced: true,
  state: {
    // 基本信息-会议、模板公用参数
    baseInfo: {
      id: '', // 会议id -- 新增可不传
      parentId: '', // 子级会议需要
      name: '', // 会议名称
      tempName: '', // 模板名称
      startTime: '', // 会议开始时间
      finishTime: '', // 会议结束时间
      endTime: '', // 截止时间
      meetPlaceId: '', // 会议地点id
      meetTypeId: '', // 会议类型id
      // officeId: sessionStorage.getItem('userzx').officeId,
      officeName: '', // 部门名称（仅展示不用提交该字段）
      officeId: '', // 部门id
      agenda: '', // 会议议程
      // content: '' // 有关事项
      fileIds: '' // 材料id集合
      // fileIds: (state) => state.meetFiles.map(v => v.id).join(',') // 材料id集合
    },
    // meetUserAll: [], // 所有类型人员
    confereeMan: [], // 参会人员
    meetAdminMan: [], // 会议管理员
    leaveApproveMan: [], // 请假审批人员
    meetCheckMan: [], // 会议查看人员
    meetFiles: [], // 会议材料
    type: '' // 区分操作类型
  },
  // 异步的改变数据
  actions: {
    // 获取基本信息
    // GET_baseInfo({ commit }, val) {
    //   commit('SET_baseInfo', val)
    // },
    // GET_pickMan({ commit }, val) {
    //   commit('SET_pickMan', val)
    // }
  },
  // 唯一能修改数据变化的地方
  mutations: {
    // 修改会议基本信息数据
    SET_baseInfo(state, data) {
      state.baseInfo = data
    },
    SET_confereeMan(state, data) {
      state.confereeMan = data
    },
    SET_meetAdminMan(state, data) {
      state.meetAdminMan = data
    },
    SET_leaveApproveMan(state, data) {
      state.leaveApproveMan = data
    },
    SET_meetCheckMan(state, data) {
      state.meetCheckMan = data
    },
    // 修改会议材料数据
    SET_meetFiles(state, data) {
      state.meetFiles = data
    },
    // 修改type
    SET_type(state, data) {
      state.type = data
    },
    // 初始化
    initData(state) {
      for (const key in state.baseInfo) {
        state.baseInfo[key] = '' // 重置参数
      }
      state.confereeMan = []
      state.meetAdminMan = []
      state.leaveApproveMan = []
      state.meetCheckMan = []
      state.meetFiles = []
      state.type = ''
    }
  },
  // 属性统一输出
  getters: {
    baseInfo: state => state.baseInfo,
    confereeMen: state => state.confereeMan,
    meetAdminMen: state => state.meetAdminMan,
    leaveApproveMen: state => state.leaveApproveMan,
    meetCheckMen: state => state.meetCheckMan,
    meetFiles: state => state.meetFiles,
    type: state => state.type
  }
}
export default meetingAdd
