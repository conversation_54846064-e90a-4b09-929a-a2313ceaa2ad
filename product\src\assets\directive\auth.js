import Vue from 'vue'
import permissions from '../js/permissions'

Vue.prototype.$hasPermission = permissions.hasPermission
Vue.directive('permissions', {
  inserted: (el, binding, vnode) => {
    // 如果没有权限就移除此节点
    if (binding.rawName === 'v-permissions:router') {
      var arr = binding.value
      var add = binding.value.splice(binding.value.length - 1, 1)
      if (!permissions.hasPermission(arr, add)) {
        el.parentNode.removeChild(el)
      }
    } else {
      if (binding.value.includes('auth')) {
        if (!permissions.hasPermission(binding.value)) {
          el.parentNode.removeChild(el)
        }
      }
    }
  },
  bind: (el, binding) => {
    if (!binding.value.includes('auth')) {
      let { to, permissions } = JSON.parse(sessionStorage.curMenuItem)
      const params = {}
      let path = {}
      if (to.indexOf('?') > 0) {
        to = to.split('?')
        to[1].split('&').forEach(item => {
          const i = item.split('=')
          params[i[0]] = i[1]
          path = `auth:${params.committee}:${to[0].split('/').slice(2).join(':')}${params.unit ? `:${params.unit}` : ''}${params.model ? `:${params.model}` : ''}:${binding.value}`
        })
      } else {
        path = `auth:${to.split('/').slice(1).join(':')}:${binding.value}`
      }
      // console.log(permissions, path)
      let disabled = true
      permissions.forEach(ele => {
        if (path === ele.replace(/\s/g, '')) {
          disabled = false
        }
      })
      el.disabled = disabled
      if (disabled) {
        el.className += ' is-disabled'
      }
    }
  }
})
