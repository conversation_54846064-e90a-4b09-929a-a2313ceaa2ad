<template>
  <div class="nav-right-bank">
    <div class="radio-header">
      <el-input
        class="title-input"
        :type="type === 1 || type === 2 ? 'text' : 'textarea'"
        :rows="4"
        v-model="form.question"
        placeholder="请输入问题题目"
      ></el-input>
    </div>
    <div v-if="type === 1 || type === 2">
      <div
        class="radio-box"
        v-for="(items, indexs) in form.choiceText"
        :key="indexs"
      >
        <div class="radio-box-width">
          <div v-if="type === 1" class="o-box" />
          <div v-if="type === 2" class="square-box" />
          <el-input
            v-model="form.choiceText[indexs]"
            clearable
            :placeholder="'选项 ' + (indexs + 1)"
          />
        </div>
        <div class="edit-list">
          <img
            v-if="indexs !== 0"
            @click="swapArray(form.choiceText, indexs - 1, indexs)"
            src="../../../../assets/images/up-two-d.png"
          />
          <img
            v-if="indexs !== form.choiceText.length - 1"
            @click="swapArray(form.choiceText, indexs, indexs + 1)"
            src="../../../../assets/images/down-two-d.png"
          />
          <img
            v-if="form.choiceText.length > 2"
            @click="swapArray(form.choiceText, indexs, indexs)"
            src="../../../../assets/images/delete-d.png"
          />
        </div>
      </div>
    </div>
    <div class="add-button flex-between">
      <div
        v-if="type === 1 || type === 2"
        style="color: #007bff; width: 100%"
        class="flex-between"
        @click="addItems()"
      >
        <i class="el-icon-plus"></i>添加单个选项
      </div>
    </div>
    <div class="buttom-botton">
      <el-button @click="cloesWin">取消</el-button>
      <el-button type="primary" @click="saveFlag">保存</el-button>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
export default {
  props: {
    type: {
      type: Number,
      default: 1
    },
    id: {
      type: String,
      default: null
    }
  },
  data () {
    return {
      form: {
        choiceText: ['', ''],
        question: ''
      }
    }
  },
  created () {
    if (this.id) {
      this.form.id = this.id
      this.getInfo()
    }
    this.form.answerType = this.type
  },
  methods: {
    saveFlag () {
      const data = _.cloneDeep(this.form)
      if (this.type !== 3) {
        var ckItem = this.form.choiceText.every(item => {
          return item
        })
        if (!data.question || !ckItem) {
          this.$message({
            message: '请确保每一项都已填写',
            type: 'warning'
          })
          return
        }
        data.choiceText = data.choiceText.join('|')
      } else {
        if (!data.question) {
          this.$message({
            message: '请填写问题',
            type: 'warning'
          })
          return
        }
        delete data.choiceText
      }
      this.$api.appManagement.questionnairequestionbank[this.id ? 'edit' : 'add'](data).then(res => {
        this.form = {
          choiceText: ['', ''],
          question: '',
          answerType: this.type
        }
        const { errcode, errmsg } = res
        if (errcode === 200) {
          this.$message({
            message: errmsg,
            type: 'success'
          })
          this.$emit('cloesWin')
        }
      })
    },
    getInfo () {
      this.$api.appManagement.questionnairequestionbank.info(this.id).then(res => {
        if (this.type !== 3) {
          this.$set(this.form, 'choiceText', res.data.choiceText.split('|'))
        }
        this.$set(this.form, 'question', res.data.question)
        this.$set(this.form, 'answerType', res.data.answerType)
      })
    },
    addItems () {
      this.form.choiceText.push('')
    },
    swapArray (arr, index1, index2) {
      if (index1 === index2) {
        arr = arr.splice(index1, 1)
        return arr
      }
      arr[index1] = arr.splice(index2, 1, arr[index1])[0]
      return arr
    },
    cloesWin () {
      this.$emit('cloesWin')
    }
  }
}
</script>
<style lang="scss">
.radio-box {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  transition: all 0.2s;
  &:hover {
    .radio-box-width {
      border: 2px dashed #ebebeb;
    }
    .edit-list {
      visibility: visible;
    }
  }
  .radio-box-width {
    border: 2px solid transparent;
    width: 60%;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding-left: 12px;
    border-radius: 4px;
    .el-input__inner {
      border: none;
    }
    .o-box,
    .square-box {
      width: 18px;
      height: 18px;
      background: #ffffff;
      border: 1px solid #cccccc;
      margin-right: 9px;
      flex-shrink: 0;
    }
    .o-box {
      border-radius: 50%;
    }
    .square-box {
      border-radius: 4px;
    }
  }
  .edit-list {
    visibility: hidden;
    display: flex;
    width: 120px;
    justify-content: space-between;
    margin-left: 20px;
    img {
      width: 30px;
      cursor: pointer;
    }
  }
}
</style>
<style lang='scss' scope>
.nav-right-bank {
  width: 600px;
  padding: 40px;
  .radio-header {
    box-sizing: border-box;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    transition: all 0.2s;
    .edit-list {
      visibility: hidden;
      display: flex;
      justify-content: flex-end;
      img {
        width: 25px;
        cursor: pointer;
        margin-left: 38px;
      }
    }
  }
  .add-button {
    width: 130px;
    cursor: pointer;
  }
  .flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .buttom-botton {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
