const sinceManagement = [

  {
    path: '/IStarted-zx', // 我的履职 政协
    name: 'IStarted-zx',
    component: resolve => (require(['@sinceManagement-zx/IStarted-zx/IStarted-zx'], resolve))
  },
  {
    path: '/SinceSituation-zx',
    name: 'SinceSituation-zx', // 履职情况统计 政协
    component: resolve => (require(['@sinceManagement-zx/SinceSituation/SinceSituation'], resolve))
  },
  {
    path: '/MySinceReport-zx',
    name: 'MySinceReport-zx',
    component: resolve => (require(['@sinceManagement-zx/SinceReport/MySinceReport'], resolve))
  },
  {
    path: '/SinceReportManagement-zx',
    name: 'SinceReportManagement-zx',
    component: resolve => (require(['@sinceManagement-zx/SinceReport/SinceReportManagement'], resolve))
  },
  {
    path: '/membersReport',
    name: 'membersReport',
    component: resolve => (require(['@sinceManagement/SinceReport/membersReport'], resolve))
  },
  {
    path: '/membersReportAdd',
    name: 'membersReportAdd',
    component: resolve => (require(['@sinceManagement/SinceReport/membersReportAdd'], resolve))
  },
  {
    path: '/membersStatistics',
    name: 'membersStatistics',
    component: resolve => (require(['@sinceManagement/SinceReport/membersStatistics'], resolve))
  },
  {
    path: '/membersReportDetail',
    name: 'membersReportDetail',
    component: resolve => (require(['@sinceManagement/SinceReport/membersReportDetail'], resolve))
  },
  {
    path: '/PerformanceReport', // 履职填报
    name: 'PerformanceReport',
    component: resolve => (require(['@sinceManagement-zx/PerformanceReport/PerformanceReport'], resolve))
  },
  {
    path: '/allocation',
    name: 'allocation',
    component: resolve => (require(['@sinceManagement-zx/SinceSituation/allocation'], resolve))
  },
  {
    path: '/duty-home',
    name: 'duty-home',
    component: () => import(/* 履职服务首页 */'@sinceManagement/duty-home/duty-home.vue')
  },
  {
    path: '/SinceSituation',
    name: 'SinceSituation',
    component: resolve => (require(['@sinceManagement/SinceSituation/SinceSituation'], resolve))
  },
  {
    path: '/IStarted',
    name: 'IStarted',
    component: resolve => (require(['@sinceManagement/IStarted/IStarted'], resolve))
  },
  {
    path: '/MySinceReport',
    name: 'MySinceReport',
    component: resolve => (require(['@sinceManagement/SinceReport/MySinceReport'], resolve))
  },
  {
    path: '/SinceReportManagement',
    name: 'SinceReportManagement',
    component: resolve => (require(['@sinceManagement/SinceReport/SinceReportManagement'], resolve))
  },
  {
    path: '/representativeAll',
    name: 'representativeAll',
    component: resolve => (require(['@sinceManagement/representative-All/representative-All'], resolve))
  },
  {
    path: '/PerformanceFiles',
    name: 'PerformanceFiles',
    component: resolve => (require(['@sinceManagement/PerformanceFiles/PerformanceFiles'], resolve))
  },
  {
    path: '/PerformanceAllocation',
    name: 'PerformanceAllocation',
    component: resolve => (require(['@sinceManagement/PerformanceAllocation/PerformanceAllocation'], resolve))
  },
  {
    path: '/PerformanceItem',
    name: 'PerformanceItem',
    component: resolve => (require(['@sinceManagement/PerformanceItem/PerformanceItem'], resolve))
  }
]
export default sinceManagement
