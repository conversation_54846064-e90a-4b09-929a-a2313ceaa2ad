<template>
  <div class="address-book-group">
    <search-button-box @search-click="search"
                       :resetButton="false">
      <template slot="button">
        <el-button type="primary"
                   icon="el-icon-plus"
                   v-permissions="'auth:txl:tree:add'"
                   @click="newData">新增</el-button>
      </template>
      <template slot="search">
        <el-input placeholder="请输入内容"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
    </search-button-box>
    <div class="information-box">
      <div class="information-tree-box">
        <div class="information-tree-text">
          <el-radio-group v-model="radio">
            <el-radio :label="11">{{$system()}}{{$position()}}</el-radio>
            <el-radio :label="12">{{$system()}}机关</el-radio>
            <el-radio :label="19">民主党派</el-radio>
            <el-radio :label="22">市工商联</el-radio>
            <el-radio :label="20">区市政协</el-radio>
          </el-radio-group>
        </div>
        <div class="information-tree">
          <zy-tree :tree="tree"
                   v-model="treeId"
                   :props="{ children: 'children', label: 'name'}"
                   @on-tree-click="choiceClick"></zy-tree>
        </div>
      </div>
      <div class="information-data-box">
        <div class="information-list">
          <zy-table>
            <el-table :data="tableData"
                      stripe
                      border
                      ref="table"
                      slot="zytable"
                      @select="selected"
                      @select-all="selectedAll">
              <el-table-column type="selection"
                               fixed="left"
                               width="60"></el-table-column>
              <el-table-column label="序号"
                               prop="sort">
              </el-table-column>
              <el-table-column label="姓名"
                               prop="name">
              </el-table-column>
              <el-table-column label="操作"
                               v-if="$hasPermission(['auth:txl:tree:edit','auth:txl:tree:del:id'])"
                               width="290">
                <template slot-scope="scope">
                  <el-button @click="modify(scope.row)"
                             type="primary"
                             v-permissions="'auth:txl:tree:edit'"
                             plain
                             size="mini">编辑</el-button>
                  <el-button @click="deleteClick(scope.row)"
                             type="danger"
                             v-permissions="'auth:txl:tree:del:id'"
                             plain
                             size="mini">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </zy-table>
        </div>
      </div>
    </div>
    <zy-pop-up v-model="show"
               title="新增分组">
      <address-book-group-new :id="id"
                              :sort="sort"
                              :treeType="radio"
                              @newCallback="newCallback"></address-book-group-new>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@mixins/tableData'
import addressBookGroupNew from './address-book-group-new'
export default {
  name: 'addressBookGroup',
  data () {
    return {
      keyword: '',
      radio: 11,
      treeId: '',
      tree: [],
      tableData: [],
      id: '',
      sort: 0,
      show: false
    }
  },
  inject: ['newTab'],
  mixins: [tableData],
  components: {
    addressBookGroupNew
  },
  mounted () {
    this.treeTist()
    this.treeTistUser()
  },
  watch: {
    radio (val) {
      this.treeTist()
      this.treeTistUser()
    }
  },
  methods: {
    search () {
      this.treeTistUser()
    },
    newData () {
      this.id = ''
      this.sort = this.sortMethods()
      this.show = true
    },
    sortMethods () {
      var sort = [0]
      this.tableData.forEach(item => {
        sort.push(Number(item.sort))
      })
      let max = sort[0]
      for (let i = 1; i < sort.length; i++) {
        if (max < sort[i]) {
          max = sort[i]
        }
      }
      return max + 1
    },
    modify (row) {
      this.id = row.id
      this.show = true
    },
    newCallback () {
      this.treeTist()
      this.treeTistUser()
      this.show = false
    },
    async treeTist () {
      const res = await this.$api.appManagement.treeTist({
        treeType: this.radio
      })
      var { data } = res
      this.treeId = ''
      this.tree = data
    },
    choiceClick (item) {
      this.treeTistUser()
    },
    async treeTistUser () {
      const res = await this.$api.appManagement.treeTist({
        parentId: this.treeId,
        treeType: this.radio,
        keyword: this.keyword
      })
      var { data } = res
      this.tableData = data
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    deleteClick (row) {
      this.$confirm('此操作将删除当前选中的分组, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.treeTistDel(row.id)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async treeTistDel (id) {
      const res = await this.$api.appManagement.treeTistDel(id)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.treeTist()
        this.treeTistUser()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.address-book-group {
  width: 100%;
  height: 100%;

  .information-box {
    height: calc(100% - 64px);
    width: 100%;
    display: flex;

    .information-tree-box {
     width: 355px;
      height: 100%;

      .information-tree-text {
        height: 52px;
        line-height: 52px;
        padding-left: 24px;
        font-size: $textSize16;
        background-color: #e6e5e8;
      }

      .information-tree {
        height: calc(100% - 52px);
      }
    }

    .information-data-box {
       width: calc(100% - 380px);
      height: 100%;
      border-left: 1px solid #e6e5e8;

      .information-list {
        height: calc(100%);
        width: 100%;
      }
    }
  }
}
</style>
