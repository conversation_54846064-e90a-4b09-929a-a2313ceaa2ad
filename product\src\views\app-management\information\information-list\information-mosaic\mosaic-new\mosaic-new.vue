<template>
  <div class="mosaic-new">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="序号"
                    class="form-title"
                    prop="title">
        <el-input placeholder="请输入序号"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="图片"
                    class="form-icon">
        <el-upload class="form-icon-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <img v-if="file.filePath"
               :src="file.filePath"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'mosaicNew',
  data () {
    return {
      form: {
        title: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入序号', trigger: 'blur' }
        ]
      },
      file: {}
    }
  },
  props: ['id', 'mosaicId'],
  mounted () {
    if (this.mosaicId) {
      this.picInfo()
    }
  },
  methods: {
    async picInfo () {
      const res = await this.$api.appManagement.picInfo(this.mosaicId)
      var { data: { sort, pic } } = res
      this.form.title = sort
      if (pic) {
        pic.filePath = pic.fullUrl
        this.file = pic
      }
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        this.file = data[0]
      })
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (JSON.stringify(this.file) === '{}') {
            this.$message({
              message: '请先上传图片再新增！',
              type: 'warning'
            })
            return
          }
          var url = '/zyinforeportpic/pic/add'
          if (this.mosaicId) {
            url = '/zyinforeportpic/pic/edit'
          }
          this.$api.systemSettings.generalAdd(url, {
            id: this.mosaicId,
            detailId: this.id,
            pic: this.file.id,
            sort: this.form.title
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
@import "./mosaic-new.scss";
</style>
