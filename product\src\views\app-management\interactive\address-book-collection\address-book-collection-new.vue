<template>
  <div class="address-book-collection-new">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="群名称"
                    class="form-title"
                    prop="name">
        <el-input placeholder="请输入群名称"
                  v-model="form.name"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="群主"
                    class="form-title"
                    v-if="!id">
        <div class="form-user-box"
             @click="userClick">
          <div v-if="!userData.length"
               class="form-user-box-text">请选择群主</div>
          <el-tag v-for="tag in userData"
                  :key="tag.userId"
                  size="medium"
                  closable
                  :disable-transitions="false"
                  @close.stop="remove(tag)">
            {{tag.name}}
          </el-tag>
        </div>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
    <zy-pop-up v-model="userShow"
               title="选择附议人">
      <candidates-user point="point_11"
                       :data="userData"
                       @userCallback="userCallback"></candidates-user>
    </zy-pop-up>
  </div>
</template>
<script>
export default {
  name: 'addressBookCollectionNew',
  data () {
    return {
      form: {
        name: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入群名称', trigger: 'blur' }
        ]
      },
      userShow: false,
      userData: []
    }
  },
  props: ['id', 'name'],
  mounted () {
    if (this.id) {
      this.form.name = this.name
    }
  },
  methods: {
    /**
     * 点击选择附议人
    */
    userClick () {
      this.userShow = !this.userShow
    },
    /**
     * 选择附议人的回调
    */
    userCallback (data, type) {
      if (type) {
        this.userData = data
      }
      this.userShow = !this.userShow
    },
    // 移除tag
    remove (data) {
      var userData = this.userData
      this.userData = userData.filter(item => item.userId !== data.userId)
    },
    async roleInfo () {
      const res = await this.$api.systemSettings.roleInfo(this.id)
      var { data: { name, sort } } = res
      this.form.name = name
      this.form.sort = sort
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var userId = ''
          var url = '/talkroup/add?'
          if (this.id) {
            url = '/talkroup/edit?'
            userId = ''
          } else {
            if (this.userData.length) {
              userId = this.userData[0].userId
            } else {
              this.$message({
                message: '请选择群主！',
                type: 'warning'
              })
              return
            }
          }
          this.$api.systemSettings.generalAdd(url, {
            id: this.id,
            groupName: this.form.name,
            groupOwner: userId,
            groupType: 1
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.address-book-collection-new {
  width: 692px;
  height: 100%;
  padding: 24px 40px;

  .form-user-box {
    width: 100%;
    min-height: 40px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    padding: 0 16px;
    padding-right: 40px;
    // overflow: hidden;
    padding-top: 6px;

    .form-user-box-text {
      color: #999;
      font-size: $textSize14;
      padding-bottom: 6px;
      line-height: 28px;
    }

    .el-tag {
      margin-bottom: 6px;
      margin-right: 12px;
    }

    &:hover {
      border-color: #199bc5;
    }

    &:focus {
      border-color: #199bc5;
    }
  }
}
</style>
