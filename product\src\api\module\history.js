import {
  // get,
  post,
  exportFile,
  postform,
  fileRequest
} from '../http'
const history = {
  paintingmanagement: { // 书画管理
    paintingledger: {
      list (params) {
        return post('calligraphypaintinginfo/list', params)
      },
      info (params) {
        return post(`calligraphypaintinginfo/info/${params}`)
      },
      add (params) {
        return post('calligraphypaintinginfo/add', params)
      },
      edit (params) {
        return post('calligraphypaintinginfo/edit', params)
      },
      del (params) {
        return post(`calligraphypaintinginfo/del/${params}`)
      },
      dels (params) {
        return post('calligraphypaintinginfo/dels', params)
      },
      // 下载模板
      importemplate (params) {
        exportFile('/calligraphypaintinginfo/importemplate', params)
      },
      // 导入
      import (params, text) {
        fileRequest('/calligraphypaintinginfo/import', params, text)
      },
      // 二维码
      previewCode (id) {
        return post('/calligraphypaintinginfo/getQrCalligraphyPainting', {
          id
        })
      }
    },
    paintingborrow: {
      list (params) {
        return post('calligraphypaintingborrow/list', params)
      },
      info (params) {
        return post(`calligraphypaintingborrow/info/${params}`)
      },
      edit (params) {
        return post('calligraphypaintingborrow/edit', params)
      },
      add (params) {
        return post('calligraphypaintingborrow/add', params)
      },
      del (params) {
        return post(`calligraphypaintingborrow/del/${params}`)
      },
      dels (params) {
        return post('calligraphypaintingborrow/dels', params)
      }
    },
    paintinginspection: {
      list (params) {
        return post('calligraphypaintinginspection/list', params)
      },
      info (params) {
        return post(`calligraphypaintinginspection/info/${params}`)
      },
      edit (params) {
        return post('calligraphypaintinginspection/edit', params)
      },
      add (params) {
        return post('calligraphypaintinginspection/add', params)
      },
      del (params) {
        return post(`calligraphypaintinginspection/del/${params}`)
      },
      dels (params) {
        return post('calligraphypaintinginspection/dels', params)
      }
    },
    paintingstorage: {
      list (params) {
        return post('calligraphypaintingposition/list', params)
      },
      info (params) {
        return post(`calligraphypaintingposition/info/${params}`)
      },
      edit (params) {
        return post('calligraphypaintingposition/edit', params)
      },
      add (params) {
        return post('calligraphypaintingposition/add', params)
      },
      del (params) {
        return post(`calligraphypaintingposition/del/${params}`)
      },
      dels (params) {
        return post('calligraphypaintingposition/dels', params)
      }
    },
    paintingcategory: {
      list (params) {
        return post('calligraphypaintingtype/list', params)
      },
      info (params) {
        return post(`calligraphypaintingtype/info/${params}`)
      },
      edit (params) {
        return post('calligraphypaintingtype/edit', params)
      },
      add (params) {
        return post('calligraphypaintingtype/add', params)
      },
      del (params) {
        return post(`calligraphypaintingtype/del/${params}`)
      },
      dels (params) {
        return post('calligraphypaintingtype/dels', params)
      }
    }
  },
  booksmanagement: { // 图书管理
    booksledger: {
      list (params) {
        return post('bookinfo/list', params)
      },
      info (params) {
        return post(`bookinfo/info/${params}`)
      },
      add (params) {
        return post('bookinfo/add', params)
      },
      edit (params) {
        return post('bookinfo/edit', params)
      },
      del (params) {
        return post(`bookinfo/del/${params}`)
      },
      dels (params) {
        return post('bookinfo/dels', params)
      },
      // 下载模板
      importemplate (params) {
        exportFile('/bookinfo/importemplate', params)
      },
      // 导入
      import (params, text) {
        fileRequest('/bookinfo/import', params, text)
      },
      // 二维码
      previewCode (id) {
        return post('/bookinfo/getQrBook', {
          id
        })
      }
    },
    booksborrow: {
      list (params) {
        return post('bookborrow/list', params)
      },
      info (params) {
        return post(`bookborrow/info/${params}`)
      },
      add (params) {
        return post('bookborrow/add', params)
      },
      edit (params) {
        return post('bookborrow/edit', params)
      },
      editState (params) {
        return post('bookborrow/editState', params)
      },
      del (params) {
        return post(`bookborrow/del/${params}`)
      },
      dels (params) {
        return post('bookborrow/dels', params)
      },
      editBorrowState (params) {
        return post('bookborrow/editBorrowState', params)
      }
    },
    bookcategory: {
      list (params) {
        return post('bookcategory/list', params)
      },
      info (params) {
        return post(`bookcategory/info/${params}`)
      },
      add (params) {
        return post('bookcategory/add', params)
      },
      edit (params) {
        return post('bookcategory/edit', params)
      },
      del (params) {
        return post(`bookcategory/del/${params}`)
      },
      dels (params) {
        return post('bookcategory/dels', params)
      }
    },
    bookinspection: {
      list (params) {
        return post('bookinspection/list', params)
      },
      info (params) {
        return post(`bookinspection/info/${params}`)
      },
      add (params) {
        return post('bookinspection/add', params)
      },
      edit (params) {
        return post('bookinspection/edit', params)
      },
      del (params) {
        return post(`bookinspection/del/${params}`)
      },
      dels (params) {
        return post('bookinspection/dels', params)
      }
    },
    bookposition: {
      list (params) {
        return post('bookposition/list', params)
      },
      info (params) {
        return post(`bookposition/info/${params}`)
      },
      add (params) {
        return post('bookposition/add', params)
      },
      edit (params) {
        return post('bookposition/edit', params)
      },
      del (params) {
        return post(`bookposition/del/${params}`)
      },
      dels (params) {
        return post('bookposition/dels', params)
      }
    },
    booktype: {
      list (params) {
        return post('bookcategory/list', params)
      },
      info (params) {
        return post(`bookcategory/info/${params}`)
      },
      add (params) {
        return post('bookcategory/add', params)
      },
      edit (params) {
        return post('bookcategory/edit', params)
      },
      del (params) {
        return post(`bookcategory/del/${params}`)
      },
      dels (params) {
        return post('bookcategory/dels', params)
      }
    }
  },
  patrolcycle: { // 书画与图书巡检
    enable (params) {
      return post(`inspectioncycle/info/${params}`)
    },
    enableedit (params) {
      return post('inspectioncycle/edit', params)
    },
    patrolstatus (params) {
      return post('calligraphypaintinginspection/editStatus', params)
    },
    bookstatus (params) {
      return post('bookinspection/editStatus', params)
    }
  },
  appointment: { // 预约
    appointmentlist: {
      list (params) {
        return post('literaryhistoryreservation/list', params)
      },
      info (params) {
        return post(`literaryhistoryreservation/info/${params}`)
      },
      add (params) {
        return post('literaryhistoryreservation/add', params)
      },
      edit (params) {
        return post('literaryhistoryreservation/edit', params)
      },
      del (params) {
        return post(`literaryhistoryreservation/del/${params}`)
      },
      dels (params) {
        return post('literaryhistoryreservation/dels', params)
      }
    }
  },
  solicitation: { // 征集
    clue: { // 征集线索
      list (params) {
        return post('lhcollectclues/list', params)
      },
      info (params) {
        return post(`lhcollectclues/info/${params}`)
      },
      add (params) {
        return post('lhcollectclues/add', params)
      },
      edit (params) {
        return post('lhcollectclues/edit', params)
      },
      del (params) {
        return post(`lhcollectclues/del/${params}`)
      },
      dels (params) {
        return post('lhcollectclues/dels', params)
      }
    },
    notice: { // 征集通知
      list (params) {
        return post('lhsolicitationnotice/list', params)
      },
      info (params) {
        return post(`lhsolicitationnotice/info/${params}`)
      },
      add (params) {
        return post('lhsolicitationnotice/add', params)
      },
      edit (params) {
        return post('lhsolicitationnotice/edit', params)
      },
      del (params) {
        return post(`lhsolicitationnotice/del/${params}`)
      },
      dels (params) {
        return post('lhsolicitationnotice/dels', params)
      }
    }
  },
  lhinfomation: { // 资讯
    lhinformationList (params) {
      return post('/lhinfodetail/list?', params)
    },
    lhinformationListInfo (params) {
      return post(`/lhinfodetail/info/${params}`)
    },
    lhgetstructure (params) {
      return post('/valley/getstructure', params)
    },
    lhpushInfo (params) {
      return post('/valley/pushInfo', params)
    },
    lhofficeList (params) {
      return post('/lhinfostructurerelation/list?', params)
    },
    lhinfostructurerelation (params) {
      return post('/lhinfostructurerelation/dels', params)
    },
    lhinformationListDel (params) {
      return post('/lhinfodetail/dels', params)
    },
    lheditInfo (params) {
      return post('/valley/lheditInfo', params)
    },
    lhinformationBatchUpdate (params) {
      return post('/lhinfodetail/batchUpdate', params)
    },
    lhpicList (params) {
      return post('/lhinforeportpic/pic/list?', params)
    },
    lhpicInfo (params) {
      return post(`/lhinforeportpic/pic/info/${params}`)
    },
    lhpicDel (params) {
      return post('/lhinforeportpic/dels', params)
    },
    lheditIsCheck (params) {
      return post('/lhinforeportpic/scrolling/report/editIsCheck', params)
    },
    lhreportList (params) {
      return post('/lhinforeportpic/scrolling/report/list?', params)
    },
    lhreportInfo (params) {
      return post(`/lhinforeportpic/scrolling/report/${params}`)
    },
    lhpictureList (params) {
      return post('/lhinforeportpic/scrolling/pic/list?', params)
    },
    lhpictureInfo (params) {
      return post(`/lhinforeportpic/scrolling/pic/${params}`)
    },
    lhassociatedList (params) {
      return post('/lhinforelation/list?', params)
    },
    lhassociatedAddList (params) {
      return post('/lhinforelation/addList?', params)
    },
    lhassociatedAdd (params) {
      return post('/lhinforelation/add', params)
    },
    lhassociatedDel (params) {
      return post('/lhinforelation/dels', params)
    },
    lhinformationColumn (params) {
      return post('/lhinfostructure/list?', params)
    },
    lhinformationColumnTree (params) {
      return post('/lhinfostructure/tree', params)
    },
    lhinformationColumnInfo (params) {
      return post(`/lhinfostructure/info/${params}`)
    },
    lhinformationColumnDel (params) {
      return post('/lhinfostructure/dels', params)
    }
  },
  notice: { // 通知公告
    lhnoticeList (params) { // 通知列表
      return post('/lhnotice/list?', params)
    },
    lhnoticeaddedit (url, params) { // 新增详情
      return post(url, params)
    },
    lhnoticeinfo (params) { //  详情
      return post(`/lhnotice/info/${params}`)
    },
    lhnoticedels (params) { //  删除
      return post('/lhnotice/dels', params)
    },
    lhreadingDetail (params) { //  阅读详情
      return post(`/lhnotice/readingDetail?id=${params}`)
    },
    lhsendRemindSms (params) { //  短信提醒
      return post('/lhnotice/sendRemindSms', params)
    },
    lhnoticereturnoptionlist (params) { //  选项列表
      return post('/lhnoticereturnoption/list', params)
    },
    lhnoticereturnoptioninfo (params) { //  选项获取详情
      return post(`/lhnoticereturnoption/info/${params}`)
    },
    lhnoticereturnlist (params) { //  回执列表
      return post('/lhnoticereturn/list', params)
    },
    lhnoticereturnoptionadd (url, params) { //  选项新增
      return post(url, params)
    },
    lhnoticereturnoptiondels (params) { //  选项新增
      return post('/lhnoticereturnoption/dels', params)
    },
    lhnoticereturndels (params) { //  回执删除
      return post('/lhnoticereturn/dels', params)
    },
    uploadFile (params) {
      return postform('/attachment/uploadFile', params, { timeout: 80000 })
    }
  },
  visualization: { // 文史馆可视化
    // 历史提案
    historicalProposalList (params) {
      return post('/literaturehistoryproposal/list?', params)
    },
    historicalProposalAdd (params) {
      return post('/literaturehistoryproposal/add?', params)
    },
    historicalProposalEdit (params) {
      return post('/literaturehistoryproposal/edit?', params)
    },
    historicalProposalInfo (params) {
      return post(`/literaturehistoryproposal/info/${params}`)
    },
    historicalProposalDel (params) {
      return post('/literaturehistoryproposal/dels?', params)
    },
    // 政协百科
    wikiList (params) {
      return post('/literaturewiki/list?', params)
    },
    wikiAdd (params) {
      return post('/literaturewiki/add?', params)
    },
    wikiEdit (params) {
      return post('/literaturewiki/edit?', params)
    },
    wikiInfo (params) {
      return post(`/literaturewiki/info/${params}`)
    },
    wikiDel (params) {
      return post('/literaturewiki/dels?', params)
    },
    // 提案知识库
    knowledgeList (params) {
      return post('/literatureknowledge/list?', params)
    },
    knowledgeAdd (params) {
      return post('/literatureknowledge/add?', params)
    },
    knowledgeEdit (params) {
      return post('/literatureknowledge/edit?', params)
    },
    knowledgeInfo (params) {
      return post(`/literatureknowledge/info/${params}`)
    },
    knowledgeDel (params) {
      return post('/literatureknowledge/dels?', params)
    },
    // 配置
    termconfigList (params) {
      return post('/literaturetermconfig/list?', params)
    },
    termconfigAdd (params) {
      return post('/literaturetermconfig/add?', params)
    },
    termconfigEdit (params) {
      return post('/literaturetermconfig/edit?', params)
    },
    termconfigInfo (params) {
      return post(`/literaturetermconfig/info/${params}`)
    },
    termconfigDel (params) {
      return post('/literaturetermconfig/dels?', params)
    },
    termconfigIsEnable (params) {
      return post('/literaturetermconfig/isEnable?', params)
    },
    termconfigIsCurrentTeam (params) {
      return post('/literaturetermconfig/currentTeam?', params)
    }
  }
}
export default history
