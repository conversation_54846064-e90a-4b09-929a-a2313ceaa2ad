<template>
  <div class="zy-widget">
    <span class="label"
          :style="`width: ${labelW};`">{{ label }}</span>
    <div class="zy-widget-slot">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'zy-widget',
  props: {
    label: String,
    labelW: {
      type: String,
      default: () => '100px'
    }
  }
}
</script>

<style lang="scss" scoped>
.zy-widget {
  margin-right: 24px;
  margin-bottom: 12px;

  .label {
    line-height: 40px;
    margin-right: 10px;
    float: left;
    width: 100px;
    text-align: right;
  }

  .zy-widget-slot {
    float: left;
  }
}
</style>
