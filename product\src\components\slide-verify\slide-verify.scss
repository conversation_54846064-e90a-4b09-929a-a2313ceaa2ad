.slide-verify-popover {
  padding: 0;
}

.slide-verify-box {
  position: relative;
}

.slide-verify-block {
  position: absolute;
  left: 0;
  top: 0;
}

.slide-verify-refresh-icon {
  position: absolute;
  right: 0;
  top: 0;
  width: 34px;
  height: 34px;
  cursor: pointer;
  background: url("../../assets/images/icon_light.png") 0 -437px;
  background-size: 34px 471px;
}

.slide-verify-slider {
  position: relative;
  text-align: center;
  width: 100%;
  height: 40px;
  line-height: 38px;
  background: #fff;
  color: #45494c;
  border: 1px solid #e4e7eb;
  border-radius: 4px;
  overflow: hidden;

  &:focus {
    outline: none;
  }
}

.slide-verify-slider-mask {
  position: absolute;
  left: 0;
  top: 0;
  height: 40px;
  border: 0 solid $zy-color;
  background: $zy-withColor;
}

.slide-verify-slider-mask-item {
  position: absolute;
  top: -1px;
  left: 0;
  width: 40px;
  height: 40px;
  background: #fff;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: background 0.2s linear;
}

.slide-verify-slider-mask-item:hover {
  background: $zy-color;
}

.slide-verify-slider-mask-item:hover .slide-verify-slider-mask-item-icon {
  background-position: 0 -13px;
}

.slide-verify-slider-mask-item-icon {
  position: absolute;
  top: 15px;
  left: 13px;
  width: 14px;
  height: 12px;
  background: url("../../assets/images/icon_light.png") 0 -26px;
  background-size: 34px 471px;
}

.container-active .slide-verify-slider-mask-item {
  height: 38px;
  top: -1px;
  border: 1px solid $zy-color;

  .slide-verify-slider-mask-item-icon {
    top: 13px;
  }
}

.container-active .slide-verify-slider-mask {
  height: 38px;
  border-width: 1px;
}

.container-success .slide-verify-slider-mask-item {
  height: 38px;
  top: -1px;
  border: 1px solid #67C23A;
  background-color: #67C23A !important;
}

.container-success .slide-verify-slider-mask {
  height: 38px;
  border: 1px solid #67C23A;
  background-color: rgba($color:#67C23A, $alpha: .1);
}

.container-success .slide-verify-slider-mask-item-icon {
  background-position: 0 0 !important;
}

.container-fail .slide-verify-slider-mask-item {
  height: 38px;
  top: -1px;
  border: 1px solid #F56C6C;
  background-color: #F56C6C !important;
}

.container-fail .slide-verify-slider-mask {
  height: 38px;
  border: 1px solid #F56C6C;
  background-color: rgba($color:#F56C6C, $alpha: .1);
}

.container-fail .slide-verify-slider-mask-item-icon {
  top: 14px;
  background-position: 0 -82px !important;
}

.slide-verify-slider-text {
  color: #999;
}

.container-active .slide-verify-slider-text,
.container-success .slide-verify-slider-text,
.container-fail .slide-verify-slider-text {
  display: none;
}