/*
 * @Author: zlei.zhou
 * @Date: 2018-04-24 13:22:18
 * @Last Modified by: mikey.zhaopeng
 * @Last Modified time: 2019-01-08 10:46:56
 */
'use strict';
var project_name = ''; //项目名称
// var server_path = 'http://114.55.219.206:9471/'; //服务器地址
var server_path = 'http://47.106.134.215:9000/'; //服务器地址
var url_config = {
    upload_url: server_path + "file/upload?proPath=" + project_name, //上传文件地址
    downloadById_url: server_path + "file/downloadById" //根据文件id下载文件
};
var file_type = {
    o: "o", //原始文件
    t: "t", //缩略图
    s: "s", //小图
    m: "m", //中图
    l: "l", //大图
    p: "p", //pdf
    h: "h" //html
};

/**
 *文件上传预览
 * @param {string} el 文件上传id
 * @param {boolean} mode 单文件和多文件上传模式  默认多文件
 * @param {boolean} showPreview 是否在上传之前预览  默认预览
 */
function Upload(el) {
    var mode = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
    var showPreview = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;

    // 上传到服务器
    this.ajaxUpload = function(file) {
        var self = this;
        var formData = new FormData();
        $.each(file, function(i, v) {
            formData.append('file' + i, v); //将文件转成二进制形式
        });
        $.ajax({
            type: "post",
            url: url_config.upload_url,
            async: true,
            contentType: false, //这个一定要写
            processData: false, //这个也一定要写，不然会报错
            data: formData,
            dataType: 'text', //返回类型，有json，text，HTML。这里并没有jsonp格式，所以别妄想能用jsonp做跨域了。
            xhr: function xhr() {
                //获取ajaxSettings中的xhr对象，为它的upload属性绑定progress事件的处理函数
                var myXhr = $.ajaxSettings.xhr();
                if (myXhr.upload) {
                    myXhr.upload.onloadstart = function() {
                        layer.msg('上传中...', {
                            icon: 16,
                            shade: [0.5, '#f5f5f5'],
                            scrollbar: false,
                            offset: '50%',
                            time: 0
                        });
                    };
                }
                return myXhr; //xhr对象返回给jQuery使用
            },
            success: function success(data) {
                if (null != data && data != "") {
                    var json = JSON.parse(data);
                    if (json.success) {
                        self.afterUpload(json.data.succFiles);
                        layer.msg('上传完成！', {
                            time: 1000,
                            icon: 1
                        });
                    } else {
                        layer.msg("上传失败：失败码-" + json.errorCode + ";信息-" + json.message, {
                            "icon": 7
                        });
                    }
                } else {
                    layer.msg("上传失败", {
                        "icon": 7
                    });
                }
            },
            error: function error(XMLHttpRequest, textStatus, errorThrown, data) {
                layer.msg("上传失败：" + errorThrown, {
                    "icon": 7
                });
            }
        });
    };
    // 上传前钩子函数
    this.beforeUpload = function(file) {
        var flag = true;
        /* $.each(file, function (i, v) {
            //判断file的类型是不是图片类型。
            if (!/image\/\w+/.test(v.type)) {
                alert(v.name + "不是图片")
                return flag = false;
            }
            //判断file的大小是否大于10M。
            if (v.size > 10240000) {
                alert(v.name + "大小超过10M");
                return flag = false;
            }
        }); */
        return flag;
    };
    // 初始化上传
    this.init = function() {
        var self = this;
        $(el).change(function() {
            var file = $(this).get(0).files;
            if (self.beforeUpload(file)) {
                if (showPreview) {
                    self.preview(file);
                }
                self.ajaxUpload(file);
            }
        });
        self.delPreview();
        return this;
    };
    // 预览本地文件
    this.preview = function(file) {
        $.each(file, function(i, v) {
            var fReader = new FileReader();
            fReader.readAsDataURL(v);
            fReader.onload = function(e) {
                console.log(this);
            };
        });
    };
    // 上传完成钩子函数
    this.afterUpload = function(imgList) {
    	//清空缩略图信息
    	$(".upload-wraper").find(".upload-preview").remove();
    	
        var self = this;
        if (!imgList || !imgList.length) return;
        $.each(imgList, function(i, v) {
            //判断文件类型
            var filetype = v.fileType;
            if (mode) {
                if (filetype == 'image') {
                    $(el).parents('.upload-wraper').prepend('<div class="upload-preview">\n<img src="' + self.getDownloadUrlById(v.fileId) + '" data-id="' + v.fileId + '" data-type="' + filetype + '" data-zoom>\n <span class="preview-del image-del">\xD7</span>\n <a href="' + self.getDownloadUrlById(v.fileId) + '" class="image-download"><i class="fa fa-arrow-down"></i></a></div>');
                    $("img[data-id='" + v.fileId + "']").parent().addClass("is-loading");
                    $("img[data-id='" + v.fileId + "']").get(0).onload = function() {
                        $(this).parent().removeClass("is-loading");
                    };
                } else {
                    $(el).parents('.upload-wraper').prepend('<div class="upload-preview" >\n                        <a class="file-link" href="' + self.getDownloadUrlById(v.fileId) + '" data-id="' + v.fileId + '" data-type="' + filetype + '">\n                            <i class="fa  fa-file-text"></i>\n                        </a>\n                            <span class="preview-del">\xD7</span>\n                    </div>');
                }
            } else {
                if (filetype == 'image') {
                    if ($(el).parents('.upload-wraper').find('.upload-preview').length) {
                        $(el).parents('.upload-wraper').find('.upload-preview').html('<img src="' + self.getDownloadUrlById(v.fileId) + '" data-id="' + v.fileId + '" data-type="' + filetype + '" data-zoom>\n <span class="preview-del image-del">\xD7</span>\n <a href="' + self.getDownloadUrlById(v.fileId) + '" class="image-download"><i class="fa fa-arrow-down"></i></a>');
                        $("img[data-id='" + v.fileId + "']").parent().addClass("is-loading");
                        $("img[data-id='" + v.fileId + "']").get(0).onload = function() {
                            $(this).parent().removeClass("is-loading");
                        };
                    } else {
                        $(el).parents('.upload-wraper').prepend('<div class="upload-preview">\n<img src="' + self.getDownloadUrlById(v.fileId) + '" data-id="' + v.fileId + '" data-type="' + filetype + '" data-zoom>\n <span class="preview-del image-del">\xD7</span>\n <a href="' + self.getDownloadUrlById(v.fileId) + '" class="image-download"><i class="fa fa-arrow-down"></i></a></div>');
                        $("img[data-id='" + v.fileId + "']").parent().addClass("is-loading");
                        $("img[data-id='" + v.fileId + "']").get(0).onload = function() {
                            $(this).parent().removeClass("is-loading");
                        };
                    }

                } else {
                    if ($(el).parents('.upload-wraper').find('.upload-preview').length) {
                        $(el).parents('.upload-wraper').find('.upload-preview').html('<a class="file-link" href="' + self.getDownloadUrlById(v.fileId) + '" data-id="' + v.fileId + '" data-type="' + filetype + '">\n                            <i class="fa  fa-file-text"></i>\n                        </a>\n                            <span class="preview-del">\xD7</span>');
                    } else {
                        $(el).parents('.upload-wraper').prepend('<div class="upload-preview" >\n                        <a class="file-link" href="' + self.getDownloadUrlById(v.fileId) + '" data-id="' + v.fileId + '" data-type="' + filetype + '">\n                            <i class="fa  fa-file-text"></i>\n                        </a>\n                            <span class="preview-del">\xD7</span>\n                    </div>');
                    }
                }
            }

        });
    };
    // 数据预览
    this.dataPreview = function(imgList, showDelBtn, preEl) {
        var self = this;
        var showDelBtn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
        var preEl = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : '.upload-wraper';
        var imageDelBtn = '<span class="preview-del image-del">\xD7</span>\n';
        var linkDelBtn = '<span class="preview-del">\xD7</span>\n';
        var downLoadClass = '';
        if (!showDelBtn) {
            imageDelBtn = '';
            linkDelBtn = '';
            downLoadClass = 'isread'
        }
        if (!imgList || !imgList.length) return;
        $.each(imgList, function(i, v) {
            //判断文件类型
            if (v.fileType === 'image') {
                $(preEl).prepend('<div class="upload-preview">\n                        <img src="' + self.getDownloadUrlById(v.fileId) + '" data-id="' + v.fileId + '" data-type="' + v.fileType + '" data-zoom>\n ' + imageDelBtn + ' <a href="' + self.getDownloadUrlById(v.fileId) + '" class="image-download ' + downLoadClass + '"><i class="fa fa-arrow-down"></i></a></div>');
                $("img[data-id='" + v.fileId + "']").parent().addClass("is-loading");
                $("img[data-id='" + v.fileId + "']").get(0).onload = function() {
                    $(this).parent().removeClass("is-loading");
                };
            } else {
                $(preEl).prepend('<div class="upload-preview" >\n                        <a class="file-link" href="' + self.getDownloadUrlById(v.fileId) + '" data-id="' + v.fileId + '" data-type="' + v.fileType + '">\n                            <i class="fa  fa-file-text"></i>\n                        </a>\n                          ' + linkDelBtn + '                    </div>');
            }
        });
    };
    // 通过文件id获取文件url
    this.getDownloadUrlById = function(id) {
        var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : file_type.o;

        return url_config.downloadById_url + "?fileId=" + id+"&filetype="+type;;
    };
    // 删除展示文件
    this.delPreview = function() {
        $(".upload-wraper").on('click', '.preview-del', function(e) {
            $(this).parent().remove();
            return false;
        });
    };
}