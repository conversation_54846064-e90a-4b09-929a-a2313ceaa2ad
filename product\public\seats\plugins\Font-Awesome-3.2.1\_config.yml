safe:             false
port:             7998
baseurl:          /Font-Awesome/  # Where GitHub serves the project up from
url:              http://localhost:7998

source:           src
destination:      _gh_pages
plugins:          src/_plugins

pygments:         true
permalink:        pretty

# ensures SCSS files are compiled
include:          [_bootstrap.scss, _core.scss, _extras.scss, _icons.scss, _mixins.scss, _path.scss, _variables.scss]

# used in building icon pages
icon_meta:        src/icons.yml
icon_layout:      icon.html    # Relative to _layouts directory
icon_destination: icon         # Relative to destination

fontawesome:
  version:        3.2.1
  minor_version:  3.2
  url:            http://fontawesome.io
  legacy_url:     http://fortawesome.github.com/Font-Awesome/
  blog_url:       http://blog.fontawesome.io
  twitter:        fontawesome
  tagline:        The iconic font designed for Bootstrap
  author:
    name:         <PERSON>
    email:        <EMAIL>
    twitter:      byscuits
    work:
      name:       Kyruus
      url:        http://kyruus.com
      title:      Lead Product Designer
  github:
    url:          https://github.com/FortAwesome/Font-Awesome
    project:      Font-Awesome
    org:          FortAwesome
  license:
    font:
      version:      SIL OFL 1.1
      url:          http://scripts.sil.org/OFL
    code:
      version:      MIT License
      url:          http://opensource.org/licenses/mit-license.html
    documentation:
      version:      CC BY 3.0
      url:          http://creativecommons.org/licenses/by/3.0/

bootstrap:
  version:        2.3.2
  url:            http://getbootstrap.com
