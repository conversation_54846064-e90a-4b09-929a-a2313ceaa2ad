// 导入封装的方法
import {
  get,
  post,
  postform,
  exportFile,
  fileRequest
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const getUrl = () => {
  return JSON.parse(sessionStorage.getItem('BigDataUrl' + sessionStorage.getItem('theme')))
}
const systemSettings = {
  generalAdd (url, params) {
    return post(url, params)
  },
  poinexistsids (type, params) {
    return post(`/poinexistsids/${type}`, params)
  },
  // 菜单和权限接口
  menuTree (params) {
    return post('/menu/tree', params)
  },
  menudel (params) {
    return post(`/menu/del/${params}`)
  },
  menuInfo (params) {
    return post(`/menu/info/${params}`)
  },
  menuAuths (params) {
    return post('/menu/auths?', params)
  },
  flagoutside (params) {
    return post('/menu/flagoutside?', params)
  },
  // 角色接口
  roleList (params) {
    return post('/role/list?', params)
  },
  roleDel (params) {
    return post('/role/del', params)
  },
  roleInfo (params) {
    return post(`/role/info/${params}`)
  },
  menutreeWithrole (params) {
    return post('/role/menutree/withrole?', params)
  },
  roleSavemenu (params) {
    return post('/role/savemenu?', params)
  },
  menutreeForole (params) {
    return post('/role/menutree/forole?', params)
  },
  roleChooseauths (params) {
    return post('/role/chooseauths', params)
  },
  roleSaveauth (params) {
    return post('/role/saveauth', params)
  },
  roleSaveuser (params) {
    return post('/role/saveuser?', params)
  },
  roleWithuser (params) {
    return post('/role/info/withuser?', params)
  },
  roleRemoveuser (params) {
    return post('/role/removeuser', params)
  },
  roleMinisetAuths (params) {
    return post('/role/miniset/auths', params)
  },
  roleMiniset (params) {
    return post('/role/miniset', params)
  },
  dataFind (params) {
    return post('/role/data/find?', params)
  },
  dataSave (params) {
    return post('/role/data/save?', params)
  },
  modulerolelist (params) {
    return post('/module/rolelist?', params)
  },
  saverolemodules (params) {
    return post('/module/saverolemodules?', params)
  },
  // 字典接口
  dictionaryList (params) {
    return post('/dictionary/list?', params)
  },
  dictionaryType (params) {
    return post('/dictionary/types', params)
  },
  dictionaryInfo (params) {
    return post(`/dictionary/info/${params}`)
  },
  dictionaryDel (params) {
    return post(`/dictionary/del/${params}`)
  },
  dictionaryPubkvs (params) {
    return post('/dictionary/pubkvs?', params)
  },
  // 树接口
  treeList (params) {
    return post('/tree/list?', params)
  },
  treeType (params) {
    return post('/tree/types', params)
  },
  treeInfo (params) {
    return post(`/tree/info/${params}`)
  },
  treeSelect (params) {
    return post('/tree/list', params)
  },
  treeDel (params) {
    return post(`/tree/del/${params}`)
  },
  // 用户接口
  userList (params) {
    return post('/wholeuser/list?', params)
  },
  userDel (params) {
    return post('/wholeuser/batch/del', params)
  },
  resetpwd (params) {
    return post('/wholeuser/batch/resetpwd', params)
  },
  userInfo (params) {
    return post(`/wholeuser/info/${params}`)
  },
  userEnable (params) {
    return post('/wholeuser/batch/startuse', params)
  },
  userDisable (params) {
    return post('/wholeuser/batch/stopuse', params)
  },
  tagroups (params) {
    return post('/wholeuser/tagroups', params)
  },
  randmobile (params) {
    return post('/wholeuser/randmobile', params)
  },
  buildaccount (params) {
    return post('/wholeuser/buildaccount?', params)
  },
  finduser (params) {
    return post('/wholeuser/finduser', params)
  },
  useroles (params) {
    return post('/role/useroles?', params)
  },
  // 游客用户管理
  touristList (params) {
    return post('/masses/list', params)
  },
  touristedit (params) {
    return post('/masses/edit', params)
  },
  touristinfo (params) {
    return post(`/masses/info/${params}`)
  },
  touristdel (params) {
    return post(`/masses/del/${params}`)
  },
  touristdels (params) {
    return post('/masses/dels', params)
  },
  touristadd (params) {
    // 增通知模板
    return post('/masses/add', params)
  },
  // 标签管理
  labeluserLabels (params) {
    return post('/labeluser/labels', params)
  },
  labeluserUsers (params) {
    return post('/labeluser/users?', params)
  },
  labelSaveuser (params) {
    return post('/labeluser/saveuser', params)
  },
  lebUsredel (params) {
    return post('/labeluser/removeuser', params)
  },
  // 配置管理
  sysconfig (params) {
    return post('/sysconfig', params)
  },
  rightWordList (params) {
    return get(getUrl() + '/words/rightWordList', params)
  },
  addRightWord (params) {
    return post(getUrl() + '/words/addRightWord', params)
  },
  deleteRightWord (params) {
    return post(getUrl() + '/words/deleteRightWord', params)
  },
  mgcWordList (params) {
    return get(getUrl() + '/words/mgcWordList', params)
  },
  addMgcWord (params) {
    return post(getUrl() + '/words/addMgcWord', params)
  },
  deleteMgckWord (params) {
    return post(getUrl() + '/words/deleteMgcWord', params)
  },
  delconfig (params) {
    return post('/delconfig?', params)
  },
  // 选人管理
  labeluserPoints (params) {
    return post('/labeluser/points', params)
  },
  labeluserPointlabel (params) {
    return post('/labeluser/pointlabel?', params)
  },
  // 日志接口
  syslog (params) {
    return post('/syslog', params)
  },
  // 日志详情
  syslogInfo (params) {
    return post(`/syslog/info/${params}`)
  },
  // 登录日志接口
  loginlog (params) {
    return post('/loginlog?', params)
  },
  // 错误日志接口
  errorlog (params) {
    return post('/errorlog', params)
  },
  exportFile (url, params, text) {
    fileRequest(url, params, text)
  },
  // 任务接口
  taskList (params) {
    return post('/task/list', params)
  },
  taskOnoff (params) {
    return post('/task/onoff', params)
  },
  // 用户关系管理
  userelationType (params) {
    return post('/userelation/types', params)
  },
  userelationTree (params) {
    return post('/userelation/tree?', params)
  },
  userelationSave (params) {
    return post('/userelation/save?', params)
  },
  setList (params) {
    return post('/smsSend/setList', params)
  },
  smsSendList (params) {
    return post('/smsSend/list', params)
  },
  smsSendInfo (params) {
    return post(`/smsSend/info/${params}`)
  },
  smsSenddDel (params) {
    return post('/smsSend/dels', params)
  },
  sendVCode (params) {
    return get('/smsSend/sendVcode', params)
  },
  smstemplate (params) {
    return post('/smstemplate/list?', params)
  },
  smstemplateInfo (params) {
    return post(`/smstemplate/info/${params}`)
  },
  smstemplateDel (params) {
    return post('/smstemplate/dels', params)
  },
  smsreceived (params) {
    return post('/smsreceived/list?', params)
  },
  smsreceivedInfo (params) {
    return post(`/smsreceived/info/${params}`)
  },
  smsreceivedDel (params) {
    return post('/smsreceived/dels', params)
  },
  // 下载模板
  importemplate (params) {
    exportFile('/wholeuser/importemplate', params)
  },
  // 导入代表或全国代表
  import (params, text) {
    fileRequest('/wholeuser/import', params, text)
  },
  // 下载模板
  companyuserImportemplate (params) {
    exportFile('/companyuser/importemplate', params)
  },
  // 导入代表或全国代表
  companyuserImport (params, text) {
    fileRequest('/companyuser/import', params, text)
  },
  // 轮播图列表
  loginimgList (params, text) {
    return post('/loginimg/list', params, text)
  },
  // 轮播图删除
  loginimgDel (params, text) {
    return post('/loginimg/del?', params, text)
  },
  apkfileAdd (params) { // 新增apk
    return postform('/apkfile/add', params, { timeout: 800000 })
  },
  apkfileSetLatest (params) { // 设置最新版本
    return post('/apkfile/setLatest', params)
  },
  apkfileList (params) { // apk列表
    return post('/apkfile/list', params)
  },
  apkfileDels (params) { // 删除apk
    return post('/apkfile/dels', params)
  },
  apkfileInfo (params) { // apk
    return post('/apkfile/Info', params)
  },
  apkfilePublicLatest (params) { // 公开接口：获取最新版本apk
    return post('/apkfile/publicLatest', params)
  },
  systemfeedbackList (params) {
    return post('/systemfeedback/list?', params)
  },
  systemfeedbackDels (params) {
    return post('/systemfeedback/dels', params)
  },
  systemfeedbackInfo (params) {
    return post(`/systemfeedback/info/${params}`)
  },
  systemfeedbackreplyAdd (params) {
    return post('/systemfeedbackreply/add?', params)
  },
  systemfeedbackreplyAEdit (params) {
    return post('/systemfeedbackreply/edit?', params)
  },
  systemfeedbackreplyList (params) {
    return post('/systemfeedbackreply/list?', params)
  },
  systemfeedbackreplyDels (params) {
    return post('/systemfeedbackreply/dels', params)
  },
  systemfeedbackreplyInfo (params) {
    return post(`/systemfeedbackreply/info/${params}`)
  },
  sharePublicShare (params) { // 下载计数
    return post('/share/publicShare', params)
  }
}
export default systemSettings
