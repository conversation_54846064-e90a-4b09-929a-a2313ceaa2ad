<template>
  <div class="activity-leave-verify">
    <search-box @search-click="search" @reset-click="reset" title="请假审核筛选">
      <zy-widget label="关键字">
        <el-input v-model="searchParams.keyword" placeholder="请输入关键字" clearable @keyup.enter.native="search"></el-input>
      </zy-widget>
      <zy-widget label="审核状态">
        <el-select v-model="searchParams.status" placeholder="请选择状态" clearable>
          <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </zy-widget>
    </search-box>
    <div class="qd-list-wrap">
      <div class="qd-btn-box">
        <el-button type="primary" @click="handleVerify(1)">审核通过</el-button>
        <el-button type="primary" @click="handleVerify(2)">审核不通过</el-button>
        <el-button type="danger" icon="el-icon-delete" plain @click="handleBatchDelete">删除</el-button>
      </div>
      <div class="qd-table-box">
        <zy-table>
          <el-table :data="list" stripe border ref="table" slot="zytable" @selection-change="handleSelectionChange">
            <el-table-column type="selection" fixed="left" width="60"></el-table-column>
            <el-table-column label="请假人" min-width="120">
              <template slot-scope="scope">
                <el-button type="text" @click="handleInfo(scope.row.id)">
                  {{scope.row.userNames}}
                </el-button>
              </template>
            </el-table-column>
            <el-table-column label="请假时间" prop="leaveDatetime" min-width="180"></el-table-column>
            <el-table-column label="请假理由" prop="reason" min-width="360" show-overflow-tooltip></el-table-column>
            <el-table-column label="审核状态" prop="status" min-width="100">
              <!-- <template slot-scope="scope">
                <span v-if="scope.row.status === 0">未审核</span>
                <span v-if="scope.row.status === 1">审核通过</span>
                <span v-if="scope.row.status === 1">审核不通过</span>
              </template> -->
            </el-table-column>
          </el-table>
        </zy-table>
      </div>
      <div class="qd-page-box">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page.sync="page" :page-sizes="[10, 20, 50, 80, 100, 200, 500]" :page-size.sync="pageSize" background layout="total, sizes, prev, pager, next, jumper" :total="total"></el-pagination>
      </div>
    </div>
    <zy-pop-up v-model="isInfo" title="请假详情">
      <info :id="id" />
    </zy-pop-up>
  </div>
</template>

<script>
import table from '@mixins/table.js'
import { filterParams, checkParams } from '@/common/handleParams'
import info from './widget/info'
export default {
  mixins: [table],
  components: { info },
  data() {
    return {
      searchParams: {
        keyword: '',
        status: null
      },
      statusList: [
        { value: '0', label: '未审核' },
        { value: '1', label: '审核通过' },
        { value: '2', label: '审核不通过' }
      ],
      isInfo: false,
      id: null
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        meetId: this.$route.query.id
      }
      params = Object.assign(params, filterParams(this.searchParams))
      this.$api.activity.leave.list(params).then(res => {
        var { data, total } = res
        this.list = data
        this.total = total
      })
    },
    search() {
      if (!checkParams(this.searchParams)) {
        return this.$message.warning('请输入关键字或选择想要搜索的')
      }
      this.getList()
    },
    reset() {
      this.searchParams = {
        keyword: '',
        status: ''
      }
      this.getList()
    },
    handleDelete(ids) {
      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activity.leave.dels(ids).then((res) => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消删除')
        return false
      })
    },
    handleBatchDelete() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要删除的项')
      }
      this.handleDelete(this.selectionList.map((v) => v.id).join(','))
    },
    handleVerify(type) {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要审核的项')
      }
      this.$confirm('此操作将修改选中的项的审核状态, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activity.leave.verify({ ids: this.selectionList.map((v) => v.id).join(','), status: type }).then((res) => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('审核成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消审核')
        return false
      })
    },
    handleInfo(id) {
      this.id = id
      this.isInfo = true
    }
  }
}
</script>
<style lang="scss" scoped>
.activity-leave-verify {
  width: 100%;
  height: 100%;
}
</style>
