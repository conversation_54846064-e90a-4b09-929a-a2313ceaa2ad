<section id="animated-spinner">
  <h2 class="page-header">Animated Spinner</h2>
  <div class="row">
    <div class="span3">
      Use the <code>icon-spin</code> class to get any icon to rotate. Works well with <code>icon-spinner</code> and
      <code>icon-refresh</code>.
    </div>
    <div class="span9">
      <div class="well well-large well-transparent lead">
        <i class="icon-spinner icon-spin icon-large"></i> Spinner icon when loading content...
      </div>
{% highlight html %}
<i class="icon-spinner icon-spin icon-large"></i> Spinner icon when loading content...
{% endhighlight %}
      <p class="alert alert-info">
        <i class="icon-info-sign"></i> CSS3 animations aren't supported in IE7 - IE9.
      </p>
    </div>
  </div>
</section>
