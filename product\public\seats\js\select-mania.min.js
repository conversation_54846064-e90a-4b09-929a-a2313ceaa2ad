/*! select-mania v1.1b */
!function(e){var a={defaults:{width:"100%",size:"medium",themes:[],placeholder:"Select an item",removable:!1,empty:!1,search:!1,ajax:!1,data:{},scrollContainer:null},setup:function(t){var i=Object.keys(this.defaults),n=!0;Object.keys(t).forEach(function(a){-1===e.inArray(a,i)&&(console.error("selectMania | wrong setup settings"),n=!1)}),n&&(this.defaults=e.extend(!0,{},a.defaults,t))}},t={internalSettings:function(e,a){return a.multiple=!1,a.values=[],a.multiple=e.is("[multiple]"),a.disabled=e.is("[disabled]"),e.find("option:selected").each(function(){a.values.push({value:this.value,text:this.text})}),a},getAttrSettings:function(e){var a={};return["width","size","placeholder","removable","empty","search","scrollContainer"].forEach(function(t){if(e.is("[data-"+t+"]")){var i=e.attr("data-"+t);"true"!==i&&"false"!==i||(i="true"===i),a[t]=i}}),a},initialize:function(a,t){var l=this,s=e.extend(!0,{},t),c=l.getAttrSettings(a);if((s=e.extend(s,c)).empty&&a.val(""),s=l.internalSettings(a,s),l.controlSettings(a,s)){var o=i.build(a,s);o.data("selectMania-originalSelect",a),a.data("selectMania-element",o),!1!==s.ajax&&l.initAjax(o,s),l.updateClean(o),a.addClass("select-mania-original"),o.insertBefore(a),a.appendTo(o),n.bind(o)}},update:function(a){var t=this;$selectManiaEl=a.data("selectMania-element"),$valueList=$selectManiaEl.find(".select-mania-values").first(),$itemList=$selectManiaEl.find(".select-mania-items").first(),a.is("[disabled]")?$selectManiaEl.addClass("select-mania-disabled"):$selectManiaEl.removeClass("select-mania-disabled"),$selectManiaEl.find(".select-mania-value").remove(),$itemList.empty(),a.find("option:selected").each(function(){e(this).is(":selected")&&$valueList.append(i.buildValue({value:this.value,text:this.text}))}),$itemList.append(i.buildItemList(a.children())),t.updateClean($selectManiaEl),n.bind($selectManiaEl)},destroy:function(e){$selectManiaEl=e.data("selectMania-element"),e.insertAfter($selectManiaEl),$selectManiaEl.remove(),e.removeClass("select-mania-original")},openDropdown:function(a){var i=this,l=a.closest(".select-mania");if(l.is("[data-selectMania-scrollContainer]")){var s=e(l.attr("data-selectMania-scrollContainer"));t.positionDropdown(a),a.addClass("select-mania-absolute"),s.off("scroll.selectMania").on("scroll.selectMania",function(){s.off("scroll.selectMania"),t.closeDropdown(e(".select-mania-dropdown.open"))}),e(window).off("resize.selectMania").on("resize.selectMania",function(){t.positionDropdown(a)})}a.stop().addClass("open").slideDown(100),a.find(".select-mania-items").scrollTop(0),i.focusSearch(a),e(document).off("keydown.selectMania").on("keydown.selectMania",n.keyboardControl)},closeDropdown:function(a){var t=a.data("selectMania-element");if(e(document).off("keydown.selectMania"),a.find(".select-mania-item").removeClass("select-mania-hover"),a.hasClass("select-mania-absolute")){var i=a.data("selectMania-element").find(".select-mania-inner").first();a.removeClass("open").hide().insertAfter(i),e(window).off("resize.selectMania");var n=e(t.attr("data-selectMania-scrollContainer"));n.length>0&&n.off("scroll.selectMania")}else a.stop().removeClass("open").slideUp(100)},positionDropdown:function(e){var a=e.data("selectMania-element"),t=e.find(".select-mania-items"),i=t.scrollTop(),n=a.offset(),l=a.outerWidth(),s=a.outerHeight();e.appendTo("body").css({position:"absolute",top:n.top+s,left:n.left,width:l}),t.scrollTop(i)},selectItem:function(e){var a=e.closest(".select-mania-dropdown"),l=a.data("selectMania-element"),s=l.data("selectMania-originalSelect");if(!e.is(".select-mania-selected")){var c=e.attr("data-value"),o=i.buildValue({value:c,text:e.text()});l.is(".select-mania-multiple")?(l.find(".select-mania-values").append(o),t.addMultipleVal(s,c)):(a.find(".select-mania-item").removeClass("select-mania-selected"),l.find(".select-mania-values .select-mania-value").remove(),l.find(".select-mania-values").append(o),s.val(c)),e.addClass("select-mania-selected"),s.trigger("change")}a.is(".select-mania-absolute")&&t.positionDropdown(a),l.is(".select-mania-multiple")||t.closeDropdown(a),t.updateClean(l),n.bind(l)},focusSearch:function(e){e.find(".select-mania-search-input").focus()},addMultipleVal:function(e,a){var t=e.val();t instanceof Array||(t=[]),t.push(a),e.val(t)},removeMultipleVal:function(a,t){var i=a.val();i instanceof Array||(i=[]),i.splice(e.inArray(t,i),1),a.val(i)},updateClean:function(e){var a=e.data("selectMania-originalSelect");null!==a.val()&&a.val().length>0?e.find(".select-mania-clear-icon").show():e.find(".select-mania-clear-icon").hide()},doSearch:function(a){var t=a.data("selectMania-dropdown"),i=t.find(".select-mania-search-input").first().val();""!==(i=i.toLowerCase().trim())?(t.find(".select-mania-item").each(function(){-1!==e(this).text().toLowerCase().indexOf(i)?e(this).removeClass("select-mania-hidden"):e(this).addClass("select-mania-hidden")}),t.find(".select-mania-group").each(function(){e(this).find(".select-mania-item:not(.select-mania-hidden)").length>0?e(this).removeClass("select-mania-hidden"):e(this).addClass("select-mania-hidden")})):t.find(".select-mania-group, .select-mania-item").removeClass("select-mania-hidden")},doSearchAjax:function(e){var a=this,i=e.data("selectMania-dropdown").find(".select-mania-search-input").first().val();e.data("selectMania-ajaxReady",!1),e.data("selectMania-ajaxPage",1),a.dropdownLoading(e),e.data("selectMania-ajaxFunction")(i,1,e.data("selectMania-ajaxData"),function(i){a.dropdownLoading(e,!0),t.replaceItems(e,i),n.bind(e),a.initAjax(e)})},addItems:function(e,a){this.addOrReplaceItems(e,a,!1)},replaceItems:function(e,a){this.addOrReplaceItems(e,a,!0)},addOrReplaceItems:function(a,t,l){var s=this,c=a.data("selectMania-dropdown"),o=a.data("selectMania-originalSelect"),r=c.find(".select-mania-items"),d=e(t);s.getVal(a).forEach(function(a){d.filter(function(){return e(this).attr("value")===a.value&&e(this).text()===a.text}).prop("selected",!0)}),$builtItems=i.buildItemList(d),!0===l&&(o.find("option").remove(":not(:checked)"),r.empty()),r.append($builtItems),o.append(d),n.bind(a)},initAjax:function(e,a){"object"==typeof a&&(a.hasOwnProperty("ajax")&&"function"==typeof a.ajax&&e.data("selectMania-ajaxFunction",a.ajax),a.hasOwnProperty("data")&&"object"==typeof a.data&&e.data("selectMania-ajaxData",a.data)),e.data("selectMania-ajaxPage",1),e.data("selectMania-ajaxReady",!0),e.data("selectMania-ajaxScrollDone",!1)},dropdownLoading:function(a,t){var i=!1;if(void 0!==t&&!0===t&&(i=!0),$dropdownContainer=a.find(".select-mania-items-container").first(),$dropdownContainer.find(".icon-loading-container").remove(),!0!==i){var n=e('<div class="icon-loading-container"></div>');n.append('<i class="icon-loading"></i>'),$dropdownContainer.append(n)}},getVal:function(a){var t=[];return a.find(".select-mania-value").each(function(){var a=e(this).find(".select-mania-value-text").first().text();t.push({value:e(this).attr("data-value"),text:a})}),t},clear:function(e){var a=e.data("selectMania-dropdown");e.find(".select-mania-value").remove(),a.find(".select-mania-item").removeClass("select-mania-selected");var t=e.data("selectMania-originalSelect");e.is(".select-mania-multiple")?t.val([]):t.val("")},setVal:function(a,t){var i=this,l=a.data("selectMania-originalSelect");i.clear(a),t.length>1&&!a.is(".select-mania-multiple")&&(t=t.slice(0,1)),t.forEach(function(t){var n=e.extend({value:"",text:"",selected:!0},t);i.setOneValSelectMania(a,n),i.setOneValOriginal(l,n)}),i.updateClean(a),n.bind(a)},setOneValSelectMania:function(a,t){var n=i.buildValue(t);a.find(".select-mania-values").append(n);var l=a.find('.select-mania-item[data-value="'+t.value+'"]').filter(function(){return e(this).text()===t.text});l.length>0&&l.first().addClass("select-mania-selected")},setOneValOriginal:function(a,t){var n=a.find('option[value="'+t.value+'"]').filter(function(){return e(this).text()===t.text});if(n.length<1){var l=i.buildOption(t);a.append(l)}else n.first()[0].selected=!0},controlTarget:function(a,t){return-1===e.inArray("isSelect",t)||a.is("select")?-1===e.inArray("isInitialized",t)||a.hasClass("select-mania-original")?-1!==e.inArray("notInitialized",t)&&a.hasClass("select-mania-original")?(console.error("selectMania | ignore because already initialized"),console.log(a[0]),!1):!(-1!==e.inArray("isSingle",t)&&a.length>1)||(console.error("selectMania | check method can be called on single element only"),console.log(a[0]),!1):(console.error("selectMania | select is not initialized"),console.log(a[0]),!1):(console.error("selectMania | invalid select element"),console.log(a[0]),!1)},controlSettings:function(a,t){return!1!==t.ajax&&"function"!=typeof t.ajax?(t.ajax=!1,console.error("selectMania | invalid ajax function"),console.log(a[0]),console.log(t),!1):-1===e.inArray(t.size,["tiny","small","medium","large"])?(t.size="medium",console.error("selectMania | invalid size"),console.log(a[0]),console.log(t),!1):!(null!==t.scrollContainer&&e(t.scrollContainer).length<1)||(t.scrollContainer=null,console.error("selectMania | invalid scroll container"),console.log(a[0]),console.log(t),!1)},controlValues:function(e,a){return a instanceof Array||(console.error("selectMania | values parameter is not a valid array"),console.log(e[0]),console.log(a),!1)},navigateItem:function(a,t){var i=a.closest(".select-mania"),n=a.find(".select-mania-items"),l=".select-mania-item:not(.select-mania-disabled):not(.select-mania-hidden)";i.hasClass("select-mania-multiple")&&(l+=":not(.select-mania-selected)");var s=a.find(l),c=a.find(l+".select-mania-hover"),o=e();if(c.length>0?"next"===t?o=s.slice(s.index(c)+1).first():"previous"===t&&(o=s.slice(0,s.index(c)).last()):o=s.first(),o.length>0){a.find(".select-mania-item").removeClass("select-mania-hover"),o.addClass("select-mania-hover");var r=o.position(),d=o.outerHeight(!0),u=n.height(),m=n.scrollTop();r.top<0?n.scrollTop(m+r.top):r.top+d>u&&n.scrollTop(m+r.top+d-u)}}},i={build:function(a,t){var i=this,n="select-mania-"+t.size,l='style="width:'+t.width+';"',s=e('<div class="select-mania '+n+'" '+l+"></div>");t.multiple&&s.addClass("select-mania-multiple"),t.disabled&&s.addClass("select-mania-disabled"),t.themes instanceof Array&&t.themes.length>0&&t.themes.forEach(function(e){s.addClass("select-mania-theme-"+e)}),!1!==t.ajax&&s.addClass("select-mania-ajax"),null!==t.scrollContainer&&s.attr("data-selectMania-scrollContainer",t.scrollContainer);var c=i.buildInner(t),o=i.buildDropdown(a,t);return s.append(c).append(o),s.data("selectMania-dropdown",o),o.data("selectMania-element",s),s},buildInner:function(a){var t=this,i=e('<div class="select-mania-inner"></div>'),n=e('<div class="select-mania-values"></div>'),l=e('<div class="select-mania-placeholder">'+a.placeholder+"</div>");n.append(l),a.values.forEach(function(e){n.append(t.buildValue(e))}),i.append(n);var s=e('<div class="select-mania-clear"></div>');return(a.removable||a.multiple)&&s.append('<i class="select-mania-clear-icon icon-cross">'),i.append(s),i.append(e('<div class="select-mania-arrow"><i class="select-mania-arrow-icon icon-arrow-down"></i></div>')),i},buildValue:function(a){var t='<div class="select-mania-value" data-value="'+a.value+'"><div class="select-mania-value-text">'+a.text+'</div><div class="select-mania-value-clear"><i class="select-mania-value-clear-icon icon-cross"></i></div></div>';return e(t)},buildOption:function(a){var t=e('<option value="'+a.value+'">'+a.text+"</option>");return t[0].selected=a.selected,t},buildDropdown:function(a,t){var i=this,n="select-mania-"+t.size,l=e('<div class="select-mania-dropdown '+n+'"></div>');if(t.search){var s=e('<div class="select-mania-dropdown-search"></div>');s.append('<input class="select-mania-search-input" />'),l.append(s)}var c=e('<div class="select-mania-items-container"></div>'),o=e('<div class="select-mania-items"></div>');return o.append(i.buildItemList(a.children())),c.append(o),l.append(c),t.themes instanceof Array&&t.themes.length>0&&t.themes.forEach(function(e){l.addClass("select-mania-theme-"+e)}),l},buildItemList:function(a){var t=this;return $itemList=e(),a.each(function(){e(this).is("optgroup")?$itemList=$itemList.add(t.buildItemGroup(e(this))):e(this).is("option")&&($itemList=$itemList.add(t.buildItem(e(this))))}),$itemList},buildItemGroup:function(a){var t=this;$group=e('<div class="select-mania-group"></div>');var i=e('<div class="select-mania-group-inner"></div>'),n=e('<div class="select-mania-group-title"></div>');a.is("[data-icon]")&&n.append('<div class="select-mania-group-icon"><i class="'+a.attr("data-icon")+'"></i></div>'),n.append('<div class="select-mania-group-text">'+a.attr("label")+"</div>"),$group.append(n);var l=a.is(":disabled");return l&&$group.addClass("select-mania-disabled"),a.find("option").each(function(){i.append(t.buildItem(e(this),l))}),$group.append(i),$group},buildItem:function(a,t){var i=a[0],n=e('<div class="select-mania-item" data-value="'+i.value+'"></div>');return a.is("[data-icon]")&&n.append('<div class="select-mania-item-icon"><i class="'+a.attr("data-icon")+'"></i></div>'),n.append('<div class="select-mania-item-text">'+i.text+"</div>"),(a.is(":disabled")||!0===l.def(t))&&n.addClass("select-mania-disabled"),a.is(":selected")&&n.addClass("select-mania-selected"),n}},n={bind:function(a){var t=this,i=a.data("selectMania-originalSelect"),n=a.data("selectMania-dropdown");a.is(".select-mania-disabled")?(i.off("focus.selectMania"),i.off("blur.selectMania"),a.find(".select-mania-clear-icon").off("click.selectMania"),a.find(".select-mania-value-clear-icon").off("click.selectMania"),a.find(".select-mania-inner").off("click.selectMania"),n.find(".select-mania-item:not(.select-mania-disabled)").off("mouseenter.selectMania"),n.find(".select-mania-item:not(.select-mania-disabled)").off("click.selectMania"),n.find(".select-mania-search-input").off("input.selectMania"),n.find(".select-mania-items").off("wheel.selectMania"),n.find(".select-mania-items").off("scroll.selectMania")):(e(document).off("click.selectMania").on("click.selectMania",t.documentClick),i.off("focus.selectMania").on("focus.selectMania",t.focus),i.off("blur.selectMania").on("blur.selectMania",t.blur),a.find(".select-mania-clear-icon").off("click.selectMania").on("click.selectMania",t.clearValues),a.find(".select-mania-value-clear-icon").off("click.selectMania").on("click.selectMania",t.clearValue),a.find(".select-mania-inner").off("click.selectMania").on("click.selectMania",t.dropdownToggle),n.find(".select-mania-item:not(.select-mania-disabled)").off("mouseenter.selectMania").on("mouseenter.selectMania",t.hoverItem),n.find(".select-mania-item:not(.select-mania-disabled)").off("click.selectMania").on("click.selectMania",t.itemSelection),n.find(".select-mania-search-input").off("input.selectMania").on("input.selectMania",t.inputSearch),n.find(".select-mania-items").off("wheel.selectMania").on("wheel.selectMania",t.scrollControl),a.is(".select-mania-ajax")&&n.find(".select-mania-items").off("scroll.selectMania").on("scroll.selectMania",t.scrollAjax))},dropdownToggle:function(a){a.stopPropagation();var i=e(this).closest(".select-mania").data("selectMania-dropdown");i.is(".open")?t.closeDropdown(i):(t.closeDropdown(e(".select-mania-dropdown.open")),t.openDropdown(i))},documentClick:function(a){e(a.target).closest(".select-mania-dropdown").length<1&&t.closeDropdown(e(".select-mania-dropdown.open"))},clearValues:function(a){a.stopPropagation();var i=e(this).closest(".select-mania"),n=i.data("selectMania-dropdown"),l=i.data("selectMania-originalSelect");t.clear(i),n.is(".select-mania-absolute")&&t.positionDropdown(n),l.trigger("change"),t.updateClean(i)},clearValue:function(a){a.stopPropagation();var i=e(this).closest(".select-mania"),n=i.data("selectMania-dropdown"),l=e(this).closest(".select-mania-value");n.find('.select-mania-item[data-value="'+l.attr("data-value")+'"]').removeClass("select-mania-selected"),l.remove();var s=i.data("selectMania-originalSelect");t.removeMultipleVal(s,l.attr("data-value")),n.is(".select-mania-absolute")&&t.positionDropdown(n),s.trigger("change"),t.updateClean(i)},itemSelection:function(){var a=e(this);t.selectItem(a)},inputSearch:function(){var a=e(this);$selectManiaEl=a.closest(".select-mania-dropdown").data("selectMania-element");var i=200;$selectManiaEl.is(".select-mania-ajax")&&(i=400),clearTimeout(a.data("selectMania-searchTimer")),a.data("selectMania-searchTimer",setTimeout(function(){$selectManiaEl.is(".select-mania-ajax")?t.doSearchAjax($selectManiaEl):t.doSearch($selectManiaEl)},i))},scrollAjax:function(a){var i=e(this),l=i.closest(".select-mania-dropdown").data("selectMania-element");if(!0!==l.data("selectMania-ajaxScrollDone")&&i.scrollTop()>=i[0].scrollHeight-i.outerHeight()-12&&!0===l.data("selectMania-ajaxReady")){var s=l.data("selectMania-ajaxPage")+1,c=l.find(".select-mania-search-input").first().val();l.data("selectMania-ajaxReady",!1),l.data("selectMania-ajaxPage",s),t.dropdownLoading(l),l.data("selectMania-ajaxFunction")(c,s,l.data("selectMania-ajaxData"),function(e){t.dropdownLoading(l,!0),""!==e.trim()?(t.addItems(l,e),n.bind(l),l.data("selectMania-ajaxReady",!0)):l.data("selectMania-ajaxScrollDone",!0)})}},scrollControl:function(a){var t=e(this);return a.originalEvent.deltaY<0?t.scrollTop()>0:t.scrollTop()+t.innerHeight()<t[0].scrollHeight},focus:function(a){var t=e(this);t.data("selectMania-element").addClass("select-mania-focused"),t.off("keydown.selectMania").on("keydown.selectMania",n.keyboardOpening)},blur:function(a){var t=e(this);t.data("selectMania-element").removeClass("select-mania-focused"),t.off("keydown.selectMania")},hoverItem:function(a){var t=e(this);t.closest(".select-mania-dropdown").find(".select-mania-item").removeClass("select-mania-hover"),t.addClass("select-mania-hover")},keyboardOpening:function(a){var i=e(this),n=i.data("selectMania-element").data("selectMania-dropdown"),l=[13,32,37,38,39,40];n.hasClass("open")||-1===e.inArray(a.keyCode,l)||(a.preventDefault(),a.stopPropagation(),i.blur(),t.openDropdown(n))},keyboardControl:function(a){var i=e(".select-mania-dropdown.open").first(),n=[9,13,27,38,40];if(i.length>0&&-1!==e.inArray(a.keyCode,n))switch(a.preventDefault(),a.stopPropagation(),a.keyCode){case 13:var l=i.find(".select-mania-item:not(.select-mania-disabled):not(.select-mania-hidden).select-mania-hover").first();l.length>0&&t.selectItem(l);break;case 9:case 27:t.closeDropdown(i);break;case 38:t.navigateItem(i,"previous");break;case 40:t.navigateItem(i,"next")}}},l={def:function(e){return void 0===e?null:e}},s={init:function(i){var n=e.extend(!0,{},a.defaults,i);return this.each(function(){var a=e(this);t.controlTarget(a,["isSelect","notInitialized"])&&t.initialize(a,n)})},update:function(){return this.each(function(){var a=e(this);t.controlTarget(a,["isInitialized"])&&t.update(a)})},destroy:function(){return this.each(function(){var a=e(this);t.controlTarget(a,["isInitialized"])&&t.destroy(a)})},check:function(){if(t.controlTarget(this,["isSingle"]))return this.hasClass("select-mania-original")},get:function(){if(t.controlTarget(this,["isSingle","isInitialized"])){var e=this.data("selectMania-element");return t.getVal(e)}},set:function(e){if(t.controlTarget(this,["isSingle","isInitialized"])&&t.controlValues(this,e)){var a=this.data("selectMania-element");t.setVal(a,e)}},clear:function(){return this.each(function(){var a=e(this);if(t.controlTarget(a,["isInitialized"])){var i=a.data("selectMania-element");t.clear(i),a.trigger("change"),t.updateClean(i)}})},setup:function(){return this.each(function(){var a=e(this);if(t.controlTarget(a,["isInitialized"])){var i=a.data("selectMania-element");t.clear(i),a.trigger("change"),t.updateClean(i)}})}};e.fn.selectMania=function(e){if(!(this.length<1)){if(s[e]){var a=Array.prototype.slice.call(arguments,1);return s[e].apply(this,a)}if("object"==typeof e||!e)return s.init.apply(this,arguments);console.error("selectMania | wrong method called"),console.log(this)}},e.extend({selectManiaSetup:function(e){a.setup(e)}})}(jQuery);