<template>
  <div class="project-information">
    <div class="button-box-list">
      <el-button type="primary"
                 v-permissions="'auth:specialsubjectnews:add'"
                 @click="newData">新增</el-button>
      <el-button type="primary"
                 v-permissions="'auth:specialsubjectnews:dels'"
                 @click="deleteClick">删除</el-button>
      <el-input placeholder="请输入内容"
                clearable
                v-model="name"
                @keyup.enter.native="search">
        <div slot="prefix"
             class="input-search"></div>
      </el-input>
    </div>
    <div class="information-mosaic-list scrollBar">
      <el-table :data="tableData"
                stripe
                border
                ref="table"
                slot="zytable"
                @select="selected"
                @select-all="selectedAll">
        <el-table-column type="selection"
                         width="48">
        </el-table-column>
        <!-- <el-table-column label="标题"
                         show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button @click="details(scope.row)"
                       type="text"
                       size="small">{{scope.row.title}}</el-button>
          </template>
        </el-table-column> -->
        <el-table-column label="标题"
                         width="60"
                         prop="sort">
        </el-table-column>
        <el-table-column label="标题"
                         prop="title"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="发布人"
                         prop="createName"
                         width="120"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="发布时间"
                         width="160"
                         prop="publishDate"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="是否发布"
                         width="90">
          <template slot-scope="scope">
            <div class="table-icon">
              <i class="el-icon-check"
                 v-if="scope.row.isPublish=='1'"></i>
              <i class="el-icon-close"
                 v-else></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作"
                         v-if="$hasPermission(['auth:specialsubjectnews:edit'])"
                         width="80">
          <template slot-scope="scope">
            <el-button @click="handleClick(scope.row)"
                       type="text"
                       size="small">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>

    <zy-pop-up v-model="show"
               :title="mosaicId==''?'新增专题资讯':'编辑专题资讯'">
      <project-information-new :id="id"
                               :mosaicId="mosaicId"
                               @callback="newCallback"></project-information-new>
    </zy-pop-up>

  </div>
</template>
<script>
import tableData from '@mixins/tableData'
import projectInformationNew from './project-information-new/project-information-new'
export default {
  name: 'projectInformation',
  data () {
    return {
      name: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      mosaicId: '',
      show: false
    }
  },
  mixins: [tableData],
  props: ['id'],
  components: {
    projectInformationNew
  },
  mounted () {
    if (this.id) {
      this.customTopicinformation()
    }
  },
  methods: {
    search () {
      this.customTopicinformation()
    },
    async customTopicinformation () {
      const res = await this.$api.appManagement.customTopicinformation({
        title: this.name,
        subjectId: this.id,
        pageNo: this.page,
        pageSize: this.pageSize
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    handleClick (row) {
      this.mosaicId = row.id
      this.show = true
    },
    howManyArticle (val) {
      this.customTopicinformation()
    },
    whatPage (val) {
      this.customTopicinformation()
    },
    newData () {
      this.mosaicId = ''
      this.show = true
    },
    newCallback () {
      this.customTopicinformation()
      this.show = false
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的专题资讯, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.customTopicinformationDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async customTopicinformationDel (id) {
      const res = await this.$api.appManagement.customTopicinformationDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.customTopicinformation()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
@import "./project-information.scss";
</style>
