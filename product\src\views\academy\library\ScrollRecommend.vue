<template>
  <div class="ScrollRecommend">
    <search-button-box @search-click="search"
                       @reset-click="reset">
      <template slot="button">
        <el-button icon="el-icon-plus"
                   @click="newData"
                   type="primary">新增</el-button>
        <el-button icon="el-icon-delete"
                   @click="deleteClick"
                   type="primary">删除</el-button>
      </template>
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="isIssue"
                   clearable
                   placeholder="请选择是否发布">
          <el-option label="已发布"
                     value="1"></el-option>
          <el-option label="未发布"
                     value="0"></el-option>
        </el-select>
      </template>
    </search-button-box>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="序号"
                           width="120"
                           prop="sort">
          </el-table-column>
          <el-table-column label="推荐词"
                           min-width="260"
                           prop="recommendedWord"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="关联书籍"
                           width="190">
            <template slot-scope="scope">
              <el-button type="text"
                         v-if="scope.row.bookId"
                         @click="library(scope.row.bookId)">{{scope.row.bookName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="封面图"
                           width="190">
            <template slot-scope="scope">
              <div class="table-img">
                <el-image style="width: 100%; height: 100%"
                          :src="scope.row.coverImgUrl"
                          :preview-src-list="[scope.row.coverImgUrl]">
                </el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="180">
            <template slot-scope="scope">
              <el-button @click="addeditor(scope.row)"
                         type="primary"
                         plain
                         size="mini">{{scope.row.isIssue=='1'?'撤回':'发布'}}</el-button>
              <el-button @click="editor(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="libraryShow"
               title="书籍详情">
      <libraryDetails :id="id"> </libraryDetails>
    </zy-pop-up>
    <zy-pop-up v-model="show"
               :title="id?'编辑滚动推荐':'新增滚动推荐'">
      <ScrollRecommendNew :id="id"
                          @newCallback="newCallback"> </ScrollRecommendNew>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import libraryDetails from '../library/libraryDetails'
import ScrollRecommendNew from './ScrollRecommendNew'
export default {
  name: 'ScrollRecommend',
  data () {
    return {
      keyword: '',
      isIssue: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      id: '',
      show: false,
      detailsShow: false,
      libraryShow: false
    }
  },
  mixins: [tableData],
  components: {
    libraryDetails,
    ScrollRecommendNew
  },
  mounted () {
    this.syRollBookList()
  },
  methods: {
    search () {
      this.syRollBookList()
    },
    reset () {
      this.keyword = ''
      this.isIssue = ''
      this.syRollBookList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    library (row) {
      this.id = row
      this.libraryShow = true
    },
    editor (row) {
      this.id = row.id
      this.show = true
    },
    newCallback () {
      this.syRollBookList()
      this.show = false
    },
    async syRollBookList () {
      const res = await this.$api.academy.syRollBookList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        isIssue: this.isIssue
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
    },
    howManyArticle () {
      this.syRollBookList()
    },
    whatPage () {
      this.syRollBookList()
    },
    addeditor (row) {
      if (row.isIssue === 1) {
        this.syRollBookUnpublish(row.id)
      } else {
        this.syRollBookIssue(row.id)
      }
    },
    async syRollBookIssue (id) {
      const res = await this.$api.academy.syRollBookIssue({
        id: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.syRollBookList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async syRollBookUnpublish (id) {
      const res = await this.$api.academy.syRollBookUnpublish({
        id: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.syRollBookList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的滚动推荐, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.syRollBookDels(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async syRollBookDels (id) {
      const res = await this.$api.academy.syRollBookDels({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.syRollBookList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.ScrollRecommend {
  height: 100%;
  width: 100%;
  .tableData {
    height: calc(100% - 116px);
    .table-img {
      height: 38px;
      overflow: hidden;

      .el-image__inner {
        width: auto;
      }
      .el-icon-circle-close {
        color: #fff;
      }
    }
  }
}
</style>
