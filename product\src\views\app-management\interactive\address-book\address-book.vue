<template>
  <div class="address-book">
    <search-button-box @search-click="search" :resetButton="false">
      <template slot="button">
        <el-button type="primary" icon="el-icon-plus" v-permissions="'auth:wholeuser:chaters'" @click="newData">新增
        </el-button>
      </template>
      <template slot="search">
        <el-input placeholder="请输入内容" v-model="keyword" clearable @keyup.enter.native="search">
        </el-input>
      </template>
    </search-button-box>
    <div class="information-box">
      <div class="information-tree-box">
        <div class="information-tree-text">
          <el-radio-group v-model="radio">
            <el-radio :label="11">{{ $system() }}{{ $position() }}</el-radio>
            <el-radio :label="12">{{ $system() }}机关</el-radio>
            <el-radio :label="19">民主党派</el-radio>
            <el-radio :label="22">市工商联</el-radio>
            <el-radio :label="20">区市政协</el-radio>
          </el-radio-group>
        </div>
        <div class="information-tree">
          <zy-tree :tree="tree" v-model="treeId" :props="{ children: 'children', label: 'name' }"
            @on-tree-click="choiceClick"></zy-tree>
        </div>
      </div>
      <div class="information-data-box">
        <div class="information-list">
          <zy-table>
            <el-table :data="tableData" stripe border ref="table" slot="zytable" @select="selected"
              @select-all="selectedAll">
              <el-table-column type="selection" fixed="left" width="60"></el-table-column>
              <el-table-column label="序号" prop="sort">
              </el-table-column>
              <el-table-column label="姓名" prop="userName">
              </el-table-column>
              <el-table-column label="操作"
                v-if="$hasPermission(['auth:wholeuser:chatersort', 'auth:wholeuser:removechater'])">
                <template slot-scope="scope">
                  <el-button @click="modify(scope.row)" type="primary" v-permissions="'auth:wholeuser:chatersort'" plain
                    size="mini">编辑</el-button>
                  <el-button @click="deleteClick(scope.row)" type="danger" v-permissions="'auth:wholeuser:removechater'"
                    plain size="mini">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </zy-table>
        </div>
        <div class="paging_box">
          <el-pagination @size-change="howManyArticle" @current-change="whatPage" :current-page.sync="page"
            :page-sizes="[10, 20, 50, 80, 100, 200, 500]" :page-size.sync="pageSize" background
            layout="total, sizes, prev, pager, next, jumper" :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
    <zy-pop-up v-model="show" title="编辑">
      <address-book-add :businessId="treeId" :businessType="radio" :userId="userId" :name="name" :sort="sort"
        @addCallback="addCallback"></address-book-add>
    </zy-pop-up>
    <zy-pop-up v-model="userShow" title="新增用户">
      <candidates-user point="point_10" :data="data" @userCallback="userCallback"></candidates-user>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@mixins/tableData'
import addressBookAdd from './address-book-add'
export default {
  name: 'addressBook',
  data () {
    return {
      keyword: '',
      radio: 11,
      treeId: '',
      tree: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: 10,
      data: [],
      userShow: false,
      name: '',
      sort: '',
      userId: '',
      show: false
    }
  },
  inject: ['newTab'],
  mixins: [tableData],
  components: {
    addressBookAdd
  },
  mounted () {
    this.treeTist()
  },
  watch: {
    radio (val) {
      this.treeTist()
    }
  },
  methods: {
    search () {
      this.treeTistUser()
    },
    /**
     * 新增的点击方法
     */
    newData () {
      if (this.treeId) {
        console.log(this.treeId)
        this.$api.systemSettings.poinexistsids('point_10', { businessType: this.radio, businessId: this.treeId }).then(res => {
          var { data, errcode } = res
          if (errcode === 200) {
            this.data = data
            this.userShow = true
          }
        })
      } else {
        this.$message({
          message: '请先选择通讯录分组！',
          type: 'warning'
        })
      }
    },
    /**
     * 选择用户的回调
     */
    userCallback (data, type) {
      this.userShow = false
      if (type) {
        var arr = []
        data.forEach(item => {
          arr.push(item.userId)
        })
        this.userelationNew(arr.join(','))
      }
    },
    /**
     * 获取当前分组下面的用户
     */
    async userelationNew (ids) {
      console.log(this.treeId)
      const res = await this.$api.appManagement.userelationNew({ businessType: this.radio, businessId: this.treeId, userIds: ids })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.treeTistUser()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    /**
     * 编辑按钮
     */
    modify (row) {
      this.name = row.userName
      this.userId = row.userId
      this.sort = row.sort
      this.show = true
    },
    /**
     * 编辑页面回调函数
     */
    addCallback () {
      this.treeTistUser()
      this.show = false
    },
    /**
     * 获取分组树结构
     */
    async treeTist () {
      const res = await this.$api.appManagement.treeTist({
        treeType: this.radio
      })
      var { data } = res
      this.tree = data
    },
    /**
     * 点击分组树结构组件的回调
     */
    choiceClick (item) {
      this.treeTistUser()
    },
    /**
     * 获取分组下面的用户
     */
    async treeTistUser () {
      const res = await this.$api.appManagement.treeTistUser({
        businessId: this.treeId,
        businessType: this.radio,
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.treeTistUser()
    },
    whatPage (val) {
      this.treeTistUser()
    },
    /**
     * 点击删除方法调用接口
     */
    deleteClick (row) {
      console.log(row)
      this.$confirm('此操作将删除当前选中分组中的用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // this.userelationDel(row.userId)
        this.$api.appManagement.userelationDel({
          businessId: row.businessId,
          businessType: this.radio,
          userId: row.userId
        }).then(res => {
          if (res.errcode === 200) {
            this.treeTistUser()
            this.$message.success('删除成功')
          } else {
            this.$message.error('删除失败')
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
<style lang="scss">
.address-book {
  width: 100%;
  height: 100%;

  .information-box {
    height: calc(100% - 64px);
    width: 100%;
    display: flex;

    .information-tree-box {
      width: 355px;
      height: 100%;

      .information-tree-text {
        height: 52px;
        line-height: 52px;
        padding-left: 24px;
        font-size: $textSize16;
        background-color: #e6e5e8;
      }

      .information-tree {
        height: calc(100% - 52px);
      }
    }

    .information-data-box {
      width: calc(100% - 380px);
      height: 100%;
      border-left: 1px solid #e6e5e8;

      .information-list {
        height: calc(100% - 52px);
        width: 100%;
      }
    }
  }
}
</style>
