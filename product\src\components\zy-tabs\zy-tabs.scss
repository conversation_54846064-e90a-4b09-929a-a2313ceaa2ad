.zy-tabs {
  width: 100%;
  height: 108px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding: 0 24px;
  border-bottom: 1px solid #e6e6e6;

  .zy-tabs-left {
    position: absolute;
    top: 16px;
    left: 0;
    height: 76px;
    line-height: 76px;
    width: 32px;
    padding-left: 9px;
    text-align: left;
    background-color: #fff;
    box-shadow: -10px 0px 50px 52px rgba(255, 255, 255, 0.8);
    z-index: 2;
  }

  .zy-tabs-right {
    position: absolute;
    top: 16px;
    right: 0;
    height: 76px;
    line-height: 76px;
    width: 32px;
    padding-right: 9px;
    text-align: right;
    background-color: #fff;
    box-shadow: 10px 0px 50px 52px rgba(255, 255, 255, 0.8);
    z-index: 2;
  }

  .zy-tabs-box {
    width: 100%;
    height: 76px;
    overflow: hidden;

    .zy-tabs-item-list {
      height: 100%;
      white-space: nowrap;
      float: left;

      .zy-tabs-item {
        height: 100%;
        width: 116px;
        background-color: $zy-color;
        border: 1px solid $zy-color;
        border-radius: 4px;
        display: inline-block;
        cursor: pointer;
        overflow: hidden;

        .zy-tabs-item-number {
          height: 36px;
          line-height: 36px;
          width: 100%;
          background-color: #e0eaf2;
          color: $zy-color;
          font-size: 20px;
          font-weight: bold;
          text-align: center;
        }

        .zy-tabs-item-text {
          height: 40px;
          line-height: 40px;
          width: 100%;
          color: #000;
          background-color: #fff;
          font-size: $textSize14;
          text-align: center;
        }
      }

      .zy-tabs-item-active {
        .zy-tabs-item-number {
          background-color: transparent;
          color: #fff !important;
        }
      }

      .zy-tabs-item + .zy-tabs-item {
        margin-left: 9px;
      }
    }
  }
}
